<template>
  <el-container class="page-container">
    <!-- 右侧列表 -->
    <el-header v-if="pageMode==='full'"></el-header>
    <el-container>
      <!--列表工具栏-->
      <el-header>
        <div class="toolbar-wrapper">
        <perm-button label="新增" type="text" icon="add" :perms="systemCode + 'x99008001'" @click="handleCreate"/>
        </div>
        <!--列表查询区-->
        <el-form :inline="true" :model="filters" :size="size">
          <el-form-item label="清理任务名称">
            <el-input v-model="filters.cleanName" maxlength="100" placeholder="请输入编码规则名称" clearable @keyup.enter.native="handleFilter"/>
          </el-form-item>
          <el-form-item label="是否发布">
            <select-plus dictType="IS_FLAG" v-model="filters.publicFlag" style="width: 150px" clearable></select-plus>
          </el-form-item>
          <el-form-item label="是否启用">
            <select-plus dictType="IS_FLAG" v-model="filters.enabledFlag" style="width: 150px" clearable></select-plus>
          </el-form-item>
          <el-form-item label="最近运行状态">
            <select-plus dictType="UIMS550_RUN_STATUS" v-model="filters.runStatus" style="width: 150px" clearable></select-plus>
          </el-form-item>
          <perm-button type="primary" label="查询" icon="uims-icon-query" @click="handleFilter"/>
          <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
        </el-form>
      </el-header>
      <!--列表表格区-->
      <el-main>
        <table-plus id="cleanData" :data="list" ref="multipleTable" border fit stripe highlight-current-row v-loading="listLoading" @row-click="clickRow" @selection-change="onSelected">
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column prop="cleanName" label="清理任务名称" width="260"></el-table-column>
          <el-table-column prop="retentionDuration" label="保留时间（天）" width="160"></el-table-column>
          <el-table-column prop="publicFlag" label="是否发布" align="center" width="120"></el-table-column>
          <el-table-column prop="enabledFlag" label="是否启用" align="center" width="120"></el-table-column>
          <el-table-column prop="runStatus" label="最近运行状态" align="center" width="120"></el-table-column>
          <el-table-column prop="startTime" label="最近运行时间" align="center" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center" width="220">
            <template slot-scope="scope">
              <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
            </template>
          </el-table-column>
        </table-plus>
      </el-main>
      <el-footer>
        <table-footer ref="tableFooter" excelName="清理数据配置" :showToolBar="true" :showPage="true" :tableRef="this.$refs.multipleTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total"
        url="/framework/clean/list"
        :filters="filters">
        </table-footer>
      </el-footer>
    </el-container>
    <clean-data-dialog @closeDialog="closeDialog" @getList="getList" :dialogStatus="dialogStatus" :codeRuleArray="selectTableRow" :dialogFormVisible="dialogFormVisible" :contentVisible="contentVisible">
    </clean-data-dialog>
    <clean-log-dialog @closeDialog="closeLogDialog" :codeRuleArray="selectTableRow" :dialogLogVisible="dialogLogVisible">
    </clean-log-dialog>
  </el-container>
</template>

<script>
import PermButtonGroup from "@/core/components/PermButtonGroup";
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus";
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SelectPlus from "@/core/components/SelectPlus";
import { resourceCode } from "@/biz/http/settings"
import CleanDataDialog from "./dialog/CleanDataDialog"
import CleanLogDialog from "./dialog/CleanLogDialog"
export default {
  components: { TablePlus, PermButton, TableFooter, SelectPlus, PermButtonGroup, CleanDataDialog, CleanLogDialog },
  data() {
    return {
      queryBt: true,
      systemCode: config.systemCode,
      dialogFormVisible: false,
      dialogLogVisible: false,
      dialogStatus: '',
      contentVisible: true,
      // table参数
      size: 'mini',
      list: [],
      listLoading: true,
      total: 0,
      currentRow: undefined,
      selectTableRow: [],
      // table查询参数
      filters: {
        currentPage: 1,
        pageSize: 20,
        cleanName: undefined,
        publicFlag: undefined,
        enabledFlag: undefined,
        runStatus: undefined
      },
      // 字典项
      dict: {
        ruleType: undefined
      },
      // 新增/编辑列表
      temp: {
        ruleId: undefined,
        systemId: undefined,
        systemName: undefined,
        name: undefined,
        type: undefined,
        sortPattern: undefined,
        rulePattern: undefined,
        serialLen: undefined,
        serialStart: undefined,
        serialStep: undefined,
        remark: undefined
      }
    }
  },
  computed: {
    pageMode() {
      return config.page_mode;
    }
  },
  // 执行方法
  methods: {
    getButtons(row) {
      let buts = [
        {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99008002'},
        {label: "启用", icon: "uims-icon-start", clickFn: this.handleEnable, perms: this.systemCode + 'x99008003'},
        {label: "停用", icon: "uims-icon-stop", clickFn: this.handleDisable, perms: this.systemCode + 'x99008004'},
        {label: "作废", icon: "uims-icon-delete", clickFn: this.handleDelete, perms: this.systemCode + 'x99008005'},
        {label: "立即执行", icon: "uims-icon-binding", clickFn: this.handleRun, perms: this.systemCode + 'x99008006'},
        {label: "任务日志", icon: "uims-icon-view", clickFn: this.handleViewLog, perms: this.systemCode + 'x99008007'},
        {label: "查看", icon: "uims-icon-view", clickFn: this.handleView, perms: this.systemCode + 'x99008008'}
      ];
      if(row.enabledFlagCode === '1') {
        buts.splice(1, 1)
      } else {
        buts.splice(2, 1)
      }
      if (row.publicFlagCode === '0') {
        buts.splice(3, 2)
        buts.splice(1, 1)
      }
      return {
        row: row,
        buttons: buts,
        showNums: 2
      }
    },
    // 监听表头拖拽事件
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    closeDialog(val) {
      this.dialogFormVisible = val
    },
    closeLogDialog(val) {
      this.dialogLogVisible = val
    },
    handleCreate() {
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
      this.contentVisible = false;
    },
    handleRun(row) {
      let cleanId = row.cleanId
      this.$confirm('您确认要立即执行该清理任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.cleanData.run({ cleanId: cleanId }, resourceCode.codeRule_del).then((res) => {
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '执行清理任务成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消执行',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleDisable(row) {
      let cleanId = row.cleanId
      this.$confirm('您确认要停用该清理任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.cleanData.enabledFlag({ cleanId: cleanId, enabledFlag: '0' }, resourceCode.codeRule_del).then((res) => {
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '停用清理任务成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消停用',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleEnable(row) {
      let cleanId = row.cleanId
      this.$confirm('您确认要启用该清理任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.cleanData.enabledFlag({ cleanId: cleanId, enabledFlag: '1' }, resourceCode.codeRule_del).then((res) => {
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '启用清理任务成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消启用',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleUpdate(row) {
      this.selectTableRow = [row]
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.contentVisible = false
    },
    handleView(row) {
      this.selectTableRow = [row]
      this.dialogStatus = 'view'
      this.dialogFormVisible = true
      this.contentVisible = true
    },
    handleDelete(row) {
      let cleanId = row.cleanId
      this.$confirm('您确认要作废该清理任务吗？作废后无法查看任务清理日志，如后续有查看日志需要，请停用任务', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.cleanData.remove({ cleanId: cleanId }, resourceCode.codeRule_del).then((res) => {
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '作废清理任务成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消作废',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleViewLog(row) {
      this.selectTableRow = [row]
      this.dialogLogVisible = true
    },
    handleFilter(resetFlag) {
      this.filters.currentPage = 1
      if(resetFlag === true) {
        this.filters.cleanName = undefined
        this.filters.publicFlag = undefined
        this.filters.enabledFlag = undefined
        this.filters.runStatus = undefined
      }
      this.getList()
    },
    getList() {
      if(!this.queryBt) {
        return;
      }
      this.queryBt = false
      this.listLoading = true
      this.$api.cleanData.getList(this.filters, resourceCode.codeRule).then((res) => {
        this.queryBt = true
        this.listLoading = false
        this.list = res.data.records
        this.total = res.data.total
      }).catch(e => {
        this.queryBt = true
        this.$errMsgBox.show({
          text: "获取数据出错",
          error: e
        })
      })
    },
    clickRow(row, event, column) {
      if (this.currentRow === row) {
        this.currentRow = undefined
        this.$refs.multipleTable.clearSelection();
      } else {
        this.currentRow = row
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    onSelected(row, event, column) {
      this.selectTableRow = Object.assign([], row);
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    ruleTypeFormatter(row) {
      if (!row || !row.type || !this.dict.ruleType) {
        return ''
      }
      for (let item of this.dict.ruleType) {
        if (item.code === row.type) {
          return item.name
        }
      }
    }
  },
  created() {
    this.getList()
  },
  watch: {
    total() {
      if (this.total === (this.filters.currentPage - 1) * this.filters.pageSize && this.total !== 0) {
        this.filters.currentPage -= 1;
        this.getList();
      }
    }
  }
}
</script>
