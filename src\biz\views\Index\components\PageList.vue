<template>
  <div class="widgetMyWork">
    <div class="searchCont">
      <div class="searchList">
        <!-- <h5>系统分类</h5>
            <div class="searchInp">
              <span class="searchItem" v-for="(item, index) in queryParamsList" :key="index">{{ item.queryName
                }}（<span>{{ item.queryValue }}</span>）</span>
            </div> -->
        <h5>范围</h5>
        <div class="searchInp">
          <el-form :inline="true" :model="filters">
            <el-form-item label="提供单位">
              <el-input v-model.trim="filters.unitName" clearable placeholder="请输入提供单位"
                @keyup.enter.native="handleFilter" maxlength="20" />
            </el-form-item>
            <el-form-item label="来源系统" style="margin-left: 30px;">
              <el-input v-model.trim="filters.powerName" clearable placeholder="请输入来源系统"
                @keyup.enter.native="handleFilter" maxlength="20" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="searchList">
        <h5>更多筛选</h5>
        <div class="searchInp">
          <el-form :inline="true" :model="filters">
            <el-form-item label="">
              <SelectPlus dictType="POWER_SHAPE" v-model="filters.powerShape" @change="handleFilter" clearable
                placeholder="资源类型" style="width: 122px;" />
            </el-form-item>
            <el-form-item label="">
              <SelectPlus dictType="POWER_SHAPE" v-model="filters.powerShape" @change="handleFilter" clearable
                placeholder="共享类型" style="width: 122px;" />
            </el-form-item>
            <el-form-item label="">
              <SelectPlus dictType="POWER_SHAPE" v-model="filters.powerShape" @change="handleFilter" clearable
                placeholder="更新周期" style="width: 122px;" />
            </el-form-item>
            <el-form-item label="发布时间">
              <el-date-picker v-model="filters.dateRange1" type="daterange" value-format="yyyy-MM-dd"
                @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                style="width: 240px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="更新时间">
              <el-date-picker v-model="filters.dateRange2" type="daterange" value-format="yyyy-MM-dd"
                @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                style="width: 240px;">
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- <div class="searchList">
            <h5></h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
              </el-form>
            </div>
          </div> -->
      <div class="searchList">
        <h5>标签</h5>
        <div class="searchInp">
          <el-checkbox-group v-model="filters.checkList">
            <el-checkbox label="全部"></el-checkbox>
            <el-checkbox v-for="(item, index) in checkList" :key="index"
              :label="item.checkName + '（' + item.checkValue + '）'">{{
            item.checkName }}（{{ item.checkValue }}）</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div class="dataCont">
      <div class="topSort">
        <el-checkbox v-model="checked">仅显示未订阅</el-checkbox>
        <div class="sort">
          <div v-for="(item, index) in sortItems" :key="index">
            <span class="iconLabel" :class="{ 'active': item.active }" @click="sort(item)">
              {{ item.label }}
            </span>
            <span v-if="item.sort" class="iconList">
              <i class="el-icon-caret-top" :class="{ 'active': item.order === 1 }" @click="orderChange(item, 1)"></i>
              <i class="el-icon-caret-bottom" :class="{ 'active': item.order === -1 }"
                @click="orderChange(item, -1)"></i>
            </span>
          </div>
        </div>
      </div>
      <div class="borderSplit"></div>
      <div class="listCont">
        <div class="listItem" v-for="item in dataList" :key="item.id">
          <div class="line1">
            <div class="title">{{ item.itemTitle }} <span>
                <img v-if="item.itemType === '1'" src="~@/assets/img/icon-withIn.png" alt="">
                <img v-if="item.itemType === '2'" src="~@/assets/img/icon-withOut.png" alt="">
              </span> </div>
            <div class="itemStatus">
              <span v-for="(it, index) in item.itemStatus" :key="index">
                {{ it }}

              </span>
            </div>
          </div>
          <div class="line2">
            <div class="itemMes">
              <span class="message">单位：<span>{{ item.unit }}</span></span>
              <span class="message">更新周期：<span>{{ item.updateCycle }}</span></span>
              <span class="message">发布时间：<span>{{ item.pubTime }}</span></span>
              <br>
              <span class="message">来源系统：<span>{{ item.sourceSys }}</span></span>
              <span class="message">标签：
                <span class="colorGreen" v-if="item.label === '1'">一般</span>
                <span class="colorOrange" v-else-if="item.label === '2'">重要</span>
              </span>
            </div>
            <div class="itemSourceType">
              <div class="itemSourceItem" v-for="(it, index) in item.sourceTypeList" :key="index">
                <div class="iconSchema" v-if="it === '1'"></div>
                <div class="iconTable" v-else-if="it === '2'"></div>
                <span v-if="it === '1'">接口</span>
                <span v-else-if="it === '2'">库表</span>
              </div>
            </div>
          </div>
          <div class="line3">
            <div class="itemNum">
              <span class="num">{{ item.visitNum }}次<span>访问</span></span>
              <span class="num">{{ item.collectNum }}次<span>收藏</span></span>
            </div>
            <div class="itemHandleBtn">
              <!-- <el-button type="primary" plain icon="el-icon-edit">订阅</el-button>
                  <el-button type="primary" plain icon="el-icon-edit">加入选数车</el-button>
                  <el-button type="primary" plain icon="el-icon-star-off">收藏</el-button> -->
              <div class="btn">
                <img src="~@/assets/img/icon-order.png" alt="">
                <span>订阅</span>
              </div>
              <div class="btn"><img src="~@/assets/img/icon-shopp.png" alt="">
                <span>加入选数车</span>
              </div>
              <div class="btn"><img src="~@/assets/img/icon-collect.png" alt="">
                <span>收藏</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import SelectExtend from "@/core/components/SelectExtend";
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SplitBar from "@/core/components/SplitBar";
import SelectPlus from "@/core/components/SelectPlus/index.vue";
export default {
  name: "pageList",
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter
  },
  props: {
  },
  data () {
    return {
      showList: true,
      queryBt: true,
      queryParamsList: [
        {
          queryName: '长江流域',
          queryValue: 86
        }, {
          queryName: '长江流域',
          queryValue: 86
        }, {
          queryName: '长江流域',
          queryValue: 86
        }, {
          queryName: '长江流域',
          queryValue: 86
        }, {
          queryName: '长江流域',
          queryValue: 86
        }
      ],
      checkList: [
        {
          checkName: '一般',
          checkValue: 20
        },
        {
          checkName: '重要',
          checkValue: 10
        }
      ],
      checked: false,
      sortItems: [
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      dataList: [
        {
          id: 1,
          itemTitle: '投资建设的固定资产投资表',
          itemStatus: ['有条件共享', '不予开放'],
          itemType: '1',
          unit: '投资处',
          updateCycle: '实时',
          pubTime: '2023-01-02 23:45:50',
          sourceSys: '投资管理系统',
          label: '1',
          sourceTypeList: ['1', '2'],
          visitNum: '2526',
          collectNum: '12'
        }, {
          id: 2,
          itemTitle: '投资建设的固定资产投资表2',
          itemStatus: ['有条件共享'],
          itemType: '2',
          unit: '投资处2',
          updateCycle: '实时1',
          pubTime: '2023-02-02 23:45:50',
          sourceSys: '投资管理系统2',
          label: '1',
          sourceTypeList: ['2'],
          visitNum: '25264',
          collectNum: '121'
        }, {
          id: 3,
          itemTitle: '投资建设的固定资产投资表3',
          itemStatus: ['不予开放'],
          itemType: '1',
          unit: '投资处3',
          updateCycle: '实时2',
          pubTime: '2023-03-02 23:45:50',
          sourceSys: '投资管理系统3',
          label: '2',
          sourceTypeList: ['1', '2'],
          visitNum: '25263',
          collectNum: '122'
        }, {
          id: 4,
          itemTitle: '投资建设的固定资产投资表4',
          itemStatus: ['有条件共享', '不予开放'],
          itemType: '1',
          unit: '投资处4',
          updateCycle: '实时3',
          pubTime: '2023-04-02 23:45:50',
          sourceSys: '投资管理系统4',
          label: '2',
          sourceTypeList: ['1'],
          visitNum: '25262',
          collectNum: '123'
        }, {
          id: 5,
          itemTitle: '投资建设的固定资产投资表5',
          itemStatus: ['有条件共享'],
          itemType: '2',
          unit: '投资处5',
          updateCycle: '实时4',
          pubTime: '2023-05-02 23:45:50',
          sourceSys: '投资管理系统5',
          label: '1',
          sourceTypeList: ['2'],
          visitNum: '25261',
          collectNum: '124'
        }
      ],
      treeDataIn: [
        {
          labelId: "1",
          labelName: "投资处",
          labelValue: "45",
          children: [
            {
              labelId: "11",
              labelName: "投资处1",
              labelValue: "145",
              children: [{
                labelId: "111",
                labelName: "投资处11",
                labelValue: "145"
              }, {
                labelId: "211",
                labelName: "投资处21",
                labelValue: "145"
              }]
            }, {
              labelId: "21",
              labelName: "投资处2",
              labelValue: "145",
              children: []
            }, {
              labelId: "31",
              labelName: "投资处3",
              labelValue: "145",
              children: []
            }
          ]
        }, {
          labelId: "2",
          labelName: "计划处",
          labelValue: "45",
          children: [
            {
              labelId: "12",
              labelName: "计划处2",
              labelValue: "145",
              children: []
            }, {
              labelId: "22",
              labelName: "计划处2",
              labelValue: "145",
              children: []
            }, {
              labelId: "32",
              labelName: "计划处3",
              labelValue: "145",
              children: []
            }
          ]
        }, {
          labelId: "3",
          labelName: "内事处",
          labelValue: "45",
          children: [
            {
              labelId: "13",
              labelName: "内事处1",
              labelValue: "145",
              children: []
            }, {
              labelId: "23",
              labelName: "内事处2",
              labelValue: "145",
              children: []
            }, {
              labelId: "33",
              labelName: "内事处3",
              labelValue: "145",
              children: []
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'labelName'
      },
      listLoading: false,
      total: 0,
      currentRow: undefined,
      filters: {
        currentPage: 1,
        pageSize: 20,
        checkList: []
      }
    }
  },
  methods: {
    sort (item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach(_item => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item;
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        console.log(item);
      })
    },
    orderChange (item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order;
        console.log(item);
      })
    },
    handleFilter () {
      this.$nextTick(() => {
        this.getList()
      })
    },
    getList () {
      if (!this.queryBt) {
        return;
      }
      this.queryBt = false
      this.listLoading = false
      this.$api.polyMonitor.getList(this.filters).then((res) => {
        this.queryBt = true
        this.listLoading = false
        this.list = res.data ? res.data.records : []
        this.total = res.data ? res.data.total : 0
      }).catch(res => {
        this.queryBt = true
      })
    }
  },
  mounted () {
  }
};
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.searchCont {
  background-color: #FFF;
  border-radius: 2px;
  padding: 18px 20px 12px 27px;
  // width: calc(100% - 127px);
  color: #333;
  font-size: 16px;

  .searchList {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;

    h5 {
      width: 75px;
      height: 30px;
      //font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      line-height: 30px;
      margin-bottom: 12px;
      text-align: right;
    }

    .searchInp {
      margin-left: 29px;
      margin-bottom: 12px;

      .searchItem {
        font-size: 16px;
        margin-right: 30px;
        line-height: 46px;

        span {
          font-weight: bold;
        }
      }

      >>>.el-form-item {
        margin-bottom: 0;

        .el-form-item__label {
          font-size: 16px;
        }
      }

      >>>.el-checkbox-group {
        .el-checkbox {
          margin-right: 40px;
        }

        .is-checked {

          // .el-checkbox__inner{
          //   background-color: #fff;
          //   border-color: #0e88eb;
          //   &::after{
          //     border: 1px solid #0e88eb
          //   }
          // }
          .el-checkbox__label {
            color: #333;
          }
        }

        .el-checkbox__label {
          font-size: 16px;
        }
      }
    }
  }
}

.dataCont {
  margin-top: 10px;
  background-color: #FFF;
  border-radius: 2px;
  padding: 20px 0px 32px 0px;
  width: 100%;
  color: #333;
  font-size: 16px;

  .topSort {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    padding-left: 23px;

    >>>.el-checkbox__label {
      font-size: 16px;
    }

    .sort {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 60px;

      div {
        display: flex;
        align-items: center;
        cursor: pointer;
        height: 30px;
        margin-right: 50px;

        .iconLabel {
          &.active {
            color: $colors;
          }
        }

        .iconList {
          margin-left: 2px;
          color: #666;
          display: flex;
          flex-direction: column;
          position: relative;

          &.active {
            color: $colors;
          }
        }

        i {
          font-size: 14px;
          color: #A3A3A4;

          &.active {
            color: $colors;
            ;
          }

          &.el-icon-caret-top:before {
            top: -9px;
            position: absolute;
          }

          &.el-icon-caret-bottom:before {
            top: -2px;
            position: absolute;
          }
        }
      }
    }
  }
}

.borderSplit {
  height: 1px;
  width: 100%;
  background: #EAEAEA;
}

.listCont {
  padding: 0 32px 0 22px;

  .listItem {
    width: 100%;
    border-bottom: 1px solid #EAEAEA;
    padding: 22px 0 22px 0;

    .line1 {
      display: flex;
      justify-content: space-between;
      // align-items: center;

      .title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }

      .itemStatus {

        span {
          background: #F6F9FF;
          border-radius: 4px;
          border: 1px solid #D9E1F1;
          font-size: 14px;
          color: #7D8496;
          line-height: 38px;
          padding: 5px 8px;
          margin-left: 8px;
        }
      }
    }

    .line2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;

      .itemMes {
        .message {
          color: #7D8496;
          font-size: 16px;
          margin-right: 35px;
          margin-bottom: 6px;
          line-height: 25px;

          span {
            color: #000;
          }
        }
      }

      .itemSourceType {
        display: flex;

        .itemSourceItem {
          display: flex;
          align-items: center;
          flex-direction: column;
          margin-left: 35px;

          .iconSchema {
            width: 20px;
            height: 20px;
            background-image: url('~@/assets/img/icon-schem.png');
          }

          .iconTable {
            width: 20px;
            height: 20px;
            background: url('~@/assets/img/icon-table.png') 100% 100% no-repeat;
          }

          span {
            font-size: 14px;
            color: $colors;
            margin-top: 2px;
          }
        }
      }
    }

    .line3 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 18px;

      .itemNum {
        .num {
          font-size: 16px;
          color: $colors;
          margin-right: 18px;

          span {
            color: #7D8496;
          }
        }
      }

      .itemHandleBtn {
        display: flex;
        align-items: center;

        .btn {
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid $colors;
          text-align: center;
          padding: 9px 10px;
          color: $colors;
          margin-left: 13px;
          cursor: pointer;
          display: flex;
          align-content: center;
          justify-content: center;

          img {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-top: 3px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}

>>>.el-input__inner {
  height: 35px;
  line-height: 35px;
}
</style>
