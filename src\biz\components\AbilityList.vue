<template>
  <el-container class="page-container">
    <!--    <el-container>-->
    <!--      <el-main>-->
    <!-- <div class="searchCont">
          <div class="searchList">
            <h5>范围</h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="提供单位">
                  <el-cascader clearable :filterable="true" :options="options" :props="{ checkStrictly: true }" v-model="orgIds" @change="handleDept" placeholder="请选择提供单位" style="width: 100%">
                    <template slot-scope="{ node, data }">
                      <div class="nowrap" :title="data.label">{{ data.label }}</div>
                    </template>
                  </el-cascader>
                </el-form-item>
                <el-form-item label="能力名称或标签名称" style="margin-left: 30px;">
                  <el-input v-model.trim="filters.name" clearable placeholder="请输入能力名称或标签名称" @change="handleFilter" maxlength="20" />
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="searchList">
            <h5>更多筛选</h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="">
                  <el-select v-model="applicableTerminal" placeholder="适用终端" collapseTags multiple clearable @change="handleTerminal" style="width: 160px;">
                    <el-option label="安卓端" value="1"></el-option>
                    <el-option label="ios端" value="2"></el-option>
                    <el-option label="微信小程序" value="3"></el-option>
                    <el-option label="支付宝小程序" value="4"></el-option>
                    <el-option label="其他" value="5"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="">
                  <el-select v-model="filters.shareType" placeholder="共享类型" clearable @change="handleFilter" style="width: 160px;">
                    <el-option label="有条件共享" value="1"></el-option>
                    <el-option label="无条件共享" value="2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="注册时间">
                  <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filters.dateRange1" type="daterange" :default-time="['00:00:00', '23:59:59']" @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="更新时间">
                  <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="filters.dateRange2" type="daterange" :default-time="['00:00:00', '23:59:59']" @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="searchList">
            <h5>能力形态</h5>
            <div class="searchInp" style="display:flex;">
              <CheckPlus dictType="POWER_SHAPE" v-model="checkListresFormatType" :filterType="'powerShape'" :num="'1'" @change="handleChecked" ref="resFormatType"></CheckPlus>
            </div>
          </div>
        </div> -->
    <div class="dataCont">
      <div class="topSort">
        <el-checkbox
          v-model="filters.onlyShowNotOrder"
          @change="changeListStatus"
          :true-label="1"
          :false-label="0"
        >
          仅显示未订阅</el-checkbox
        >
        <div class="sort">
          <div v-for="(item, index) in sortItems" :key="index">
            <span class="iconLabel" :class="{ active: item.active }" @click="sort(item)">
              {{ item.label }}
            </span>
            <span v-if="item.sort" class="iconList">
              <i
                class="el-icon-caret-top"
                :class="{ active: item.order === 1 }"
                @click="orderChange(item, 1)"
              ></i>
              <i
                class="el-icon-caret-bottom"
                :class="{ active: item.order === -1 }"
                @click="orderChange(item, -1)"
              ></i>
            </span>
          </div>
        </div>
      </div>
      <div class="borderSplit"></div>
      <div class="listCont" v-loading="listLoading">
        <div
          class="listItem"
          v-for="item in dataList"
          :key="item.id"
          @click="handleCheckDetail(item)"
        >
          <div class="line1">
            <div class="title">
              <div class="ellis" :title="item.powerName">{{ item.powerName }}</div>
              <span style="width: 23px; margin-left: 4px">
                <img v-if="item.innerOrgFlag === '1'" src="~@/assets/img/icon-withIn.svg" alt="" />
                <img v-else src="~@/assets/img/icon-withOut.svg" alt="" />
              </span>
              <span
                style="
                  padding: 0 4px;
                  border-radius: 4px;
                  font-weight: 300;
                  text-align: center;
                  color: #fff;
                  background-color: #3572ff;
                "
                v-if="item.isOrderFalg && item.isOrderFalg === '1'"
                >已订阅</span
              >
            </div>
            <div class="itemStatus">
              <span v-if="item.shareType">{{ item.shareType }}</span>
            </div>
          </div>
          <div class="line2">
            <div class="itemMes">
              <span class="message"
                >提供单位：<span>{{ item.registerUnitName }}</span></span
              >
              <span class="message"
                >注册时间：<span>{{ item.registerTime }}</span></span
              >
              <span class="message"
                >更新时间：<span>{{ item.updateTime }}</span></span
              >
              <br />
              <span class="message"
                >适用终端：<span>{{ item.applicableTerminalNames }}</span></span
              >
              <span class="message"
                >能力主题：<span>{{ powerTheme(item.powerTheme) }}</span></span
              >
              <span class="message" v-if="item.powerLabelNames"
                >标签：<span>{{ item.powerLabelNames }}</span>
              </span>
              <span class="message"
                >共享类型：<span>{{ item.shareType }}</span></span
              >
              <br />
              <span class="message messageEll"
                >能力简介：<span :title="item.powerRemark">{{ item.powerRemark }}</span></span
              >
            </div>
            <div class="itemSourceType">
              <div class="itemSourceItem">
                <div class="iconSchema" v-if="item.powerShape === 'API'"></div>
                <div class="iconTable" v-else-if="item.powerShape === 'SDK'"></div>
                <span>{{ item.powerShape.toUpperCase() }}</span>
              </div>
            </div>
          </div>
          <div class="line3">
            <div class="itemNum">
              <span class="num">{{ item.orderSum || 0 }}次<span>订阅</span></span>
              <span class="num">{{ item.browseSum || 0 }}次<span>访问</span></span>
              <span class="num">{{ item.collectSum || 0 }}次<span>收藏</span></span>
            </div>
            <div class="itemHandleBtn">
              <el-button
                class="icsp-button-grey"
                disabled
                v-if="
                  (currentUser && item.registerUnitCode === currentUser.unitCode) ||
                  (item.isOrderFalg && item.isOrderFalg === '1')
                "
              >
                <svg-icon icon-class="gxsl-apply-btn" />
                订阅
              </el-button>
              <el-button v-else class="icsp-button" plain @click.stop="handlePowerApply(item)">
                <svg-icon icon-class="gxsl-apply-btn" />
                订阅
              </el-button>

              <el-button
                class="icsp-button-grey"
                disabled
                v-if="currentUser && item.registerUnitCode === currentUser.unitCode"
              >
                <svg-icon icon-class="gxsl-shoppingCart" />
                加入选数车
              </el-button>
              <el-button
                v-else
                :disabled="queryBt"
                :class="item.isAddCartFalg === '1' ? 'icsp-button2' : 'icsp-button'"
                plain
                @click.stop="handleAddPowerToCart(item)"
              >
                <svg-icon icon-class="gxsl-shoppingCart" />
                {{ item.isAddCartFalg === '1' ? '取消加入选数车' : '加入选数车' }}
              </el-button>

              <el-button
                class="icsp-button-grey"
                disabled
                v-if="item.registerUnitCode === currentUser.unitCode"
              >
                <svg-icon icon-class="gxsl-collect" />
                收藏
              </el-button>
              <el-button
                v-else
                :disabled="queryBt"
                :class="item.isCollectFalg === '1' ? 'icsp-button2' : 'icsp-button'"
                plain
                @click.stop="handleCollect(item)"
              >
                <svg-icon icon-class="gxsl-collect" />
                {{ item.isCollectFalg === '1' ? '取消收藏' : '收藏' }}
              </el-button>

              <!-- <el-button class="icsp-button-grey" disabled v-if="item.registerUnitCode === currentUser.unitCode">
                    <svg-icon icon-class="feedback" />
                    问题反馈
                  </el-button>
                  <el-button v-else class="icsp-button" plain @click.stop="handleFeedback(item)">
                    <svg-icon icon-class="feedback" />
                    问题反馈
                  </el-button> -->
            </div>
          </div>
        </div>
      </div>
      <el-empty v-if="!listLoading && dataList.length === 0" description="暂无数据"></el-empty>
      <div class="icsp-pagination-wrap">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="filters.currentPage"
          :page-size="filters.pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>

    <!-- 数据订阅 -->
    <SubscribeDialog
      :dialogVisible.sync="dialogVisibleApply"
      :type="type"
      :title="title"
      :data="dialogData"
    ></SubscribeDialog>
    <ToolBar @onRefreshList="getList" />
    <FeedbackDialog :dialogVisible.sync="feedbackVisible" :data="dialogData"></FeedbackDialog>
    <!--      </el-main>-->
    <!--    </el-container>-->
  </el-container>
</template>

<script>
import ToolBar from '@/biz/components/common/toolbar'
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index.vue'
import CheckPlus from '@/biz/components/common/checkPlus'
import SubscribeDialog from '@/biz/components/SubscribeDialog'
import FeedbackDialog from '@/biz/components/FeedbackDialog'
import Cookies from 'js-cookie'

export default {
  name: 'polyMonitor',
  components: {
    FeedbackDialog,
    ToolBar,
    SelectPlus,
    CheckPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog
  },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      type: undefined,
      title: '',
      showList: true,
      dialogData: undefined,
      dialogVisibleApply: false,
      feedbackVisible: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      queryParamsList: [],
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      checkList: [
        {
          checkName: 'API',
          checkValue: 1
        },
        {
          checkName: 'SDK',
          checkValue: 2
        }
      ],
      checkOption: ['一般', '重要'],
      sortItems: [
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 1
        },
        {
          label: '订阅量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 2
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 3
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false,
          sortMode: 4
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      themeArr: [],
      powerTreeData: {
        allNum: 0,
        allNumIn: 0,
        allNumOut: 0,
        treeDataIn: [],
        treeDataOut: []
      },
      dataList: [],
      treeDataIn: [],
      treeDataOut: [],
      defaultProps: {
        children: 'childList',
        label: 'orgName'
      },
      listLoading: false,
      queryBt: false,
      checkListresFormatType: [],
      total: 0,
      currentRow: undefined,
      applicableTerminal: [],
      filters: {
        name: '',
        labelName: '',
        currentPage: 1,
        onlyShowNotOrder: 0,
        applicableTerminal: '',
        orgId: '',
        dateRange1: [],
        dateRange2: [],
        pageSize: 10,
        sortMode: '',
        sortType: '',
        powerShape: ''
      },
      orgIds: [],
      options: []
    }
  },
  methods: {
    handleChecked(val, type) {
      this.filters[type] = val
      this.handleFilter()
    },
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    getPowerTreeData() {
      this.$api.bizApi.pageRequest
        .getPowerTree({
          ...this.filters,
          registerTimeStart: this.filters.dateRange1?.length
            ? this.filters.dateRange1[0]
            : undefined,
          registerTimeEnd: this.filters.dateRange1?.length ? this.filters.dateRange1[1] : undefined,
          updateTimeStart: this.filters.dateRange2?.length ? this.filters.dateRange2[0] : undefined,
          updateTimeEnd: this.filters.dateRange2?.length ? this.filters.dateRange2[1] : undefined
        })
        .then((res) => {
          console.log(res)
          this.powerTreeData.allNum = res.data.powerNum
          for (const item of res.data.childList) {
            if (item.orgName === config.innerItemName) {
              this.powerTreeData.treeDataIn = item.childList
              this.powerTreeData.allNumIn = item.powerNum
            }
            if (item.orgName === config.outItemName) {
              this.powerTreeData.treeDataOut = item.childList
              this.powerTreeData.allNumOut = item.powerNum
            }
          }
          console.log(this.powerTreeData)
        })
    },
    handleNodeClick(data) {
      console.log(data)
      this.filters.orgId = data.orgId
      this.getList()
    },
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.includes(value)
    },
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    changeListStatus(val) {
      this.handleFilter()
    },
    handleFilter() {
      this.filters.currentPage = 1
      this.getList()
    },
    getList() {
      let param = this.$route.query
      if (param.portalSearchText) {
        this.filters.name = param.portalSearchText
      }

      this.listLoading = true
      const params = {
        ...this.filters,

        registerTimeStart: this.filters.dateRange1?.length ? this.filters.dateRange1[0] : undefined,
        registerTimeEnd: this.filters.dateRange1?.length ? this.filters.dateRange1[1] : undefined,
        updateTimeStart: this.filters.dateRange2?.length ? this.filters.dateRange2[0] : undefined,
        updateTimeEnd: this.filters.dateRange2?.length ? this.filters.dateRange2[1] : undefined
      }
      this.$api.bizApi.pageRequest
        .getPowerPage(params)
        .then((res) => {
          if (res.code === '200') {
            this.listLoading = false
            this.dataList = res.data ? res.data.records : []
            this.total = res.data ? res.data.total : 0
          } else {
            this.listLoading = false
            this.$message({
              message: res.message
            })
          }
        })
        .catch((e) => {
          this.listLoading = false
          this.$message({
            message: e
          })
        })
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    // 获取所属部门
    getUnitsByUnitCode() {
      this.$api.bizApi.common
        .getUnitsByUnitCode()
        .then((res) => {
          if (res.code === '200') {
            let a = res.data || []
            this.circle(a)
            this.options = a
            // 如果route中含有提供方，需要反显选中
            if (this.filters.orgId) {
              let ary = this.getParentsById(this.options, this.filters.orgId)
              // console.log('ary' + ary)
              this.orgIds = ary
            }
          }
        })
        .catch((e) => {
          this.$message({
            message: e.message
          })
        })
    },
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          // console.log('node' + node)
          if (node !== undefined) {
            // 如能查询到把父节点加到数组前面
            node.unshift(list[i].id)
            return node
          }
        }
      }
    },
    handleDept(val) {
      if (val.length === 1) {
        this.filters.orgId = val[0]
      } else if (val.length === 0) {
        this.filters.orgId = ''
      } else {
        this.filters.orgId = val[val.length - 1]
      }
      this.handleFilter()
    },
    circle(data) {
      data.forEach((i) => {
        i.value = i.id
        i.label = i.name
        if (i.children && i.children.length > 0) {
          this.circle(i.children)
        }
      })
    },
    getPowerTheme() {
      this.themeArr = []
      this.$api.dict.getDictByType({ type: 'POWER_THEME' }).then((res) => {
        this.themeArr = this.convertObjectToKeyValuePairs(res.data, this.themeArr)
      })
    },
    powerTheme(val) {
      return this.themeArr.filter((item) => item.value === val)[0].label
    },
    convertObjectToKeyValuePairs(obj, arr1) {
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          arr1.push({ label: obj[key], value: key })
        }
      }
      return arr1
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    // 查看详情
    handleCheckDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detail/index',
          query: {
            powerId: data.powerId
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/power/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            powerId: data.powerId
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    // 能力收藏
    handleCollect(data) {
      if (data.isCollectFalg !== '1') {
        this.queryBt = true
        this.$api.bizApi.pageRequest
          .addPowerToFavorites({ powerId: data.powerId })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('收藏成功')
              this.$set(data, 'isCollectFalg', '1')
              this.$set(data, 'collectSum', data.collectSum + 1)
              // this.handleFilter()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false
          })
      } else {
        this.$confirm('是否取消收藏该能力?', '取消收藏', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .cancelPowerToFavorites({ powerId: data.powerId })
            .then((res) => {
              if (res.code === '200') {
                // this.handleFilter()
                this.$message({
                  type: 'success',
                  message: '取消收藏成功!'
                })
                this.$set(data, 'isCollectFalg', '0')
                this.$set(data, 'collectSum', data.collectSum - 1)
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    },
    handleTerminal() {
      this.filters.applicableTerminal = this.applicableTerminal.join(',')

      this.handleFilter()
    },
    // 能力订阅
    handlePowerApply(data) {
      if (this.isLogOn()) {
        this.type = 'power'
        this.title = '能力订阅'
        this.dialogData = data
        this.dialogVisibleApply = true
        this.dialogData.powerId = data.powerId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart(data) {
      if (data.isAddCartFalg !== '1') {
        this.queryBt = true
        this.$api.bizApi.pageRequest
          .addPowerToCart({ powerId: data.powerId })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('加入选数车成功')
              this.handleFilter()
              window.refreshCountNum()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false
          })
      } else {
        this.$confirm('是否从选数车中取消该能力?', '取消确定', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .delPowerFromCart({ powerId: data.powerId })
            .then((res) => {
              if (res.code === '200') {
                this.handleFilter()
                this.$message.success('取消成功!')
                window.refreshCountNum()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    },
    handleFeedback(data) {
      if (this.isLogOn()) {
        this.dialogData = data
        this.feedbackVisible = true
        this.dialogData.powerId = data.powerId
      }
    }
  },
  created() {
    this.getUnitsByUnitCode()
    this.getPowerTheme()
    this.getList()
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        console.log(newVal, 'newValWatch3')
        console.log(newVal.path.includes('/search'), '是否')
        if (newVal.path.includes('/search')) {
          this.getList()
          document.documentElement.scrollTop = 0
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  margin-left: 0 !important;

  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.messageEll {
  display: flex;
  span {
    max-width: 820px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
