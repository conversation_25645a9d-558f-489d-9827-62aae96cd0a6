export default {
  state: {
    showRightPanel: false, // Whether show the settings right-panel
    roleShortcuts: [], // 角色快捷菜单
    userShortcuts: [] // 用户自定义快捷菜单
  },
  getters: {

  },
  mutations: {
    setShowRightPanel(state, showRightPanel) {
      state.showRightPanel = showRightPanel;
    },
    setRoleShortcuts(state, roleShortcuts) {
      state.roleShortcuts = roleShortcuts;
    },
    setUserShortcuts(state, userShortcuts) {
      state.userShortcuts = userShortcuts;
    }
  },
  actions: {

  }
}
