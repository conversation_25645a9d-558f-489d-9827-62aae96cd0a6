<template>
  <div class="menu-horizontal-container">
    <el-scrollbar ref="horizontalMenu" wrap-class="scrollbar-wrapper" style="height:100%;" :vertical="true">
      <el-menu default-active="1" class="el-menu-demo" mode="horizontal" background-color="red" text-color="#fff" active-text-color="#ffd04b" @select="handleSelect">
        <menu-tree v-for="item in menuData" :key="item.id" :menu="item"></menu-tree>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import MenuTree from "@/core/components/MenuTree";
export default {
  name: 'TopNavMenu',
  components: {
    MenuTree
  },
  data() {
    return {
      menuData: null
    }
  },
  computed: {
    ...mapState({
      navTree: state => state.menu.navTree
    })
  },
  methods: {
    handleSelect(key, keyPath) {
      // console.log(key, keyPath);
      ;
    },
    onResize() {
      let nums = Math.floor(this.$refs.horizontalMenu.$el.clientWidth / 200);
      let [...menuArr] = this.navTree
      let [...moreMenuArr] = this.navTree
      menuArr = menuArr.slice(0, nums)
      moreMenuArr = moreMenuArr.slice(nums, this.navTree.length)
      if(moreMenuArr.length > 0) {
        menuArr = [...menuArr, {
          "id": "999A+",
          "name": "...",
          "icon": 'noneDisplay',
          "uri": null,
          "children": moreMenuArr
        }]
      }
      this.menuData = menuArr
    }
  },
  mounted() {
    window.onresize = () => {
      return this.onResize()
    }
    this.onResize()
  },
  watch: {
    navTree(val) {
      this.menuData = this.navTree
      this.onResize()
    }
  }
}
</script>
