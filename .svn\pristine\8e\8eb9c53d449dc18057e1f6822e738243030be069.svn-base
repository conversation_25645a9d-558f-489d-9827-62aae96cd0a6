<template>
  <div ref="horizontalMenu">
    <el-submenu popper-class="pop-menu" v-if="menu.children && menu.children.length >= 1" :index="'' + menu.id">
      <template slot="title">
        <!-- <img v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" class="noneb" /> -->
        <svg-icon v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" icon-class='menu'/>
        <svg-icon v-else-if="menu.icon.indexOf('/icons/svg/')==0" :icon-class="menu.icon.replace('/icons/svg/', '')" />
        <svg-icon v-else-if="menu.icon.indexOf('.svg')>0" :file-path="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`" icon-class='menu'/>
        <svg-icon v-else-if="menu.icon.indexOf('noneDisplay')==0" icon-class='noneDisplay' />
        <img v-else :src="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`"/>
        <span :class="['menu-name', menu.name === '...' ? 'menu-name-bold' : '']" slot="title" :title="menu.name.length > (8 - menu.menuLevel) ? menu.name : null">{{menu.name}}</span>
      </template>
      <el-scrollbar v-if="isShowScroll(menu)" ref="horizontalMenu" wrap-class="scrollbar-wrapper" :wrap-style="wrapStyle" wrapClass="wrapClass" :vertical="true">
        <MenuTree :ref="`menuTree${item.id}`" v-for="item in menu.children" :key="item.id" :menu="item" :mainContent="mainContent"></MenuTree>
      </el-scrollbar>
      <MenuTree v-else v-for="item in menu.children" :key="item.id" :menu="item" :mainContent="mainContent"></MenuTree>
    </el-submenu>
    <el-menu-item v-else :index="'' + menu.id" @click="handleRoute(menu)">
      <div class="menu-left-bg"></div>
      <!-- <img v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" class="noneb" /> -->
      <svg-icon v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" icon-class='menu'/>
      <svg-icon v-else-if="menu.icon.indexOf('/icons/svg/')==0" :icon-class="menu.icon.replace('/icons/svg/', '')" />
      <svg-icon v-else-if="menu.icon.indexOf('.svg')>0" :file-path="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`" icon-class='menu'/>
      <svg-icon v-else-if="menu.icon.indexOf('noneDisplay')==0" icon-class='noneDisplay' />
      <img v-else :src="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`"/>
      <span :class="menu.name === '...' ? 'menu-name-bold' : ''" slot="title" :title="menu.name.length > (8 - menu.menuLevel) ? menu.name : null">{{menu.name}}</span>
    </el-menu-item>
  </div>
</template>

<script>
  import SvgIcon from '@/core/components/SvgIcon/index.vue'
  // eslint-disable-next-line
  import {getIFrameUrl, getIFramePath} from '@/core/utils/iframe'

  export default {
    name: 'MenuTree',
    components: {
      SvgIcon
    },
    data() {
      return {
        wrapStyle: 'height: 222px',
        navbar_mode: config.navbar_mode,
        appCode_uims: config.appCode_uims
      }
    },
    props: {
      mainContent: Object,
      menu: {
        type: Object,
        required: true
      }
    },
    computed: {
      spaceId: {
        get() {
          return this.$store.state.system.spaceId
        }
      }
    },
    methods: {
      handleRoute (menu) {
        // 如果是嵌套页面，转换成iframe的path
        let path = getIFramePath(menu.url)
        if(!path) {
          path = menu.url
        };
        if(this.$route.path !== '/' + menu.url) {
          let routes = this.$router.options.routes;
          findUrl(routes);
        }
        function findUrl(routes) {
          routes.forEach(el => {
            if(el.path === menu.url) {
              if(typeof el.meta.keys === 'undefined') {
                el.meta.keys = 0
              }else{
                el.meta.keys++;
              }
            }else if(el.children && el.children.length > 0) {
              findUrl(el.children)
            }
          });
        }
        this.mainContent.toRefresh(path)
        // 通过菜单URL跳转至指定路由
        this.$router.push({
          path: "/" + path,
          query: this.spaceId ? {'x-space-id': this.spaceId} : {}
        });
      },
      isShowScroll(menu) {
        if(this.navbar_mode === '2' && typeof menu.menuLevel !== 'undefined' && menu.menuLevel === 0) {
          if(menu.children && menu.children.length * 50 > document.body.clientHeight * 0.75) {
            let n = document.body.clientHeight * 0.75 / 50;
            this.wrapStyle = `height: ${n * 50}px`;
            return true;
          } else {
            this.wrapStyle = '';
            return false;
          }
        } else {
          this.wrapStyle = '';
          return false;
        }
      },
      onResize() {
        this.isShowScroll(this.menu);
      }
    },
    mounted() {
      window.onresize = () => {
        return this.onResize()
      }
      this.onResize()
    }
  }
</script>
<style lang="scss" scoped>
.el-scrollbar >>>.wrapClass {
  height: calc(100vh / 2);
}
</style>
