<template>
  <!--表格显示列界面-->
  <div class="table-col-select__wrapper">
    <el-button-group>
      <el-tooltip content="列显示">
        <el-button size="mini" icon="fa fa-filter" @click="displayFilterColumnsDialog"></el-button>
      </el-tooltip>
      <el-tooltip content="导出" v-if="!isDownFromBackEnd">
        <el-button
          :loading="downloadLoading"
          size="mini"
          icon="fa fa-file-excel-o"
          @click="exportExcelFile"
        ></el-button>
      </el-tooltip>
      <el-tooltip content="导出全部" v-if="showAllDownload">
        <el-button
          :loading="downloadLoading"
          size="mini"
          icon="fa fa-file-excel-o"
          @click="exportAllExcelFile"
        ></el-button>
      </el-tooltip>
      <el-dropdown ref="dropDwnBtn" @visible-change="maxRows = undefined" trigger="click" placement="top-start" v-if="isDownFromBackEnd">
        <el-tooltip content="导出">
          <el-button size="mini" icon="fa fa-file-excel-o"></el-button>
        </el-tooltip>
        <el-dropdown-menu slot="dropdown" class="baseUIDropdownBtn">
          <li style="text-align:left;">
            <el-input
              :placeholder="'默认1000行'"
              style="width:108px;margin-bottom:10px;"
              v-model.number="maxRows" :maxlength="5" max="9999"
              oninput="value=value.replace(/[^\d]/g,'');if(value>9999){value=9999}">
            </el-input>
            <tip :tipInfo="{titles:[], infos:['导出当前查询的所有数据，默认导出前1000条数据。如需修改，请调整自定义最大行数再行导出。']}"></tip>
            <br/>
            <el-button type="text" @click="exportExcelFileFromBackend()"
              :loading="okLoading" :disabled="btnLoading">导出</el-button>
          </li>
          <li @click="exportExcelFile" class="item--divided">
            <el-button type="text">导出当前页</el-button>
          </li>
        </el-dropdown-menu>
      </el-dropdown>
    </el-button-group>
    <!--表格显示列界面-->
    <table-column-filter-dialog ref="tableColumnFilterDialog" :tableRef="tableRef"></table-column-filter-dialog>
  </div>
</template>

<script>
import TableColumnFilterDialog from "./TableColumnFilterDialog";
import { parseTime } from '@/core/utils/utils'
import Tip from "@/core/components/Tip";
export default {
  name: "TableToolBar",
  components: { TableColumnFilterDialog, Tip },
  props: {
    tableRef: Object,
    excelName: String,
    showAllDownload: Boolean,
    resourceCode: String,
    url: String, // 后台地址(后台导出数据)
    filters: Object // 过滤条件(后台导出数据)
  },
  data() {
    return {
      downloadLoading: false,
      maxRows: undefined,
      okLoading: false,
      btnLoading: false
    };
  },
  computed: {
    isDownFromBackEnd() {
      return (this.url && this.url.trim().length > 0) || false
    }
  },
  methods: {
    // 处理表格列过滤显示
    displayFilterColumnsDialog: function() {
      this.$refs.tableColumnFilterDialog.setDialogVisible(true);
    },
    // 导出Excel用户信息
    exportExcelFile: function() {
      this.$refs.dropDwnBtn && this.$refs.dropDwnBtn.hide();
      this.downloadLoading = true;
      import("@/core/vendor/Export2Excel").then(excel => {
        const tHeader = this.tableRef.getColumnsProp("label");
        const filterVal = this.tableRef.getColumnsProp("property");
        const list = this.tableRef.data;
        const data = this.formatJson(filterVal, list);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.excelName ? this.excelName : this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType
        });
        this.downloadLoading = false;
      });
    },
    // 导出当前全部数据
    exportAllExcelFile: function() {
      this.$emit("exportAllExcelFile");
    },
    // 后台导出
    exportExcelFileFromBackend() {
      let params = {
        "dataUrl": this.url,
        "filters": {
            "currentPage": 1,
            "pageSize": this.maxRows || 1000
        },
        "tableInfo": {}
      }
      let filters = Object.assign({}, this.filters)
      delete filters.currentPage
      delete filters.pageSize
      params.filters = JSON.stringify(Object.assign(params.filters, filters || {}))
      const tProperty = this.tableRef.getColumnsProp("property");
      tProperty.forEach(hd => {
        params.tableInfo[hd.prop] = hd.column.label
      })
      params.tableInfo = JSON.stringify(params.tableInfo)
      this.okLoading = true;
      this.btnLoading = true;
      this.$api.authority.expExcel(params, this.resourceCode).then((res) => {
        this.download((this.excelName || 'excel-list') + ".xlsx", res.data)
        this.$notify({
          title: '操作成功',
          message: '下载成功',
          type: 'success',
          duration: 2000
        })
        this.okLoading = false
        this.btnLoading = false
      }).catch(e => {
        this.$errMsgBox.show({
          text: "下载失败",
          error: e
        })
        this.okLoading = false
        this.btnLoading = false
      })
      // this.$refs.dropDwnBtn.hide();
    },
    download(fileName, content) {
      const blob = new Blob([content], {type: "application/vnd.ms-excel"})
      if ('download' in document.createElement('a')) { // 非IE下载
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = window.URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        window.URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v, i) =>
        filterVal.map(c => {
          let j = c.prop
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return c.column.formatter && typeof c.column.formatter === 'function' ? c.column.formatter(v, c.item, v[j], i) : v[j];
          }
        })
      );
    },
    updateColumns() {
      this.$refs.tableColumnFilterDialog.updateColumns();
    }
  }
};
</script>

<style scoped lang="scss">
.el-dropdown-menu {
  border-radius: 2px;
  padding: 10px;
  background-color: #F2F2F2;
  border-color: #E6E6E6;
  li {
    width: 125px;
    list-style: none;
    text-align: center;
    >>>.el-input__inner {
      height: 30px;
      line-height: 30px;
    }
    &.item--divided::before {
      content: "";
      width: 100%;
      height: 1px;
      margin: 5px auto;
      display: block;
      background-color: #E6E6E6;
    }
    button:not(.el-tooltip) {
      width: 100%;
      padding: 7px 10px;
      /* border: 1px solid transparent; */
      /* background-color: transparent; */
      color: #FFF;
      height: 30px;
      box-sizing: border-box;
      border-radius: 2px;
      span {
        display: flex;
        justify-content: center;
      }
    }
    button.el-tooltip {
      position: absolute;
      margin-left: 5px;
    }
  }
  >>>.popper__arrow {
    border-top-color: #E6E6E6;
  }
  >>>.popper__arrow::after {
    border-top-color: #F2F2F2;
  }
}
body.thinvent_darken .el-dropdown-menu.baseUIDropdownBtn {
  background-color: #37404C;
  border-color: #1A1B20;
  li {
    &.item--divided::before {
      background-color: #545454;
    }
    .el-button.is-disabled, .el-button.el-tooltip {
      background-color: transparent;
      border-color: transparent;
    }
  }
  >>>.popper__arrow {
    border-top-color: #1A1B20;
  }
  >>>.popper__arrow::after {
    border-top-color: #37404C;
  }
}
</style>
