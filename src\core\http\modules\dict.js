import axios from '../axios'
/*
 * 获取权限模块
 */

// 查找导航菜单树
export const findAuthority = (params) => {
  return axios({
    url: `/${config.appCode}/authority/getSysResource`,
    method: 'get',
    params,
    headers: { isBaseApi: true }
  })
}

export const getDictByTypeWithGxsl = (data, resourceCode) => {
  return axios({
    url:
      config.gxslInvokeSwitch === true
        ? `/${config.appCode}/dict/map/${data.type}?fromPowerInvoke=1`
        : `/${config.appCode}/innerGxsl/dict/map/${data.type}`,
    method: 'get',
    data,
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// 导出脚本
export const downSql = (resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/downSql2`,
    method: 'post',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// 查找联合用户
export const findUnionUser = (params) => {
  return axios({
    url: `/${config.appCode}/authority/getUnionUser`,
    method: 'get',
    params,
    headers: { isBaseApi: true }
  })
}

export function getColsByDataRule(params) {
  return axios({
    url: `/${config.appCode}/authority/getSysDataruleCol`,
    method: 'get',
    params,
    headers: { isBaseApi: true }
  })
}

// 切换用户
export const switchUser = (data) => {
  return axios({
    url: `/${config.appCode_auth}/token/switch`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: 'form',
    data,
    headers: { isBaseApi: true }
  })
}

// 获取系统列表
export function getSystemList() {
  return axios({
    url: `/${config.appCode}/authuser/userSystem`,
    method: 'get',
    headers: { isBaseApi: true }
  })
}

// 根据token获取用户信息
export function getUserInfoByToken(params) {
  return axios({
    url: `/${config.appCode_auth}/oauth/check_token`,
    method: 'get',
    params,
    headers: { isBaseApi: true }
  })
}

export const getDictByType = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/map/${data.type}`,
    method: 'get',
    data,
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

export const getDictListByType = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/list/${data.type}`,
    method: 'get',
    data,
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

export const getDictListByTypeWithGxsl = (data, resourceCode) => {
  return axios({
    url:
      config.gxslInvokeSwitch === true
        ? `/${config.appCode}/dict/list/${data.type}?fromPowerInvoke=1`
        : `/${config.appCode}/innerGxsl/dict/list/${data.type}`,
    method: 'get',
    data,
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

export const getDictType = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dictType/list`,
    method: 'get',
    data,
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

export const getDict = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/list`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: 'form',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// 删除字典
export const delDict = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/delete/${data}`,
    method: 'get',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// // 删除字典项
// export const delDict = (data, resourceCode) => {
//   return axios({
//     url: `/${config.appCode}/dict/delDict',
//     method: 'post',
//     data
//   })
// }
export const getDictionaries = (data) => {
  return axios({
    url:
      config.gxslInvokeSwitch === true
        ? `/${config.appCode}/dictionary`
        : `/${config.appCode}/innerGxsl/dictionary`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: 'form',
    data,
    headers: { isBaseApi: true }
  })
}

export const updateDictFlag = (data) => {
  return axios({
    url: `/${config.appCode}/dict/updateDictFlag`,
    method: 'post',
    postType: 'form',
    data,
    headers: { isBaseApi: true }
  })
}

export const reload = (resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/reload`,
    method: 'get',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}
// 新增字典
export const saveDict = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/add`,
    // headers: {'Content-type': 'application/json;charset=UTF-8'},
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: 'form',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// // 保存字典
// export const saveDict = (data, resourceCode) => {
//   return axios({
//     url: `/${config.appCode}/dict/saveDict',
//     method: 'post',
//     data
//   })
// }

export const updateDict = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/update`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: 'form',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/get/${data}`,
    method: 'get',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}

// 根据type和code获取字典
export const getByTypeCode = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/dict/map/${data}`,
    method: 'get',
    headers: { 'X-resource-code': resourceCode, isBaseApi: true }
  })
}
