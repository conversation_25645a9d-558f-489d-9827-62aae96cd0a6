<template>
  <DialogPlus v-dialogDrag="{ zoom: true }" class="padding0" title="添加用户" :visible.sync="dialogMainFormVisible" @close="destrory()" @open="init" width="880px">
    <el-container>
      <el-header class="margin0">
        <!--列表查询区-->
        <el-form :inline="true" :model="filters" :size="size" class="searchForm borderTop0">
          <el-form-item label="用户名称">
            <el-input v-model="filters.realName" @keyup.enter.native="getList(1)" placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="登录账号">
            <el-input v-model="filters.userName" @keyup.enter.native="getList(1)" placeholder="请输入登录账号"></el-input>
          </el-form-item>
          <perm-button class="iconBtn bg" type="primary" size="mini" icon="uims-icon-query" @click="getList(1)" />
        </el-form>
      </el-header>
      <el-main>
        <table-plus name="selectUser" :data="userList" border fit stripe highlight-current-row v-loading="listLoading" ref="multipleTable" @row-click="clickUserRow" @selection-change="selectRow">
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column prop="realName" label="用户名称" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="userName" label="登录账号" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="userType" label="用户类型" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="unitName" label="所属机构" show-overflow-tooltip width="160"></el-table-column>
          <el-table-column prop="enabled" label="是否启用" align="center" width="100"></el-table-column>
        </table-plus>
      </el-main>
      <el-footer>
        <table-footer ref="tableFooter" :showToolBar="true" :showPage="true" :tableRef="this.$refs.multipleTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total">
        </table-footer>
      </el-footer>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button icon="uims-icon-cancel" @click="dialogMainFormVisible = false">
        取消
      </el-button>
      <el-button icon="uims-icon-save" type="primary" @click="selectUserSave">
        确定
      </el-button>
    </div>
  </DialogPlus>
</template>

<script>
import TablePlus from "@/core/components/TablePlus"
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SelectPlus from "@/core/components/SelectPlus";
import DialogPlus from "@/core/components/DialogPlus";
import PermButton from '@/core/components/PermButton'
export default {
  name: "SelectUserDialog",
  components: {
    TablePlus,
    TableFooter,
    SelectPlus,
    PermButton,
    DialogPlus
  },
  data() {
    return {
      currentRow: undefined,
      userList: [],
      filters: {
        currentPage: 1,
        pageSize: 20,
        userName: undefined,
        realName: undefined,
        sort: '+id',
        enabled: '1'
      },
      listLoading: true,
      dialogMainFormVisible: false,
      dict: {
        isFlag: undefined,
        userType: undefined
      },
      size: 'small',
      userTempData: [],
      total: 0
    }
  },
  props: {
    dialogFormVisible: Boolean,
    resCode: String
  },
  methods: {
    destrory() {
      this.$refs.multipleTable.clearSelection();
      this.filters.realName = undefined
      this.filters.userName = undefined
    },
    init() {
      this.getList();
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1;
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    selectRow(rows) {
      if (rows) {
        this.userTempData = JSON.parse(JSON.stringify(rows));
      } else {
        this.$notify({
          title: '提示',
          message: '请选择用户',
          type: 'info',
          duration: 2000
        })
        return false;
      }
    },
    clickUserRow(row, event, column) {
      if (this.currentRow === row) {
        this.currentRow = undefined
        this.$refs.multipleTable.clearSelection();
      } else {
        this.currentRow = row
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    getList(fg) {
      if(fg) {
        this.filters.currentPage = 1
      }
      this.$api.user.getList(this.filters, this.resCode).then((res) => {
        this.userList = res.data.records
        this.total = res.data.total
        this.listLoading = false
      })
    },
    selectUserSave() {
      if (this.userTempData && this.userTempData.length > 0) {
        if (this.userTempData.length > 1) {
          this.$notify({
            title: '提示',
            message: '只能选择一条用户信息',
            type: 'info',
            duration: 2000
          });
        } else {
          if (this.selectModel === 'delegate') {
            this.$emit("getTemp", this.userTempData[0].userId, this.userTempData[0].userName)
          } else {
            this.$emit("getTemp", this.userTempData[0].userId, this.userTempData[0].userName)
          }
          this.dialogMainFormVisible = false
        }
      } else {
        this.$notify({
          title: '提示',
          message: '请选择用户',
          type: 'info',
          duration: 2000
        });
      }
    }
  },
  watch: {
    dialogFormVisible: function (newValue, oldValue) {
      this.dialogMainFormVisible = newValue
      if (newValue) {
        this.getList()
      }
    },
    dialogMainFormVisible: function (newV, oldV) {
      this.$emit('closeDialog', newV)
    }
  }
}
</script>
