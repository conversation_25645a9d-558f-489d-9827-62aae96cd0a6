{"name": "ui", "version": "2.0.2", "description": "UimsAdminUi", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js"}, "dependencies": {"axios": "^0.19.2", "babel-polyfill": "^6.26.0", "circular-json-es6": "^2.0.2", "crypto-js": "^3.3.0", "element-ui": "^2.15.13", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "font-awesome-animation": "^0.2.1", "js-base64": "^3.7.5", "js-cookie": "^2.2.1", "mavon-editor": "^2.9.0", "qs": "^6.12.1", "vditor": "^3.3.3", "vue": "^2.7.14", "vue-awesome-swiper": "^4.1.1", "vue-fragment": "^1.6.0", "vue-router": "^3.3.4", "vuex": "^3.5.1", "xlsx": "^0.15.6"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "autoprefixer": "^7.1.2", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^8.1.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "chalk": "^2.0.1", "compression-webpack-plugin": "^1.1.12", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "element-theme-chalk": "^2.13.2", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "mockjs": "^1.1.0", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.26", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "script-loader": "^0.7.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "svg-sprite-loader": "^4.3.0", "swiper": "^5.4.5", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.7.14", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}