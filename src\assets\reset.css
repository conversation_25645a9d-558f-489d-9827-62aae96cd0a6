@charset "utf-8";
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em:not(.ql-snow em),
font,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: inherit;
    font-weight: inherit;
    font-style: inherit;
    font-family: inherit;
    vertical-align: baseline;
}

html {
  /*font-family: -apple-system,BlinkMacSystemFont,"Microsoft YaHei","微软雅黑","Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB",Arial,sans-serif;*/
  font-family: -apple-system, "Helvetica Neue", "Helvetica", "<PERSON>l", "PingFang SC",
               "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "Microsoft JhengHei", SimSun, sans-serif;

    font-size: 14px;
    background: #fff;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

img {
    border: 0; max-width: 100%!important;
    vertical-align: middle;
}

ol,
ul {
    list-style: none;
}

a {
    text-decoration: none;
    color: inherit;
}

a:hover,
a:focus {
    outline: none;
}

caption,
th {
    text-align: left;
    font-weight: normal;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
    font-size: 100%;
}

q:before,
q:after {
    content: "";
}

abbr,
acronym {
    border: 0;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

:before,
:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section,
summary {
    display: block;
}


/**
 * 1. Change the default font family in all browsers (opinionated).
 * 2. Prevent adjustments of font size after orientation changes in IE and iOS.
 */
html {
  -ms-text-size-adjust: 100%;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 1 */
  -webkit-tap-highlight-color: transparent;
  /* 2 */
  height: 100%;
}
/**
 * Remove the margin in all browsers (opinionated).
 */
body {
  margin: 0;
  /* 1 */
  font-size: 14px;
}
/* HTML5 display definitions
   ========================================================================== */
/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 * 2. Add the correct display in IE.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
/**
 * Add the correct display in iOS 4-7.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}
/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}
/**
 * Add the correct display in IE 10-.
 * 1. Add the correct display in IE.
 */
template, /* 1 */
[hidden] {
  display: none;
}
/* Links
   ========================================================================== */
/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */
a {
  background-color: transparent;
  /* 1 */
  -webkit-text-decoration-skip: objects;
  /* 2 */
  text-decoration: none;
}
/**
 * Remove the outline on focused links when they are also active or hovered
 * in all browsers (opinionated).
 */
a:active,
a:hover {
  outline-width: 0;
}
/* Text-level semantics
   ========================================================================== */
/**
 * 1. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  text-decoration: underline;
  /* 1 */
  text-decoration: underline dotted;
  /* 1 */
}
/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}
/**
 * Add the correct font style in Android 4.3-.
 */
dfn {
  font-style: italic;
}
/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10-.
 */
img {
  border-style: none;
}
/**
 * Hide the overflow in IE.
 */
svg:not(:root) {
  overflow: hidden;
}
/* Grouping content
   ========================================================================== */
/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}
/**
 * 1. Contain overflow in all browsers.
 * 2. Improve readability of pre-formatted text in all browsers.
 */
pre {
  overflow: auto;
  /* 1 */
  white-space: pre;
  /* 2 */
  white-space: pre-wrap;
  /* 2 */
  word-wrap: break-word;
  /* 2 */
}
/* Forms
   ========================================================================== */
/**
 * 1. Correct color not being inherited.
      Known issue: affects color of disabled elements.
 * 2. Change font properties to `inherit` in all browsers (opinionated).
 * 3. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  /* 1 */
  font: inherit;
  /* 2 */
  margin: 0;
  /* 3 */
  vertical-align: middle;
}
/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 * 2. Show the overflow in Edge, Firefox, and IE.
 */
button,
input,  /* 1 */
input {
  /* 2 */
  overflow: visible;
}
/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}
/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */
button,
html [type="button"], /* 1 */
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
}
/* Re-set default cursor for disabled elements. */
[disabled] {
  cursor: default;
}
/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}
/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}
/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on OS X.
 */
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}
/* 1. Remove default vertical scrollbar in IE 8/9/10/11. */
textarea {
  overflow: auto;
  /* 1 */
  resize: none;
  vertical-align: top;
}
/* Don't inherit the `font-weight` (applied by a rule above).
NOTE: the default cannot safely be changed in Chrome and Safari on OS X. */
optgroup {
  font-weight: 700;
}
/* webkit focus outline */
input,
select,
textarea {
  outline: 0;
}
/* Android 下 input focus 消除高亮外框 */
textarea,
input {
  -webkit-user-modify: read-write-plaintext-only;
}
input::-ms-clear,
input::-ms-reveal {
  display: none;
}
/**
 * placeholder text color
 */
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: inherit;
  opacity: 0.54;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}
/* table
   ========================================================================== */
/* Remove table cell spacing and padding */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/*h1, h2, h3, h4, h5, h6 {*/
/*  font-size: 100%;*/
/*  font-weight: 400;*/
/*  line-height: 2;*/
/*}*/

html, body {
  color: #333;
}
