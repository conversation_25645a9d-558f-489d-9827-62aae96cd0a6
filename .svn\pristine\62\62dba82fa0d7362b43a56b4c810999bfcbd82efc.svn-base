<template>
  <div class="main-container" v-if="typeof userId !== 'undefined'"
       :class="(pageMode !=='full' ? '' : 'full-page ') + ($store.state.app.collapse ? 'position-collapse-left':'position-left')">
    <!-- 标签页 -->
    <div class="tab-container">
      <el-tabs ref="tabs" class="tabs" :class="$store.state.app.collapse?'position-collapse-left':'position-left'"
               v-if="pageMode !=='full'"
               v-model="mainTabsActiveName" :closable="true" type="card"
               @tab-click="selectedTabHandle" @tab-remove="removeTabHandle">
        <el-dropdown class="tabs-tools" :show-timeout="0" trigger="hover">
          <div class="arrow-down"><i class="el-icon-arrow-down"></i></div>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item @click.native="tabsCloseCurrentHandle">关闭当前标签</el-dropdown-item>
            <el-dropdown-item @click.native="tabsCloseOtherHandle">关闭其它标签</el-dropdown-item>
            <el-dropdown-item @click.native="tabsCloseAllHandle">关闭全部标签</el-dropdown-item>
            <el-dropdown-item @click.native="tabsRefreshCurrentHandle">刷新当前标签</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tab-pane v-for="item in mainTabs" :key="item.name" :label="item.title" :name="item.name">
          <div slot="label"><div class="topLine"></div><i :class="item.icon"></i><div v-if="item.icon" style="width:2px;display:inline-block;"></div><span class="title">{{item.title}}</span></div>
        </el-tab-pane>
      </el-tabs>
      <div class="tab-container-full-page" v-if="pageMode === 'full'">
        <template v-for="(item, i) in fullPageActives">
          <span class="disabled-active-name" :key="i" v-if="!item.routeName"> {{item.name}}</span>
          <span class="enabled-active-name" :key="i" v-if="item.routeName" @click="toRouter(item.routeName)">{{item.name}}</span>
          <span class="active-name-middle" :key="i + '/'" v-if="i < fullPageActives.length - 1">/</span>
        </template>
      </div>
    </div>
    <!-- 主内容区域 -->
    <div :class="'main-content' + (pageMode !=='full' ? '' : ' main-content-full-page')">
      <transition name="fade" mode="out-in">
        <keep-alive :exclude="[introKey]" :max="pageMode !=='full' ? 20 : 1">
          <router-view :key="key"></router-view>
        </keep-alive>
      </transition>
      <!-- iframe页 -->
      <component v-for="item in hasOpenComponentsArr" :key="item.name" :is="item.name" v-show="$route.path === item.path">
      </component>
    </div>
  </div>
</template>

<script>
  import Vue from 'vue'
  export default {
    data() {
      return {
        componentsArr: [],
        isInit: false,
        refreshUrl: undefined
      }
    },
    computed: {
      pageMode() {
        return config.page_mode;
      },
      // 实现懒加载，只渲染已经打开过（hasOpen:true）的iframe页
      hasOpenComponentsArr() {
        return this.componentsArr.filter(item => item.hasOpen);
      },
      introKey() {
        let key = '首页' + (sessionStorage.getItem('user') ? JSON.parse(sessionStorage.getItem('user')).userName : '');
        return key;
      },
      key() {
        if(this.pageMode === 'full') {
          let key = this.$route.fullPath + (sessionStorage.getItem('user') ? JSON.parse(sessionStorage.getItem('user')).userName : '');
          return key;
        } else {
          // 这一段是用来解决点击资源重刷，点击tab不刷，由于不确定是否和单页面冲突，因此分支
          let routekey = '';
          if(this.$route.meta.keys >= 0) {
            routekey = this.$route.meta.keys;
          }
          let key = this.$route.fullPath + routekey + (sessionStorage.getItem('user') ? JSON.parse(sessionStorage.getItem('user')).userName : '');
          return key;
        }
      },
      userId() {
        return this.$store.state.user.currentUser.userId
      },
      mainTabs: {
        get () {
          return this.$store.state.tab.mainTabs
        },
        set (val) {
          this.$store.commit('updateMainTabs', val)
        }
      },
      // 单页面模式，获取当前页面路由名称路径上的全部路由
      // 如果 机构及用户管理/用户管理/权限管理 其中 机构及用户管理/用户管理、机构及用户管理/用户管理/权限管理 存在路由
      fullPageActives: {
        get () {
          let activeName = this.$store.state.tab.mainTabsActiveName
          // 按 / 拆解
          let activeNames = activeName ? activeName.split("/") : []
          let rootActiveNames = ''
          let router = this.$router;
          let routes = router.options.routes[0].children;
          let result = []
          activeNames.forEach(item => {
            if(rootActiveNames) {
              rootActiveNames = rootActiveNames + "/" + item
            } else {
              rootActiveNames = rootActiveNames + item
            }
            let routerC = routes.filter(ri => ri.name === rootActiveNames);
            // if(routerC.length > 0) {
            //   routerC[0].name = '用户管理'
            //   routerC[0].path = '/user/index'
            // }
            result.push({
              name: item,
              routeName: routerC && routerC.length > 0 && routerC[0].path ? rootActiveNames : undefined
            })
          })
          return result;
        }
      },
      mainTabsActiveName: {
        get () {
          return this.$store.state.tab.mainTabsActiveName
        },
        set (val) {
          this.$store.commit('updateMainTabsActiveName', val)
        }
      },
      spaceId: {
        get() {
          return this.$store.state.system.spaceId
        }
      }
    },
    methods: {
      toRouter(name) {
        this.$router.push({ name: name })
      },
      toRefresh(url) {
        this.refreshUrl = url
      },
      // 根据当前路由设置hasOpen
      isOpenIframePage() {
        const target = this.componentsArr.find(item => {
          return item.path === this.$route.path
        });
        if (target) {
          if(this.refreshUrl && target.hasOpen) {
            target.hasOpen = false
          } else {
            target.hasOpen = true
          }
        }
        this.refreshUrl = undefined
      },
      // 遍历路由的所有页面，把含有iframeComponent标识的收集起来
      getComponentsArr() {
        const router = this.$router;
        const routes = router.options.routes[0].children;
        const iframeArr = routes.filter(item => item.iframeComponent);

        return iframeArr.map((item) => {
          const name = item.name || item.path.replace('/', '');
          return {
            name: name,
            path: '/' + item.path,
            hasOpen: false, // 是否打开过，默认false
            component: item.iframeComponent // 组件文件的引用
          };
        });
      },
      init() {
        if(!this.isInit) {
          this.isInit = true;
          // 设置iframe页的数组对象
          const componentsArr = this.getComponentsArr();
          componentsArr.forEach((item) => {
            Vue.component(item.name, item.component);
          });
          this.componentsArr = componentsArr;
        }
        // 判断当前路由是否iframe页
        this.isOpenIframePage();
      },
      // 选中Tab
      selectedTabHandle (tab) {
        tab = this.mainTabs.filter(item => item.name === tab.name)
        if (tab.length >= 1) {
          this.$router.push({
            name: tab[0].name,
            query: this.spaceId ? {'x-space-id': this.spaceId} : {}
          })
        }
      },
      // 移除Tab
      removeTabHandle (tabName) {
        let _MainTabsActiveName = this.mainTabsActiveName
        let event = new Event('click')
        document.getElementById("tab-" + tabName) && document.getElementById("tab-" + tabName).dispatchEvent(event) // 触发点击事件
        let index = this.mainTabs.findIndex(item => item.name === tabName)
        let oldTabItem = this.mainTabs[index]
        this.mainTabs = this.mainTabs.filter(item => item.name !== tabName)
        if (this.mainTabs.length >= 1) {
          if (tabName === this.mainTabsActiveName) {
            let name = this.mainTabs[this.mainTabs.length - 1].name
            if(this.mainTabsActiveName !== _MainTabsActiveName) {
              name = _MainTabsActiveName
            }
            this.$router.push({
              name: name,
              query: this.spaceId ? {'x-space-id': this.spaceId} : {}
              }, () => {
              this.mainTabsActiveName = this.$router.name
            })
            this.mainTabsActiveName = name
            this.$refs.tabs && this.$refs.tabs.setCurrentName(name)
          }
        } else {
          this.$router.push({
            path: '/intro',
            query: this.spaceId ? {'x-space-id': this.spaceId} : {}
          })
        }
        return [index, oldTabItem]
      },
      // 关闭当前Tab
      tabsCloseCurrentHandle () {
        if (this.mainTabsActiveName !== '首页') {
          this.removeTabHandle(this.mainTabsActiveName)
        }
      },
      // 关闭其他Tab
      tabsCloseOtherHandle () {
        const subTab = this.mainTabs.filter(item => (item.name !== this.mainTabsActiveName && item.name !== '首页'))
        // this.mainTabs = this.mainTabs.filter(item => (item.name === this.mainTabsActiveName || item.name === '首页'))
        let _this = this
        subTab.forEach(item => {
          _this.removeTabHandle(item.name)
        });
      },
      // 关闭所有Tab
      tabsCloseAllHandle () {
        const subTab = this.mainTabs.filter(item => item.name !== '首页')
        // this.mainTabs = this.mainTabs.filter(item => item.name === '首页')
        let _this = this
        subTab.forEach(item => {
          _this.removeTabHandle(item.name)
        });
        this.$router.push({
          path: '/intro',
          query: this.spaceId ? {'x-space-id': this.spaceId} : {}
        })
      },
      // 刷新当前Tab
      tabsRefreshCurrentHandle () {
        var tempTabName = this.mainTabsActiveName
        let [index, oldTabItem] = this.removeTabHandle(tempTabName)
        this.$nextTick(() => {
          this.$router.push({
            name: tempTabName,
            query: this.spaceId ? {'x-space-id': this.spaceId} : {}
          }).then(() => this.initTabs(index, oldTabItem, tempTabName))
        })
      },
      initTabs(index, oldTabItem, tempTabName) {
        this.mainTabs = this.mainTabs.filter(item => item.name === '首页' || this.$router.options.routes[0].children.findIndex(r => r.name === item.name) > -1)
        var tab = this.mainTabs.filter(item => item.name === '首页')[0]
        if(typeof index !== 'undefined') {
          if(tempTabName !== '首页') {
            let suf = this.mainTabs.slice(index).filter(item => item.name !== tempTabName);
            this.$router.push({
              name: tempTabName,
              query: this.spaceId ? {'x-space-id': this.spaceId} : {}
            })
            if(this.mainTabs.filter(item => item.name === tempTabName).length === 0) {
              this.mainTabs = this.mainTabs.slice(0, index).concat([oldTabItem]).concat(suf)
            } else if(this.mainTabs.findIndex(item => item.name === tempTabName) > -1) {
              this.mainTabs = this.mainTabs.filter(item => item.name !== tempTabName)
              this.mainTabs = this.mainTabs.slice(0, index).concat([oldTabItem]).concat(suf)
            }
          } else {
            this.mainTabs = this.mainTabs.slice(0, index).concat(this.mainTabs.filter(tab => tab.name === this.mainTabsActiveName)).concat(this.mainTabs.slice(index, this.mainTabs.length - 1))
          }
        }
        if (!tab) {
          tab = {
            name: '首页',
            title: '首页',
            icon: 'fa fa-home fa-lg'
          }
          this.mainTabs = [tab, ...this.mainTabs]
        }
      }
    },
    watch: {
      $route() {
        this.init();
      }
    },
    mounted () {
      this.initTabs()
    },
    created() {
      window.addEventListener("message", (e) => {
        if(typeof e.data !== 'string') {
          return false;
        }
        try {
          let data = JSON.parse(e.data)
          const BaseUIType = data.BaseUIType || null;
          if(BaseUIType === 'iframeLoaded') {
            this.init()
          }
        } catch(e) {}
      })
    }
  }
</script>
