import axios from "@/core/http/axios";

// 查看登录用户的选数车数据
export const getCartResourceList = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/getCartResourceList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getCartResourceList`,
    method: "get",
    params: data
  });
};

export const getCartPowerList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/powerSearch/getSelectnumPowerPage`,
    method: "get",
    params: data
  });
};

// 删除选数车中的数据
export const deleteCartResource = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/deleteCartResource?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/deleteCartResource`,
    method: "post",
    params: data
  });
};

export const deleteCartPower = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/powerSearch/delPowerFromCart`,
    method: "post",
    params: data
  });
};

// 下载订阅附件
export const downLoadOrderTipFile = (data) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/attachment/downloadFile?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/attachment/downloadFile`,
    method: "get",
    params: data,
    responseType: "blob"
  });
}
