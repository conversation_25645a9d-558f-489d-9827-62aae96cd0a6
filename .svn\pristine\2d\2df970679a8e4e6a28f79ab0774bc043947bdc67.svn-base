/* Background */ .highlight-chroma {  }
/* Error */ .highlight-chroma .highlight-err {  }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; width: auto; overflow: auto; display: block; }
/* LineHighlight */ .highlight-chroma .highlight-hl { display: block; width: 100%;background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Keyword */ .highlight-chroma .highlight-k { color: #008000; font-weight: bold }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #008000; font-weight: bold }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #008000; font-weight: bold }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #008000; font-weight: bold }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #008000 }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #008000; font-weight: bold }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #b00040 }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #7d9029 }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #008000 }
/* NameClass */ .highlight-chroma .highlight-nc { color: #0000ff; font-weight: bold }
/* NameConstant */ .highlight-chroma .highlight-no { color: #880000 }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #aa22ff }
/* NameEntity */ .highlight-chroma .highlight-ni { color: #999999; font-weight: bold }
/* NameException */ .highlight-chroma .highlight-ne { color: #d2413a; font-weight: bold }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #0000ff }
/* NameLabel */ .highlight-chroma .highlight-nl { color: #a0a000 }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #0000ff; font-weight: bold }
/* NameTag */ .highlight-chroma .highlight-nt { color: #008000; font-weight: bold }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #19177c }
/* LiteralString */ .highlight-chroma .highlight-s { color: #ba2121 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #ba2121 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #ba2121 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #ba2121 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #ba2121 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #ba2121; font-style: italic }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #ba2121 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #bb6622; font-weight: bold }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #ba2121 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #bb6688; font-weight: bold }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #008000 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #bb6688 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #ba2121 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #19177c }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #666666 }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #666666 }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #666666 }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #666666 }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #666666 }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #666666 }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #666666 }
/* Operator */ .highlight-chroma .highlight-o { color: #666666 }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #aa22ff; font-weight: bold }
/* Comment */ .highlight-chroma .highlight-c { color: #408080; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #408080; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #408080; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #408080; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #408080; font-style: italic }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #bc7a00 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #bc7a00 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #a00000 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #ff0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #000080; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #00a000 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #000080; font-weight: bold }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #800080; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #0044dd }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #bbbbbb }

/*
Description: Foundation 4 docs style for highlight.js
Author: Dan Allen <<EMAIL>>
Website: http://foundation.zurb.com/docs/
Version: 1.0
Date: 2013-04-02
*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: #eee; color: black;
}

.hljs-link,
.hljs-emphasis,
.hljs-attribute,
.hljs-addition {
    color: #070;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong,
.hljs-string,
.hljs-deletion {
    color: #d14;
}

.hljs-strong {
    font-weight: bold;
}

.hljs-quote,
.hljs-comment {
    color: #998;
    font-style: italic;
}

.hljs-section,
.hljs-title {
    color: #900;
}

.hljs-class .hljs-title,
.hljs-type {
    color: #458;
}

.hljs-variable,
.hljs-template-variable {
    color: #336699;
}

.hljs-bullet {
    color: #997700;
}

.hljs-meta {
    color: #3344bb;
}

.hljs-code,
.hljs-number,
.hljs-literal,
.hljs-keyword,
.hljs-selector-tag {
    color: #099;
}

.hljs-regexp {
    background-color: #fff0ff;
    color: #880088;
}

.hljs-symbol {
    color: #990073;
}

.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
    color: #007700;
}

