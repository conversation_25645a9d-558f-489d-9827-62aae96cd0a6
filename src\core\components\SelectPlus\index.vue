<template>
  <el-select
    v-model="model"
    @change="onChange"
    :multiple="multiple"
    :collapse-tags="collapseTags"
    :disabled="disabled"
    :clearable="clearable"
    filterable
    :placeholder="placeholder"
  >
    <el-option
      v-for="temp in optionsList"
      :key="temp.key"
      :label="temp.value"
      :value="options[temp.key]"
    ></el-option>
  </el-select>
</template>

<script>
import { setDictCache, getDictCache } from '@/core/utils/tabCache'
export default {
  name: 'SelectPlus',
  props: {
    dictType: String,
    mode: { type: String, default: 'sjnl' },
    value: String,
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: false },
    placeholder: { type: String, default: '请选择' },
    initFunction: Function,
    data: Array,
    // 是否多选
    multiple: { type: Boolean, default: false },
    // 多选时是否将选中值按文字的形式展示
    collapseTags: { type: Boolean, default: false }
  },
  data() {
    return {
      options: {},
      model: null,
      optionsArr: [],
      optionsList: []
    }
  },
  methods: {
    onChange(val) {
      if (this.multiple) {
        let vals = typeof val === 'object' ? val || [] : [val]
        let newVals = []
        vals.forEach((v) => {
          let o = this.optionsArr.filter((a) => a[1] === v)
          if (o.length > 0) {
            newVals.push(o[0])
          }
        })
        this.$emit('change', newVals)
      } else {
        let o = this.optionsArr.filter((a) => a[1] === val)
        this.$emit('change', o[0])
      }
    }
  },
  watch: {
    model: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (this.multiple) {
          let valStr = val && typeof val === 'object' ? val.join(',') : ''
          let oldValStr =
            oldVal && typeof oldVal === 'object' ? oldVal.join(',') : ''
          if (valStr !== oldValStr) {
            let labels = []
            let values = []
            let vals = typeof val === 'object' ? val || [] : [val]
            vals.forEach((v) => {
              let o = this.optionsArr.filter((a) => a[1] === v)
              if (o.length > 0) {
                labels.push(o[0][1])
                values.push(o[0][0])
              }
            })
            // this.model = labels;
            this.$emit('input', values.join(',')) // 返回匹配到的值或者null
          }
        } else if (val !== oldVal) {
          let o = this.optionsArr.filter((a) => a[1] === val)
          this.model = o && o[0] ? o[0][1] : null
          this.$emit('input', o && o[0] ? o[0][0] : null) // 返回匹配到的值或者null
        }
      }
    },
    value: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (this.multiple) {
          let vals = []
          if (typeof val === 'object') {
            vals = val || []
          } else if (typeof val === 'string') {
            vals = val.split(',')
          }
          let newVals = []
          vals.forEach((v) => {
            let o = this.optionsArr.filter((a) => a[0] === v)
            if (o.length > 0) {
              newVals.push(o[0][1])
            }
          })
          this.model = newVals
        } else {
          let o = this.optionsArr.filter((a) => a[0] === val)
          this.model = o && o[0] ? o[0][1] : null
        }
      }
    }
  },
  created() {
    if (this.dictType) {
      let that = this
      if (this.data) {
        this.optionsList = this.data
        if (this.initFunction) {
          this.initFunction(this.optionsList)
        }
        this.optionsList.forEach((temp) => {
          that.options[temp.key] = temp.value
          that.optionsArr.push([temp.key, temp.value])
        })
        if (that.value) {
          let o = that.optionsArr.filter((a) => a[0] === that.value)
          that.model = o && o[0] ? o[0][1] : null
          // that.$emit("input", o && o[0] ? o[0][0] : null);
        }
      } else {
        if (getDictCache(this.dictType)) {
          let data = getDictCache(this.dictType)
          if (this.initFunction) {
            this.initFunction(data)
          }
          this.optionsList = data
          data.forEach((temp) => {
            that.options[temp.key] = temp.value
            that.optionsArr.push([temp.key, temp.value])
          })
          if (that.value) {
            if (that.multiple) {
              let newVals = []
              that.value.split(',').forEach((v) => {
                let o = that.optionsArr.filter((a) => a[0] === v)
                if (o.length > 0) {
                  newVals.push(o[0][1])
                }
              })
              // that.model = newVals;
              // that.$emit("input", newVals.join(","));
            } else {
              let o = that.optionsArr.filter((a) => a[0] === that.value)
              that.model = o && o[0] ? o[0][1] : null
              that.$emit('input', o && o[0] ? o[0][0] : null)
            }
          }
        } else {
          console.log(this.mode, 'mode')
          if (this.mode === 'sjnl') {
            this.$api.dict
              .getDictListByType({ type: this.dictType })
              .then((res) => {
                // if (res.code === "200") {
                setDictCache(this.dictType, res.data)
                if (this.initFunction) {
                  this.initFunction(res.data)
                }
                this.optionsList = res.data
                res.data.forEach((temp) => {
                  that.options[temp.key] = temp.value
                  that.optionsArr.push([temp.key, temp.value])
                })
                if (that.value) {
                  if (that.multiple) {
                    let newVals = []
                    that.value.split(',').forEach((v) => {
                      let o = that.optionsArr.filter((a) => a[0] === v)
                      if (o.length > 0) {
                        newVals.push(o[0][1])
                      }
                    })
                    that.model = newVals
                    // that.$emit("input", newVals.join(","));
                  } else {
                    let o = that.optionsArr.filter((a) => a[0] === that.value)
                    that.model = o && o[0] ? o[0][1] : null
                    that.$emit('input', o && o[0] ? o[0][0] : null)
                  }
                }
                // }
              })
          } else {
            this.$api.dict
              .getDictListByTypeWithGxsl({ type: this.dictType })
              .then((res) => {
                // if (res.code === "200") {
                setDictCache(this.dictType, res.data)
                if (this.initFunction) {
                  this.initFunction(res.data)
                }
                this.optionsList = res.data
                res.data.forEach((temp) => {
                  that.options[temp.key] = temp.value
                  that.optionsArr.push([temp.key, temp.value])
                })
                if (that.value) {
                  if (that.multiple) {
                    let newVals = []
                    that.value.split(',').forEach((v) => {
                      let o = that.optionsArr.filter((a) => a[0] === v)
                      if (o.length > 0) {
                        newVals.push(o[0][1])
                      }
                    })
                    that.model = newVals
                    // that.$emit("input", newVals.join(","));
                  } else {
                    let o = that.optionsArr.filter((a) => a[0] === that.value)
                    that.model = o && o[0] ? o[0][1] : null
                    that.$emit('input', o && o[0] ? o[0][0] : null)
                  }
                }
                // }
              })
          }
        }
      }
    }
  }
}
</script>
