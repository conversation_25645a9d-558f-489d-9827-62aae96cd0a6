<template>
  <el-container class="page-container">
    <CollectDialog :visible.sync="drawerVisible" drawerSize="100%" />
  </el-container>
</template>

<script>
import CollectDialog from '@/biz/components/CollectDialog'

export default {
  name: 'ThirdCollect',
  components: {
    CollectDialog
  },
  data() {
    return {
      drawerVisible: false
    }
  },
  methods: {
    initQuery(row) {
      this.$nextTick(function () {
        this.drawerVisible = true
      })
    }
  },
  activated() {},
  watch: {
    $route: {
      handler(newVal, oldVal) {
        console.log(newVal.path, 'newVal.path')
        if (newVal.path === '/collectDialog') {
          this.initQuery(newVal.query)
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
</style>
