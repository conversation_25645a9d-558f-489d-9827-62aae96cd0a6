import axios from "@/core/http/axios";
import Cookies from "js-cookie";

// 能力树
export function getPowerTree(params) {
  var url;
  if(!Cookies.get(config.cookieName)) {
    url = `/${config.appCode}/powerOpen/getPowerTree`;
  } else {
    url = `/${config.appCode}/powerSearch/getPowerTree`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 能力列表
export function getPowerPage(params) {
  var url;
  if(!Cookies.get(config.cookieName)) {
    url = `/${config.appCode}/powerOpen/getPowerPage`;
  } else {
    url = `/${config.appCode}/powerSearch/getPowerPage`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 目录树
export function getCatalogTree(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getCataTypeCntFromPower?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceDir/getCataTypeCntFromPower?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getCataTypeCntFromPower`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getCataTypeCntFromPower`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 目录列表
export function getCatalogPage(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getCatalogPage?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceDir/getCatalogPage?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getCatalogPage`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getCatalogPage`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}

// 资源列表
export function getResourcePage(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getResourcePage?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceApply/getResourcePage?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getResourcePage`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getResourcePage`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}

// 资源树
export function getResourceTree(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
    ? `/${config.appCode}/dataResourceOpen/getCataTypeResCntFromPower?fromPowerInvoke=1`
    : `/${config.appCode}/dataResourceDir/getCataTypeResCntFromPower?fromPowerInvoke=1`
  } else {
    url = !Cookies.get(config.cookieName)
    ? `/${config.appCode}/innerGxsl/dataResourceOpen/getCataTypeResCntFromPower`
    : `/${config.appCode}/innerGxsl/dataResourceDir/getCataTypeResCntFromPower`
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 接入数据统计
export function getCntFromAccept(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
    ? `/${config.appCode}/shareReport/getCntToPower?fromPowerInvoke=1`
    : `/${config.appCode}/innerGxsl/shareReport/getCntToPower`,
    method: "get",
    params
  });
}
// 能力相关信息统计
export function getPowerCnt(params) {
  return axios({
    url: `/${config.appCode}/powerOpen/getPowerCnt`,
    // url: !Cookies.get(config.cookieName)
    // ? `/${config.appCode}/powerOpen/getPowerCnt`
    // : `/${config.appCode}/powerSearch/getPowerCnt`,
    method: "get",
    params
  });
}
// 首页专题导航数据获取
export function getCataTypeCnt(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/getCataTypeCnt?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/getCataTypeCnt`,
    method: "get",
    params
  });
}
// 首页系统分类数据获取
export function getSystemList(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/system/list?fromPowerInvoke=1`
      : `/${config.appCode}/system/list?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/system/list`
      : `/${config.appCode}/innerGxsl/system/list`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 最新目录列表
export function getNewDirList(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/indexLogin/newDirList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/indexLogin/newDirList`,
    method: "get",
    params
  });
}
// 最热目录列表
export function getHostDirList(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/indexLogin/getHostDirList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/indexLogin/getHostDirList`,
    method: "get",
    params
  });
}
// 最新资源列表
export function getNewResourceList(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/indexLogin/getNewResourceList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/indexLogin/getNewResourceList`,
    method: "get",
    params
  });
}
// 最热资源列表
export function getHostResourceApply(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/indexLogin/getHostResourceApply?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/indexLogin/getHostResourceApply`,
    method: "get",
    params
  });
}
// 最新能力列表
export function getLatestPowerList(params) {
  return axios({
    url: !Cookies.get(config.cookieName)
    ? `/${config.appCode}/powerOpen/latestPowerList`
    : `/${config.appCode}/powerSearch/latestPowerList`,
    method: "get",
    params
  });
}
// 最热能力列表
export function getHotPowerList(params) {
  return axios({
    url: !Cookies.get(config.cookieName)
    ? `/${config.appCode}/powerOpen/hotPowerList`
    : `/${config.appCode}/powerSearch/hotPowerList`,
    method: "get",
    params
  });
}
// 能力收藏
export function addPowerToFavorites(params) {
  return axios({
    url: `/${config.appCode}/powerSearch/addPowerToFavorites`,
    method: "post",
    params
  });
}
// 取消能力收藏
export function cancelPowerToFavorites(params) {
  return axios({
    url: `/${config.appCode}/powerSearch/cancelPowerToFavorites`,
    method: "post",
    params
  });
}
// 能力订阅
export function handlePowerApply(data) {
  return axios({
    url: `/${config.appCode}/powerApply/apply`,
    method: "post",
    data,
    transformRequest: function (data, headers) {
      const formData = new FormData()
      for (const key of Object.keys(data)) {
        if(data[key]) {
          formData.append(key, data[key])
        }
      }
      return formData
    }
  });
}
// 能力加入选数车
export function addPowerToCart(data) {
  return axios({
    url: `/${config.appCode}/powerSearch/addPowerToCart`,
    method: "post",
    data,
    transformRequest: function (data, headers) {
      const formData = new FormData()
      for (const key of Object.keys(data)) {
        if(data[key]) {
          formData.append(key, data[key])
        }
      }
      return formData
    }
  });
}
// 能力取消加入选数车
export function delPowerFromCart(params) {
  return axios({
    url: `/${config.appCode}/powerSearch/delPowerFromCart`,
    method: "post",
    params
  });
}
// 资源收藏
export function addResourceToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/addResourceToFavorites?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/addResourceToFavorites`,
    method: "post",
    params
  });
}
// 取消资源收藏
export function cancelResourceToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/cancelResourceCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/cancelResourceCollecting`,
    method: "post",
    params
  });
}
// 资源加入选数车
export function addResourceToCart(data) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addToCart?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addToCart`,
    method: "post",
    data,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
}
// 目录收藏
export function addCatalogToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addCatalogToFavorites?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addCatalogToFavorites`,
    method: "post",
    params
  });
}
// 取消目录收藏
export function cancelCatalogToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/cancelCatalogCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/cancelCatalogCollecting`,
    method: "post",
    params
  });
}
// 目录加入选数车
export function addCatalogToCart(data) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addDirToCart?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addDirToCart`,
    method: "post",
    data,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
}
// 选数车页面 目录资源取消加入选数车
export function delDataFromCart(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/deleteCartResource?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/deleteCartResource`,
    method: "post",
    params
  });
};

// 目录列表、目录详情取消加入选数车
export function delCatalogFromCart(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/deleteCartCata?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/deleteCartCata`,
    method: "post",
    params
  });
};

// 目录左侧树
export function getBaseTypeTree(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getBaseTypeTree?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceDir/getBaseTypeTree?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getBaseTypeTree`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getBaseTypeTree`;
  }

  return axios({
    url: url,
    method: "get",
    params
  });
}

export function getBaseTypeTreeIntro(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/getBaseTypeTree?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/getBaseTypeTree`,
    method: "get",
    params
  });
}

export function getBaseTypeTreeNotTypeIntro(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/getBaseTypeTreeNotType?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/getBaseTypeTreeNotType`,
    method: "get",
    params
  });
}

// 资源左侧树
export function getBaseTypeTreeWithResCnt(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/getBaseTypeTreeWithResCnt?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getBaseTypeTreeWithResCnt`,
    method: "get",
    params
  });
}

// 获取目录详情
export function getCatalogDetails(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getCatalogDetails?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceDir/getCatalogDetails?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getCatalogDetails`
      : `/${config.appCode}/innerGxsl/dataResourceDir/getCatalogDetails`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}
// 获取目录下库表信息详情
export function getDirResourceDatabase(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/dirResourceDatabase?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/dirResourceDatabase`,
    method: "get",
    params
  });
}
// 获取目录下服务信息详情
export function getDirResourceService(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/dirResourceService?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/dirResourceService`,
    method: "get",
    params
  });
}
// 获取目录下文件信息详情
export function getDirResourceFile(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/dirResourceFile?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/dirResourceFile`,
    method: "get",
    params
  });
}
// 获取目录下文件夹信息详情
export function getDirResourceFolder(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceOpen/dirResourceFolder?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceOpen/dirResourceFolder`,
    method: "get",
    params
  });
}
// 获取目录下资源
export function getCatalogInfo(params) {
  var url;
  if(config.gxslInvokeSwitch === true) {
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/dataResourceOpen/getCatalogInfo?fromPowerInvoke=1`
      : `/${config.appCode}/dataResourceApply/getCatalogInfo?fromPowerInvoke=1`;
  }else{
    url = !Cookies.get(config.cookieName)
      ? `/${config.appCode}/innerGxsl/dataResourceOpen/getCatalogInfo`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getCatalogInfo`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}

// 文件 下载
export const downloadFile = (params) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/downloadFile?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/downloadFile`,
    method: "get",
    responseType: "blob",
    params
  });
};

// 获取目录文件下载
export function dirDownloadFile(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/downloadFile?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/downloadFile`,
    method: "get",
    params
  });
}
// 目录/资源订阅
export function handleDataRequest(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/dataRequest?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/dataRequest`,
    method: "post",
    params
  });
}
// 目录加入选数车
export function handleAddDirToCart(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addDirToCart?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addDirToCart`,
    method: "post",
    params
  });
}
// 资源加入选数车
export function handleAddToCart(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addToCart?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addToCart`,
    method: "post",
    params
  });
}
// 目录/资源取消加入选数车
export function handleDeleteCartResource(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/deleteCartResource?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/deleteCartResource`,
    method: "post",
    params
  });
}
// 目录收藏
export function handleAddCatalogToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/addCatalogToFavorites?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/addCatalogToFavorites`,
    method: "post",
    params
  });
}
// 目录取消收藏
export function handleCancelCatalogCollecting(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/cancelCatalogCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/cancelCatalogCollecting`,
    method: "post",
    params
  });
}
// 资源收藏
export function handleAddResourceToFavorites(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/addResourceToFavorites?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/addResourceToFavorites`,
    method: "post",
    params
  });
}
// 资源取消收藏
export function handleCancelResourceCollecting(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/cancelResourceCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/cancelResourceCollecting`,
    method: "post",
    params
  });
}
// 获取能力详情
export function getPowerDetail(params) {
  var url;
  if(!Cookies.get(config.cookieName)) {
    url = `/${config.appCode}/powerOpen/getPowerDetail`;
  } else {
    url = `/${config.appCode}/powerSearch/getPowerDetail`;
  }
  return axios({
    url: url,
    method: "get",
    params
  });
}

// 获取数据统计
export function getCntToPower(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/shareReport/getCntToPower?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/shareReport/getCntToPower`,
    method: "get",
    params
  });
}

// 能力附件下载
export function handleDownloadFile(annexId) {
  return axios({
    url: `/${config.appCode}/annex/download/${annexId}`,
    method: "get",
    responseType: 'blob' // 字节流：arraybuffer，文件流：blob
  });
}

// 能力订阅后附件下载
export function downloadPowerSdk(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/downloadSdk`,
    method: 'get',
    params,
    responseType: 'blob' // 字节流：arraybuffer，文件流：blob
  })
}
