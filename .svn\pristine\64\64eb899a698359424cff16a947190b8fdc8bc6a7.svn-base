<template>
  <el-container class="page-container">
<!--    <el-header v-if="pageMode==='full'"></el-header>-->
    <!-- <el-header> -->
      <!--列表工具栏-->
<!--      <div class="toolbar-wrapper">-->
<!--        <perm-button label="编辑" type="text" icon="edit" :perms="systemCode + 'x99003001'" @click="handleUpdate" />-->
<!--      </div>-->
    <!-- </el-header> -->
    <el-main>
      <!--列表表格区-->
      <table-plus id="Params" :data="list" @header-dragend="handleHeaderDrag" border fit stripe highlight-current-row v-loading="listLoading" @row-click="clickRow" ref="configTable" @selection-change="selectMainTableRow">
        <el-table-column type="selection" width="60" align="center"></el-table-column>
        <el-table-column prop="code" label="编码" width="250"></el-table-column>
        <el-table-column prop="value" label="取值" width="100"></el-table-column>
        <el-table-column prop="remark" label="备注" width="500" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modifyTime" label="修改时间" align="center" width="180"></el-table-column>
        <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center" width="100">
          <template slot-scope="scope">
            <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
          </template>
        </el-table-column>
      </table-plus>
    </el-main>
    <el-footer>
      <table-footer ref="tableFooter" :showToolBar="true" excelName="参数管理" :showPage="true" :tableRef="this.$refs.configTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total"
      url="/config/list"
      :filters="filters">
      </table-footer>
    </el-footer>
    <update-params-dialog @closeDialog="closeDialog" @getList="getList" :dialogStatus="dialogStatus" :paramsArray="selectTableRow" :dialogFormVisible="dialogFormVisible">
    </update-params-dialog>
  </el-container>
</template>

<script>
import PermButtonGroup from "@/core/components/PermButtonGroup";
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus"
import UpdateParamsDialog from "./Dialog/UpdateParamsDialog"
import TableFooter from "@/core/components/TablePlus/TableFooter";
import { resourceCode } from "@/biz/http/settings";

export default {
  name: 'config',
  components: {
    TablePlus,
    PermButton,
    UpdateParamsDialog,
    TableFooter,
    PermButtonGroup
  },
  data() {
    return {
      queryBt: true,
      systemCode: config.systemCode,
      currentRow: undefined,
      // 系统List
      list: [],
      listLoading: true,
      // 编辑时的table选择row
      selectTableRow: [],
      // 选择用户查询过滤条件
      total: 0,
      filters: {
        currentPage: 1,
        pageSize: 20
      },
      // 新增\编辑窗口显示控制
      dialogFormVisible: false,
      dialogStatus: '',
      size: 'small'
    }
  },
  computed: {
    pageMode() {
      return config.page_mode;
    }
  },
  methods: {
    getButtons(row) {
      let buts = [
        {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99003001'}
      ];
      return {
        row: row,
        buttons: buts,
        showNums: 2
      }
    },
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.configTable.doLayout();
      })
    },
    closeDialog(val) {
      this.dialogFormVisible = val
    },
    handleCreate() {
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1;
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    getList() {
      if(!this.queryBt) {
        return;
      }
      this.queryBt = false
      this.listLoading = true
      this.$api.config.getList(this.filters, resourceCode.config).then((res) => {
        this.queryBt = true
        this.listLoading = false
        this.selectTableRow = [];
        this.list = res.data.records;
        this.total = res.data.total
      }).catch(res => {
        this.queryBt = true
      })
    },
    clickRow(row, event, column) {
      if (this.currentRow === row) {
        this.currentRow = undefined
        this.$refs.configTable.clearSelection();
      } else {
        this.currentRow = row
        this.$refs.configTable.clearSelection();
        this.$refs.configTable.toggleRowSelection(row)
      }
    },
    selectMainTableRow(row, event, column) {
      this.selectTableRow = Object.assign([], row);
    },
    handleUpdate(row) {
      this.selectTableRow = [row]
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },
    handleFilter() {
      this.getList()
    }
  },
  created() {
    this.getList()
  }
}
</script>
