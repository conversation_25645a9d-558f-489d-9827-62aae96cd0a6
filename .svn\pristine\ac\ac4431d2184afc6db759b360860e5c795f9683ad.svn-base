hljs.registerLanguage("vala",function(t){return{k:{keyword:"char uchar unichar int uint long ulong short ushort int8 int16 int32 int64 uint8 uint16 uint32 uint64 float double bool struct enum string void weak unowned owned async signal static abstract interface override virtual delegate if while do for foreach else switch case break default return try catch public private protected internal using new this get set const stdout stdin stderr var",built_in:"DBus GLib CCode Gee Object Gtk Posix",literal:"false true null"},c:[{cN:"class",bK:"class interface namespace",e:"{",eE:!0,i:"[^,:\\n\\s\\.]",c:[t.UTM]},t.CLC<PERSON>,t.CBCM,{cN:"string",b:'"""',e:'"""',r:5},t.ASM,t.QSM,t.CNM,{cN:"meta",b:"^#",e:"$",r:2}]}});