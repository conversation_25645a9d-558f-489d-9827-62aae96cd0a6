<template>
  <!-- 新增Form -->
  <DialogPlus v-dialogDrag="{ zoom: false }" autoHeight :title="modeMap[dialogModes]" :visible.sync="dialogMainFormVisible" width="500px" @open="initDialog"
             @close="destrory()">
    <el-form ref="dataForm" :rules="dialogModes === 'view'? {} : rules" :model="temp" label-position="right" label-width="70px">
      <el-form-item label="配置编码">
        <el-input v-model="temp.code" :disabled=true  maxlength="50"/>
      </el-form-item>
      <el-form-item label="配置名称">
        <el-input v-model="temp.settingName" :disabled=true  maxlength="50"/>
      </el-form-item>
      <el-form-item label="配置值" prop="value" class="margin-bottom_0">
        <el-input v-if="temp.tsysUserConfigRange.type === 'text'" v-model="temp.value" :disabled="dialogModes === 'view'" maxlength="50"/>
        <el-select v-if="temp.tsysUserConfigRange.type === 'enum'" v-model="temp.value" :disabled="dialogModes === 'view'" placeholder="请选择配置值">
          <el-option
            v-for="(v, k) in temp.tsysUserConfigRange.enumlist"
            :key="k"
            :label="v"
            :value="k">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">{{dialogModes==='view'?'关闭':'取消'}}</el-button>
      <el-button class="save-btn" type="primary" @click="save()"
                 v-if="!(dialogModes === 'view')" :loading="okLoading">保存
      </el-button>
    </div>
  </DialogPlus>
</template>

<script>
  import {resourceCode} from "@/biz/http/settings";
  import DialogPlus from "@/core/components/DialogPlus";
  export default {
    name: "MsgInBoxMainDialog",
    components: { DialogPlus },
    data() {
      var validateProsserAction = (rule, value, callback) => {
        if(this.temp.tsysUserConfigRange.regex) {
          var reg = new RegExp(this.temp.tsysUserConfigRange.regex)
          if(!reg.test(value)) {
            return callback(new Error('校验不通过；' + (this.temp.tsysUserConfigRange.regexText ? this.temp.tsysUserConfigRange.regexText : "")));
          }
        }
        callback();
      };
      return {
        resCode: '',
        okLoading: false,
        modeMap: {
          update: '编辑用户设置',
          view: '查看用户设置'
        },
        // 新增/编辑列表
        temp: {
          userId: undefined,
          code: undefined,
          settingName: undefined,
          value: undefined,
          tsysUserConfigRange: {
            type: undefined,
            value: undefined,
            regex: undefined,
            regexText: undefined,
            enumlist: {}
          },
          range: undefined
        },
        valueOptions: undefined,
        dialogMainFormVisible: false,
        rules: {
          value: [{validator: validateProsserAction, trigger: 'blur'}]
        }
      }
    },
    props: {
      dialogModes: String,
      dialogFormVisible: Boolean,
      ucId: String,
      code: String
    },
    methods: {
      destrory() {
        this.okLoading = false;
        this.$refs['dataForm'].clearValidate()
      },
      initDialog() {
      },
      getValueOptions(range) {
        this.valueOptions = [];
        for(let temp in range) {
          let json = {}
          json.value = temp
          json.label = temp + "-" + range[temp]
          this.valueOptions.push(json)
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid, obj) => {
          if (valid) {
            this.okLoading = true;
            this.resCode = resourceCode.msgUserConfig
            this.$api.msgUserConfig.save(this.temp, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: '编辑成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogModes === 'update') {
            this.resCode = resourceCode.msgUserConfig
            this.$api.msgUserConfig.getById({ucId: this.ucId, code: this.code}, this.resCode).then((res) => {
              this.temp = res.data
              // if (this.temp.valueType === 'enum') {
              //   let rangeJson = JSON.parse(this.temp.range);
              //   this.getValueOptions(rangeJson["enumlist"]);
              // }
            })
          } else if (this.dialogModes === 'view') {
            this.resCode = resourceCode.msgUserConfig
            this.$api.msgUserConfig.getById({ucId: this.ucId, code: this.code}, this.resCode).then((res) => {
              this.temp = res.data
            })
          }
        }
      },
      dialogMainFormVisible: function (newV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
