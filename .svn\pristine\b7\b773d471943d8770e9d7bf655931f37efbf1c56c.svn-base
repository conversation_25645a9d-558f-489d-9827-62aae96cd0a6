import axios from '../axios'

/*
 * 用户管理模块
 */
// 修改用户密码
export const changePassword = (data) => {
  return axios({
    url: config.isOnUims ? `/${config.appCode}/security-frame/change-password` : `/${config.appCode_uims}/user/changePassword`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'isBaseApi': true}
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/userInfo/user`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const updateUserInfo = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userInfo/updateUserInfo`,
    method: 'post',
    headers: {'X-resource-code': 'uimsx98001', 'isBaseApi': true},
    data
  })
}

// 获取用户授权
// 用于:个人信息
export const getPriv = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/userInfo/userPriv`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const mobileLoginChange = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/mLoginEnabled`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    data
  })
}

export const emailLoginChange = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/eLoginEnabled`,
    method: 'post',
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const checkMobileBounded = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/checkMobileBounded`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const checkEmailBounded = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/checkEmailBounded`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const checkMobileVisibleCode = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/validateMCode`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const checkEmailVisibleCode = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/validateECode`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const saveMobileBind = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/saveMobileBound`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

export const saveEmailBind = (data) => {
  return axios({
    url: `/${config.appCode_uims}/userSafe/saveEmailBound`,
    headers: {'X-resource-code': 'uimsx98002', 'isBaseApi': true},
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

// 获取用户列表
// 用于:用户管理-用户表格/联合用户管理-新增\修改窗口-添加用户窗口
export const getList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/user/list`,
    method: 'post',
    headers: {
      'X-resource-code': resourceCode, 'isBaseApi': true
    },
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

// 获取用户消息内容
export const getInstantMsg = (data) => {
  return axios({
    url: `/${config.appCode}/msgInBox/getMsgByMiId`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'isBaseApi': true}
  })
}

// 消息内容
export const sendReceipt = (data) => {
  return axios({
    url: `/${config.appCode}/msgInBox/receipt`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'isBaseApi': true}
  })
}
