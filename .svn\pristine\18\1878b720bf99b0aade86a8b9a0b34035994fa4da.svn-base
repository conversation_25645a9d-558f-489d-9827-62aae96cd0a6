@import "./mixi.scss";
$colors: #3572ff;
$themeColor: #3572ff;
.colorGreen {
  color: #00c818 !important;
}
.colorOrange {
  color: #ff5400 !important;
}
.el-table.projectTable {
  // border: 1px solid #E5E5E5;
  .el-table__cell {
    padding: 15px 0;
  }
  .el-table__footer-wrapper {
    td.el-table__cell {
      background-color: #ffffff;
    }
  }
}
// 分页
.icsp-pagination-wrap {
  padding: 0.16rem 0.2rem;
  text-align: right;
}

.smart_city__el-drawer {
  .el-drawer__container {
    .el-drawer {
      .el-drawer__header {
        margin-bottom: 0;
        padding: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid #dcdfe6;
      }
      .el-drawer__body {
        .smart_city__el-drawer-body {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          height: 100%;
          &-content {
            flex: 1;
            box-sizing: border-box;
            padding: 16px 32px;
            overflow-y: auto;
          }
          &-footer {
            width: 100%;
            padding: 21px 32px 26px;
            box-sizing: border-box;
            border-top: 1px solid #f2f2f2;
            &-btn {
              width: 100%;
              display: flex;
              justify-content: flex-end;
              align-items: center;
              .el-button.close-btn {
                background-color: transparent;
                color: #666;
                border-color: #c9c9c9;
                &:hover {
                  background-color: #f6f6f6;
                  color: #666;
                  border-color: #c9c9c9;
                }
              }
            }
          }
        }
      }
    }
  }
}

.icsp-button {
  flex-shrink: 0;
  padding: 10px;
  border: 1px solid #3572ff;
  background-color: #fff;
  color: #3572ff;
  .svg-icon {
    width: 15px;
    height: 15px;
    margin-bottom: -2px;
    margin-right: 4px;
    fill: #3572ff;
  }
  &:hover {
    background-color: #3572ff;
    color: #fff;
    .svg-icon {
      fill: #fff;
    }
  }
}
// 置灰按钮
.icsp-button-grey {
  lex-shrink: 0;
  padding: 10px;
  background-color: #efefef !important;
  color: #333 !important;
  .svg-icon {
    width: 15px;
    height: 15px;
    margin-bottom: -2px;
    margin-right: 4px;
    fill: #333;
  }
  &:hover {
    background-color: #eee;
    color: #333;
  }
}
.icsp-button2 {
  flex-shrink: 0;
  padding: 10px;
  border: 1px solid #0dc143;
  color: #0dc143;
  .svg-icon {
    width: 15px;
    height: 15px;
    margin-bottom: -2px;
    margin-right: 4px;
    fill: #0dc143;
  }
  &:hover {
    background-color: #0dc143;
    color: #fff;
    .svg-icon {
      fill: #fff;
    }
  }
}
