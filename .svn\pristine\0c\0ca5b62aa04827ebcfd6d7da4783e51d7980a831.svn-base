<template>
  <el-dialog v-dialogDrag :title="textMapDict[dialogDictStatus]" :visible.sync="dialogMainFormVisible" width="600px" @open="initDialog" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <el-form ref="dataDictForm" :rules="rules" :model="tempDict" label-position="right" label-width="100px" @keyup.enter.native="dialogDictStatus==='create'?createDictData('1'):nullFlag()">
          <!--<el-input v-model="tempDict.dictId" style="display: none"/>-->
          <el-form-item label="字典类型">
            <el-input v-model="tempDict.dictType" disabled />
          </el-form-item>
          <el-form-item label="字典类型名称">
            <el-input v-model="tempDict.dictTypeName" disabled />
          </el-form-item>
          <el-form-item label="字典编码" prop="code">
            <el-input v-model="tempDict.code" :disabled="dialogDictStatus!=='create'?true:false" maxlength="20" />
          </el-form-item>
          <el-form-item label="字典名称" prop="name">
            <el-input v-model="tempDict.name" maxlength="25" />
          </el-form-item>
          <el-form-item label="排序号">
            <el-input-number v-model="tempDict.orderNo" controls-position="right" :precision="0" :min="1" :max="9999" />
          </el-form-item>
          <el-form-item label="是否启用" prop="enenabled">
            <!--<el-select v-model="tempDict.enabled" placeholder="请选择启用状态">-->
            <!--<el-option v-for="item in dict.isEnable" :key="item.code" :label="item.name" :value="item.code"/>-->
            <!--</el-select>-->
            <select-plus dictType="IS_ENABLE" v-model="tempDict.enabled"></select-plus>
          </el-form-item>
          <el-form-item label="备注" class="margin-bottom_0">
            <el-input style="margin-bottom: 2px;" v-model="tempDict.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" maxlength="125" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDict">
        取消
      </el-button>
      <el-button class="saveAdd-btn" type="primary" @click="createDictData('1')" v-if="dialogDictStatus==='create'" :loading="okLoading">
        新增保存
      </el-button>
      <el-button class="save-btn" type="primary" @click="dialogDictStatus==='create'?createDictData('0'):updateDictData()" :loading="okLoading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import SelectPlus from "@/core/components/SelectPlus";
// import { checkCode } from "@/biz/utils/validate"
import { resourceCode } from "@/biz/http/settings"
export default {
  name: "DictDialog",
  components: { SelectPlus },
  data() {
    const checkCode = (rule, value, callback) => {
      let patter = /^(?![_/: -])(?!.*?[_/: -]$)[a-zA-Z0-9_/: -]+$/;
      if (!patter.test(value)) {
        return callback(new Error('只能有字母、数字、空格和_/:-且只能以字母、数字开头和结尾'))
      } else {
        callback()
      }
    };
    return {
      okLoading: false,
      textMapDict: {
        update: '编辑字典项',
        create: '新增字典项'
      },
      // 新增/编辑字典项界面临时数据存放
      tempDict: {
        dictId: undefined,
        dictType: undefined,
        dictTypeName: undefined,
        code: undefined,
        name: undefined,
        fullName: undefined,
        orderNo: 1,
        enabled: '1',
        remark: undefined
      },
      dialogMainFormVisible: false,
      resCode: '',
      // 表单校验规则
      rules: {
        code: [{ required: true, message: '字典编码为必填项', trigger: 'blur' }, { validator: checkCode, trigger: 'blur' }],
        name: [{ required: true, message: '字典名称为必填项', trigger: 'blur' }],
        enabled: [{ required: true, message: '是否启用为必选项' }]
      },
      // 字典
      dict: {
        isEnable: undefined
      }
    }
  },
  props: {
    dialogDictStatus: String,
    dialogDictVisible: Boolean,
    dictArray: Array,
    dictTypeTemp: Object
  },
  methods: {
    destrory() {
      this.dialogMainFormVisible = false;
      this.okLoading = false;
      this.resetTemp()
    },
    closeDict() {
      this.dialogMainFormVisible = false;
    },
    nullFlag() { },
    initDialog() {
      // this.loadDict()
    },
    createDictData(val) {
      this.$refs['dataDictForm'].validate((valid) => {
        if (valid) {
          this.okLoading = true
          this.$api.dict.saveDict(this.tempDict, this.resCode).then((res) => {
            this.okLoading = false
            if (val === '0') {
              this.dialogMainFormVisible = false;
              this.$emit("getList");
            } else {
              this.$emit("getList");
              this.resetTemp()
              this.$nextTick(() => {
                this.$refs['dataDictForm'].clearValidate()
              })
            }
            this.$notify({
              title: '操作成功',
              message: '新增字典项成功',
              type: 'success',
              duration: 2000
            })
          }).catch((res) => {
            this.okLoading = false
          })
        }
      })
    },
    updateDictData() {
      this.$refs['dataDictForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.tempDict)
          this.okLoading = true
          this.$api.dict.updateDict(tempData, this.resCode).then((res) => {
            this.okLoading = false
            this.dialogMainFormVisible = false;
            this.$emit("getList");
            this.$notify({
              title: '操作成功',
              message: '编辑字典项成功',
              type: 'success',
              duration: 2000
            })
          }).catch((res) => {
            this.okLoading = false
          })
        }
      })
    },
    resetTemp() {
      this.tempDict = {
        dictId: undefined,
        dictType: this.dictTypeTemp.code,
        dictTypeName: this.dictTypeTemp.name,
        code: undefined,
        name: undefined,
        fullName: undefined,
        orderNo: 1,
        enabled: '1',
        remark: undefined
      }
    }
  },
  watch: {
    dialogDictVisible: function (newValue, oldValue) {
      this.dialogMainFormVisible = newValue
      if (newValue) {
        if (this.dialogDictStatus === 'create') {
          this.resCode = resourceCode.dict_addC
          this.resetTemp()
          this.$nextTick(() => {
            this.$refs['dataDictForm'].clearValidate()
          })
        } else if (this.dialogDictStatus === 'update') {
          this.resCode = resourceCode.dict_eidtC
          // this.tempDict = Object.assign({}, this.dictArray[0]);
          // this.tempDict.enabled = this.tempDict.enabledCode;
          this.$api.dict.getById(this.dictArray[0].dictId, this.resCode).then((res) => {
            this.tempDict = Object.assign({}, res.data)
          })
          this.$nextTick(() => {
            this.$refs['dataDictForm'].clearValidate()
          })
        }
      }
    },
    dialogMainFormVisible: function (newV, oldV) {
      this.$emit('closeDialog', newV)
    }
  }
}
</script>

<style scoped lang="scss">
</style>
