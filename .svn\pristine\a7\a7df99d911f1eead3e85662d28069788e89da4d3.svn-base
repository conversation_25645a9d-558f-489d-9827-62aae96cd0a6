import axios from '@/core/http/axios'

// 获取通知公告列表
export const getList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgNotice/list`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

// 新增\修改
export const save = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgNotice/save`,
    method: 'post',
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

// 删除
export const del = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgNotice/del`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgNotice/getById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
