<template>
  <el-row class="tree-transfer-wrapper">
    <el-col :span="10" style="width: 210px;">
      <div class="grid-content panel">
        <div class="title">可选权限</div>
        <div class="body">
          <el-scrollbar ref="lscrollbar" wrap-class="scrollbar-wrapper" style="height: 100%;">
            <el-tree show-checkbox v-loading="listLoading" :props="treeProp" :data="navTree" :default-checked-keys="defaultCheckedKeys" node-key='id' highlight-current :expand-on-click-node="false" ref="resourceTree" />
          </el-scrollbar>
        </div>
      </div>
    </el-col>
    <el-col :span="4" style="width: 65px;">
      <div class="grid-content buttons-container">
        <el-tooltip content="移除" placement="top" popper-class="shortcut-del-btn">
          <el-button icon="el-icon-arrow-left" circle @click="removeCheckedNodes"></el-button>
        </el-tooltip>
        <el-tooltip content="添加" placement="bottom" popper-class="shortcut-add-btn">
          <el-button icon="el-icon-arrow-right" circle @click="getCheckedNodes"></el-button>
        </el-tooltip>
      </div>
    </el-col>
    <el-col :span="10" style="width: 210px;">
      <div class="grid-content panel">
        <div class="title">已选权限</div>
        <div class="body">
          <el-scrollbar ref="rscrollbar" wrap-class="scrollbar-wrapper" style="height: 100%;">
            <el-tree show-checkbox v-loading="listLoading" :props="treeProp" :data="resourceList" node-key='id' highlight-current :expand-on-click-node="false" ref="resourceList" draggable :allow-drop="allowDrop" />
          </el-scrollbar>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: "TreeTransfer",
  data() {
    return {
      treeProp: {
        children: 'children',
        label: 'name'
      },
      listLoading: false,
      resourceList: [], // 已选资源菜单
      defaultCheckedKeys: [] // 初始化选中的树节点
    }
  },
  props: {
    initData: { type: Array, default: () => [] }
  },
  computed: {
    ...mapState({
      navTree: state => state.menu.navTree
    })
  },
  methods: {
    // 树节点点击事件响应方法(该节点所对应的对象、节点对应的 Node、节点组件本身)
    getCheckedNodes() {
      let ns = this.$refs.resourceTree.getCheckedNodes();
      this.resourceList = ns.filter(n => (!n.children || n.children.length === 0))
      this.$emit('setValue', this.resourceList);
    },
    // 移除右侧选中的节点
    removeCheckedNodes() {
      // 左侧选中的节点
      let t = this.$refs.resourceTree.getCheckedNodes();
      // 右侧选中的节点
      let l = this.$refs.resourceList.getCheckedNodes();
      // 1、左侧选中的节点排除右侧选中的节点
      let s = t.filter(n => (l.filter(o => o.id === n.id).length === 0));
      // 2、右侧列表删除选中的节点
      this.resourceList = this.resourceList.filter(n => (l.filter(o => o.id === n.id).length === 0));
      // 3、更新左侧选中节点
      this.$refs.resourceTree.setCheckedKeys(this.getTreeCheckedKeys(s));
      this.$emit('setValue', this.resourceList);
    },
    // 拖拽顺序控制
    allowDrop(draggingNode, dropNode, type) {
      if (type === "inner") {
        return false;
      } else {
        return true;
      }
    },
    // 初始化树的选中节点
    getTreeCheckedKeys(s) {
      let n = s.map((item, index, arr) => {
        let lc = item.children;
        if (item.children && item.children.length > 0) {
          if (s.filter(o => (lc.filter(c => o.id === c.pid).length > 0)).length > 0) {
            return item.id;
          }
        } else {
          return item.id;
        }
      });
      return n;
    },
    // 初始化选中节点
    init() {
      this.resourceList = this.initData;
      this.defaultCheckedKeys = this.getTreeCheckedKeys(this.resourceList);
    },
    cancel(originalData) {
      this.resourceList = originalData;
      this.$refs.resourceTree.setCheckedKeys(this.getTreeCheckedKeys(originalData));
    }
  },
  created() {
    this.init();
  }
};
</script>
