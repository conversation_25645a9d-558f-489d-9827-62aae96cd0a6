<template>
  <div id="app">
    <keep-alive>
      <router-view />
    </keep-alive>
  </div>
</template>

<script>
// import '@/assets/css/theme/434343/index.css'
// import '@/assets/css/theme/4E5BE2/index.css'
// import '@/assets/css/theme/800080/index.css'
// import '@/assets/css/theme/E7408C/index.css'

// import '@/assets/thinvent/index.scss'
// import '@/assets/thinvent_darken/index.scss'
// import '@/assets/orange/index.scss'
import '@/assets/common.scss'
import '@/assets/iconfont/iconfont.scss'

export default {
  name: 'App',
  created() {
    // 在页面加载时读取sessionStorage里的状态信息
    if (sessionStorage.getItem('store')) {
      this.$store.replaceState(
        Object.assign({}, this.$store.state, JSON.parse(sessionStorage.getItem('store')))
      )
    }
    // 在页面刷新时将vuex里的信息保存到sessionStorage里
    window.addEventListener('beforeunload', () => {
      sessionStorage.setItem('store', JSON.stringify(this.$store.state))
    })

    // 监听权限数据变化，实时保存到sessionStorage
    // this.$store.watch(
    //   (state) => state.user.perms,
    //   (newPerms) => {
    //     if (newPerms && newPerms.length > 0) {
    //       sessionStorage.setItem('store', JSON.stringify(this.$store.state))
    //     }
    //   },
    //   { immediate: true }
    // )
  }
}
</script>

<style>
#app {
  /* font-family: 'Avenir', Helvetica, Arial, sans-serif; */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /*text-align: center;*/
  /* color: #2c3e50; */
  background-color: #f1f5fa;
}
</style>
