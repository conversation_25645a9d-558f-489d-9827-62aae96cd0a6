<template>
  <div class="menu-bar-container" :class="collapse?'menu-bar-collapse-width':'menu-bar-width'">
    <!-- home -->
    <div class="home-wrapper">
      <!-- 导航收缩 -->
      <div class="nav-btn" @click="onCollapse">
        <span class="svg-container">
          <svg-icon icon-class="double-left-arrow" :class="collapse?'rotateZ180deg':'rotateZ360deg'"/>
        </span>
      </div>
      <!-- <div class="home" @click="onDrawerSystem">
        <el-tooltip :disabled="!collapse&&appName.length>7?false:true" :content="appName" placement="bottom" effect="light">
          <span>{{collapse?'':appName}}</span>
        </el-tooltip>
        <i class="el-icon-caret-right"></i>
      </div> -->
      <div class="home">
        <span>功能菜单</span>
      </div>
    </div>
    <!-- menu -->
    <div :class="['menu-wrapper', thumbnailPicture] ">
      <!-- 导航菜单 -->
      <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
        <el-menu background-color="#343434" ref="navmenu" default-active="1" :class="collapse?'menu-bar-collapse-width':'menu-bar-width'"
                 :collapse="collapse" :collapse-transition="false" :unique-opened="true" active-text-color="#008aff"
                 @open="handleopen" @close="handleclose" @select="handleselect">
          <!-- 导航菜单树组件，动态加载菜单 -->
            <menu-tree v-for="item in navTree" :key="item.id" :menu="item" :mainContent="mainContent"></menu-tree>
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import MenuTree from '@/core/components/MenuTree'
export default {
  props: {
    mainContent: Object
  },
  components: {
    MenuTree
  },
  computed: {
    ...mapState({
      appName: state => state.app.appName,
      collapse: state => state.app.collapse,
      navTree: state => state.menu.navTree,
      thumbnailPicture: state => state.app.thumbnail.thumbnailPicture
    }),
    mainTabs: {
      get () { return this.$store.state.tab.mainTabs },
      set (val) { this.$store.commit('updateMainTabs', val) }
    },
    mainTabsActiveName: {
      get () { return this.$store.state.tab.mainTabsActiveName },
      set (val) { this.$store.commit('updateMainTabsActiveName', val) }
    },
    backgroundColor() {
      var bgColor = /^([0-9a-fA-F]{6}){1}$/g.test(this.$store.state.app.themeColor) ? '#' + this.$store.state.app.themeColor : this.$store.state.app.themeColor
      return bgColor
    }
  },
  watch: {
    $route: 'handleRoute'
  },
  created () {
    this.handleRoute(this.$route)
  },
  methods: {
    // 折叠系统导航栏
    onDrawerSystem: function () {
      this.$store.commit('onDrawerSystem')
    },
    // 折叠导航栏
    onCollapse: function () {
      this.$store.commit('onCollapse')
    },
    handleopen() {
      // console.log('handleopen')
    },
    handleclose() {
      // console.log('handleclose')
    },
    handleselect(a, b) {
      // console.log('handleselect')
    },
    // 路由操作处理
    handleRoute (route) {
      // tab标签页选中, 如果不存在则先添加
      var tab = this.mainTabs.filter(item => item.name === route.name)[0]
      if (!tab) {
        tab = {
          name: route.name,
          title: route.name,
          icon: route.meta.icon,
          isFromBtn: route.meta.isFromBtn
        }
        this.mainTabs = this.mainTabs.concat(tab)
      }
      this.mainTabsActiveName = tab.name
      // 切换标签页时同步更新高亮菜单
      if(this.$refs.navmenu != null) {
        this.$refs.navmenu.activeIndex = '' + route.meta.index
        this.$refs.navmenu.initOpenedMenu()
      }
    }
  }
}
</script>
