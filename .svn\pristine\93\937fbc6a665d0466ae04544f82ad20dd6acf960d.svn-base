body.thinvent .loginPage-container {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  background: url("login_bg.jpg") center center no-repeat #01010d;
  background-size: auto auto
}

body.thinvent .loginPage-container .form-wrapper {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 488px;
  height: 541px;
  top: calc((100% - 445px) / 2);
  left: calc((100% - 495px) / 2);
  position: absolute;
  background: url("login_k.png") center center no-repeat
}

body.thinvent .loginPage-container .form-wrapper .logo {
  position: absolute;
  margin-left: -13px;
  margin-top: -678px;
  margin-bottom: 54px
}

body.thinvent .loginPage-container .form-wrapper .logo.noFieldset {
  margin-top: -325px;
  margin-bottom: 80px
}

body.thinvent .loginPage-container .form-wrapper .logo img, body.thinvent .loginPage-container .form-wrapper .logo p {
  vertical-align: middle
}

body.thinvent .loginPage-container .form-wrapper .logo p {
  display: inline-block;
  font-size: 38px;
  font-weight: 500;
  background-image: -webkit-linear-gradient(bottom, #12d4e8, #dbf4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent
}

body.thinvent .loginPage-container .form-wrapper .login-container {
  width: 420px;
  height: 310px;
  margin: 0 auto
}

body.thinvent .loginPage-container .form-wrapper .login-container .title {
  margin: 0px auto 27px auto;
  text-align: center
}

body.thinvent .loginPage-container .form-wrapper .login-container .title span {
  font-size: 28px;
  text-shadow: none;
  color: #00d8ff
}

body.thinvent .loginPage-container .form-wrapper .login-container .title hr {
  display: none
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item {
  margin-bottom: 30px;
  border: 1px solid transparent;
  border-bottom-color: #0e495c;
  border-radius: 0;
  background-color: transparent;
  color: #454545
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content {
  padding-left: 0px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .svg-icon {
  font-size: 22px;
  vertical-align: -0.25em;
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .vLine {
  border-left: solid 1px transparent;
  height: 22px;
  vertical-align: middle;
  display: inline-block;
  margin-left: 5px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input {
  width: 85% !important
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input .el-input__inner {
  height: 40px;
  line-height: 40px;
  border: 0;
  padding-left: 5px;
  vertical-align: middle;
  font-size: 18px;
  background-color: transparent;
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input .el-input__inner::-webkit-input-placeholder {
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input .el-input__inner:-moz-placeholder {
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input .el-input__inner::-moz-placeholder {
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item .el-form-item__content .el-input .el-input__inner:-ms-input-placeholder {
  color: #04c7d6
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.captchaInput .el-form-item__content .svg-container, body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.captchaInput .el-form-item__content .vLine {
  display: inline-block
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.captchaInput .el-form-item__content .el-input__inner {
  font-size: 18px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.captchaInput .el-form-item__content .captchaImg-wrapper {
  position: relative;
  top: -6px;
  right: 125px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.captchaInput .el-form-item__content .captchaImg {
  width: 85px;
  height: 38px;
  top: -4px;
  right: 10px;
  position: absolute;
  cursor: pointer
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.mobileLoginItem, body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.emailLoginItem {
  margin-bottom: 65px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.mobileLoginItem .countDown, body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.emailLoginItem .countDown {
  position: absolute;
  top: 6px;
  right: 35px;
  font-size: 12px;
  color: #F56C6C
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.mobileLoginItem .countDown b, body.thinvent .loginPage-container .form-wrapper .login-container .el-form-item.emailLoginItem .countDown b {
  width: 20px;
  display: inline-block
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button {
  width: 100%;
  border-radius: 0;
  background-color: #0070a3;
  border-color: #0070a3
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button:hover {
  box-shadow: none
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.captchaBtn {
  width: 90px;
  height: 27px;
  position: absolute;
  top: 7px;
  right: 35px;
  padding: 0;
  vertical-align: middle;
  border-radius: 0
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.accountLoginBtn, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.mobileLoginBtn, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.emailLoginBtn {
  padding: 0;
  height: 53px;
  width: 407px;
  border: 0px;
  background: url("login_button1.png") center center no-repeat
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.accountLoginBtn:hover, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.mobileLoginBtn:hover, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.emailLoginBtn:hover {
  background: url("login_button2.png") center center no-repeat
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.accountLoginBtn span, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.mobileLoginBtn span, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.emailLoginBtn span {
  display: none
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.accountLoginBtn {
  margin-top: 42px
}

body.thinvent .loginPage-container .form-wrapper .login-container .el-button.mobileLoginBtn, body.thinvent .loginPage-container .form-wrapper .login-container .el-button.emailLoginBtn {
  margin-top: 44px
}

body.thinvent .loginPage-container .form-wrapper .copyright {
  margin-right: 6px;
  bottom: -200px;
  position: absolute;
  font-size: 12px;
  color: #fff
}

body.thinvent .loginPage-container .svg-container {
  width: 22px;
  margin-left: 0px !important;
  color: #89a3ff;
  vertical-align: middle;
  display: inline-block
}

body.thinvent .loginPage-container fieldset {
  width: 385px;
  margin: 0 auto;
  padding: 10px 6px 9px 6px;
  border: 0;
  border-top: #11567c solid 1px;
  text-align: center;
  margin-bottom: -48px;
  margin-top: 20px;
  left: 0;
  margin-top: 98px
}

body.thinvent .loginPage-container fieldset legend {
  margin: 0 auto;
  padding: 0 10px;
  text-align: center;
  font-size: 14px;
  color: #7f7f7f
}

body.thinvent .loginPage-container fieldset ul {
  padding-left: 0px;
  margin: 0px
}

body.thinvent .loginPage-container fieldset ul li {
  display: inline;
  padding: 0 6px;
  list-style-type: none;
  font-size: 36px
}

body.thinvent .loginPage-container fieldset ul li a {
  outline: none
}

body.thinvent .loginPage-container fieldset ul li .svg-container {
  width: 48px
}

body.thinvent .loginPage-container fieldset ul li .svg-container .svg-icon {
  color: #FFFFFF
}

body.thinvent .loginPage-container fieldset ul li .svg-container .svg-icon.cur {
  border-radius: 50%;
  box-shadow: 0px 0px 5px 0.2px #08c4ec;
  transform: scale(1);
  transition: box-shadow 0.6s, transform 0.5s;
  font-size: 40px;
  color: #08c4ec
}

body.thinvent .loginPage-container fieldset ul li.dis .svg-icon {
  color: #666666;
  cursor: default
}

body.thinvent .loginPage-container fieldset ul li.dis {
  color: #666
}

body.thinvent .loginPage-container fieldset ul li:not(.dis) :hover {
  color: #08c4ec
}

body.thinvent .loginPage-container fieldset ul li:not(.dis) :hover .svg-icon {
  border-radius: 50%;
  box-shadow: 0px 0px 5px 0.2px #08c4ec;
  transform: scale(1);
  transition: box-shadow 0.6s, transform 0.5s;
  font-size: 40px;
  color: #08c4ec
}

body.thinvent .loginPage-container body {
  padding: 0;
  margin: 0
}

body.thinvent .loginPage-container html {
  height: 100%;
  display: grid
}

body.thinvent .loginPage-container #space {
  top: calc((100% - 1080px) / 2);
  left: calc((100% - 1920px) / 2);
  position: absolute;
  width: 1920px;
  height: 1080px;
  background: url("login_bg.jpg") center center no-repeat #01010d;
  display: grid;
  place-items: center center;
  overflow: hidden;
  --perspective: 100px;
  -webkit-perspective: var(--perspective);
  perspective: var(--perspective);
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d
}

body.thinvent .loginPage-container #space:after {
  display: block;
  content: '';
  grid-area: 1 / 1;
  width: 100%;
  height: 100%;
  mix-blend-mode: overlay
}

body.thinvent .loginPage-container #space time {
  grid-area: 1 / 1;
  width: 100px;
  height: 100px;
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
  will-change: transform;
  background-color: white;
  box-shadow: 0 0 .5rem white, 0 0 1rem white
}

@supports (-moz-transition: all) {
  body.thinvent .loginPage-container #space time {
    box-shadow: 0 0 .5rem white
  }
}

body.thinvent .logo-wrapper img {
  width: 269px;
  height: 42px
}

body.thinvent .navigation-container {
  background: url("login_bg.jpg") center center no-repeat #01010d
}

body.thinvent .navigation-container .navigation-wrapper {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  z-index: 1
}

body.thinvent .navigation-container .navigation-wrapper .logo {
  position: absolute;
  margin-top: -635px;
  width: auto;
  height: auto;
  background: none
}

body.thinvent .navigation-container .navigation-wrapper .logo img {
  vertical-align: middle;
  position: relative;
  left: 0;
  top: 0;
  transform: inherit
}

body.thinvent .navigation-container .navigation-wrapper .logo p {
  vertical-align: middle;
  display: inline-block;
  font-size: 38px;
  font-weight: 500;
  background-image: -webkit-linear-gradient(bottom, #12d4e8, #dbf4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent
}

body.thinvent .navigation-container .navigation-wrapper .jt {
  display: none
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper {
  width: 190px;
  height: 36px;
  position: relative;
  top: -600px;
  right: -420px;
  line-height: 36px;
  font-size: 12px;
  background: url("zh_bg.png") center center no-repeat;
  color: #40e3fb
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper .svg-container {
  padding-right: 7px;
  color: #40e3fb
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper .userinfo {
  float: left;
  padding-left: 10px
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper .userinfo .svg-container {
  margin-top: -30px
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper .userinfo .title {
  width: 82px;
  display: inline-block;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #1ee3f3
}

body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper .logout {
  float: right;
  padding-right: 10px;
  cursor: pointer
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper {
  position: relative;
  margin-top: 50px;
  width: 1194px;
  height: 516px;
  background: url("system_bg.png") center center no-repeat;
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper .system-wrapper-bg {
  width: 100%;
  height: 100%;
  /*border: 1px solid #094a74;*/
  /*background-color: #102343;*/
  /*box-shadow: 0px 0px 35px #04c7d6 inset;*/
  /*opacity: 0.5;*/
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper .system-wrapper-corner {
  /*width: calc(100% + 7px);*/
  /*height: calc(100% + 9px);*/
  /*margin-top: -522px;*/
  /*margin-left: -2px;*/
  /*position: relative;*/
  /*background: url("../kj_t-l.png") top left no-repeat, url("../kj_t-r.png") top right no-repeat, url("../kj_b-l.png") bottom left no-repeat, url("../kj_b-r.png") bottom right no-repeat*/
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper .el-scrollbar {
  margin-top: -494px
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul {
  padding: 0 0 0 10px;
  margin: 0 auto;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul li {
  width: 326px;
  height: 111px;
  margin: 21px 31px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url("li_bg.png") center center no-repeat;
  cursor: pointer
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul li:hover {
  text-shadow: 0 0 0.8em #27a3d6;
  background: url("li_bg2.png") center center no-repeat
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul li:hover span{
  color: #ffa633;
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul li img {
  width: 40px;
  height: 40px;
  margin-right: 18px
}

body.thinvent .navigation-container .navigation-wrapper .system-wrapper ul li span {
  width: 200px;
  font-size: 18px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
  color: #1ee3f3
}

body.thinvent .navigation-container .navigation-wrapper .copyright {
  position: relative;
  margin-top: 20px;
  font-size: 12px;
  color: #fff
}

body.thinvent .navigation-container body {
  padding: 0;
  margin: 0
}

body.thinvent .navigation-container html {
  height: 100%;
  display: grid
}

body.thinvent .navigation-container #space {
  top: calc((100% - 1080px) / 2);
  left: calc((100% - 1920px) / 2);
  position: absolute;
  width: 1920px;
  height: 1080px;
  background: url("login_bg.jpg") center center no-repeat #01010d;
  display: grid;
  place-items: center center;
  overflow: hidden;
  --perspective: 100px;
  -webkit-perspective: var(--perspective);
  perspective: var(--perspective);
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d
}

body.thinvent .navigation-container #space:after {
  display: block;
  content: '';
  grid-area: 1 / 1;
  width: 100%;
  height: 100%;
  mix-blend-mode: overlay
}

body.thinvent .navigation-container #space time {
  grid-area: 1 / 1;
  width: 100px;
  height: 100px;
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
  will-change: transform;
  background-color: white;
  box-shadow: 0 0 .5rem white, 0 0 1rem white
}

@supports (-moz-transition: all) {
  body.thinvent .navigation-container #space time {
    box-shadow: 0 0 .5rem white
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  body.thinvent .loginPage-container .form-wrapper .logo {
    position: absolute;
    margin-top: -575px;
    margin-left: -8px
  }

  body.thinvent .loginPage-container .form-wrapper .logo.noFieldset {
    margin-top: -363px
  }

  body.thinvent .loginPage-container .form-wrapper .login-container {
    position: absolute;
    margin-top: -95px
  }

  body.thinvent .loginPage-container .form-wrapper fieldset {
    position: absolute;
    margin-top: 200px;
    text-align: center;
    margin-left: 46px
  }

  body.thinvent .loginPage-container .form-wrapper .copyright {
    position: relative;
    margin-top: calc(100vh / 2 - 180px)
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) and (min-width: 1920px) {
  body.thinvent .loginPage-container .form-wrapper .copyright {
    position: absolute;
    bottom: -180px;
    margin-top: 0px
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  body.thinvent .navigation-container .navigation-wrapper .logo {
    margin-left: -11px;
    margin-top: -650px
  }

  body.thinvent .navigation-container .navigation-wrapper .copyright {
    position: absolute;
    margin-top: calc(100vh - 190px)
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) and (min-width: 1920px) {
  body.thinvent .navigation-container .navigation-wrapper .system-wrapper {
    margin-top: 53px
  }

  body.thinvent .navigation-container .navigation-wrapper .copyright {
    position: relative;
    margin-top: 200px
  }

  body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper {
    margin-top: -220px
  }
}

@-moz-document url-prefix() {
  body.thinvent .loginPage-container .form-wrapper .logo {
    position: absolute;
    margin-top: -676px;
    margin-left: -13px
  }
  body.thinvent .loginPage-container .form-wrapper .logo.noFieldset {
    margin-top: -650px
  }
  body.thinvent .loginPage-container .form-wrapper .login-container {
    position: absolute;
    margin-top: -195px
  }
  body.thinvent .loginPage-container .form-wrapper fieldset {
    position: absolute;
    margin-top: 360px;
    text-align: center;
    margin-left: 46px
  }
  body.thinvent .navigation-container .navigation-wrapper .logo {
    margin-left: -20px;
    margin-top: -635px
  }
  body.thinvent .navigation-container .navigation-wrapper .copyright {
    position: absolute;
    margin-top: calc(100vh - 50px)
  }
  @media screen and (min-width: 1920px) {
    body.thinvent .navigation-container .navigation-wrapper .system-wrapper {
      margin-top: 53px
    }

    body.thinvent .navigation-container .navigation-wrapper .copyright {
      position: relative;
      margin-top: 200px
    }

    body.thinvent .navigation-container .navigation-wrapper .userinfo-wrapper {
      margin-top: -220px
    }
  }
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  body.thinvent .navigation-container .navigation-wrapper .logo {
    margin-top: -660px
  }
}
