hljs.registerLanguage("step21",function(e){var i="[A-Z_][A-Z0-9_.]*",r={keyword:"HEADER ENDSEC DATA"},t={cN:"meta",b:"ISO-10303-21;",r:10},n={cN:"meta",b:"END-ISO-10303-21;",r:10};return{aliases:["p21","step","stp"],cI:!0,l:i,k:r,c:[t,n,e.CLCM,e.CBCM,e.C("/\\*\\*!","\\*/"),e.CNM,e.inherit(e.ASM,{i:null}),e.inherit(e.QSM,{i:null}),{cN:"string",b:"'",e:"'"},{cN:"symbol",v:[{b:"#",e:"\\d+",i:"\\W"}]}]}});