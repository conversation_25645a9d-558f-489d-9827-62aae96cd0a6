<template>
  <!--表格Footer组件，包括工具栏及翻页组件-->
  <div class="table-footer__wrapper">
    <!--列表工具栏-->
    <table-tool-bar v-if="showToolBar" :tableRef="tableRef" :excelName="excelName" ref="tableToolBar"
                    :showAllDownload="showAllDownload" @exportAllExcelFile="exportAllExcelFile"
                    :url="url" :filters="filters" :resourceCode="resourceCode"></table-tool-bar>
    <transition name="table-footer">
      <div :class="['table-footer__notify', notifyType]" v-show="isShow">{{notifiation}}</div>
    </transition>
    <!--列表翻页-->
    <el-pagination
      v-if="showPage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    ></el-pagination>
  </div>
</template>

<script>
import TableToolBar from "@/core/components/TablePlus/TableToolBar";
export default {
  name: "TableFooter",
  components: { TableToolBar },
  props: {
    tableRef: Object,
    currentPage: {type: Number, default: 1},
    pageSizes: { type: Array, default: () => [10, 20, 50, 100] },
    pageSize: {type: Number, default: 20},
    url: String, // 后台地址(后台导出数据)
    filters: Object, // 过滤条件(后台导出数据)
    total: Number,
    excelName: String, // 导出excel文件名称,
    resourceCode: String,
    showToolBar: { type: Boolean, default: true },
    showPage: { type: Boolean, default: true },
    showAllDownload: {type: Boolean, default: false}
  },
  data() {
    return {
      isShow: false,
      notifiation: "",
      notifyType: "info" // 取值范围：info、warning、error
    };
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("sizeChange", val);
    },
    handleCurrentChange(val) {
      // console.log(val);
      this.$emit("currentChange", val);
    },
    // 推送消息
    notify() {
      let arr = [];
      Array.prototype.push.apply(arr, arguments);
      if(arr.length === 0) {
        return;
      }
      this.isShow = !this.isShow;
      this.notifyType = arr.length === 2 ? arr[0] : "info";
      this.notifiation = arr.length === 2 ? arr[1] : arr[0];
    },
    // 导出全部数据
    exportAllExcelFile() {
      this.$emit("exportAllExcelFile");
    },
    updateColumns() {
      this.$refs.tableToolBar.updateColumns();
    }
  }
};
</script>

<style lang="scss" scoped>
.el-pagination {
  text-align: right;
}
</style>
