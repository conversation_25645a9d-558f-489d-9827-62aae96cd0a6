<template>
  <div>
    <TDrawer :visible.sync="visible" :size="drawerSize ? drawerSize : 'calc(100vw - 200px)'" :center="true" :autoHeight="true" @open="handleOpen" @close="handleClose" title="我的收藏">
      <el-tabs v-model="status.activeTab" @tab-click="changeActiveTab">
        <el-tab-pane label="数据资源" name="2"></el-tab-pane>
        <el-tab-pane label="能力" name="3"></el-tab-pane>
<!--        <el-tab-pane label="数据目录" name="1"></el-tab-pane>-->
      </el-tabs>

      <TablePlus ref="collectTableRef" :data="status.data" :loading="status.loading" height="550" border fit stripe highlight-current-row>
        <el-table-column align="center" width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            {{(status.filters.currentPage-1) * status.filters.pageSize+scope.$index+1}}
          </template>
        </el-table-column>
        <el-table-column prop="collectName" label="名称" header-align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="jumpText" @click="handleCheckDetail(scope.row)">{{ scope.row.collectName }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="status.activeTab === '1' || status.activeTab === '2'" header-align="center"  prop="provider" label="所属单位" width="200" show-overflow-tooltip />
        <el-table-column v-else prop="registerUnitName" header-align="center"  label="所属单位" width="200" show-overflow-tooltip />
        <!--        <el-table-column prop="provider" label="所属单位" align="center" show-overflow-tooltip></el-table-column>-->
        <el-table-column prop="collectTime" label="收藏时间" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="{ row }">
            <perm-button-group :config="getButtons(row)" />
          </template>
        </el-table-column>
      </TablePlus>
      <div class="pagination-wrap">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="status.filters.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="status.filters.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="status.total"></el-pagination>
      </div>

      <template #footer>
        <el-button class="close-btn" size="mini" @click="handleClose">关闭</el-button>
      </template>
    </TDrawer>

    <!-- 数据订阅 -->
    <SubscribeDialog :dialogVisible.sync="status.dialogVisibleApply" :type="status.type" :data="status.dialogData" :title="status.title"></SubscribeDialog>
  </div>
</template>

<script>
import { computed, defineComponent, reactive, getCurrentInstance, ref } from 'vue'

import DialogPlus from '@/core/components/DialogPlus'
import TDrawer from '@/biz/components/t-drawer'
import PermButton from '@/core/components/PermButton'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import SelectPlus from '@/core/components/SelectPlus'
import TablePlus from '@/core/components/TablePlus'
import SubscribeDialog from '../components/SubscribeDialog'

export default defineComponent({
  name: 'SelectNumDialog',
  components: {
    DialogPlus,
    PermButton,
    PermButtonGroup,
    SelectPlus,
    TablePlus,
    SubscribeDialog,
    TDrawer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    drawerSize: {
      type: String
    }
  },
  setup(props, { emit }) {
    const { proxy } = getCurrentInstance()
    const collectTableRef = ref()
    const visible = computed({
      get: () => props.visible,
      set: (val) => {
        emit('update:visible', val)
      }
    })

    const status = reactive({
      allSelectTableRow: [],
      dialogData: undefined,
      dialogVisibleApply: false,
      title: '',
      type: '',
      activeTab: '2',
      loading: false,
      total: 0,
      data: [],
      filters: {
        currentPage: 1,
        pageSize: 20,
        collectType: '1'
      }
    })
    const handleCheckDetail = (data) => {
      if (status.activeTab === '1') {
        const href = window.location.href
        let url = new URL(href)
        if (url.href === window.top.location.href) {
          let routeUrl = proxy.$router.resolve({
            path: '/detailCatalog/index',
            query: {
              cataId: data.cataId
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataDir/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              cataId: data.cataId
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (status.activeTab === '2') {
        const href = window.location.href
        let url = new URL(href)
        if (url.href === window.top.location.href) {
          let routeUrl = proxy.$router.resolve({
            path: '/detailResource/index',
            query: {
              cataId: data.cataId,
              type: '1',
              resourceId: data.resourceId,
              resType: data.resourceTypeCode
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataRes/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              cataId: data.cataId,
              type: '1',
              resourceId: data.resourceId,
              resType: data.resourceTypeCode
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else {
        const href = window.location.href
        let url = new URL(href)
        if (url.href === window.top.location.href) {
          let routeUrl = proxy.$router.resolve({
            path: '/detail/index',
            query: {
              powerId: data.powerId
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/power/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              powerId: data.powerId
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      }
    }
    const handleOpen = () => {
      getList()
    }
    const handleClose = () => {
      visible.value = false
      let params = {
        IGDPType: 'IGDP_CLOSE_DRAWER'
      }
      window.parent.postMessage(JSON.stringify(params), '*')
    }

    const fetchDataList = (apiFunc, collectType) => {
      status.loading = true
      status.filters.collectType = collectType
      const param = status.filters
      apiFunc(param)
        .then((res) => {
          status.data = res.data.records
          status.total = res.data.total
          status.loading = false
        })
        .catch((e) => {
          status.loading = false
          proxy.$message({ message: e.message, type: 'error', duration: 2000 })
        })
    }

    const fetchCloseCancel = (params, id, apiFunc) => {
      let param = {}
      if (params === 'cataId') {
        param = { cataId: id }
      } else if (params === 'resourceId') {
        param = { resourceId: id }
      } else {
        param = { collectId: id }
      }

      apiFunc(param)
        .then((res) => {
          proxy.$message({
            message: '取消收藏成功',
            type: 'success',
            duration: 2000
          })
          getList()
        })
        .catch((e) => {
          status.loading = false
          proxy.$message({ message: e.message, type: 'error', duration: 2000 })
        })
    }

    const getList = () => {
      switch (status.activeTab) {
        case '1':
        case '2':
          // 数据目录 || 数据资源 传 2 3
          fetchDataList(
            proxy.$api.bizApi.collect.resourceList,
            status.activeTab === '1' ? '2' : '3'
          )
          break
        case '3':
          // 能力 传 3
          fetchDataList(proxy.$api.bizApi.collect.powerList, status.activeTab)
          break
        default:
          status.data = []
          break
      }
    }

    const getButtons = (row) => {
      let buttons = []
      if (status.activeTab !== '1') {
        buttons.push({
          label: '订阅',
          icon: 'download',
          clickFn: batchApply
        });
      }
      buttons.push({
        label: '取消收藏',
        icon: 'delete',
        clickFn: handelCancelCollect
      });
      // let buttons = [
      //   // {
      //   //   label: '订阅',
      //   //   icon: 'download',
      //   //   clickFn: batchApply
      //   // },
      //   {
      //     label: '取消收藏',
      //     icon: 'delete',
      //     clickFn: handelCancelCollect
      //   }
      // ]
      return {
        row: row,
        buttons,
        showNums: 3
      }
    }

    const batchApply = (row) => {
      if (row) {
        if (status.activeTab === '2') {
          status.type = 'singeApply'
          status.title = '数据资源订阅'
          status.dialogData = {}
          status.dialogVisibleApply = true
          status.dialogData.resourceId = row.resourceId
        } else if (status.activeTab === '3') {
          status.type = 'power'
          status.title = '能力订阅'
          status.dialogData = row
          status.dialogVisibleApply = true
          status.dialogData.powerId = row.powerId
        }
      }
    }

    const handelCancelCollect = (row) => {
      proxy
        .$confirm(`您确认要取消收藏吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(() => {
          switch (status.activeTab) {
            case '1':
              // 数据目录
              fetchCloseCancel(
                'cataId',
                row.cataId,
                proxy.$api.bizApi.collect.cancelCatalogCollecting
              )
              break
            case '2':
              // 数据资源
              fetchCloseCancel(
                'resourceId',
                row.resourceId,
                proxy.$api.bizApi.collect.cancelResourceCollecting
              )
              break
            case '3':
              // 能力
              fetchCloseCancel(
                'collectId',
                row.collectId,
                proxy.$api.bizApi.collect.cancelPowerToFavorites
              )
              break
            default:
              break
          }
        })
    }

    const handleSizeChange = (val) => {
      status.filters.pageSize = val
      getList()
    }
    const handleCurrentChange = (val) => {
      status.filters.currentPage = val
      getList()
    }

    const changeActiveTab = () => {
      status.filters.currentPage = 1
      getList()
    }

    return {
      visible,
      status,
      handleOpen,
      handleClose,
      getButtons,
      collectTableRef,
      handleSizeChange,
      handleCurrentChange,
      changeActiveTab,
      handleCheckDetail
    }
  }
})
</script>

<style scoped lang="scss">
/deep/ .el-dialog__header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-size: 20px;
  font-weight: bold;
}

.pagination-wrap {
  height: 96px;
  padding: 33px 20px;
  text-align: right;
}

.jumpText {
  color: #606266;
}

.jumpText:hover {
  color: #0e88eb;
  cursor: pointer;
  border-bottom: 1px solid #0e88eb;
}
/deep/.el-table th.el-table__cell {
  background-color: #4EACFE;
  padding: 12px 0;
  .cell {
    background-color: #4EACFE;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color:#f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
