<template>
  <el-container class="page-container">
    <el-header v-if="pageMode==='full'"></el-header>
    <el-container>
      <el-header>
        <div class="toolbar-wrapper">
          <perm-button label="新增" type="text" :perms="systemCode + 'x99005002001'" icon="add" @click="handleCreate"/>
<!--          <perm-button label="编辑" type="text" :perms="systemCode + 'x99005002002'" icon="edit" @click="handleUpdate"/>-->
<!--          <perm-button label="删除" type="text" :perms="systemCode + 'x99005002003'" icon="delete" @click="handleDelete"/>-->
<!--          <perm-button label="查看" type="text" :perms="systemCode + 'x99005002004'" icon="see" @click="handleView"/>-->
<!--          <perm-button label="撤回" type="text" :perms="systemCode + 'x99005002005'" icon="goback" @click="handleBack"/>-->
<!--          <perm-button label="发送日志" type="text" :perms="systemCode + 'x99005002006'" icon="uims-icon-query" @click="handleMsgLog"/>-->
        </div>
        <!--列表查询区-->
        <el-form :inline="true" :model="filters" :size="size" @submit.native.prevent>
          <el-form-item label="消息名称">
            <el-input v-model="filters.subject" maxlength="100" placeholder="请输入消息名称" clearable @keyup.enter.native="handleFilter"/>
          </el-form-item>
          <el-form-item label="发件时间">
            <el-date-picker v-model="filters.sendTimeInterVal" type="datetimerange" :picker-options="pickerOptions"
                            :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" align="right">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发件状态">
            <select-plus dictType="MSG_SEND_STATUS" v-model="filters.status" clearable
                         style="width:150px;"></select-plus>
          </el-form-item>
          <el-form-item label="是否需要回执">
            <select-plus dictType="IS_FLAG" v-model="filters.needReceipt" clearable style="width:150px;"></select-plus>
          </el-form-item>
          <perm-button label="查询" type="primary" icon="uims-icon-query" @click="handleFilter"/>
          <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
        </el-form>
      </el-header>
      <!--列表表格区-->
      <el-main>
        <table-plus
          @header-dragend="handleHeaderDrag"
          id="msgoutbox"
          :data="list"
          ref="multipleTable"
          border
          fit
          default-expand-all
          v-loading="listLoading"
          tooltip-effect="light"
          stripe
          highlight-current-row
          @row-click="clickRow"
          @selection-change="onSelected">
          <el-table-column type="selection" width="60" align="center"> </el-table-column>
          <el-table-column prop="receiverNames" label="收件人" width="200" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="subject" label="消息名称" width="280" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="sendTime" label="发件时间" width="180" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="status" label="发件状态" width="100" align="center" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="needReceipt" label="是否需要回执" width="120" align="center"> </el-table-column>
          <el-table-column width="220" label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <perm-button-group :config="getButtons(row)" />
            </template>
          </el-table-column>
        </table-plus>
      </el-main>
      <el-footer>
        <table-footer ref="tableFooter"
                      :showToolBar="true"
                      excelName="发件箱"
                      :showPage="true"
                      :tableRef="this.$refs.multipleTable"
                      @sizeChange="handleSizeChange"
                      @currentChange="handleCurrentChange"
                      :currentPage="filters.currentPage"
                      :pageSizes="[10, 20, 50, 100]"
                      :pageSize="filters.pageSize"
                      url="/msgOutBox/list"
                      :filters="filters"
                      :resourceCode="resourceCode"
                      :total="total">
        </table-footer>
      </el-footer>
    </el-container>
    <msg-out-box-main-dialog
      @closeDialog="closeDialog"
      @getList="getList"
      :msgOutBoxArray="selectTableRow"
      :dialogModes="dialogModes"
      :dialogFormVisible="dialogFormVisible">
    </msg-out-box-main-dialog>
    <subscript-log
      @closeDialog="closeLogDialog"
      :moId="moIdSelect"
      :dialogFormVisible="dialogLogVisible"
    >
    </subscript-log>
  </el-container>
</template>

<script>
  import PermButton from '@/core/components/PermButton'
  import TablePlus from "@/core/components/TablePlus";
  import SelectPlus from "@/core/components/SelectPlus";
  import MsgOutBoxMainDialog from "./Dialog/MsgOutBoxMainDialog";
  import TableFooter from "@/core/components/TablePlus/TableFooter";
  import {resourceCode} from "@/biz/http/settings";
  import SubscriptLog from "../msgOutBox/Dialog/SubscriptLog";
  import PermButtonGroup from '@/core/components/PermButtonGroup';

  export default {
    components: {MsgOutBoxMainDialog, SelectPlus, TablePlus, PermButton, TableFooter, SubscriptLog, PermButtonGroup},
    data() {
      return {
        queryBt: true,
        systemCode: config.systemCode,
        resourceCode: resourceCode.msgOutBox,
        // table参数
        size: 'mini',
        list: [],
        listLoading: true,
        total: 0,
        currentRow: undefined,
        selectTableRow: [],
        // table查询参数
        filters: {
          currentPage: 1,
          pageSize: 20,
          msgType: '1',
          subject: undefined,
          status: undefined,
          needReceipt: undefined,
          sendTimeInterVal: undefined,
          sendTimeStart: undefined,
          sendTimeEnd: undefined
        },
        // 表单的参数设置
        dialogFormVisible: false,
        dialogLogVisible: false,
        moIdSelect: '',
        dialogModes: '',
        pickerOptions: {
          shortcuts: [{
            text: '最近一小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近十二小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 12);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        }
      }
    },
    computed: {
      pageMode() {
        return config.page_mode;
      }
    },
    // 执行方法
    methods: {
      getButtons(row) {
        let buts = [
          {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99005002002'},
          {label: "删除", icon: "delete", clickFn: this.handleDelete, perms: this.systemCode + 'x99005002003'},
          {label: "查看", icon: "see", clickFn: this.handleView, perms: this.systemCode + 'x99005002004'},
          {label: "撤回", icon: "goback", clickFn: this.handleBack, perms: this.systemCode + 'x99005002005'},
          {label: "发送日志", icon: "uims-icon-query", clickFn: this.handleMsgLog, perms: this.systemCode + 'x99005002006'}
        ];
        return {
          row: row,
          buttons: buts,
          showNums: 2
        }
      },
      closeLogDialog(val) {
        this.dialogLogVisible = val
      },
      // 监听表头拖拽事件
      handleHeaderDrag(newWidth, oldWidth, column, event) {
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      },
      closeDialog(val) {
        this.dialogFormVisible = val
      },
      timeFormat(val) {
        if (val != null) {
          var date = new Date(val);
          return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
        }
      },
      handleFilter(resetFlag) {
         if(resetFlag === true) {
          this.filters.msgType = '1'
          this.filters.subject = undefined
          this.filters.status = undefined
          this.filters.needReceipt = undefined
          this.filters.sendTimeInterVal = undefined
          this.filters.sendTimeStart = undefined
          this.filters.sendTimeEnd = undefined
        }
        if(!this.queryBt) {
          return;
        }
        this.filters.currentPage = 1
        if (this.filters.sendTimeInterVal && this.filters.sendTimeInterVal.length === 2) {
          this.filters.sendTimeStart = this.timeFormat(this.filters.sendTimeInterVal[0])
          this.filters.sendTimeEnd = this.timeFormat(this.filters.sendTimeInterVal[1])
        } else {
          this.filters.sendTimeStart = undefined
          this.filters.sendTimeEnd = undefined
        }
        const filter = Object.assign({}, this.filters)
        delete filter.sendTimeInterVal
        this.queryBt = false
        this.listLoading = true
        this.$api.msgOutBox.getList(filter, resourceCode.msgOutBox).then((res) => {
          this.queryBt = true
          this.listLoading = false
          this.list = res.data ? res.data.records : []
          this.total = res.data ? res.data.total : 0
        }).catch(res => {
          this.queryBt = true
        })
      },
      getList() {
        this.listLoading = true
        this.$api.msgOutBox.getList(this.filters, resourceCode.msgOutBox).then((res) => {
          this.listLoading = false
          this.list = res.data ? res.data.records : []
          this.total = res.data ? res.data.total : 0
        })
      },

      clickRow(row, event, column) {
        if (this.currentRow === row) {
          this.currentRow = undefined
          this.$refs.multipleTable.clearSelection();
        } else {
          this.currentRow = row
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(row)
        }
      },
      onSelected(row, event, column) {
        this.selectTableRow = Object.assign([], row);
      },
      handleSizeChange(val) {
        this.filters.pageSize = val
        this.filters.currentPage = 1;
        this.getList()
      },
      handleCurrentChange(val) {
        this.filters.currentPage = val
        this.getList()
      },
      handleCreate() {
        this.dialogModes = 'create'
        this.dialogFormVisible = true
      },
      handleUpdate(row) {
        this.selectTableRow = [row]
        if (this.selectTableRow[0].statusCode !== '0') {
          this.$notify({
            title: '提示',
            message: '只能对未发送的消息进行更改',
            type: 'info',
            duration: 2000
          })
          return
        }
        this.dialogModes = 'update'
        this.dialogFormVisible = true
      },
      handleDelete(row) {
        let moIds = row.moId
        this.$confirm('您确认要删除当前选中的消息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.msgOutBox.del({moIds: moIds}, resourceCode.msgOutBox).then((res) => {
            this.getList()
            this.$notify({
              title: '操作成功',
              message: '删除消息成功',
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {
          this.$notify({
            message: '已取消删除',
            type: 'info',
            duration: 2000
          })
        })
      },
      handleBack(row) {
        this.selectTableRow = [row]
        let moIds = ''
        for (let i = 0; i < this.selectTableRow.length; i++) {
          if (this.selectTableRow[i].statusCode !== '2') { // 通过后台 JsonDictionary 中的copyName来获取状态的数值
            this.$notify({
              title: '提示',
              message: '只能对已发送的消息进行撤回',
              type: 'info',
              duration: 2000
            });
            return
          }
          if (i === this.selectTableRow.length - 1) {
            moIds += this.selectTableRow[i].moId
          } else {
            moIds += this.selectTableRow[i].moId + ','
          }
        }
        this.$confirm('您确认要撤回当前选中的消息吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.msgOutBox.back({moIds: moIds}, resourceCode.msgOutBox).then((res) => {
            this.getList()
            this.$notify({
              title: '操作成功',
              message: '撤回成功',
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {
          this.$notify({
            message: '已取消撤回',
            type: 'info',
            duration: 2000
          })
        })
      },
      handleMsgLog(row) {
        this.selectTableRow = [row]
        this.moIdSelect = this.selectTableRow[0].moId
        this.dialogLogVisible = true
      },
      handleView(row) {
        this.selectTableRow = [row]
        this.dialogModes = 'view'
        this.dialogFormVisible = true
      }
    },
    created() {
      this.getList();
    }
  }
</script>

<style scoped lang="scss">

</style>
