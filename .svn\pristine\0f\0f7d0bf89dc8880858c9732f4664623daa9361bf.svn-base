.page-container {
  //width: 1600px;
  margin: 0 100px 0 20px;
  height: 100%;
  min-height: 100vh;

  >>> .el-main {
    padding: 0 0 0 16px;
  }

  >>> .el-aside {
    margin-left: 0;
  }

  .toolbarTree-wrapper {
    .searchPart {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 2px;
      height: 55px;
      width: 100%;

      .el-input {
        width: 243px;
        height: 38px;
        margin: 9px 12px;

        >>> .el-input__inner {
          border-radius: 2px;
          border: 1px solid #e2e2e2;
        }
      }
    }

    .searchTotal {
      margin: 11px 0;
      width: 100%;
      height: 44px;
      background-image: url("~@/assets/img/pic-total.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .totalName {
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 18px;
        color: #fefeff;
        line-height: 20px;
        margin-left: 36px;
      }

      .totalNum {
        font-family: D-DIN;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        line-height: 20px;
        margin-right: 13px;
      }
    }

    .searchTotalWith {
      width: 100%;
      margin: 10px 0 15px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .totalName {
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 18px;
        color: #666666;
        line-height: 20px;

        img {
          margin-right: 6px;
        }
      }

      .totalNum {
        font-family: D-DIN;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        line-height: 20px;
        margin-right: 13px;

        span {
          color: $colors;
        }
      }
    }
  }

  .searchCont {
    background-color: #fff;
    border-radius: 2px;
    padding: 18px 20px 12px 27px;
    // width: calc(100% - 127px);
    color: #333;
    font-size: 16px;

    .searchList {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;

      h5 {
        width: 75px;
        height: 30px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
        line-height: 30px;
        margin-bottom: 12px;
        text-align: right;
      }

      .searchInp {
        margin-left: 29px;
        margin-bottom: 12px;

        .searchItem {
          font-size: 16px;
          margin-right: 30px;
          line-height: 46px;

          span {
            font-weight: bold;
          }
        }

        >>> .el-form-item {
          margin-bottom: 0;

          .el-form-item__label {
            font-size: 16px;
          }
        }

        >>> .el-checkbox {
          margin-right: 40px;

          .el-checkbox__label {
            font-size: 16px;
          }
        }

        >>> .el-checkbox-group {
          .el-checkbox {
            margin-right: 40px;
          }

          .is-checked {
            // .el-checkbox__inner{
            //   background-color: #fff;
            //   border-color: #0e88eb;
            //   &::after{
            //     border: 1px solid #0e88eb
            //   }
            // }
            .el-checkbox__label {
              color: #333;
            }
          }

          .el-checkbox__label {
            font-size: 16px;
          }
        }
      }
    }
  }

  .searchCont + .dataCont {
    margin-top: 10px;
  }
  .dataCont {
    background-color: #fff;
    border-radius: 2px;
    padding: 20px 0px 32px 0px;
    width: 100%;
    color: #333;
    font-size: 16px;

    .topSort {
      display: flex;
      align-items: center;
      margin-bottom: 18px;
      padding-left: 23px;

      >>> .el-checkbox__label {
        font-size: 16px;
      }

      .sort {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 60px;

        div {
          display: flex;
          align-items: center;
          cursor: pointer;
          height: 30px;
          margin-right: 50px;

          .iconLabel {
            &.active {
              color: $colors;
            }
          }

          .iconList {
            margin-left: 2px;
            color: #666;
            display: flex;
            flex-direction: column;
            position: relative;

            &.active {
              color: $colors;
            }
          }

          i {
            font-size: 14px;
            color: #a3a3a4;

            &.active {
              color: $colors;
            }

            &.el-icon-caret-top:before {
              top: -9px;
              position: absolute;
            }

            &.el-icon-caret-bottom:before {
              top: -2px;
              position: absolute;
            }
          }
        }
      }
    }
  }

  .borderSplit {
    height: 1px;
    width: 100%;
    background: #eaeaea;
  }

  .listCont {
    padding: 0 32px 0 22px;

    .listItem {
      width: 100%;
      border-bottom: 1px solid #eaeaea;
      padding: 22px 0 22px 0;
      cursor: pointer;
      .line1 {
        display: flex;
        justify-content: space-between;
        // align-items: center;

        .title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
        }

        .itemStatus {
          span {
            background: #f6f9ff;
            border-radius: 4px;
            border: 1px solid #d9e1f1;
            font-size: 14px;
            color: #7d8496;
            line-height: 38px;
            padding: 5px 8px;
            margin-left: 8px;
          }
        }
      }

      .line2 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .itemMes {
          .message {
            color: #7d8496;
            font-size: 16px;
            margin-right: 35px;
            margin-bottom: 6px;
            line-height: 30px;

            span {
              color: #000;
            }
          }
        }

        .itemSourceType {
          display: flex;

          .itemSourceItem {
            display: flex;
            align-items: center;
            flex-direction: column;
            margin-left: 35px;

            .iconSchema {
              width: 21px;
              height: 19px;
              background-image: url("~@/assets/img/icon-api.png");
              background-repeat: no-repeat;
              background-size: 100%;
            }

            .iconTable {
              width: 20px;
              height: 20px;
              background: url("~@/assets/img/icon-sdk.png") 100% 100% no-repeat;
            }

            span {
              font-size: 14px;
              color: $colors;
              margin-top: 4px;
              width: 54px;
              text-align: center;
            }
          }
        }
      }

      .line3 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .itemNum {
          .num {
            font-size: 16px;
            color: $colors;
            margin-right: 18px;

            span {
              color: #7d8496;
            }
          }
        }

        .itemHandleBtn {
          display: flex;
          align-items: center;

          .btn {
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid $colors;
            text-align: center;
            padding: 9px 10px;
            color: $colors;
            margin-left: 13px;
            cursor: pointer;
            display: flex;
            align-content: center;
            justify-content: center;

            img {
              display: inline-block;
              width: 16px;
              height: 16px;
              margin-top: 3px;
              margin-right: 8px;
            }

            &.isOrderFalg {
              background-color: #dedede;
              cursor: not-allowed;
              border: 1px solid #aaaaaa;
              color: #666666;
            }
          }
        }
      }
    }
  }
}

.filterBtn {
  width: 110px;
  height: 27px;
  background: rgba(0, 0, 0, 0);
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  line-height: 27px;
  // margin-top: 5px;
  cursor: pointer;
}
.filterBtn1 {
  @extend .filterBtn;
  width: 50px;
  // margin-top: 0;
  margin-left: 10px;
}

>>> .el-input__inner {
  height: 35px;
  line-height: 35px;
}

>>> .el-tree {
  background-color: transparent;

  .el-tree-node__content {
    border-left: 3px solid transparent;
    padding-left: 30px !important;
  }

  // padding-left: 16px;
  > .el-tree-node {
    background-color: #fff;
    margin-bottom: 10px;

    > .el-tree-node__content {
      padding-left: 16px !important;
      height: 48px;
    }

    > .el-tree-node__children {
      .el-tree-node__children > .el-tree-node > .el-tree-node__content {
        padding-left: 50px !important;
      }
    }
  }

  .el-tree-node__children {
    > .el-tree-node {
      background-color: #fff;

      > .el-tree-node__content {
        height: 41px;
      }
    }
  }

  .el-tree-node__expand-icon {
    position: absolute;
    right: 3px;
    transform: rotate(0deg);
  }

  .el-tree-node__expand-icon.expanded {
    transform: rotate(90deg);
  }

  .el-tree-node__label {
    padding-left: 15px;
  }

  // 有子节点 且已展开
  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    background: url("~@/assets/img/arrow.png") no-repeat;
    content: "";
    display: block;
    width: 7px;
    height: 12px;
  }

  // 有子节点 且未展开
  .el-icon-caret-right:before {
    background: url("~@/assets/img/arrow.png") no-repeat;
    content: "";
    display: block;
    width: 7px;
    height: 12px;
  }

  // 没有子节点
  .el-tree-node__expand-icon.is-leaf::before {
    background: #fff;
    content: "";
    display: block;
    width: 0px;
    height: 0px;
    font-size: 0px;
    background-size: 0px;
  }

  .custom-tree-node {
    width: 100%;

    .dataValue {
      display: flex;
      justify-content: space-between;

      .dataName {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
      }

      .dataNum {
        font-weight: 400;
        font-size: 18px;
        // color: #333333;
        color: $colors;
        margin-right: 30px;
      }
    }
  }
}

>>> .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #f9f9f9 !important;
  border-left: 3px solid $colors;

  .dataName {
    color: $colors;
  }

  .is-leaf ~ .custom-tree-node {
    .dataValue {
      .dataName {
        color: $colors;
      }

      .dataNum {
        color: $colors;
      }
    }
  }
}

>>> .el-link {
  .el-link--inner {
    color: rgba(39, 117, 255, 1);
    font-size: 18px;
  }
}

.custom-tree-node {
  box-sizing: border-box;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /*font-size: 14px;*/
  overflow: hidden;
  font-size: 14px;

  .custom-tree-name {
    font-size: 16px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    /*padding-right: 10px;*/
  }

  .custom-tree-btn {
    flex-shrink: 0;
    margin-right: 30px;
  }
}

.right-info {
  max-width: 246px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  .type-info {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    width: 48px;
    height: 48px;
    background: #f6f9ff;
    border-radius: 4px;
    margin-right: 10px;
    color: #7d8496;
    .svg-icon {
      width: 17px;
      height: 17px;
    }
    &.active {
      color: #3572ff;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
