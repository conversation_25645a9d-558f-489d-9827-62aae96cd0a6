/*================    自定义   =====================*/

@-webkit-keyframes lr {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0)
  }
  100% {
    -webkit-transform: rotateY(180deg);
    transform: rotateY(180deg)
  }
}

@keyframes lr {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0)
  }
  100% {
    -webkit-transform: rotateY(360deg);
    transform: rotateY(360deg)
  }
}
.faa-parent.animated-hover:hover > .faa-lr, .faa-lr.animated, .faa-lr.animated-hover:hover {
  -webkit-animation: lr 1.5s linear infinite;
  animation: lr 1.5s linear infinite;
  animation-iteration-count:1
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  > li {
    -webkit-animation: lr .7s linear infinite!important;
    animation: lr .7s linear infinite!important;
    animation-iteration-count:1!important;
    color: #5daf34!important;
  }
}

.faa-parent.animated-hover:hover > .faa-lr.faa-fast, .faa-lr.animated-hover.faa-fast:hover, .faa-lr.animated.faa-fast {
  -webkit-animation: lr .7s linear infinite;
  animation: lr .7s linear infinite;
  animation-iteration-count:1
}

.faa-parent.animated-hover:hover > .faa-lr.faa-slow, .faa-lr.animated-hover.faa-slow:hover, .faa-lr.animated.faa-slow {
  -webkit-animation: lr 2.2s linear infinite;
  animation: lr 2.2s linear infinite;
  animation-iteration-count:1
}
