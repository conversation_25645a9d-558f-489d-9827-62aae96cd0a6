<template>
  <div>
    <DialogPlus
      custom-class="icsp-dialog"
      :title="title"
      :autoHeight="true"
      :visible.sync="_dialogVisible"
      width="1250px"
      @open="openDialog"
      @closed="closedDialog"
    >
      <el-container>
        <el-header style="height: 45px">
          <el-steps :active="activeStep" finish-status="success" simple>
            <el-step :title="type !== 'power' ? '选择资源' : '选择能力'"></el-step>
            <el-step title="填写订阅信息"></el-step>
          </el-steps>
        </el-header>
        <!-- 订阅资源时，敏感数据提示语言 -->
        <div class="tip-box" style="" v-if="type !== 'power' && this.showGradeAlert == '1'">
          <p>
            <span class="orange"
              >您即将订阅的数据属于敏感数据，根据《数据安全法》及相关法规要求，请务必注意：谨慎存储：采用加密存储措施，避免非授权访问；谨慎传输：通过安全通道传输，禁止通过非合规渠道流转；谨慎使用：仅限在授权业务场景下使用，禁止用于未授权用途；谨慎共享：未经数据提供方书面许可，不得向第三方披露。</span
            >
          </p>
        </div>
        <div class="icsp-dialog-container">
          <template v-if="activeStep === 1">
            <!-- 目录信息 -->
            <TitleInfo title="目录信息" v-if="type !== 'batchApply' && type !== 'power'" />
            <el-descriptions
              v-if="type !== 'batchApply' && type !== 'power'"
              :column="2"
              border
              :labelStyle="{ 'text-align': 'right', width: '140px' }"
              :contentStyle="{ width: '300px' }"
            >
              <el-descriptions-item label="信息目录名称">
                {{ directoryInfo.directoryName }}
              </el-descriptions-item>
              <el-descriptions-item label="信息目录分类">
                {{ directoryInfo.resClassification }}
              </el-descriptions-item>
              <el-descriptions-item label="信息目录摘要">
                {{ directoryInfo.cataAbstract }}
              </el-descriptions-item>
            </el-descriptions>
            <!-- 选择资源 -->
            <TitleInfo
              :title="type !== 'power' ? '选择资源' : '选择能力'"
              style="margin-top: 10px"
            />
            <table-plus
              :loading="tableLoading"
              key="resourceList"
              id="resourceListTable"
              :data="resourceList"
              ref="resourceListTable"
              border
              fit
              stripe
              height="330"
              highlight-current-row
              @row-click="clickRow"
              row-key="resourceId"
              @selection-change="selectMainTableRow"
              @header-dragend="handleHeaderDrag"
              :header-cell-style="{ background: '#4EACFE' }"
            >
              <el-table-column
                type="selection"
                width="60"
                align="center"
                :reserve-selection="true"
              ></el-table-column>
              <!-- 资源目录 -->
              <template v-if="type !== 'power'">
                <el-table-column
                  prop="resourceName"
                  label="资源名称"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="resourceType"
                  label="资源类型"
                  width="80"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="dataUpdateTime"
                  label="业务数据更新时间"
                  width="180"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="systemName"
                  label="资源所属系统"
                  width="180"
                  header-align="center"
                  how-overflow-tooltip
                ></el-table-column>
                <!--
                <el-table-column prop="orderContent" label="订阅提示" width="120" header-align="center" show-overflow-tooltip></el-table-column>
-->
                <el-table-column
                  label="下载订阅附件"
                  width="200"
                  show-overflow-tooltip
                  align="center"
                >
                  <template slot-scope="{ row, $index }">
                    <el-tag
                      v-if="row.orderTipFile && row.orderTipFile.fileName"
                      size="small"
                      class="file-tag icsp-ellipsis-line"
                      @click="downloadOrderTipFile(row, $index)"
                      style="cursor: pointer; width: 180px"
                      :title="row.orderTipFile ? row.orderTipFile.fileName : ''"
                    >
                      {{ row.orderTipFile ? row.orderTipFile.fileName : '' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" show-overflow-tooltip align="center">
                  <template slot-scope="{ row, $index }">
                    <perm-button
                      v-if="['1', '4'].includes(row.resourceTypeCode)"
                      type="primary"
                      label="自定义选择订阅字段"
                      @click="openDialogSubscribe(row, $index)"
                    />
                    <perm-button
                      v-if="row.resourceTypeCode === '3' && row.isOrderResParam === '1'"
                      type="primary"
                      label="自定义订阅参数字段"
                      @click="openDialogParams(row, $index)"
                    />
                  </template>
                </el-table-column>
              </template>
              <!-- 能力 -->
              <template v-else>
                <el-table-column
                  prop="powerName"
                  label="能力名称"
                  width="200"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="powerShape"
                  label="能力形态"
                  width="90"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="updateTime"
                  label="能力更新时间"
                  width="300"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="powerScene"
                  label="能力应用场景"
                  width="120"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="powerVersion"
                  label="版本信息"
                  width="120"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="powerRemark"
                  label="能力简介"
                  header-align="center"
                  show-overflow-tooltip
                ></el-table-column>
              </template>
            </table-plus>
            <!-- 提示 -->
            <div class="tip-box">
              <p>
                <span class="orange">提示：</span
                >按照“谁经手，谁使用，谁管理，谁负责”的原则，使用部门应根据履行职责需要依法依规使用共享信息，并加强共享信息使用全过程管理，使用部门对从共享平台获得的信息，只能按照明确的使用用途用于本部门履行职责需要，不得直接或以改变数据的方式提供给第三方，也不得变相用于其他目的。
              </p>
            </div>
          </template>
          <template v-else>
            <!-- 填写订阅信息 -->
            <div class="title-info">
              <TitleInfo title="填写订阅信息" class="title-info-tip"></TitleInfo>
              <div class="tip-box" style="margin-top: 5px">
                <p style="font-size: 15px; font-weight: bold">
                  <span class="orange">提示：</span
                  >{{ type !== 'power' ? resourceInfo : powerInfo }}
                </p>
              </div>
            </div>
            <el-form
              ref="formRef"
              :model="formState"
              :rules="rules"
              size="small"
              label-width="170px"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item label="使用方系统" prop="required.orderSystemId">
                    <!-- <el-col :span="18" style="margin-right: 5px">-->
                    <el-col>
                      <el-select
                        v-model="formState.required.orderSystemId"
                        placeholder="请选择使用方系统"
                        ref="elSelect"
                      >
                        <el-option
                          class="selbox nowrap"
                          v-for="item of systemList"
                          :key="item.systemId"
                          :label="item.systemName"
                          :value="item.systemId"
                        >
                          <template v-if="item.systemName.length >= 30">
                            <el-tooltip
                              class="item"
                              effect="dark"
                              :content="item.systemName"
                              placement="top-start"
                            >
                              <div>{{ item.systemName }}</div>
                            </el-tooltip>
                          </template>
                        </el-option>
                      </el-select>
                    </el-col>
                    <el-col :span="6" style="width: 22%">
                      <!-- 隐藏新增系统按钮-->
                      <el-button
                        type="primary"
                        size="mini"
                        style="display: none; width: 100%"
                        @click="openDialogAdd"
                        >新增</el-button
                      >
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="使用方" prop="required.dept">
                    <el-input v-model="formState.required.dept" disabled></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <!-- 联系人可选择可输入，选择时联系人电话跟随变化，输入时联系人电话置空 -->
                  <el-form-item label="联系人" prop="required.telPeople">
                    <el-select
                      v-model="formState.required.telPeople"
                      filterable
                      allow-create
                      placeholder="请选择或输入联系人"
                      @change="changeLinkMan"
                    >
                      <el-option
                        v-for="(item, index) in manOptions"
                        :key="index + item.linkMan"
                        :label="item.linkMan"
                        :value="item.linkMan"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系人电话" prop="required.phone">
                    <el-input
                      v-model.trim="formState.required.phone"
                      placeholder="请输入联系人电话"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="display: flex" v-if="isBaas">
                <el-form-item label="是否上链" prop="required.depositFlag">
                  <el-radio-group v-model="formState.required.depositFlag" @input="handleTip">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <p style="color: #666; margin-top: 8px; margin-left: 20px">
                  （注：选是，资源订阅记录将上区块链保存，上链后可查看该上链证书）
                </p>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="联系人邮箱" prop="required.email">
                    <el-input
                      v-model.trim="formState.required.email"
                      placeholder="请输入联系人邮箱"
                      maxlength="50"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="上传附件"
                    :prop="this.containsTheState === '1' ? 'required.fileName' : ''"
                  >
                    <el-row>
                      <el-col :span="18" style="margin-right: 5px">
                        <el-input
                          v-model="formState.required.fileName"
                          disabled
                          placeholder="点击右侧按钮上传"
                        ></el-input>
                        <input
                          v-show="false"
                          type="file"
                          id="fbFile"
                          name="fbFile"
                          value=""
                          ref="fileInput"
                          @change="handleFileChange($event)"
                          accept="*.*"
                        />
                      </el-col>
                      <el-col :span="6" style="width: 22%">
                        <el-button
                          type="primary"
                          :loading="uploadLoading"
                          @click="selectFile"
                          style="width: 100%"
                          >上传
                        </el-button>
                      </el-col>
                    </el-row>
                    <el-form-item>
                      <div class="tip-div">
                        <span class="level-tip"
                          >（注：只能传
                          {{ containsTheState === '1' ? '10M' : '20M' }}
                          以内的Word或Excel或PDF文件或zip压缩包）</span
                        >
                      </div>
                    </el-form-item>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="应用场景" v-if="type !== 'power'" prop="required.workScense">
                    <select-plus
                      dictType="POWER_SCENE"
                      v-model="formState.required.workScense"
                    ></select-plus>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="订阅理由" prop="required.applyReason">
                    <el-input
                      v-model.trim="formState.required.applyReason"
                      type="textarea"
                      placeholder="请输入订阅理由"
                      maxlength="150"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-row>
                <el-col :span="12">
                  <el-form-item label="共享协议" prop="required.checked" ref="checked">
                    <el-checkbox v-model="formState.required.checked" @change="clickCheck">同意</el-checkbox>
                    <el-link v-if="trigger_mode === '2'" type="primary" style="margin-left:20px;margin-bottom: 2px;" @click="downloadReport">共享协议</el-link>
                  </el-form-item>
                </el-col>
              </el-row>-->
              <!-- 选中国办接口资源的时候出现 -->
              <!-- 选中库表的时候出现 -->
              <template v-if="libraryType">
                <TitleInfo title="填写库表订阅信息" style="margin-top: 10px" />
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="订阅数据源" prop="library.dsCode">
                      <el-select
                        v-model="formState.library.dsCode"
                        @change="handleDsName('library')"
                      >
                        <el-option
                          v-for="item of formState.library.voDataSourceList"
                          :key="item.dsCode"
                          :label="item.dsName"
                          :value="item.dsCode"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <span style="color: red"
                    >提示：若单位存在多个数据源提供不同软件厂家使用的情况，请注意核对订阅数据源，订阅审核通过后将交换数据到此数据源。</span
                  >
                  <!--      <el-col :span="12">
                    <el-form-item label="抽取过滤条件">
                      <template #label>
                        抽取过滤条件
                        <el-popover placement="bottom" width="200" min="1" max="99999" trigger="hover" content="可填写常用过滤条件，如: flag ='1'and status ='2'">
                          <template #reference>
                            <i class="el-icon-warning-outline" />
                          </template>
                        </el-popover>
                      </template>
                      <el-input v-model.trim="formState.library.fiterCondition"></el-input>
                    </el-form-item>
                  </el-col>-->
                </el-row>
              </template>
              <!-- 选中回流的时候出现 -->
              <!-- <template v-if="huiliuType && formState.huiliu.voDataSourceList.length">
                <TitleInfo title="填写回流订阅信息" style="margin-top: 10px;" />
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="订阅数据源" prop="huiliu.dsCode">
                      <el-select v-model="formState.huiliu.dsCode" @change="handleDsName('huiliu')">
                        <el-option v-for="item of formState.huiliu.voDataSourceList" :key="item.dsCode" :label="item.dsName" :value="item.dsCode">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="抽取过滤条件">
                      <template #label>
                        抽取过滤条件
                        <el-popover placement="bottom" width="200" min="1" max="99999" trigger="hover" content="可填写常用过滤条件，如: flag ='1'and status ='2'">
                          <template #reference>
                            <i class="el-icon-warning-outline" />
                          </template>
                        </el-popover>
                      </template>
                      <el-input v-model.trim="formState.huiliu.fiterCondition"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </template> -->
              <!-- 选中接口资源的时候出现 -->
              <!-- <template v-if="serviceType">
                <TitleInfo title="填写接口订阅信息" style="margin-top: 10px;" />
                <el-col :span="12">
                  <el-form-item label="订阅类型" prop="service.applyType">
                    <el-select style="width:100%" v-model="formState.service.applyType" clearable placeholder="请选择订阅类型" @change="handleApplyTypeChange">
                      <el-option label="永久权限" value="0"></el-option>
                      <el-option label="限时权限" value="1"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="使用时间范围" prop="service.serviceUserTimes">
                    <el-radio-group v-model="formState.service.serviceUserTimes">
                      <el-radio label="1">工作日（ 8:00:00-18:00:00 ）</el-radio>
                      <el-radio label="2">全天（含非工作日）</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="调用次数限制" prop="service.serviceMaxTimes">
                    <template #label>
                      调用次数限制
                      <el-popover placement="bottom" width="200" min="1" max="99999" trigger="hover" content="注：调用次数限制（次/天），不限制则输入0">
                        <template #reference>
                          <i class="el-icon-warning-outline" />
                        </template>
                      </el-popover>
                    </template>
                    <el-input v-model.trim="formState.service.serviceMaxTimes" type="number" placeholder="请输入数字" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="授权时间从" prop="service.usingTime" v-if="formState.service.applyType === '1'">
                    <el-date-picker v-model="formState.service.usingTime" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" :picker-options="disabledBeforeToday" style="width:100%;" />
                  </el-form-item>
                  <el-form-item label="允许使用IP" prop="service.ipLimit">
                    <el-input v-model.trim="formState.service.ipLimit" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" maxlength="125" show-word-limit placeholder="如果输入IP，只允许这些IP调用，如果有多个IP，请用英文逗号分隔，另外IP每段都可以支持123-255这样的区间值，用于便捷处理多个IP段的情况（例：***********-255）。" />
                  </el-form-item>
                </el-col>
              </template> -->
            </el-form>
          </template>
        </div>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="changeStep"
          :disabled="selectTableRow.length === 0 || kbResourceDisabled"
          >{{ activeStep === 1 ? '下一步' : '上一步' }}</el-button
        >
        <el-button type="primary" v-if="activeStep === 2" @click="submitDialog" :loading="loading"
          >提交订阅</el-button
        >
        <el-button type="plain" @click="closedDialog">关闭</el-button>
      </div>
    </DialogPlus>
    <!-- 自定义选择订阅字段 -->
    <DialogPlus
      custom-class="icsp-dialog"
      title="自定义选择订阅字段"
      :visible.sync="dialogVisibleSubscribe"
      width="900px"
      :autoHeight="true"
    >
      <div class="icsp-dialog-container">
        <table-plus
          key="subscribeList"
          id="subscribeTable"
          :data="subscribeListTemp"
          ref="subscribeTable"
          border
          fit
          stripe
          height="400"
          highlight-current-row
          @row-click="clickRow1"
          @selection-change="selectMainTableRow1"
          :header-cell-style="{ background: '#4EACFE' }"
        >
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column
            prop="fieldCode"
            label="字段名"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fieldName"
            label="信息项名称"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fieldType"
            label="字段类型"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fieldLength"
            label="字段长度"
            align="right"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fieldIspk"
            label="是否主键"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
        </table-plus>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDialogSubscribe">保存</el-button>
        <el-button type="plain" @click="closedDialogSubscribe">取消</el-button>
      </div>
    </DialogPlus>
    <!-- 新增使用方系统 -->
    <DialogPlus
      custom-class="icsp-dialog"
      title="新增使用方系统"
      :visible.sync="dialogVisibleAdd"
      :autoHeight="true"
      @closed="closedDialogAdd"
    >
      <div class="icsp-dialog-container">
        <el-form
          ref="formRef1"
          :model="systemData"
          :rules="dataRules"
          size="small"
          label-width="170px"
        >
          <el-form-item label="系统名称" prop="systemName">
            <el-input
              v-model.trim="systemData.systemName"
              placeholder="请输入系统名称"
              maxlength="25"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统承建商" prop="systemBiz">
            <el-input
              v-model.trim="systemData.systemBiz"
              placeholder="请输入系统承建商"
              maxlength="50"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统负责人" prop="systemMan">
            <el-input
              v-model.trim="systemData.systemMan"
              placeholder="请输入系统负责人"
              maxlength="50"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统负责人联系方式" prop="systemPhone">
            <el-input
              v-model.trim="systemData.systemPhone"
              placeholder="请输入系统负责人联系方式"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统访问地址" prop="systemUrl">
            <el-input
              v-model.trim="systemData.systemUrl"
              placeholder="请输入系统访问地址"
              maxlength="100"
            ></el-input>
          </el-form-item>
          <el-form-item label="系统简介" prop="systemDesc">
            <el-input
              v-model.trim="systemData.systemDesc"
              type="textarea"
              placeholder="请输入系统简介"
              maxlength="300"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDialogAdd" :loading="saveLoading">保存</el-button>
        <el-button type="plain" @click="closedDialogAdd">取消</el-button>
      </div>
    </DialogPlus>
    <!-- 自定义订阅参数字段 -->
    <DialogPlus
      custom-class="icsp-dialog"
      title="自定义订阅参数字段"
      :visible.sync="dialogVisibleParam"
      width="900px"
      :autoHeight="true"
    >
      <div class="icsp-dialog-container">
        <table-plus
          key="paramList"
          row-key="paramId"
          id="paramTable"
          :data="paramListTemp"
          ref="paramTable"
          border
          fit
          stripe
          height="400"
          highlight-current-row
          @row-click="clickRow2"
          @selection-change="selectMainTableRow2"
          :header-cell-style="{ background: '#4EACFE' }"
        >
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <!-- <el-table-column prop="funcName" label="函数名称" show-overflow-tooltip></el-table-column> -->
          <el-table-column
            prop="paramName"
            label="参数名"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="paramDesc"
            label="参数描述"
            header-align="center"
            show-overflow-tooltip
          ></el-table-column>
        </table-plus>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveDialogParam">确认</el-button>
        <el-button type="plain" @click="closedDialogParam">取消</el-button>
      </div>
    </DialogPlus>
    <!-- <DialogPlus custom-class="icsp-dialog" title="共享协议" :show-close="false" :visible.sync="agreementVisible" width="1200px" :autoHeight="true">
      <el-container>
        <el-header>
          <div style="font-weight: bold">
            订阅之前，请先阅读《{{shareTitle}}》
          </div>
        </el-header>
        <el-scrollbar style="height: 430px">
          <div style="text-align: center">
            {{shareTitle}}
            &lt;!&ndash; 江西省数据共享和开放平台数据保密协议 &ndash;&gt;
          </div>
          <div id="content" style="padding-left: 10px; box-sizing: border-box"></div>
          <div style="display: flex">
            <p style="width: 500px">甲方：{{ aName }}</p>
            <p style="width: 500px">乙方：{{ currentUser.unitName || '' }}</p>
          </div>
          <div style="display: flex">
            <p style="width: 500px">
              协议日期：{{ new Date().toLocaleDateString() }}
            </p>
            <p style="width: 500px">
              协议日期：{{ new Date().toLocaleDateString() }}
            </p>
          </div>
        </el-scrollbar>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAccept" :disabled="countDown >= 0">接受<span v-if="countDown >= 0">（{{ countDown }}s）</span></el-button>
        <el-button type="plain" @click="closeAgreeDialog">不接受</el-button>
      </div>
    </DialogPlus>-->
    <handProgress
      :type="type"
      :isShow="isProgress"
      :total="batchTotal"
      :error="batchError"
      :isError="isProgressError"
      :finish="batchFinish"
      @close="closeProgress"
      :errorList="errorList"
    ></handProgress>
  </div>
</template>

<script>
import Tip from '@/core/components/Tip'
import DialogPlus from '@/core/components/DialogPlus'
import TablePlus from '@/core/components/TablePlus'
import SelectPlus from '@/core/components/SelectPlus'
import PermButton from '@/core/components/PermButton'
import TitleInfo from 'biz/components/common/t-titleInfo'
import { setDictCache, getDictCache } from '@/core/utils/tabCache'
import handProgress from './common/handProgress'
import { FileUtils } from '@/biz/utils/download'
import { mapState } from 'vuex'
import { checkPhone, checkEmail, checkInternetURL, checkFigure } from '../utils/validate.js'

export default {
  name: 'dialogApply',
  components: {
    DialogPlus,
    TablePlus,
    SelectPlus,
    PermButton,
    TitleInfo,
    Tip,
    handProgress
  },
  data() {
    let checkdepositFlag = (rule, value, callback) => {
      if (value === 0) {
        return callback(new Error('请选择是否上链'))
      } else {
        callback()
      }
    }
    let checkShare = (rule, value, callback) => {
      if (value === false) {
        return callback(new Error('请勾选同意共享协议'))
      } else {
        callback()
      }
    }
    let validateIp = (rule, value, callback) => {
      const reg =
        /^(?:(?:^|,)((?:[0-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])(-(?:[0-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5]))?)(?:\.(?:[0-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])(-(?:[0-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5]))?){3})+$/
      if (value) {
        if (!reg.test(value)) {
          callback(new Error('请输入正确的ip地址'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    let checkName = (rule, value, callback) => {
      let regExp = /^(?!_)(?!-)(?!,)(?!.*?,$)(?!.*?[-_]$)[a-zA-Z0-9_,\u4e00-\u9fa5-]+$/
      if (value) {
        if (!regExp.test(value)) {
          return callback(
            new Error(
              '请输入汉字、英文、数字、下划线、短横线及英文逗号且不以下划线、短横线和逗号开头和结尾'
            )
          )
        } else {
          callback()
        }
        if (value.length > 15) {
          return callback(new Error('联系人名称长度不能超过15个字'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      trigger_mode: '1',
      countDown: 5, // 倒计时时间（单位：秒）
      aName: '',
      content: '',
      agreementVisible: false,
      errorList: [],
      // 批量操作对话框
      isProgress: false, // 是否展示
      isProgressError: false, // 是否有操作失败的
      batchTotal: 0, // 批量操作的数量
      batchFinish: 0, // 批量操作完成的数量
      batchError: 0, // 批量操作失败的数量
      queryBt: true,
      tableLoading: false,
      loading: false, // 防止多次点击确定导致多次调用接口
      saveLoading: false,
      uploadLoading: false,
      downloadLoading: false,
      isBaas: false, // 是否开启了区块链功能
      directoryInfo: {},
      systemList: [],
      resourceList: [{}],
      resourceInfo:
        '为确保供数部门及时审核资源，建议需求部门在订阅资源前，先与供数部门进行充分的沟通，明确数据需求后再进行订阅，提高资源订阅效率。',
      powerInfo:
        '为确保供数部门及时审核能力，建议需求部门在订阅能力前，先与能力提供部门进行充分的沟通，明确能力需求后再进行订阅，提高能力订阅效率。',
      formState: {
        // 必填项
        required: {
          orderSystemId: '',
          dept: '',
          telPeople: '',
          phone: '',
          depositFlag: '',
          email: '',
          // -----------------------以下，附件相关
          file: [],
          fileName: undefined,
          path: undefined,
          fileType: undefined,
          fileSize: 1,
          realName: undefined,
          // ------------------------
          workScense: undefined,
          applyReason: '',
          checked: true,
          isShardType: undefined
        },
        // 库表项
        library: {
          dsCode: '',
          dsName: '',
          fiterCondition: undefined,
          voDataSourceList: [] // 订阅数据源
        },
        // 回流
        huiliu: {
          dsCode: '',
          dsName: '',
          fiterCondition: undefined,
          voDataSourceList: [] // 订阅数据源
        },
        // 接口项
        service: {
          applyType: '',
          usingTime: [],
          serviceUserTimes: '1',
          // applyBasis: "",
          // serviceMaxTimes: "",
          ipLimit: '',
          serviceMaxTimes: '',
          serviceAvgTimes: '',
          useDays: '',
          useTime: undefined
        }
      },
      manOptions: [],
      rules: {
        required: {
          orderSystemId: [
            {
              required: true,
              message: '使用方系统为必选项',
              trigger: 'change'
            }
          ],
          telPeople: [
            { required: true, message: '联系人为必填项', trigger: 'blur' },
            { validator: checkName, trigger: 'blur' }
          ],
          phone: [
            { required: true, message: '联系人电话为必填项', trigger: 'blur' },
            { validator: checkPhone, trigger: 'blur' }
          ],
          email: [{ validator: checkEmail, trigger: 'blur' }],
          depositFlag: [
            { required: true, message: '是否上链为必选项', trigger: 'change' },
            { validator: checkdepositFlag, trigger: 'change' }
          ],
          workScense: [{ required: true, message: '应用场景为必填项', trigger: 'change' }],
          applyReason: [{ required: true, message: '订阅理由为必填项', trigger: 'blur' }],
          fileName: [{ required: true, message: '请上传附件', trigger: 'blur' }],
          checked: [
            { required: true, message: '共享协议为必选项', trigger: 'change' },
            { validator: checkShare, trigger: 'change' }
          ]
        },
        library: {
          dsCode: [{ required: true, message: '请选择订阅数据源', trigger: 'change' }]
        },
        huiliu: {
          dsCode: [{ required: true, message: '请选择订阅数据源', trigger: 'change' }]
        },
        wenjian: {
          dsCode: [{ required: true, message: '请选择订阅数据源', trigger: 'change' }]
        },
        wenjianjia: {
          dsCode: [{ required: true, message: '请选择订阅数据源', trigger: 'change' }]
        },
        service: {
          applyType: [{ required: true, message: '请选择订阅类型', trigger: 'change' }],
          usingTime: [],
          serviceUserTimes: [{ required: true, message: '请选择使用时间范围', trigger: 'change' }],
          // applyBasis: [
          //   { required: true, message: "订阅依据为必选项", trigger: "blur" }
          // ],
          serviceMaxTimes: [
            {
              required: true,
              trigger: 'blur',
              messageSelf: '请输入调用次数限制',
              validator: checkFigure
            }
          ],
          ipLimit: [{ validator: validateIp, trigger: 'blur' }]
        }
      },
      timeOptionRange: '',
      disabledBeforeToday: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && !maxDate) {
            this.timeOptionRange = minDate
          }
          if (maxDate) {
            this.timeOptionRange = ''
          }
        },
        disabledDate: (time) => {
          if (this.timeOptionRange !== '') {
            return (
              time.getTime() === this.timeOptionRange.getTime() ||
              time.getTime() < Date.now() - 8.64e7
            )
          }
          // time是一个Date对象
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      // ===================系统
      systemData: {
        systemId: '',
        systemName: '',
        systemUrl: '',
        systemCode: '',
        clientSkipUrl: '',
        isEncryption: '1'
      },
      dataRules: {
        systemName: [
          { required: true, message: '系统名称为必填项', trigger: 'blur' },
          { validator: checkName, trigger: 'blur' }
        ],
        systemBiz: [
          { required: true, message: '系统承建商为必填项', trigger: 'blur' },
          { validator: checkName, trigger: 'blur' }
        ],
        systemMan: [
          { required: true, message: '系统负责人为必填项', trigger: 'blur' },
          { validator: checkName, trigger: 'blur' }
        ],
        systemPhone: [
          {
            required: true,
            message: '系统负责人联系方式为必填项',
            trigger: 'blur'
          },
          { validator: checkPhone, trigger: 'blur' }
        ],
        systemUrl: [
          { required: true, message: '系统访问地址为必填项', trigger: 'blur' },
          { validator: checkInternetURL, trigger: 'blur' }
        ],
        systemDesc: [{ required: true, message: '系统简介为必填项', trigger: 'blur' }]
      },
      shareName: '',
      shareTitle: '',
      copyTableRow: [], // copy TableRow
      selectTableRow: [], // 修改时的table选择row
      kbResourceDisabled: false,
      dialogVisibleSubscribe: false,
      dialogVisibleParam: false,
      dialogVisibleAdd: false,
      selectTableRow1: [],
      subscribeListTemp: [], // 临时
      subscribeList: new Map(), // 格式: {资源id: 字段信息}
      selectTableRow2: [],
      paramListTemp: [], // 临时
      paramList: new Map(), // 格式: {资源id: 字段信息}
      nowIndex: undefined,
      nowIndex1: undefined,
      activeStep: 1,
      libraryType: false, // 资源类型-库表
      serviceType: false, // 资源类型-接口
      wenjianType: false, // 资源类型-文件
      wenjianjiaType: false, // 资源类型-文件夹
      huiliuType: false, // 资源类型-回流
      columnType: new Map(), // 字段类型字典映射
      gbNationResFlag: false, // 是否国办 接口数据
      showGradeAlert: '0' // 是否展示敏感数据提示
    }
  },
  props: {
    step: {
      type: Number,
      default: 1
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    shop: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '数据资源订阅'
    },
    // 是否是批量订阅batchApply，是则不展示目录信息
    type: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    _dialogVisible: {
      get: function () {
        return this.dialogVisible
      },
      set: function (val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    ...mapState({
      currentUser: (state) => state.user.currentUser,
      systemConfig: (state) => state.icsp.systemConfig
    })
  },
  methods: {
    handleAccept() {
      this.agreementVisible = false
    },
    closeAgreeDialog() {
      this.agreementVisible = false
      // this.closedDialog();
      this.formState.required.checked = false
    },
    clickCheck() {
      if (this.formState.required.checked && config.trigger_mode === '1') {
        this.agreementVisible = true
        this.$nextTick(() => {
          this.getContent()
        })
        this.startCountDown() // 接受倒计时
      } else {
        console.log('2')
      }
    },
    startCountDown() {
      this.countDown = 5
      let timer = setInterval(() => {
        if (this.countDown >= 0) {
          this.countDown--
        } else {
          clearInterval(timer)
        }
      }, 1000)
    },
    // 如选择不上链，弹出提示
    handleTip(label) {
      if (label === '0') {
        this.$confirm('如选择不上链，则后续无法溯源存证', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(() => {
          this.formState.required.depositFlag = '1'
        })
      }
    },
    // 查询是否开启了区块链功能
    getIsBaas() {
      this.$api.bizApi.common.getIsBaas().then((res) => {
        this.isBaas = res.data
        if (this.isBaas) {
          this.formState.required.depositFlag = '1'
        }
      })
    },
    // 切换step
    changeStep() {
      // this.containsTheState;
      this.containsTheState = '0'
      this.copyTableRow = this.selectTableRow
      if (this.activeStep === 1 && this.subscribeList.size === 0) {
        // 此处要把库表/回流、接口中的自定义订阅字段和自定义订阅参数字段给每个资源绑上
        // 给每个库表和回流类型的资源ID绑定所有的colIds
        console.log('走了上面')
        this.copyTableRow.forEach((j) => {
          if (['1', '4'].includes(j.resourceTypeCode)) {
            let colIds = []
            ;(j.columnList || []).forEach((k, index) => {
              colIds.push(k.colId)
            })
            this.subscribeList.set(j.resourceId, colIds)
          }
          console.log(j)
          if (j.isExternalImport === '9') {
            this.containsTheState = '1'
          }
        })
      } else if (this.activeStep === 1 && this.paramList.size === 0) {
        console.log('走了下面')
        this.copyTableRow.forEach((j) => {
          if (j.resourceTypeCode === '3') {
            // 给每个接口类型的资源ID绑定所有的paramIds
            let paramIds = []
            ;(j.trsServiceFuncParams || []).forEach((k, index) => {
              paramIds.push(k.paramId)
            })
            this.paramList.set(j.resourceId, paramIds)
          }
          console.log(j)
          if (j.isExternalImport === '9') {
            this.containsTheState = '1'
          }
        })
      } else {
        // console.log(this.subscribeList)
      }
      console.log('containsTheState现在是：' + this.containsTheState)
      this.activeStep = this.activeStep === 1 ? 2 : 1
      this.gbNationResFlag = false
      if (this.activeStep === 2) {
        this.selectResType()
      } else {
        // 返回上一步的时候勾选中已选中的内容
        this.$nextTick(() => {
          this.copyTableRow.forEach((i) => {
            this.clickRow(i)
          })
        })
        this.selectResType()
      }
    },
    // 给订阅数据源名称赋值
    handleDsName(type) {
      this.formState[type].voDataSourceList.forEach((i, index) => {
        if (this.formState[type].dsCode === i.dsCode) {
          this.formState[type].dsName = i.dsName
        }
      })
    },
    // 打开弹框
    openDialog() {
      this.trigger_mode = config.trigger_mode || '1'
      this.shareName = config.shareName
      this.shareTitle = config.shareTitle
      // 选中第一步
      this.activeStep = 1
      // 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
      this.handleReset() // 重置
      this.getIsBaas() // 查询是否开启了区块链功能
      this.getLinkMan() // 获取联系人
      // 获取该目录下的资源信息
      let params = {}
      console.log(this.data, 'data')
      params.cataId = this.data?.cataId
      params.resourceId = this.data?.resourceId
      params.powerId = this.data?.powerId
      this.showGradeAlert = this.data.showGradeAlert
      this.data?.shardType === '无条件共享'
        ? (this.formState.required.isShardType = '1')
        : (this.formState.required.isShardType = '0')
      console.log(this.data, 'this.data')
      if (params.resourceId && params.resourceId.indexOf(',') > -1) {
        this.tableLoading = true
        // 选数车多选查询
        this.$api.bizApi.resApply
          .getResourcesInfo({ resourceIds: params.resourceId })
          .then((res) => {
            this.resourceList = res.data.resource || []

            // 从选数车多条批量订阅跳转过来时，资源信息全部选中
            if (this.step === 1) {
              this.$nextTick(() => {
                let unitNames = []
                this.resourceList.forEach((i) => {
                  this.$refs.resourceListTable.toggleRowSelection(i, true)
                  // 根据共享条件判断上传附件的必填
                  if (
                    i.barindCondition &&
                    (i.barindCondition.indexOf('1') > -1 || i.barindCondition.indexOf('5') > -1)
                  ) {
                    this.rules.required.fileName.push({
                      required: true,
                      message: '上传附件为必填项',
                      trigger: 'blur'
                    })
                  }
                  unitNames.push(i.createUnitName)
                })
                unitNames = [...new Set(unitNames)]
                unitNames.forEach((j, index) => {
                  index === 0 ? (this.aName = j) : (this.aName = this.aName + ',' + j)
                })
                this.selectTableRow = this.resourceList
                this.copyTableRow = this.selectTableRow
              })
            } else {
              console.log(this.resourceList, ' this.resourceList')
              console.log('step')
              this.selectTableRow = this.resourceList
              this.copyTableRow = this.selectTableRow

              this.changeStep()
            }
            this.tableLoading = false
          })
      } else {
        if (params.powerId) {
          this.resourceList[0] = this.data
          // if (this.data.length) {
          //   this.resourceList = this.data
          // } else {
          //   this.resourceList[0] = this.data
          // }
          let unitNames = []
          console.log(this.resourceList, 'this.resourceList')
          this.resourceList.forEach((i) => {
            unitNames.push(i.registerUnitName)
          })
          unitNames = [...new Set(unitNames)]
          unitNames.forEach((j, index) => {
            index === 0 ? (this.aName = j) : (this.aName = this.aName + ',' + j)
          })
        } else {
          this.tableLoading = true
          this.$api.bizApi.resApply.getCatalogInfo(params).then((res) => {
            this.directoryInfo = res.data
            this.resourceList = res.data.resource || []
            if (res && res.data && res.data.resource) {
              const kbResource = res.data.resource.find((item) => item.resourceTypeCode === '1')
              if (
                kbResource &&
                (!kbResource.voDataSourceList || kbResource.voDataSourceList.length === 0)
              ) {
                this.$message.warning(
                  '当前登录用户下无可用的数据源，请先到【数据交换系统】-【数据源管理】模块注册数据源！'
                )
                this.kbResourceDisabled = true
              } else {
                this.kbResourceDisabled = false
              }
            }
            // 从资源或者选数车单条批量订阅跳转过来时，资源信息全部选中
            if (['singleApply', 'batchApply'].includes(this.type)) {
              this.$nextTick(() => {
                let unitNames = []
                this.resourceList.forEach((i) => {
                  this.$refs.resourceListTable.toggleRowSelection(i, true)
                  // 根据共享条件判断上传附件的必填
                  if (
                    i.barindCondition &&
                    (i.barindCondition.indexOf('1') > -1 || i.barindCondition.indexOf('5') > -1)
                  ) {
                    this.rules.required.fileName.push({
                      required: true,
                      message: '上传附件为必填项',
                      trigger: 'blur'
                    })
                  }
                  unitNames.push(i.createUnitName)
                })
                unitNames = [...new Set(unitNames)]
                unitNames.forEach((j, index) => {
                  index === 0 ? (this.aName = j) : (this.aName = this.aName + ',' + j)
                })

                this.selectTableRow = this.resourceList
                this.copyTableRow = this.selectTableRow
              })
            } else {
              // 从resList单条跳转过来时 确认aName
              let unitNames = []
              this.resourceList.forEach((i) => {
                unitNames.push(i.createUnitName)
              })
              unitNames = [...new Set(unitNames)]
              unitNames.forEach((j, index) => {
                index === 0 ? (this.aName = j) : (this.aName = this.aName + ',' + j)
              })
            }

            this.tableLoading = false
          })
        }
      }
      this.getSystemList() // 获取系统列表
      // 获取登录人的部门信息 以及联系信息
      this.formState.required.dept = this.currentUser.unitName
      this.formState.required.telPeople = this.currentUser.realName
      this.formState.required.phone = this.currentUser.mobile
      this.formState.required.email = this.currentUser.email
    },
    // 获取联系人
    getLinkMan() {
      this.$api.bizApi.resApply.getLinkManList().then((res) => {
        this.manOptions = res.data || []
      })
    },
    // 更改联系人
    changeLinkMan(value) {
      let has = this.manOptions.find((e) => e.linkMan === value)
      if (!has) {
        this.formState.required.phone = undefined
      } else {
        this.formState.required.phone = has.linkPhone
      }
    },
    // 获取系统列表
    async getSystemList(val) {
      await this.$api.bizApi.system.getSystemByLoginUnit().then((res) => {
        this.systemList = res.data
        if (val && val.systemId) {
          this.formState.required.orderSystemId = val.systemId
        }
      })
    },
    // 关闭弹框
    closedDialog() {
      this.$emit('resetFilter')
      this._dialogVisible = false
      if (this.$refs.formRef) {
        // 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
        this.$refs.formRef.resetFields()
      }
      // 因为open的时候、close之前手动选中了所有或部分资源，所以close的时候要手动取消选中这部分资源
      if (this.activeStep === 1 && this.selectTableRow.length > 0) {
        this.selectTableRow.forEach((i) => {
          this.$refs.resourceListTable.toggleRowSelection(i, false)
        })
      }
      this.handleReset()
    },
    // 重置
    handleReset() {
      this.formState = {
        // 必填项
        required: {
          orderSystemId: '',
          dept: '',
          telPeople: '',
          phone: '',
          depositFlag: '',
          email: '',
          // -----------------------以下，附件相关
          fileName: undefined,
          path: undefined,
          fileType: undefined,
          fileSize: 1,
          realName: undefined,
          // ------------------------
          file: '',
          workScense: undefined,
          applyReason: '',
          checked: true,
          isShardType: undefined
        },
        // 库表项
        library: {
          dsCode: '',
          dsName: '',
          fiterCondition: undefined,
          voDataSourceList: []
        },
        // 回流
        huiliu: {
          dsCode: '',
          dsName: '',
          fiterCondition: undefined,
          voDataSourceList: []
        },
        // 接口项
        service: {
          applyType: '',
          usingTime: [],
          serviceUserTimes: '1',
          // applyBasis: "",
          // serviceMaxTimes: "",
          ipLimit: '',
          serviceMaxTimes: '',
          serviceAvgTimes: '',
          useDays: '',
          useTime: undefined
        }
      }
      this.serviceType = false
      this.libraryType = false
      this.wenjianType = false
      this.wenjianjiaType = false
      this.huiliuType = false
      this.directoryInfo = {}
      this.resourceList = []
      this.selectTableRow = []
      this.copyTableRow = []
      this.selectTableRow1 = [] // 清除自定义字段
      this.subscribeList.clear() // 清除自定义字段
      this.selectTableRow2 = [] // 清除自定义订阅参数字段
      this.paramList.clear() // 清除自定义订阅参数字段
    },
    // 判断选中的资源的资源类型
    selectResType() {
      // 重置表单状态
      this.resetFormState()

      // 如果没有选中的资源，直接返回
      if (!this.copyTableRow.length) {
        return
      }

      // 处理选中的资源
      this.copyTableRow.forEach((resource) => {
        this.processResourceByType(resource)
      })
    },

    // 重置表单状态
    resetFormState() {
      this.formState.service.voDataSourceList = []
      this.formState.huiliu.voDataSourceList = []
      this.formState.library.voDataSourceList = []
      this.serviceType = false
      this.huiliuType = false
      this.libraryType = false
    },

    // 根据资源类型处理资源
    processResourceByType(resource) {
      const { resourceTypeCode } = resource

      switch (resourceTypeCode) {
        case '3':
          this.handleServiceResource(resource)
          break
        case '1':
          this.handleLibraryResource(resource)
          break
        case '4':
          this.handleHuiliuResource(resource)
          break
        default:
          break
      }
    },

    // 处理接口资源
    handleServiceResource(resource) {
      this.serviceType = true
      // 接口 - 国办业务
      if (resource.gbNationResourceId) {
        this.gbNationResFlag = true
      }
    },

    // 处理库表资源
    handleLibraryResource(resource) {
      this.libraryType = true
      this.mergeDataSourceList(resource, 'library')
    },

    // 处理回流资源
    handleHuiliuResource(resource) {
      this.huiliuType = true
      this.mergeDataSourceList(resource, 'huiliu')
    },

    // 合并数据源列表并去重
    mergeDataSourceList(resource, type) {
      const dataSourceList = resource.voDataSourceList || []

      // 添加数据源到表单状态
      dataSourceList.forEach((item) => {
        this.formState[type].voDataSourceList.push(item)
      })

      // 去重处理
      this.deduplicateDataSourceList(type)

      // 设置默认选中的数据源
      this.setDefaultDataSource(type)
    },

    // 数据源去重
    deduplicateDataSourceList(type) {
      const map = new Map()
      this.formState[type].voDataSourceList = this.formState[type].voDataSourceList.filter(
        (item) => !map.has(item.dsName) && map.set(item.dsName, item)
      )
    },

    // 设置默认数据源
    setDefaultDataSource(type) {
      const dataSourceList = this.formState[type].voDataSourceList

      if (dataSourceList.length > 0) {
        // 优先选择 isUnitDefault=1 的数据源
        const defaultDataSource =
          dataSourceList.find((item) => item.isUnitDefault === '1') || dataSourceList[0]

        this.formState[type].dsCode = defaultDataSource.dsCode
        this.formState[type].dsName = defaultDataSource.dsName
      } else {
        this.formState[type].dsCode = ''
        this.formState[type].dsName = ''
      }
    },
    handleApplyTypeChange(val) {
      if (val === '1') {
        this.rules.service.usingTime = [
          { required: true, message: '请选择授权时间', trigger: 'change' }
        ]
      } else {
        this.rules.service.usingTime = []
        this.formState.service.usingTime = []
      }
    },
    // 关闭批量提示的弹窗
    closeProgress() {
      this.isProgress = false
      this.isProgressError = false
      this.handleReset()
      this.closedDialog()
      if (this.shop) {
        this.$emit('getShopList')
      }
    },
    // 提交订阅 数据处理
    async submitDialog() {
      if (!this.queryBt) {
        return
      }
      try {
        this.queryBt = false
        this.errorList = []
        this.batchFinish = 0
        this.batchError = 0
        this.isProgressError = false
        this.isProgress = false
        await this.$refs.formRef.validate()
        // 获取选中的资源
        if (!this.selectTableRow.length) {
          this.$errMsgBox.show({
            text: '至少需要选中一条资源'
          })
          return
        }
        // 多条数据的时候展示进度条弹框
        if (this.selectTableRow.length > 1) {
          this.isProgress = true
          this.batchTotal = this.selectTableRow.length
        }
        let resType = new Map()
        if (this.type !== 'power') {
          const applyResource = new Map()
          // 选中资源id存入数组
          for (let resource of this.selectTableRow) {
            applyResource.set(resource.resourceId, [])
            resType.set(resource.resourceId, resource.resourceTypeCode)
          }
          // 库表/回流选中列处理 自定义订阅字段
          for (let [key, value] of this.subscribeList.entries()) {
            if (applyResource.has(key)) {
              applyResource.set(key, value)
            }
          }
          // 接口选中列处理 自定义订阅参数字段
          for (let [key, value] of this.paramList.entries()) {
            if (applyResource.has(key)) {
              applyResource.set(key, value)
            }
          }

          // 批量订阅
          for (const [key, value] of applyResource.entries()) {
            try {
              for (const [key1, value1] of resType.entries()) {
                if (key1 === key) {
                  // console.log(key, value)
                  await this.applySingleResource(key, value, value1)
                }
              }
            } catch (e) {}
          }
        } else {
          const applyAbility = new Map()
          // 选中资源id存入数组
          for (let resource of this.selectTableRow) {
            applyAbility.set(resource.powerId, [])
          }

          for (const [key, value] of applyAbility.entries()) {
            try {
              await this.applySingleAbility(key)
            } catch (e) {
              console.log(e, 'e')
            }
          }

          console.log(applyAbility, 'applyAbility')
        }

        // 单条数据手动关闭弹框
        if (this.selectTableRow.length === 1) {
          if (this.batchError) {
            // this.$singleMessageBox(this.errorList[0].message, '错误提示', {
            //   confirmButtonText: '关闭',
            //   type: 'warning'
            // });
            // this.closedDialog();
            this.$refs.elSelect.blur() // 解决：订阅提交过程中下拉展开订阅系统，所有订阅提交完毕后，订阅系统页面停留在界面
          } else {
            this.$message({
              message: '提交订阅成功',
              type: 'success'
            })
            this.closedDialog() // 自动关闭弹框
            this.$refs.elSelect.blur() // 解决：订阅提交过程中下拉展开订阅系统，所有订阅提交完毕后，订阅系统页面停留在界面
          }
        }
        this.queryBt = true
        this.loading = false
      } catch (e) {
        this.queryBt = true
        this.loading = false
      } finally {
        // 刷新界面统计
        window.refreshCountNum && window.refreshCountNum()
      }
    },
    async applySingleAbility(powerId) {
      console.log(this.formState.required, 'this.formState.required')
      const { powerPath, resourcePath, ...powerRequired } = this.formState.required
      let deepForm = {}
      deepForm = JSON.parse(
        JSON.stringify({
          ...powerRequired,
          powerId
        })
      )
      deepForm.path = powerPath
      console.log(deepForm, 'deepForm')
      let item = this.resourceList.filter((i) => i.powerId === powerId)[0]
      try {
        console.log(deepForm, 'deepForm111')
        const { code, message } = await this.$api.bizApi.resApply.powerApply(deepForm)
        if (code === '200') {
          this.batchFinish++
          this.errorList.push({
            powerName: item.powerName,
            powerShape: item.powerShape,
            state: '成功',
            message: ''
          })
        } else {
          this.batchError++
          this.isProgressError = true
          this.errorList.push({
            powerName: item.powerName,
            powerShape: item.powerShape,
            state: '失败',
            message: message
          })
        }
      } catch (e) {
        this.$message({
          message: e.message,
          type: 'error'
        })
        this.batchError++
        this.isProgressError = true
        this.errorList.push({
          powerName: item.powerName,
          powerShape: item.powerShape,
          state: '失败',
          message: e.message
        })
      }
    },
    // 提交订阅接口
    async applySingleResource(resourceId, colIds, resType) {
      const { powerPath, resourcePath, ...resRequired } = this.formState.required
      // 订单类型 2 :回流订单；0：普通订单；1：开放订单
      if (resType === '4') {
        this.formState.required.orderType = '2'
      } else {
        this.formState.required.orderType = '0'
      }
      let deepForm
      if (resType === '1') {
        // 库表
        deepForm = JSON.parse(
          JSON.stringify({
            ...resRequired,
            dsCode: this.formState.library.dsCode,
            dsName: this.formState.library.dsName,
            fiterCondition: this.formState.library.fiterCondition
          })
        )
      } else if (resType === '4') {
        // 回流
        deepForm = JSON.parse(
          JSON.stringify({
            ...resRequired,
            dsCode: this.formState.huiliu.dsCode,
            dsName: this.formState.huiliu.dsName,
            fiterCondition: this.formState.huiliu.fiterCondition
          })
        )
      } else if (resType === '3') {
        // 接口
        deepForm = JSON.parse(
          JSON.stringify({
            ...resRequired,
            ...this.formState.service
          })
        )
      } else if (resType === '2') {
        // 文件
        deepForm = JSON.parse(
          JSON.stringify({
            ...resRequired
          })
        )
      } else if (resType === '5') {
        // 文件夹
        deepForm = JSON.parse(
          JSON.stringify({
            ...resRequired
          })
        )
      }
      if (deepForm.usingTime) {
        deepForm.usingTimeStart = deepForm.usingTime[0]
        deepForm.usingTimeEnd = deepForm.usingTime[1]
        delete deepForm.usingTime
      }
      // 国办业务处理
      if (deepForm.useTime) {
        let useTimeStr = deepForm.useTime[0] + '-' + deepForm.useTime[1]
        deepForm.useTime = useTimeStr
      }
      deepForm.orderType = this.formState.required.orderType
      deepForm.path = resourcePath
      deepForm.applyResource = JSON.stringify({ resourceId, colIds })
      this.loading = true
      let item = this.resourceList.filter((i) => i.resourceId === resourceId)[0]
      try {
        console.log(deepForm, 'deepForm111')
        const { code, message } = await this.$api.bizApi.resApply.dataRequest(deepForm)
        if (code === '200') {
          this.batchFinish++
          this.errorList.push({
            resourceName: item.resourceName,
            resourceType: item.resourceType,
            state: '成功',
            message: ''
          })
        } else {
          this.batchError++
          this.isProgressError = true
          this.errorList.push({
            resourceName: item.resourceName,
            resourceType: item.resourceType,
            state: '失败',
            message: message
          })
        }
      } catch (e) {
        this.batchError++
        this.isProgressError = true
        this.errorList.push({
          resourceName: item.resourceName,
          resourceType: item.resourceType,
          state: '失败',
          message: e.message
        })
        this.$message({
          message: e.message,
          type: 'error'
        })
        console.log('error2', this.errorList)
      }
    },
    // -------------------------------------------------------------表格样式
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.resourceListTable.doLayout()
      })
    },
    clickRow(row, event, column) {
      this.$refs.resourceListTable.toggleRowSelection(row)
    },
    selectMainTableRow(selection) {
      this.selectTableRow = Object.assign([], selection)
    },
    // -----------------------------------------------------------------自定义选择定义字段
    async openDialogSubscribe(row, index) {
      this.dialogVisibleSubscribe = true
      this.subscribeListTemp = await this.resourceList[index].columnList
      this.nowIndex = index
      // 判断是否已经选中了，对列表数据进行选中
      if (this.subscribeList.get(this.resourceList[index].resourceId)) {
        for (let temp in this.subscribeListTemp) {
          if (
            this.subscribeList
              .get(this.resourceList[index].resourceId)
              .indexOf(this.subscribeListTemp[temp].colId) > -1
          ) {
            this.$nextTick(() => {
              this.$refs.subscribeTable.toggleRowSelection(this.subscribeListTemp[temp], true)
            })
          }
        }
      } else {
        for (let temp in this.subscribeListTemp) {
          this.$nextTick(() => {
            this.$refs.subscribeTable.toggleRowSelection(this.subscribeListTemp[temp], true)
            // 此时选中所有同时要存入list中 防止用户直接点关闭而非保存导致无法存入colIds
            const columnIds = []
            for (let column of this.selectTableRow1) {
              columnIds.push(column.colId)
            }
            this.subscribeList.set(this.resourceList[this.nowIndex].resourceId, columnIds)
          })
        }
      }
    },
    closedDialogSubscribe() {
      this.dialogVisibleSubscribe = false
    },
    clickRow1(row, event, column) {
      this.$refs.subscribeTable.toggleRowSelection(row)
    },
    selectMainTableRow1(selection) {
      this.selectTableRow1 = Object.assign([], selection)
    },
    saveDialogSubscribe() {
      // 判断是否选择了主键
      let hasPk = false
      this.$refs.subscribeTable.selection.forEach((e) => {
        if (e.fieldIspkCode === '1') {
          hasPk = true
        }
      })
      if (!hasPk) {
        this.$message('请选择主键')
        return
      }
      if (this.selectTableRow1.length) {
        const columnIds = []
        for (let column of this.selectTableRow1) {
          columnIds.push(column.colId)
        }
        this.subscribeList.set(this.resourceList[this.nowIndex].resourceId, columnIds)
        this.closedDialogSubscribe()
      } else {
        this.subscribeList.delete(this.resourceList[this.nowIndex].resourceId)
        this.closedDialogSubscribe()
      }
    },
    // -----------------------------------------新增使用方系统
    openDialogAdd() {
      this.dialogVisibleAdd = true
    },
    closedDialogAdd() {
      this.dialogVisibleAdd = false
      this.resetDialogAdd()
    },
    resetDialogAdd() {
      this.systemData = {
        systemName: undefined,
        systemBiz: undefined,
        systemMan: undefined,
        systemPhone: undefined,
        systemUrl: undefined,
        systemDesc: undefined,
        isEncryption: '1'
      }
      if (this.$refs.formRef1) {
        // 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
        this.$refs.formRef1.resetFields()
      }
    },
    async saveDialogAdd() {
      this.$refs['formRef1'].validate((valid) => {
        if (valid) {
          this.saveLoading = true
          const formData = Object.assign({}, this.systemData)
          this.$api.bizApi.resApply
            .saveSystem(formData)
            .then((res) => {
              this.closedDialogAdd()
              this.getSystemList(res.data)
              this.saveLoading = false
            })
            .catch((e) => {
              this.saveLoading = false
              this.$message({
                message: e.message
              })
            })
        }
      })
    },
    // ------------------------------------------自定义订阅参数字段
    async openDialogParams(row, index) {
      this.dialogVisibleParam = true
      this.paramListTemp = await this.resourceList[index].trsServiceFuncParams
      this.nowIndex1 = index
      // 判断是否已经选中了，对列表数据进行选中
      if (this.paramList.get(this.resourceList[index].resourceId)) {
        for (let temp in this.paramListTemp) {
          if (
            this.paramList
              .get(this.resourceList[index].resourceId)
              .indexOf(this.paramListTemp[temp].paramId) > -1
          ) {
            this.$nextTick(() => {
              this.$refs.paramTable.toggleRowSelection(this.paramListTemp[temp], true)
            })
          }
        }
      } else {
        for (let temp in this.paramListTemp) {
          this.$nextTick(() => {
            this.$refs.paramTable.toggleRowSelection(this.paramListTemp[temp], true)
            // 此时选中所有同时要存入list中 防止用户直接点关闭而非保存导致无法存入colIds
            const columnIds = []
            for (let column of this.selectTableRow2) {
              columnIds.push(column.paramId)
            }
            this.paramList.set(this.resourceList[this.nowIndex1].resourceId, columnIds)
          })
        }
      }
    },
    closedDialogParam() {
      this.dialogVisibleParam = false
    },
    clickRow2(row, event, column) {
      this.$refs.paramTable.toggleRowSelection(row)
    },
    selectMainTableRow2(selection) {
      this.selectTableRow2 = Object.assign([], selection)
    },
    saveDialogParam() {
      if (this.selectTableRow2.length) {
        const columnIds = []
        for (let column of this.selectTableRow2) {
          columnIds.push(column.paramId)
        }
        this.paramList.set(this.resourceList[this.nowIndex1].resourceId, columnIds)
        this.closedDialogParam()
      } else {
        // this.paramList.delete(this.resourceList[this.nowIndex1].resourceId);
        this.paramList.set(this.resourceList[this.nowIndex1].resourceId, [])
        this.closedDialogParam()
      }
    },
    // ----------------------------------附件上传
    selectFile() {
      this.$refs.fileInput.click()
    },
    handleFileChange(event) {
      console.log(this.type, 'type')
      if (this.type === 'power') {
        let file = event.target.files[0]
        if (this.checkFile(file)) {
          return
        }
        this.uploadLoading = true
        let tempData = Object.assign({}, { annex: file })
        this.$api.bizApi.upload
          .annexUploadFile(tempData)
          .then((res) => {
            console.log(res, 'res')
            this.formState.required.powerPath = res.data[0].annexPath
              ? res.data[0].annexPath
              : res.data[0].filPath
            this.formState.required.fileName = res.data[0].annexName
            this.formState.required.fileType = res.data[0].annexType
            this.formState.required.fileSize = res.data[0].annexSize
            this.formState.required.realName = res.data[0].annexName
            this.uploadLoading = false
          })
          .catch((e) => {
            this.uploadLoading = false
            this.$message({
              message: e.message
            })
          })
      } else {
        let file = event.target.files[0]
        if (this.checkFile(file)) {
          return
        }
        this.uploadLoading = true
        let tempData = Object.assign({}, { file: file, uploadType: '1' })
        this.$api.bizApi.upload
          .uploadFile(tempData)
          .then((res) => {
            console.log(res, 'res')
            this.formState.required.resourcePath = res.data.path ? res.data.path : res.data.filPath
            this.formState.required.fileName = res.data.fileName
            this.formState.required.fileType = res.data.fileType
            this.formState.required.fileSize = res.data.fileSize
            this.formState.required.realName = res.data.realName
            this.uploadLoading = false
          })
          .catch((e) => {
            this.uploadLoading = false
            this.$message({
              message: e.message
            })
          })
        console.log(this.formState.required, 555)
      }
    },
    checkFile(file) {
      let suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
      let format = ['doc', 'docx', 'xls', 'xlsx', 'pdf', 'zip']
      if (format.indexOf(suffix) < 0) {
        // this.$errMsgBox.show({
        //   text: "文件格式不正确，请检查"
        // });
        this.$message('文件格式不正确，请检查')
        return true
      }
      if (this.containsTheState === '1') {
        if (file.size > 10 * 1024 * 1024) {
          // this.$errMsgBox.show({
          //   text: "上传的文件不能超过20M"
          // });
          this.$message('上传的文件不能超过10M')
          return true
        }
      } else {
        if (file.size > 20 * 1024 * 1024) {
          // this.$errMsgBox.show({
          //   text: "上传的文件不能超过20M"
          // });
          this.$message('上传的文件不能超过20M')
          return true
        }
      }
      if (file.size === 0) {
        // this.$errMsgBox.show({
        //   text: "文件为空，请检查"
        // });
        this.$message('文件为空，请检查')
        return true
      }
    },
    // 附件下载
    downloadOrderTipFile(row) {
      this.$api.bizApi.resList
        .downLoadOrderTipFile({ id: row.orderTipFile.attachId })
        .then((res) => {
          FileUtils.fileDownload([res.data], row.orderTipFile.fileName)
        })
        .catch((e) => {
          // this.$message.show({
          //   text: '下载文件失败',
          //   error: e
          // })
        })
    },
    getRowKeys2(row) {
      return row.paramId
    },
    // 下载共享协议
    downloadReport() {
      let link = document.createElement('a')
      link.setAttribute('download', '共享协议.pdf')
      let href = window.location.href
      let url = href.split('#')[0]
      console.log(url)
      if (url.includes(`${config.systemCode}`) || url.includes('gxmh')) {
        console.log('1')
        link.href = `./gxxy.pdf`
      } else {
        console.log('2')
        link.href = `./${config.systemCode}/gxxy.pdf`
      }
      link.click()
    },
    getColumnTypeMap() {
      let dictType = 'ZYML_FIELD_TYPE'
      if (getDictCache(dictType)) {
        let data = getDictCache(dictType)
        for (let key in data) {
          this.columnType.set(key, data[key])
        }
      } else {
        this.$api.dict.getDictByType({ type: dictType }).then((res) => {
          setDictCache(dictType, res.data)
          for (let key in res.data) {
            this.columnType.set(key, res.data[key])
          }
        })
      }
    },
    columnTypeFormatter(row, column, cellValue, index) {
      return this.columnType.get(cellValue)
    },
    getContent() {
      if (config.sharingAgreementContent && typeof config.sharingAgreementContent === 'function') {
        this.content = config.sharingAgreementContent(this.aName, this.currentUser.unitName)
      } else {
        this.content = `

甲方：${this.aName}

乙方：${this.currentUser.unitName || ''}

江西省政务数据共享和开放平台（以下简称“共享开放平台”）主要实现全省政务数据资源整合汇聚和共享开放，提高行政效率，提升服务水平。为更好的规范部门数据资源共享工作，保障数据使用的安全性、规范性和有效性，经双方友好协商，签订如下协议。

一、协议依据

1.《国务院关于印发促进大数据发展行动纲要的通知》(国发〔2015〕50号)。

2.《国务院关于印发政务信息资源共享管理暂行办法的通知》(国发〔2016〕51号)。

3.《国务院关于加快推进“互联网+政务服务”工作的指导意见》(国发〔2016〕55号)。

4.《江西省人民政府关于印发政务信息资源共享管理实施细则的通知》(赣府发〔2017〕2号)。

5.《江西省人民政府关于印发加快推进“互联网+政务服务”工作实施方案的通知》(赣府字〔2017〕2号)。

6.《江西省人民政府办公厅印发关于加快推进全省政务数据共享工作方案的通知》(赣府厅字[2018]95号)。

二、保密信息

1. 在项目中所涉及的以一定形式记录、保存的文件、资料、图表和数据等各类信息资源。

2. 甲方在项目实施中为乙方及乙方工作人员提供必要的数据、文件、用户名、口令和资料等。

3. 其他甲方合理认为，并告知乙方属于保密的内容。

三、甲方的权力和义务

1．甲方为共享开放平台数据提供及使用单位。

2．甲方以共享为原则，不共享为例外，负责将各类政务信息资源按照“无条件共享”“有条件共享”“不予共享”等方式在共享开放平台上进行注册、挂接，涉及国家秘密和安全的，按相关法律法规执行。

3．甲方作为信息资源提供方，需要保证提供给共享开放平台信息资源的有效性、完整性、真实性。不能对共享开放平台正在使用的信息资源进行随意删除、修改，如有变更，应通过共享开放平台提前告知资源使用方。

4．甲方唯一拥有对共享开放平台中所提供信息资源的使用和审核的管理权。第三方订阅甲方信息资源经甲方审核通过后，才能通过乙方所建的共享开放平台提供给申请的第三方；对于为“无条件共享”的信息资源，第三方订阅甲方资源不需要经过审核；对于“不予共享”的信息资源，第三方不能订阅甲方资源。乙方会留存所有资源使用日志，供甲方查询。

5．甲方因工作需要有权向共享开放平台提出信息资源共享需求，在共享交换平台上进行订阅申请获取其他单位信息资源，乙方积极配合甲方和信息资源提供部门进行对接。

6．甲方唯一拥有对其提供在共享开放平台中的信息资源向互联网开放的权限，经甲方授权开放的信息资源的真实性、及时性，由甲方负责。

四、乙方的权力和义务

1．乙方为共享开放平台建设及运维管理单位, 负责共享开放平台稳定运行。

2．乙方应根据甲方需求，对其提供的信息资源进行传输和存储加密，以及敏感数据的脱敏服务，确保信息资源的安全性。

3．乙方需对共享开放平台的信息资源的使用者（包括甲方和乙方运维人员）进行严格的访问权限控制，加强日志审计等安全防护措施，同时建立安全应急处理和容灾恢复机制。

4．乙方作为共享开放平台项目建设及运维管理单位，有权要求甲方提供在履行职责过程中制作或获取的，以一定形式记录、保存的文件、资料、图表和数据等各类信息资源，包括政务部门直接或通过第三方依法采集的、依法授权管理的和因履行职责需要依托政务信息系统形成的信息资源等。

5．乙方有义务对甲方提供的各类信息资源进行有效保护。包括如下措施：

（1）签订保密协议。和本单位内部工作人员、项目承建公司及具体实施人员签订保密协议，从法律层面约束相关工作人员不得违规私自留存与数据共享和开放平台相关的任何形式的文本、电子资料。

（2）制定共享和开放的规则。属于无条件共享类的信息资源，第三方部门在共享平台上直接获取；属于有条件共享类的信息资源，第三方部门通过共享开放平台线上向甲方提出申请订阅，经过甲方审核通过后才能获取数据，对不予共享的，甲方应说明理由。

（3）乙方对甲方提出的共享、开放、加密、脱敏等需求，应及时响应，并提供服务。

五、数据使用范围

甲方对于其提供的所有信息资源，可限定其他使用部门的使用范围、使用期限、使用方式。任何使用方未经甲方授权不得超出甲方规定的使用范围以及给第三方使用。

六、数据保密责任范围

1．甲方保密范围为把资源和目录注册到开放平台上之前的保密和申请后获得的资源的保密。

2．乙方负责整个共享开放平台中所有信息资源的安全和保密工作。

3．甲方通过共享开放平台使用第三方的信息资源时，必须遵照第三方的约定，做好相关的信息资源安全和保密工作。

七、违约责任

1．甲方作为共享开放平台数据的使用者，如因数据被泄露而造成严重后果的，应追究部门领导及当事人法律责任。

2．乙方认真遵守国家保密法律、法规和规章制度及信息安全保护要求，履行保密义务。如因乙方原因造成甲方提供的信息泄密，应追究部门领导及当事人法律责任。

八、协议变更和终止条款

1．由于国家或省有关政策法规变动而必须修改或中止协议，双方协商后，按有关政策法规执行。

2．本协议的任何修改都应该以书面形式提出，经双方授权代表认可、签字盖章后，可与本协议享有同样的法律效力。

3．因地震等不可抗力因素导致乙方共享开放平台服务中断，乙方不承担相应事故责任。

九、其他事项

1．本协议书未尽事项，由双方协商解决。

2．本协议自双方签定之日起生效。

3．本协议书一式二份，甲方双方各一份。

4．如工作需要更改或终止本协议，需甲乙双方同时确认。



---------------[以下无正文，本协议书结束]---------------



`
      }
      const contentText = document.querySelector('#content')
      contentText.innerText = this.content
    }
  },
  mounted() {
    this.getColumnTypeMap()
  }
}
</script>

<style scoped lang="scss">
>>> .el-scrollbar__wrap {
  overflow-x: hidden !important;
  overflow-y: scroll;
}
//>>> .el-table th.el-table__cell > .cell {
//  color: #ffffff;
//}
.el-steps--simple {
  padding: 11px 20%;
}
.icsp-scrollbar-wrapper {
  height: calc(100vh - 15vh - 200px - 50px);
}
.tip-div {
  position: relative;
  top: -20px;
  .level-tip {
    color: #666;
    position: absolute;
    top: 14px;
    font-size: 14px;
  }
}
.tip-box {
  margin-top: 10px;
  margin-bottom: 10px;
  color: #333333;
  .orange {
    color: #ff6114;
  }
}
.nowrap {
  width: 260px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-dialog__header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-size: 20px;
  font-weight: bold;
}

/deep/ .el-dialog__body .el-select {
  width: 100%;
}

/deep/ .el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.icsp-ellipsis-line {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .el-link--inner {
  font-size: 14px !important;
}

/deep/ .el-link {
  width: 58px !important;
}
>>> .title-info {
  width: 100%;
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
  justify-content: flex-start;
  align-items: center;
  .title-info-tip {
    margin-top: 10px;
    width: 150px;

    .titleInfo {
      margin-bottom: 0 !important;
      border: none !important;
    }
  }
}
/deep/.el-table th.el-table__cell {
  background-color: #4eacfe;
  padding: 12px 0;
  .cell {
    background-color: #4eacfe;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
