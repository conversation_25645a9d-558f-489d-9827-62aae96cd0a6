<!--
 * @Description: 组件：字典项复选框/单选框按钮组
 * @Author: 刘芸
 * @Date: 2023-09-15 10:31:50
 * @LastEditTime: 2023-09-18 17:15:24
 * @LastEditors: 刘芸
-->
<template>
  <el-checkbox-group
    v-if="type === 'checkbox'"
    v-model="checkList"
    :min="min"
    :max="max"
    :size="size"
    :disabled="disabled"
    :text-color="textColor"
    :fill="fill"
    @change="hdlChg">
    <el-checkbox v-for="item in options" :label="item.code + ''" :disabled="item.enabled === '0'" :key="item.dictId" :border="border">{{item.name}}</el-checkbox>
  </el-checkbox-group>
  <el-radio-group
    v-else
    v-model="radioRst"
    :size="size"
    :disabled="disabled"
    :text-color="textColor"
    :fill="fill"
    @change="hdlInput"
    @input="hdlInput">
    <el-radio v-for="item in options" :label="item.code + ''" :disabled="item.enabled === '0'" :key="item.dictId" :border="border">{{item.name}}</el-radio>
  </el-radio-group>
</template>

<script>
  import { setDictCache, getDictCache } from "@/core/utils/tabCache"
  export default {
    name: 'CheckButton',
    props: {
      value: {
        type: [String, Array, Number]
      },
      dictType: String,
      type: {
        type: String,
        default: 'checkbox'
      },
      data: Array,
      initFunction: Function,
      disabled: {
        type: Boolean,
        default: false
      },
      border: {
        type: Boolean,
        default: false
      },
      size: {
        type: String,
        default: 'small'
      },
      min: {
        type: Number
      },
      max: {
        type: Number
      },
      textColor: {
        type: String
      },
      fill: {
        type: String
      }
    },
    data () {
      return {
        checkList: [],
        radioRst: 99999999,
        options: [],
        optionsArr: []
      };
    },
    methods: {
      hdlInput(label) {
        // 选中的 Radio label 值
        this.$emit("change", this.radioRst)
      },
      hdlChg(value) {
        // 更新后的值
        this.$emit("change", [...this.checkList])
      },
      init(value) {
        this.radioRst = undefined;
        this.checkList = [];
        if(this.type === 'checkbox') {
          this.checkList = value;
        } else {
          this.radioRst = value;
        }
      }
    },
    watch: {
      checkList(val, oldVal) {
        this.$emit("input", val);
      },
      radioRst(val, oldVal) {
        this.$emit("input", val);
      },
      value: {
        handler(newVal, oldVal) {
          if(this.type === 'checkbox') {
            this.checkList = newVal;
          } else {
            this.radioRst = newVal;
          }
        },
        immediate: true
      },
      data: {
        deep: true,
        immediate: true,
        handler: function(newVal, oldVal) {
          if(this.initFunction) {
            this.initFunction(newVal)
          }
          this.options = newVal
        }
      }
    },
    created() {
      this.options = []
      if(this.data) {
        // 若组件外部直接传递了data数据，则取data数据作为字典选项
        this.options = this.data
      } else if(getDictCache(this.dictType)) {
        // 若组件外部传递了字典类型，且存在缓存，则取缓存数据作为字典选项
        this.options = getDictCache(this.dictType)[this.dictType]
      } else if (!this.dictType) {
        // 否则，既没有传data，也没有传字典类型dictType，直接终止
        return false;
      }
      let that = this;
      if((typeof this.options === 'undefined' || this.options.length === 0) && this.dictType) {
        // 若选项范围为空，且传了字典类型dictType，则调API获取字典选项
        this.$api.dict.getDictionaries({types: this.dictType}).then(res => {
          // 字典选项存缓存
          setDictCache(this.dictType, res.data)
          if(this.initFunction) {
            this.initFunction(res.data)
          }
          that.options = res.data[this.dictType];
        })
      }
    }
  }
</script>
