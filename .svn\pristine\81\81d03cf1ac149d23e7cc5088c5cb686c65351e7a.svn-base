<script>
  import { mapState } from 'vuex'
  import {Table} from 'element-ui'

  export default {
    extends: Table, // 继承el-table
    name: 'TablePlus',
    props: {
      id: String, // 表格唯一标识
      resourceCode: String, // 资源编码
      mapper: String, // 后台资源mapper方法路径
      height: {type: String, default: '100%'},
      showHeader: {type: Boolean, default: true}
    },
    data() {
      return {
        resizeObserver: null,
        plus_originColumns: null,
        plus_columns: null,
        plus_fixedColumns: null,
        plus_rightFixedColumns: null,
        // 原始操作列
        operatorCol: null,
        displayWrapper: undefined
      }
    },
    computed: {
        ...mapState({
            currentUser: state => state.user.currentUser
        }),
        /**
         * 获取当前用户cookie名称
         */
        cookieName() {
          return `com.thinvent.${config.systemCode}.${this.currentUser.userName}`
        }
    },
    watch: {
      'layout.scrollY'(newVal, oldVal) {
        // 监听滚动条变化，调整控制操作列显示状态按钮的定位
        this.adjustOptBtnPos();
      },
      'layout.bodyWidth'(newVal, oldVal) {
        this.autoAdjustWidth();
      },
      'layout.headerHeight'(newVal, oldVal) {
        if(this.showHeader) {
          const { headerWrapper, fixedBodyWrapper, rightFixedBodyWrapper } = this.$refs;
          if (fixedBodyWrapper) fixedBodyWrapper.style.top = `${headerWrapper.clientHeight}px`;
          if (rightFixedBodyWrapper) rightFixedBodyWrapper.style.top = `${headerWrapper.clientHeight}px`;
          this.$nextTick(() => {
            this.doLayout()
          })
        }
      },
      data: {
        deep: true,
        immediate: true,
        handler: function(newVal, oldVal) {
          let isShow = this.isShow();
          this.showColumnsExceptOpt(isShow);
        }
      }
    },
    methods: {
      /**
       * 获取操作列显示状态缓存（没有缓存时，默认显示状态）
       */
      isShow() {
        let userCookies = localStorage.getItem(this.cookieName) ? JSON.parse(localStorage.getItem(this.cookieName)) : {};
        let tableCols = userCookies[this.id] || {}
        if(typeof tableCols.optShowStatus === 'undefined') {
          return true;
        } else {
          return tableCols.optShowStatus;
        }
      },
      // 取列表字段标签属性（type取值范围"label"或"property"）
      getColumnsProp(type) {
        let arr = [];
        this.getRealColumns().forEach((item, index) => {
          if (!item.label || !item.property) {
            return false;
          }
          arr.push(type && type === "label" ? item.label : {
            column: item,
            prop: item.property
          });
        });
        return arr;
      },
      getRealColumns() {
        return this.store.states.originColumns;
      },
      getAllColumns() {
        return this.plus_originColumns;
      },
      getAllColumnsFilterDynamic() {
        if(this.data && this.data.length > 0) {
          let cols = Object.keys(this.data[0])
          return this.plus_originColumns.filter(c => cols.findIndex(d => d === c.property) !== -1)
        } else {
          return this.plus_originColumns
        }
      },
      // 缓存操作列
      getLastColInfo() {
        this.operatorCol = this.store.states._columns[this.store.states._columns.length - 1];
      },
      async filterColsByCookie() {
        if(!this.id) {
          return;
        }
        if(localStorage.getItem(this.cookieName)) {
          let userCookies = JSON.parse(localStorage.getItem(this.cookieName));
          let cols = userCookies[this.id] && userCookies[this.id].showCols;
          if(cols) {
            await this.showColumns(cols);
          } else {
            this.updateOptBtnPos()
          }
        } else {
          this.updateOptBtnPos()
        }
      },
      updateOptBtnPos() {
        // 操作列显示状态变更
        this.$nextTick(() => {
          const isShow = this.isShow();
          if(isShow === false) {
            this.showColumnsExceptOpt(isShow);
          } else {
            this.adjustOptBtnPos();
          }
        });
      },
      // 调整控制操作列显示状态按钮的定位
      adjustOptBtnPos() {
        if(this.id && this.fixWrapper && this.displayWrapper) {
          setTimeout(() => {
            let rightFixedWrapper = this.$refs.rightFixedWrapper;
            let span = (this.displayWrapper && this.displayWrapper.childNodes[0]) || null;
            if(rightFixedWrapper) {
              span.classList.remove('show');
              this.displayWrapper.style.right = Number(rightFixedWrapper.style.width.split("px")[0]) +
                                Number(rightFixedWrapper.style.right.split("px")[0]) + "px";
            } else {
              span.classList.add('show');
              this.displayWrapper.style.right = this.layout.scrollY ? (this.border ? this.layout.gutterWidth : (this.layout.gutterWidth || 0)) + 'px' : '0';
            }
          }, 0)
        }
      },
      filterCols(allCols, cols) {
        return allCols.filter(c => typeof c.property === 'undefined' || cols.findIndex(d => d === c.property) !== -1 || c.isExtendColumn || c.type === 'selection')
      },
      filterCols2(allCols, cols, isShow) {
        if(isShow) {
          return allCols.filter(c => c.fixed === 'right' || cols.findIndex(d => d === c.property) !== -1 || c.isExtendColumn || c.type === 'selection')
        } else {
          return allCols.filter(c => (c.fixed !== 'right' && cols.findIndex(d => d === c.property) !== -1) || c.isExtendColumn || c.type === 'selection')
        }
      },
      async getColsByDataRule() {
        // 后台API列权限过滤查询参数
        let param = {'resourceCode': this.resourceCode, 'mapper': this.mapper}
        let token = this.Cookies.get(config.cookieName)
        // let token = sessionStorage.getItem(config.cookieName);
        // 发送请求时携带token
        if (token && this.resourceCode && this.mapper) {
          this.$api.authority.getColsByDataRule(param).then(res => {
            if (res.code === "200" && res.data) {
              if (res.data.visibleCols) {
                let cols = res.data.visibleCols.split(',')
                // 展现可见列
                this.store.states.originColumns = this.filterCols(this.store.states.originColumns, cols);
                this.store.states.columns = this.filterCols(this.store.states.columns, cols);
                this.store.states.fixedColumns = this.filterCols(this.store.states.fixedColumns, cols);
                this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
                this.store.states.rightFixedColumns = this.filterCols(this.store.states.rightFixedColumns, cols);
                this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
              } else if (res.data.hiddenCols) {
                // 去除隐藏列
                let cols = res.data.hiddenCols.split(',')
                cols.forEach(hc => {
                  if(this.store.states.originColumns.findIndex(c => c.property === hc) > -1) {
                    this.store.states.originColumns.splice(this.store.states.originColumns.findIndex(c => c.property === hc), 1)
                  }
                  if(this.store.states.columns.findIndex(c => c.property === hc) > -1) {
                    this.store.states.columns.splice(this.store.states.columns.findIndex(c => c.property === hc), 1)
                  }
                  if(this.store.states.fixedColumns.findIndex(c => c.property === hc) > -1) {
                    this.store.states.fixedColumns.splice(this.store.states.fixedColumns.findIndex(c => c.property === hc), 1)
                    this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
                  }
                  if(this.store.states.rightFixedColumns.findIndex(c => c.property === hc) > -1) {
                    this.store.states.rightFixedColumns.splice(this.store.states.rightFixedColumns.findIndex(c => c.property === hc), 1)
                    this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
                  }
                })
              }
              this.$nextTick(async function () {
                await this.doLayout()
              })
            }
          })
        }
      },
      async showColumns(params) {
        // 后台API列权限过滤查询参数
        await this.getColsByDataRule()
        if(params) {
          let cols = params
          // 展现可见列
          this.store.states.originColumns = this.filterCols(this.plus_originColumns, cols);
          this.store.states.columns = this.filterCols(this.plus_columns, cols);
          this.store.states.fixedColumns = this.filterCols(this.plus_fixedColumns, cols);
          this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
          this.store.states.rightFixedColumns = this.filterCols(this.plus_rightFixedColumns, cols);
          this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
          await this.doLayout()
          if(this.layout.bodyWidth < this.$el.clientWidth) {
            let i = this.store.states._columns.findIndex((c) => {
              return c.fixed === 'right'
            })
            let index = i !== -1 ? i : this.store.states._columns.length
            if(this.store.states._columns.filter(c => c.isExtendColumn).length === 0) {
              this.store.commit('insertColumn', {
                id: 'ext_' + new Date().getTime(),
                minWidth: 1,
                'isExtendColumn': true,
                sortable: false,
                renderHeader: function(h, { column }) {
                  return <div></div>;
                },
                renderCell: function(h, { $index, column }) {
                  return <div></div>;
                }
              }, index, null);
            }
            this.store.states.originColumns = this.filterCols(this.store.states._columns, cols);
            this.store.states.columns = this.filterCols(this.store.states._columns, cols);
            this.store.states.fixedColumns = this.filterCols(this.store.states.fixedColumns, cols);
            this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
            this.store.states.rightFixedColumns = this.filterCols(this.store.states.rightFixedColumns, cols);
            this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
          }
        }
        await this.doLayout()
        this.updateOptBtnPos()
      },
      // 获取在备选列范围内的动态显示列
      getDynamicShowColsInAlternativeFields() {
        let cols = []
        if(localStorage.getItem(this.cookieName) && this.plus_originColumns) {
          let userCookies = JSON.parse(localStorage.getItem(this.cookieName));
          cols = (userCookies[this.id] && userCookies[this.id].hideCols) || []
        }
        return cols
      },
      async showColumnsExceptOpt(isShow) {
        if(!this.id) {
          return;
        }
        let userCookies = localStorage.getItem(this.cookieName) ? JSON.parse(localStorage.getItem(this.cookieName)) : {};
        let cols = (userCookies[this.id] && userCookies[this.id].showCols) || this.store.states._columns.map(c => c.property);
        if(this.plus_originColumns) {
          let addCols = []
          let delCols = []
          // 获取备选列字段范围
          const hideCols = this.getDynamicShowColsInAlternativeFields()
          if(this.data && this.data.length > 0) {
            // 根据表格的业务数据，过滤掉业务数据中没有返回的设置了labelClassName='baseUI-dynamicShow'的列
            let keys = Object.keys(this.data[0])
            delCols = this.plus_originColumns.filter(col => col.labelClassName === 'baseUI-dynamicShow' && keys.indexOf(col.property) === -1).map(col => col.property)
            // 有业务数据，且不在人为设置的备选列范围里面时，需要加入可见列范围
            addCols = this.plus_originColumns.filter(col => col.labelClassName === 'baseUI-dynamicShow' && keys.indexOf(col.property) !== -1 && hideCols.indexOf(col.property) === -1).map(col => col.property)
          } else {
            // 表格没有业务数据时，默认过滤掉所有labelClassName='baseUI-dynamicShow'的列
            delCols = this.plus_originColumns.filter(col => col.labelClassName === 'baseUI-dynamicShow').map(col => col.property)
          }
          // 删除没有返回业务数据的隐藏列
          delCols.forEach(c => {
            if(cols.indexOf(c) !== -1) {
              cols.splice(cols.indexOf(c), 1)
            }
          })
          // 当业务数据返回了不在备选列范围的隐藏列数据时，加入可见列范围
          addCols.forEach(c => {
            if(cols.indexOf(c) === -1) {
              cols.push(c)
            }
          })
        }
        if(cols) {
          // 后台API列权限过滤查询参数
          await this.getColsByDataRule()
          // 展现可见列
          if(isShow) {
            if(this.$refs.rightFixedWrapper === null || typeof this.$refs.rightFixedWrapper === 'undefined') {
              this.$refs.rightFixedWrapper = this.fixWrapper;
            }
            let noOpt = this.store.states._columns.map(c => c.id).indexOf(this.operatorCol.id) === -1
            this.displayWrapper && noOpt && this.store.states._columns.push(this.operatorCol);
          } else {
            if(this.store.states._columns[this.store.states._columns.length - 1].label === '操作') {
              this.store.states._columns.splice(this.store.states._columns.length - 1)
            }
          }
          this.store.states.originColumns = this.filterCols2(this.plus_originColumns, cols, isShow);
          this.store.states.columns = this.filterCols2(this.plus_columns, cols, isShow);
          this.store.states.fixedColumns = this.filterCols2(this.plus_fixedColumns, cols, isShow);
          this.store.states.rightFixedColumns = this.filterCols2(this.plus_rightFixedColumns, cols, isShow);
          this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
          this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
          // await this.doLayout()
          if(this.layout.bodyWidth < this.$el.clientWidth) {
            let i = this.store.states._columns.findIndex(c => c.fixed === 'right')
            let index = i !== -1 ? i : this.store.states._columns.length
            if(this.store.states._columns.filter(c => c.isExtendColumn).length === 0) {
              this.store.commit('insertColumn', {
                id: 'ext_' + new Date().getTime(),
                minWidth: 1,
                'isExtendColumn': true,
                sortable: false,
                renderHeader: function(h, { column }) {
                  return <div></div>;
                },
                renderCell: function(h, { $index, column }) {
                  return <div></div>;
                }
              }, index, null);
            }
            if(isShow) {
              if(this.$refs.rightFixedWrapper === null || typeof this.$refs.rightFixedWrapper === 'undefined') {
                this.$refs.rightFixedWrapper = this.fixWrapper;
              }
              let noOpt = this.store.states._columns.map(c => c.id).indexOf(this.operatorCol.id) === -1
              this.displayWrapper && noOpt && this.store.states._columns.push(this.operatorCol);
            } else {
              if(this.store.states._columns[this.store.states._columns.length - 1].label === '操作') {
                this.store.states._columns.splice(this.store.states._columns.length - 1)
              }
            }
            this.store.states.originColumns = this.filterCols2(this.store.states._columns, cols, isShow);
            this.store.states.columns = this.filterCols2(this.store.states._columns, cols, isShow);
            this.store.states.fixedColumns = this.filterCols2(this.store.states.fixedColumns, cols, isShow);
            this.store.states.rightFixedColumns = this.filterCols2(this.store.states.rightFixedColumns, cols, isShow);
            this.store.states.fixedLeafColumnsLength = this.store.states.fixedColumns.length;
            this.store.states.rightFixedLeafColumnsLength = this.store.states.rightFixedColumns.length;
          }
          // await this.doLayout()
          this.adjustOptBtnPos()
        }
        // 缓存操作列显示状态
        userCookies[this.id] = userCookies[this.id] || {}
        userCookies[this.id].optShowStatus = isShow;
        localStorage.setItem(this.cookieName, JSON.stringify(userCookies))
        this.$nextTick(() => {
          this.doLayout()
        })
      },
      autoAdjustWidth() {
        if(this.layout.bodyWidth < this.$el.clientWidth) {
          let i = this.store.states._columns.findIndex((c) => {
            return c.fixed === 'right'
          })
          let index = i !== -1 ? i : this.store.states._columns.length
          if(this.store.states._columns.filter(c => c.isExtendColumn).length === 0) {
            this.store.commit('insertColumn', {
              id: 'ext_' + new Date().getTime(),
              minWidth: 1,
              'isExtendColumn': true,
              sortable: false,
              renderHeader: function(h, { column }) {
                return <div></div>;
              },
              renderCell: function(h, { $index, column }) {
                return <div></div>;
              }
            }, index, null);
          }
          let cols = []
          if(this.id) {
            if(localStorage.getItem(this.cookieName)) {
              let userCookies = JSON.parse(localStorage.getItem(this.cookieName));
              cols = (userCookies[this.id] && userCookies[this.id].showCols) || [];
            }
          }
          if(cols && cols.length > 0) {
            this.store.states.originColumns = this.filterCols(this.store.states._columns, cols);
            this.store.states.columns = this.filterCols(this.store.states._columns, cols);
          } else {
            this.store.states.originColumns = this.store.states._columns.filter(c => this.store.states.originColumns.filter(c2 => c2.id === c.id).length > 0 || c.isExtendColumn || c.type === 'selection')
            this.store.states.columns = this.store.states._columns.filter(c => this.store.states.columns.filter(c2 => c2.id === c.id).length > 0 || c.isExtendColumn || c.type === 'selection')
          }
          let columns = this.store.states._columns.filter(c => c.isExtendColumn)
          columns.length > 0 && delete columns[0].width
          this.doLayout()
        } else if(this.layout.bodyWidth > this.$el.clientWidth) {
          let index = this.store.states.originColumns.findIndex(c => { return c.isExtendColumn })
          if(index >= 0 && this.$el.clientWidth > 0) {
            this.store.states.originColumns = this.store.states.originColumns.slice(0, index).concat(this.store.states.originColumns.slice(index + 1))
            this.store.states.columns = this.store.states.columns.slice(0, index).concat(this.store.states.columns.slice(index + 1))
          }
          let columns = this.store.states._columns.filter(c => c.isExtendColumn)
          columns.length > 0 && delete columns[0].width
        }
      }
    },
    created() {
      // 后台API列权限过滤查询参数
      this.getColsByDataRule()
      this.$nextTick(() => {
        this.fixWrapper = this.$refs.rightFixedWrapper;
        if(this.fixWrapper && this.id) {
          let div = document.createElement("div");
          div.className = "displayWrapper";
          let span = document.createElement("span");
          div.appendChild(span);
          var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
          svg.setAttribute('width', '20');
          svg.setAttribute('height', '20');
          svg.classList.add("svg-icon");
          span.appendChild(svg);
          var use = document.createElementNS('http://www.w3.org/2000/svg', 'use');
          use.setAttributeNS('http://www.w3.org/1999/xlink', "xlink:href", "#icon-right");
          svg.appendChild(use);
          span.addEventListener('click', function(e) {
            if(this.$refs.rightFixedWrapper === null || typeof this.$refs.rightFixedWrapper === 'undefined' || this.$refs.rightFixedWrapper.classList.contains('hide')) {
              this.showColumnsExceptOpt(true)
            } else {
              this.showColumnsExceptOpt(false)
            }
          }.bind(this));
          this.$refs.rightFixedWrapper.parentNode.insertBefore(div, this.$refs.rightFixedWrapper.nextSibling);
          this.displayWrapper = div;
        }
      })
    },

    activated() {
      // 赋初始值
      this.filterColsByCookie();
    },
    mounted () {
      // 赋初始值
      this.plus_originColumns = this.store.states.originColumns
      this.plus_columns = this.store.states.columns
      this.plus_fixedColumns = this.store.states.fixedColumns
      this.plus_rightFixedColumns = this.store.states.rightFixedColumns
      this.filterColsByCookie();
      this.getLastColInfo();
      this.resizeObserver = new ResizeObserver(entries => {
        // 计算是否需要添加空白列
        this.autoAdjustWidth()
        // 后台API列权限过滤查询参数
        // this.getColsByDataRule()
      });
      this.resizeObserver.observe(this.$el)
    },
    beforeDestroyed () {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }
    }
  }
</script>

<style type="scss" scoped>
>>>.displayWrapper {
  width: 25px;
  height: 100%;
  top: 0;
  position: absolute;
  z-index: 5;
  display: flex;
  align-items: center;
  &:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 1px;
    box-shadow: 0 0 10px rgba(0,0,0,.18);
    display: none;
  }
}
>>>.displayWrapper  span {
  width: 25px;
  height: 47px;
  /* border-radius: 0 25% 25% 0; */
  box-shadow: -2px 0px 8px 0px rgba(195,195,195,0.5);
  border-radius: 6px 0px 0px 6px;
  background-color: #FFF;
  /* display: flex; */
  display: none;
  align-items: center;
  justify-content: end;
  cursor: pointer;
}
>>>.displayWrapper span.show {
  transform: rotateY(180deg);
  border-radius: 0px 6px 6px 0px;
}
>>>.displayWrapper span svg {
  font-size: 12px;
  /* width: 1em; */
  height: 1em;
  fill: currentColor;
}
>>>.displayWrapper:hover span {
  display: flex;
}
>>>.displayWrapper:hover:after {
  display: flex;
}
body.thinvent_darken .el-table >>>.displayWrapper span {
  background-color: #1287fc;
}
>>>.el-table__fixed-right:hover + .displayWrapper span,
>>>.el-table__fixed-right:hover + .displayWrapper:after,
>>>.el-table__fixed-right:hover + .el-table__fixed-right-patch + .displayWrapper span,
>>>.el-table__fixed-right:hover + .el-table__fixed-right-patch + .displayWrapper:after {
    display: flex;
}
>>>.el-table__fixed-right.hide {
  display: none;
}
</style>
