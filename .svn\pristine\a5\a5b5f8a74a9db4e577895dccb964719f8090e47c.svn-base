import Vue from 'vue'
import SvgIcon from '@/core/components/SvgIcon'// svg component

// register globally
Vue.component('svg-icon', SvgIcon)

// 业务系统的字体图标
const bizReq = require.context('biz/icons/svg', false, /\.svg$/)
// 基础框架的字体图标
const req = require.context('@/core/icons/svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)
requireAll(bizReq)
requireAll(req)
