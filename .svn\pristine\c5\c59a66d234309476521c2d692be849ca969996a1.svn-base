hljs.registerLanguage("flix",function(e){var t={cN:"string",b:/'(.|\\[xXuU][a-zA-Z0-9]+)'/},i={cN:"string",v:[{b:'"',e:'"'}]},n={cN:"title",b:/[^0-9\n\t "'(),.`{}\[\]:;][^\n\t "'(),.`{}\[\]:;]+|[^0-9\n\t "'(),.`{}\[\]:;=]/},c={cN:"function",bK:"def",e:/[:={\[(\n;]/,eE:!0,c:[n]};return{k:{literal:"true false",keyword:"case class def else enum if impl import in lat rel index let match namespace switch type yield with"},c:[e.CLCM,e.CBCM,t,i,c,e.CNM]}});