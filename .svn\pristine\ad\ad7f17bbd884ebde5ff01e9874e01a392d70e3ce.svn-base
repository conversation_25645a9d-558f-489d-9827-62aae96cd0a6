import axios from "@/core/http/axios";

// 选数车订阅时多个资源信息
export const getResourcesInfo = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/getResourcesInfo?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getResourcesInfo`,
    method: "get",
    params: data
  });
};

// 订阅时获取目录信息和资源信息
export const getCatalogInfo = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/getCatalogInfo?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getCatalogInfo`,
    method: "get",
    params: data
  });
};

// 获取联系人列表
export const getLinkManList = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/getLinkManList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getLinkManList`,
    method: "get",
    params: data
  });
};

// 数据申请(创建订单数据)
export const dataRequest = (formData, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/dataRequest?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/dataRequest`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 数据申请(创建订单数据)
export const powerApply = (formData, resourceCode) => {
  return axios({
    url: `/${config.appCode}/powerApply/apply`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 新增资源需求方系统
export const saveSystem = (formData, resourceCode) => {
  return axios({
    url: `/${config.appCode}/powerApply/saveSystem`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 下载订阅附件
export const downLoadOrderTipFile = (data) => {
  return axios({
    url: `/${config.appCode}/annex/download`,
    method: "get",
    params: data,
    responseType: "blob"
  });
};

// 提交留言
export const saveLeaveAMessage = (formData, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/saveLeaveAMessage?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/saveLeaveAMessage`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 通过目录ID获取资源信息
export const getResourceDictList = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/getResourceDictList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/getResourceDictList`,
    method: "get",
    params: data
  });
};
