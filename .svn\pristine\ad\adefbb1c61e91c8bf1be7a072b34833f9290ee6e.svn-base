<template>
  <el-drawer :visible="visible" @close="handleCloseDrawer" :size="size" :title="title" :wrapperClosable="false" class="smart_city__el-drawer" v-bind="$attrs" :destroyOnClose="true" @keydown.enter.prevent modal-append-to-body @opened="handleOpenDrawer">
    <div class="smart_city__el-drawer-body">
      <div class="smart_city__el-drawer-body-content" v-loading="loading">
        <slot> </slot>
      </div>

      <div class="smart_city__el-drawer-body-footer" v-if="footer">
        <div class="smart_city__el-drawer-body-footer-footerForm">
          <slot name="footerForm"> </slot>
        </div>
        <div class="smart_city__el-drawer-body-footer-btn">
          <slot name="footer"> </slot>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { computed, defineComponent } from 'vue'

export default defineComponent({
  name: 'TDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: '70%'
    },
    footer: {
      type: Boolean,
      default: true
    },
    isDestroyWrapper: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['saveCallback', 'submitCallback'],
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.visible,
      set: (val) => {
        emit('update:visible', val)
      }
    })

    const handleCloseDrawer = () => {
      visible.value = false
      if (props.isDestroyWrapper) {
        document.getElementsByClassName('v-modal')[0].style.display = 'none'
      }
      emit('close')
    }
    const handleOpenDrawer = () => {
      emit('open')
    }

    window.addEventListener('popstate', () => {
      visible.value = false
      document.getElementsByClassName('v-modal')[0].style.display = 'none'
    })

    window.addEventListener('pushState', () => {
      visible.value = false
      document.getElementsByClassName('v-modal')[0].style.display = 'none'
    })

    return {
      visible,
      handleCloseDrawer,
      handleOpenDrawer
    }
  }
})
</script>

<style lang="scss">
</style>
