<template>
  <div class="instant-login-container">
    <div :class="thumbnailImg + ' login-container'">
      <p>
        <span class="login-info">上次登录时间：</span><span class="login-info">{{loginInfo.loginTime}}</span>
      </p>
      <p>
        <span class="login-info">上次登录ＩＰ：</span><span class="login-info">{{loginInfo.ip}}</span>
      </p>
      <p>
        <span class="login-info">上次登录地区：</span><span class="login-info">{{loginInfo.ipAddress}}</span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginInfo',
  props: {
    thumbnailImg: String,
    loginInfo: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  methods: {
  }
}
</script>
<style scoped lang="scss">
.instant-login-container {
  width: 285px;
  .orange.login-container, .thinvent.login-container, .blue.login-container, .green.login-container {
    border: 1px solid;
    border-color: #e1e2e6;
    background-color: #f9f9f9;
    padding: 10px 10px;
    margin-top: 20px;
    margin-bottom: 10px;
    p {
      font-size: 12px;
      line-height: 30px;
    }
  }
  .thinvent_darken.login-container {
    border: 1px solid;
    border-color: #282f36;
    background-color: #2d363f;
    padding: 10px 10px;
    margin-top: 20px;
    margin-bottom: 10px;
    p {
      font-size: 12px;
      line-height: 30px;
    }
  }
}
</style>
