<template>
  <el-container class="page-container">
<!--    <el-header v-if="pageMode==='full'"></el-header>-->
    <el-header>
      <!--列表工具栏-->
<!--      <div class="toolbar-wrapper">-->
<!--        <perm-button label="编辑" type="text" :perms="systemCode + 'x99006001'" icon="edit"  @click="handleUpdate"/>-->
<!--        <perm-button label="查看" type="text" :perms="systemCode + 'x99006002'" icon="see"  @click="handleView"/>-->
<!--      </div>-->
      <!--列表查询区-->
      <el-form :inline="true" :model="filters" :size="size">
        <el-form-item label="配置编码">
          <el-input v-model="filters.code" maxlength="100" placeholder="请输入配置编码" clearable @keyup.enter.native="handleFilter"/>
        </el-form-item>
        <perm-button label="查询" type="primary" icon="uims-icon-query" @click="handleFilter"/>
        <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
      </el-form>
    </el-header>
    <!--列表表格区-->
    <el-main>
      <table-plus
        @header-dragend="handleHeaderDrag"
        id="notice"
        :key="0"
        row-key="noticeId"
        :data="list"
        ref="multipleTable"
        border
        fit
        default-expand-all
        v-loading="listLoading"
        tooltip-effect="light"
        stripe
        highlight-current-row
        @row-click="clickRow"
        @selection-change="selectMainTableRow">
        <el-table-column type="selection" width="60" align="center"></el-table-column>
        <el-table-column label="配置编码" width="220" prop="code"></el-table-column>
        <el-table-column label="配置名称" width="220" prop="settingName"></el-table-column>
        <el-table-column label="配置值" width="100" prop="value"></el-table-column>
        <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center" width="220">
          <template slot-scope="scope">
            <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
          </template>
        </el-table-column>
      </table-plus>
    </el-main>
    <el-footer>
      <table-footer ref="tableFooter"
                    :showToolBar="true"
                    excelName="用户设置"
                    :showPage="true"
                    :tableRef="this.$refs.multipleTable"
                    @sizeChange="handleSizeChange"
                    @currentChange="handleCurrentChange"
                    :currentPage="filters.currentPage"
                    :pageSizes="[10, 20, 50, 100]"
                    :pageSize="filters.pageSize"
                    url="/msgUserConfig/list"
                    :filters="filters"
                    :total="total">
      </table-footer>
    </el-footer>
    <msg-user-config-dialog
      @getList="getList"
      @closeDialog="closeDialog"
      :dialogModes="dialogModes"
      :dialogFormVisible="dialogFormVisible"
      :ucId="selectedUcId"
      :code="selectCode">
    </msg-user-config-dialog>
  </el-container>
</template>
<script>
  import PermButtonGroup from "@/core/components/PermButtonGroup";
  import PermButton from '@/core/components/PermButton';
  import TablePlus from "@/core/components/TablePlus";
  import MsgUserConfigDialog from "./Dialog/MsgUserConfigDialog";
  import TableFooter from "@/core/components/TablePlus/TableFooter";
  import SelectPlus from "@/core/components/SelectPlus";
  import { resourceCode } from "@/biz/http/settings";

  export default {
    components: {MsgUserConfigDialog, TablePlus, PermButton, TableFooter, SelectPlus, PermButtonGroup},
    data() {
      return {
        queryBt: true,
        systemCode: config.systemCode,
        // 主页面table参数
        size: 'mini',
        list: [],
        listLoading: true,
        total: 0,
        selectTableRow: [],
        filterText: '',
        selectedUcId: undefined,
        selectCode: undefined,
        currentRow: undefined,
        filters: {
          currentPage: 1,
          pageSize: 20,
          code: undefined
        },
        // 表单的参数设置
        dialogFormVisible: false,
        dialogModes: ''
      }
    },
    computed: {
      pageMode() {
        return config.page_mode;
      }
    },
    methods: {
      getButtons(row) {
        let buts = [
          {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99006001'},
          {label: "查看", icon: "see", clickFn: this.handleView, perms: this.systemCode + 'x99006002'}
        ];
        return {
          row: row,
          buttons: buts,
          showNums: 2
        }
      },
      // 监听表头拖拽事件
      handleHeaderDrag(newWidth, oldWidth, column, event) {
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      },
      closeDialog(val) {
        this.dialogFormVisible = val;
      },
      getList() {
        if(!this.queryBt) {
          return;
        }
        this.queryBt = false
        this.listLoading = true;
        this.$api.msgUserConfig.getList(this.filters, resourceCode.msgUserConfig).then((res) => {
          this.queryBt = true
          this.listLoading = false;
          this.list = res.data.records;
          if(this.list && this.list.length > 0) {
            for(let i = 0; i < this.list.length; i++) {
              if(this.list[i].tsysUserConfigRange.type === "enum" && (this.list[i].value || this.list[i].value === 0 || this.list[i].value === '0')) {
                this.list[i].value = this.list[i].tsysUserConfigRange.enumlist[this.list[i].value]
              }
            }
          }
          this.total = res.data.total
        }).catch(res => {
          this.queryBt = true
        })
      },
      handleView(row) {
        this.selectTableRow = [row]
        this.selectedUcId = row.ucId
        this.selectCode = row.code
        this.dialogFormVisible = true
        this.dialogModes = 'view'
      },
      handleUpdate(row) {
        this.selectTableRow = [row]
        this.selectedUcId = row.ucId
        this.selectCode = row.code
        this.dialogFormVisible = true
        this.dialogModes = 'update'
      },
      handleFilter(resetFlag) {
        this.filters.currentPage = 1;
         if(resetFlag === true) {
          this.filters.code = undefined
        }
        this.getList();
      },
      // 点击前部checkbox
      selectMainTableRow(row, event, column) {
        this.selectTableRow = Object.assign([], row)
        this.selectedUcId = this.selectTableRow && this.selectTableRow.length > 0 ? this.selectTableRow[0].ucId : ''
        this.selectCode = this.selectTableRow && this.selectTableRow.length > 0 ? this.selectTableRow[0].code : ''
      },

      // 点击行选中checkbox
      clickRow(row) {
        if(this.currentRow === row) {
          this.currentRow = undefined
          this.$refs.multipleTable.clearSelection();
        } else{
          this.currentRow = row
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(row)
        }
      },
      handleSizeChange(val) {
        this.filters.pageSize = val
        this.filters.currentPage = 1;
        this.getList()
      },
      handleCurrentChange(val) {
        this.filters.currentPage = val
        this.getList()
      }
    },
    created() {
      this.getList()
    }
  }
</script>

<style scoped lang="scss">
</style>
