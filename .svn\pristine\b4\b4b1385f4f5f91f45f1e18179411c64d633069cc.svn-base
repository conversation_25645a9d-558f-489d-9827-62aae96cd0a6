import axios from '@/core/http/axios'

export const delBizDict = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/delBizDict`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const changeEnabled = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/changeEnabled`,
    method: 'post',
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/list`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const save = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/save`,
    method: 'post',
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const update = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/update`,
    method: 'post',
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const del = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/del`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/getById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const saveSubscript = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/saveSubscript`,
    method: 'post',
    data,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getSubscriptByMbId = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/getSubscriptByMbId`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getBizHistory = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/getBizHistory`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getBizHistoryByMhId = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/msgBiz/getBizHistoryByMhId`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
