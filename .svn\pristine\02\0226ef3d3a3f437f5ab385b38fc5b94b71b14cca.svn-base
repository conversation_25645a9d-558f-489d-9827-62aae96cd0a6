<template>
  <el-container class="page-container user-union">
    <el-header>
      <!--列表工具栏-->
      <el-row>
        <el-col :span="24">
          <div class="grid-content title">当前账号已设置联合登录情况如下：
          </div>
        </el-col>
      </el-row>
      <div class="toolbar-wrapper">
        <perm-button label="添加" type="text" icon="add" :perms="systemCode + 'x98003001'" @click="handleCreate" />
        <perm-button label="删除" type="text" icon="delete" :perms="systemCode + 'x98003002'" @click="handleDelete" />
      </div>
    </el-header>
    <el-main>
      <!--列表表格区-->
      <table-plus style="width: 100%" @header-dragend="handleHeaderDrag" :data="list" border fit stripe highlight-current-row v-loading="listLoading" @row-click="clickRow" ref="userUnoinTable" @selection-change="selectMainTableRow">
        <el-table-column type="selection" align="center" width="60"></el-table-column>
        <el-table-column fixed prop="userName" label="用户" width="350" show-overflow-tooltip></el-table-column>
        <el-table-column prop="userType" label="用户类型" width="200" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="enabled" label="是否启用" width="100" align="center"></el-table-column>
        <!-- <el-table-column label="caozuo" width="100" align="center"></el-table-column> -->
      </table-plus>
    </el-main>
    <DialogPlus v-dialogDrag="{ zoom: false }" autoHeight title="新增联合登录账号" :visible.sync="dialogFormVisible" width="400px">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="65px" autocomplete="nope2">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" auto-complete="new-username" maxlength="25" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input type="password" auto-complete="new-password" v-model="temp.password" maxlength="50" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button class="save-btn" type="primary" @click="createData()" :loading="okLoading">
          保存
        </el-button>
      </div>
    </DialogPlus>
  </el-container>
</template>

<script>
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus";
import DialogPlus from "@/core/components/DialogPlus";
import { rsa } from "../../utils/utils"
import TableToolBar from "@/core/components/TablePlus/TableToolBar";
import {resourceCode} from "@/biz/http/settings";
import { setDictCache, getDictCache } from "@/core/utils/tabCache"
export default {
  name: 'system',
  components: {
    PermButton,
    TablePlus,
    TableToolBar,
    DialogPlus
  },
  data() {
    return {
      systemCode: config.systemCode,
      okLoading: false,
      dict: {
        isFlag: undefined,
        userType: undefined
      },
      // 系统List
      list: [],
      listLoading: true,
      // 编辑时的table选择row
      selectTableRow: [],
      // 新增/编辑界面临时数据存放
      temp: {
        username: undefined,
        password: undefined
      },
      // 表单校验规则
      rules: {
        username: [{ required: true, message: '用户名为必填项', trigger: 'blur' }],
        password: [{ required: true, message: '密码为必填项', trigger: 'blur' }]
      },
      // 添加窗口显示控制
      dialogFormVisible: false,
      size: 'small'
    }
  },
  methods: {
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.userUnoinTable.doLayout();
      })
    },
    userTypeFormatter: function (row, column) {
      if (!row || !row.userType || !this.dict.userType) {
        return ''
      }
      for (let item of this.dict.userType) {
        if (item.code === row.userType) {
          return item.name
        }
      }
    },
    enabledFormatter(row, column) {
      if (!row || !row.enabled || !this.dict.isFlag) {
        return ''
      }
      for (let item of this.dict.isFlag) {
        if (item.code === row.enabled) {
          return item.name
        }
      }
    },
    handleCreate() {
      this.dialogFormVisible = true;
      this.temp = {
        username: undefined,
        password: undefined
      };
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          for (let ul of this.list) {
            if (ul.userName === this.temp.username) {
              this.$notify({
                title: '操作失败',
                message: '该用户已被关联',
                type: 'info',
                duration: 2000
              })
              return
            }
          }
          // 对密码加密
          let data = {
            username: this.temp.username,
            password: this.temp.password
          }
          /* data = encrypt({
            data: data,
            key: "thinvent.incloud",
            param: ["password"]
          }) */
          data.password = rsa(data.password)
          this.okLoading = true
          this.$api.userUnion.chenckNameAndPwd(data, resourceCode.userUnion).then((res) => {
            this.okLoading = false
            if (res.data) {
              this.$api.userUnion.unionLoginAdd({ userName: this.temp.username }, resourceCode.userUnion).then((res) => {
                this.dialogFormVisible = false;
                this.getList();
                // 更新联合用户信息
                this.$api.authority.findUnionUser().then(res => {
                  this.$store.commit('setUnionUsers', res.data)
                })
                this.$notify({
                  title: '操作成功',
                  message: '新增联合登录用户成功',
                  type: 'success',
                  duration: 2000
                })
              })
            } else {
              this.$notify({
                title: '操作失败',
                message: '用户名或密码有误',
                type: 'info',
                duration: 2000
              })
            }
          }).catch((res) => {
            this.okLoading = false;
          })
        }
      })
    },
    getList() {
      this.listLoading = true
      this.$api.userUnion.getUnionUsers({}, resourceCode.userUnion).then((res) => {
        this.listLoading = false
        this.list = res.data;
      })
    },
    clickRow(row, event, column) {
      this.$refs.userUnoinTable.toggleRowSelection(row);
    },
    selectMainTableRow(row, event, column) {
      this.selectTableRow = Object.assign([], row);
    },
    loadDict() {
      if(getDictCache("IS_FLAG")) {
        this.dict.userType = getDictCache("IS_FLAG")
        let List = []
        for (let key in this.dict.isFlag) {
          let is = {}
          is.code = key
          is.name = this.dict.isFlag[key]
          List.push(is)
        }
        this.dict.isFlag = List
      } else {
        this.$api.dict.getDictByType({ type: "IS_FLAG" }).then((res) => {
          this.dict.isFlag = res.data
          setDictCache("IS_FLAG", res.data)
          let List = []
          for (let key in this.dict.isFlag) {
            let is = {}
            is.code = key
            is.name = this.dict.isFlag[key]
            List.push(is)
          }
          this.dict.isFlag = List
        });
      }
      if(getDictCache("USER_TYPE")) {
        this.dict.userType = getDictCache("USER_TYPE")
        let List = []
        for (let key in this.dict.userType) {
          let is = {}
          is.code = key
          is.name = this.dict.userType[key]
          List.push(is)
        }
        this.dict.userType = List
      } else {
        this.$api.dict.getDictByType({ type: "USER_TYPE" }).then((res) => {
          this.dict.userType = res.data
          setDictCache("USER_TYPE", res.data)
          let List = []
          for (let key in this.dict.userType) {
            let is = {}
            is.code = key
            is.name = this.dict.userType[key]
            List.push(is)
          }
          this.dict.userType = List
        })
      }
    },
    handleDelete() {
      if (this.selectTableRow && this.selectTableRow.length > 0) {
        let userId = this.selectTableRow[0].userId;
        this.$confirm('您确认要删除该联合登录用户吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.userUnion.unionLoginDel({ userId: userId }, resourceCode.userUnion).then((res) => {
            this.getList();
            // 更新联合用户信息
            this.$api.authority.findUnionUser().then(res => {
              this.$store.commit('setUnionUsers', res.data)
            })
            this.$notify({
              title: '操作成功',
              message: '删除联合登录用户成功',
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {
          this.$notify({
            message: '已取消删除',
            type: 'info',
            duration: 2000
          })
        })
      } else {
        this.$notify({
          title: '提示',
          message: '请选择一个联合登录用户',
          type: 'info',
          duration: 2000
        })
      }
    }
  },
  created() {
    this.getList();
    this.loadDict()
  }
}
</script>
