<template>
  <div>
    <div class="aside-box">
      <div ref="asideBox" style="overflow: hidden">
        <div
          class="aside-item"
          v-for="(item, index) in asideList"
          v-if="item.show"
          :key="index + 'aside'"
          @click="changeItem(item.value)"
        >
          <svg-icon :icon-class="item.icon" />
          <p>{{ item.label }}</p>
          <el-badge
            :value="item.msg"
            :max="99"
            class="item"
            v-if="item.value === 1 && item.msg !== 0"
          />
        </div>
      </div>
      <div class="contract" @click="contract" v-if="visibleAsideList.length > 3">
        <i :class="isReduce ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
      </div>
    </div>
    <!-- 数据订阅 -->
    <!-- <SubscribeDialog
      :dialogVisible.sync="dialogVisibleApply"
      :type="'1'"
      :data="dialogData"
    ></SubscribeDialog> -->
    <!-- 提出需求 -->
    <DemandDialog :visible.sync="demandVisible" />
    <!-- 一键申请 -->
    <ApplyDialog :visible.sync="applyVisible" />
    <!-- 选数车 -->
    <SelectNumDialog :visible.sync="selectNumVisible" @getRefresh="getNewList" />
    <!-- 我的收藏 -->
    <CollectDialog :visible.sync="collectVisible" />
    <!-- 问题反馈 -->
    <FeedbackDialog :dialogVisible.sync="feedbackVisible" />
  </div>
</template>

<script>
// import SubscribeDialog from "@/biz/components/SubscribeDialog";
import CollectDialog from '@/biz/components/CollectDialog'
import DemandDialog from '@/biz/components/DemandDialog'
import ApplyDialog from '@/biz/components/ApplyDialog'
import SelectNumDialog from '@/biz/components/SelectNumDialog'
import FeedbackDialog from '@/biz/components/FeedbackDialog'
import Cookies from 'js-cookie'
import { mapState } from 'vuex'
import { hasPermission } from '@/core/utils/permission.js'
export default {
  name: 'fixedBox',
  components: {
    // SubscribeDialog,
    CollectDialog,
    DemandDialog,
    ApplyDialog,
    SelectNumDialog,
    FeedbackDialog
  },
  props: {
    cataId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogData: undefined,
      isReduce: false, // 是否收缩
      filters: {
        currentPage: 1,
        pageSize: 1000
      },
      total1: 0,
      total2: 0,
      asideList: [
        // { value: 1, label: '收藏夹', icon: 'gxsl-favorites', msg: 100, show: true },
        // { value: 2, label: '数据需求', icon: 'gxsl-dataDemand', msg: 15, show: true },
        { value: 1, label: '选数车', icon: 'shoppingCart', msg: 0, show: hasPermission('sjnl003') },
        // { value: 2, label: "提出需求", icon: "demand", msg: 0 },
        // { value: 3, label: "一键申请", icon: "apply", msg: 0, show: true },
        { value: 4, label: '问题反馈', icon: 'feedback', msg: 0, show: config.showFeedback },
        // { value: 5, label: "客服", icon: "custom", msg: 0 },
        { value: 6, label: '收藏', icon: 'collect', msg: 0, show: true }
      ],
      dialogVisibleApply: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      feedbackVisible: false
    }
  },
  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser,
      userLoginFlag: (state) => state.icsp.userLoginFlag,
      systemConfig: (state) => state.icsp.systemConfig,
      isDataReturnOrg: (state) => state.icsp.isDataReturnOrg
    }),
    // 获取实际可见的按钮列表
    visibleAsideList() {
      return this.asideList.filter((item) => item.show)
    }
  },
  created() {
    this.getNum()
  },
  methods: {
    getNewList() {
      this.getNum()
      this.$emit('onRefreshList')
    },
    // 点击tab跳出响应的弹框
    changeItem(val) {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
      } else {
        switch (val) {
          case 1:
            // 选数车权限检查
            if (hasPermission('sjnl003')) {
              this.selectNumVisible = true
            } else {
              this.$message.warning('您没有访问选数车的权限')
            }
            break
          case 2:
            this.demandVisible = true
            break
          case 3:
            this.applyVisible = true
            break
          case 4:
            this.feedbackVisible = true
            break
          case 5:
            break
          case 6:
            this.collectVisible = true
            break

          default:
            break
        }
      }
    },
    // 点击是否收缩盒子
    contract() {
      this.isReduce ? (this.isReduce = false) : (this.isReduce = true)
      if (this.isReduce) {
        this.$refs.asideBox.style.transition = 'all .3s'
        this.$refs.asideBox.style.height = '170px'
      } else {
        this.$refs.asideBox.style.transition = 'all .3s'
        this.$refs.asideBox.style.height = 85 * this.visibleAsideList.length + 'px'
      }
    },
    getNum() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        return false
      } else {
        this.$api.bizApi.resList.getCartResourceList(this.filters).then((res) => {
          if (res.code === '200') {
            this.total1 = res.data.total
            this.asideList[0].msg = this.total1 + this.total2
          }
        })

        this.$api.bizApi.resList.getCartPowerList(this.filters).then((res) => {
          if (res.code === '200') {
            this.total2 = res.data.total
            this.asideList[0].msg = this.total1 + this.total2
          }
        })
      }
    }
  },
  mounted() {
    window.refreshCountNum = this.getNum
  }
}
</script>
<style scoped lang="scss">
.aside-box {
  position: fixed;
  bottom: 50%;
  // left: 50%;
  // transform: translateX(630px);
  right: 0;
  z-index: 999;
  .aside-item {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    position: relative;
    align-items: center;
    width: 80px;
    height: 80px;
    background-color: #fff;
    box-shadow: -1px 5px 10px 0px rgba(63, 78, 103, 0.05);
    border-radius: 6px;
    padding: 18px 0 9px;
    margin-bottom: 5px;
    cursor: pointer;
    color: #3572ff;
    // transition: all 0.3s;
    .item {
      position: absolute;
      top: 5px;
      right: 0;
    }
    .svg-icon {
      width: 25px;
      height: 25px;
    }
    p {
      font-size: 16px;
      color: #000;
    }
    &:hover {
      background-color: #3572ff;
      color: #fff;
      p {
        color: #fff;
      }
    }
  }
  .contract {
    width: 80px;
    height: 26px;
    background: #ffffff;
    box-shadow: -1px 5px 10px 0px rgba(63, 78, 103, 0.05);
    border-radius: 6px;
    cursor: pointer;
    text-align: center;
    line-height: 26px;
    i {
      color: #3572ff;
      font-size: 14px;
      font-weight: bold;
      transition: all 0.3s;
    }
  }
}
</style>
