.page-container {
  width: 1600px;
  margin: 0 auto;
  min-height: 100vh;

  >>> .el-main {
    padding: 0;
  }

  .basicMes {
    width: 100%;
    height: 300px;
    background: #ffffff;
    border-radius: 2px;
    margin-bottom: 20px;
    padding: 26px 20px 26px 30px;
    position: relative;

    .returnBack {
      position: absolute;
      right: 0;
      top: 0;
      width: 106px;
      height: 37px;
      display: flex;
      align-items: center;
      background-image: url("~@/assets/img/pic-backBg.png");
      background-repeat: no-repeat;
      background-size: 100%;

      img {
        margin-left: 30px;
        margin-right: 7px;
      }

      span {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 17px;
      }
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 24px;
      color: #333333;
      margin-bottom: 16px;
    }

    .centerPart {
      display: flex;
      justify-content: space-between;
      height: calc(100% - 100px);

      .leftPart {
        width: 200px;
        height: 196px;
        padding: 41px 46px 59px 42px;
        background: #f1f5fa;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .middlePart {
        width: calc(100% - 450px);
        padding-top: 11px;

        .singleMes {
          margin-left: 50px;
          font-weight: 400;
          font-size: 16px;
          color: #7d8496;
          line-height: 30px;

          span {
            color: #333333;

            &.qua {
              font-size: 14px;
              color: #ffffff;
              width: 21px;
              height: 23px;
              line-height: 23px;
              display: inline-block;
              text-align: center;
              align-items: center;
              justify-content: center;
              background-image: url("~@/assets/img/pic-qua.png");
            }
          }
        }
      }

      .rightPart {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-end;
        width: 250px;
        text-align: right;

        .itemStatus {
          margin-top: 5px;

          span {
            background: #f6f9ff;
            border-radius: 4px;
            border: 1px solid #d9e1f1;
            font-size: 14px;
            color: #7d8496;
            line-height: 38px;
            padding: 5px 8px;
            margin-left: 8px;
          }
        }

        .itemSourceType {
          margin-top: 16px;
          display: flex;
          justify-content: flex-end;

          .itemSourceItem {
            display: flex;
            align-items: center;
            flex-direction: column;
            margin-left: 11px;

            .iconSchema {
              width: 20px;
              height: 20px;
              background-image: url("~@/assets/img/icon-api.png");
            }

            .iconTable {
              width: 20px;
              height: 20px;
              background: url("~@/assets/img/icon-sdk.png") 100% 100%
                no-repeat;
            }

            span {
              font-size: 14px;
              color: $colors;
              margin-top: 2px;
              width: 54px;
              text-align: center;
            }
          }
        }
      }
    }

    .bottomPart {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 18px;

      .itemNum {
        .num {
          font-size: 16px;
          color: $colors;
          margin-right: 18px;

          span {
            color: #7d8496;
          }
        }
      }

      .itemHandleBtn {
        display: flex;
        align-items: center;
        z-index: 10;

        .btn {
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid $colors;
          text-align: center;
          padding: 9px 10px;
          color: $colors;
          margin-left: 13px;
          cursor: pointer;
          display: flex;
          align-content: center;
          justify-content: center;

          img {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-top: 3px;
            margin-right: 8px;
          }

          &.isOrderFalg {
            background-color: #dedede;
            cursor: not-allowed;
            border: 1px solid #aaaaaa;
            color: #666666;
          }
        }
      }
    }
  }

  .otherMes {
    width: 100%;
    background: #ffffff;
    box-shadow: -1px 5px 10px 0px rgba(63, 78, 103, 0.05);
    border-radius: 2px;

    .mainConte {
      width: 100%;
      display: flex;

      .leftPart {
        width: 290px;
        padding: 30px;

        .tableItem {
          background: #ffffff;
          border: 1px solid #e5e5e5;
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          line-height: 51px;
          text-align: center;
          margin-bottom: 10px;
          cursor: pointer;

          &.active {
            color: $colors;
            border: 1px solid $colors;
          }
        }
      }

      .rightPart {
        width: calc(100% - 290px);
        padding: 30px;
        padding-left: 0;

        .middleTitle {
          font-weight: bold;
          font-size: 16px;
          color: #333333;
          line-height: 24px;
          margin: 25px 0 3px 6px;
        }

        .line {
          width: 12px;
          height: 2px;
          background: #3572ff;
          margin: 0px 0 23px 7px;
        }
      }

      .fullPart {
        width: 100%;
        padding: 30px;

        .border {
          border: 1px solid #e5e5e5;
        }
      }
      .rightMessage {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24px;
        .bottomPart {
          .itemStatus {
            margin-top: 5px;

            span {
              background: #f6f9ff;
              border-radius: 4px;
              border: 1px solid #d9e1f1;
              font-size: 14px;
              color: #7d8496;
              line-height: 38px;
              padding: 5px 8px;
              margin-left: 8px;
            }
          }
        }
      }
    }
  }
}
.titlepart {
  p {
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 2px;
    font-weight: bold;
  }
  .line {
    margin-bottom: 10px;
  }
  .message {
    margin-right: 20px;
    font-weight: 400;
    font-size: 16px;
    color: #7d8496;
    line-height: 30px;

    span {
      color: #333333;

      &.qua {
        font-size: 14px;
        color: #ffffff;
        width: 21px;
        height: 23px;
        line-height: 23px;
        display: inline-block;
        text-align: center;
        align-items: center;
        justify-content: center;
        background-image: url("~@/assets/img/pic-qua.png");
      }
    }
  }
}

.tableHandleBtn {
  display: flex;
  align-items: center;
  justify-content: space-around;

  .btn {
    color: $colors;
    cursor: pointer;
    display: flex;
    justify-content: center;

    img {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-top: 3px;
      margin-right: 4px;
    }
  }
}

>>> .el-tabs {
  .el-tabs__header {
    margin: 0;

    .el-tabs__nav-wrap {
      &::after {
        background-color: #e6e6e6;
      }

      .el-tabs__item {
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 66px;
      }

      .el-tabs__active-bar {
        background: $colors;
        border-radius: 2px;
        margin-left: 20px;
      }

      .el-tabs__nav {
        height: 66px;
        padding-left: 20px;
      }
    }
  }
}

/*服务 - 描述列表*/
/deep/ .el-descriptions-item__label {
  width: 200px !important;
}

/deep/ .el-descriptions-item__content {
  width: 300px !important;
}

.collapseCont {
  padding: 36px;

  .fun-info-p {
    font-size: 16px;
    line-height: 40px;
    margin-bottom: 10px;

    .label {
      color: var(--black);
    }

    .content {
      color: #666666;
    }
  }

  .fun-info-table {
    font-size: 16px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    .info-table {
      display: flex;
      margin-bottom: 10px;
    }

    .label {
      color: var(--black);
    }

    .content {
      color: #666666;
    }
  }
}

>>> .el-collapse-item__header {
  height: 57px;
  line-height: 57px;
  background: #f8f8f8;
  padding-left: 36px;
}

>>> .el-collapse-item.is-active {
  border: 1px solid #9dc0ff;
}

.desc {
  font-weight: 400;
  font-size: 16px;
  color: #666666;
  line-height: 18px;
  margin-left: 10px;
  padding: 22px 0;
}

.right-info {
  max-width: 246px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  .type-info {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    width: 48px;
    height: 48px;
    background: #f6f9ff;
    border-radius: 4px;
    margin-right: 10px;
    color: #7d8496;
    .svg-icon {
      width: 17px;
      height: 17px;
    }
    &.active {
      color: #3572ff;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
