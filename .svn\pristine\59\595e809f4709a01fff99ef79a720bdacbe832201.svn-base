<template>
  <div :class="themeClass">
  <!-- top -->
  <div class="titleInfo">
     <span></span>
    <span>{{ title }}</span>
    <span v-if="animation"> <div class="arrowWidth"  @click="activeChanges"><i  :class="['el-icon-arrow-right',value ? 'isActiveT' :'isActiveF']"></i></div></span>
  </div>
  <!-- slotContent -->
    <div v-if="animation">
    <el-collapse-transition>
    <div v-show="value"> <slot name="content"></slot> </div>
  </el-collapse-transition>
  </div>
  <div v-else>
    <div> <slot name="content"></slot> </div>
  </div>
    </div>
</template>
<script>
export default {
  name: "TitleInfo",
  props: {
     title: {
      type: String,
      default: ""
    },
    value: {
      type: Boolean,
      default: true
    },
    animation: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      themeClass: ''
    }
  },
  methods: {
    activeChanges() {
      this.$emit('input', !this.value)
    }
  },
  mounted() {
    this.themeClass = this.$store.state.app.thumbnail.thumbnailImg
  }
}

</script>

<style scoped lang="scss">
.titleInfo {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  span:nth-child(1) {
    display: inline-block;
    width: 6px;
    height: 14px;
    opacity: 1;
    border-radius: 15px;
    background: rgba(0, 132, 255, 1);
    margin-right: 8px;
  }
  span:nth-child(2) {
    flex: 1;
    color: black;
  }
   span:nth-child(3) {
     cursor: pointer;
    text-align: right;
    color: #606266;
    .arrowWidth {
    width: 100px;
    .isActiveT {
    transform: rotate(90deg);
    transition:transform 0.5s,
    }
  .isActiveF{
   transform: rotate(0deg);
  transition:transform 0.5s,
  }
}
  }
}
.blue > .titleInfo{
  span:nth-child(1) {
    background: #0e88eb;
  }
}
.thinvent_darken > .titleInfo{
  span:nth-child(1) {
    background: #0e88eb;
  }
  span:nth-child(2),span:nth-child(3) {
    color: #fff;
  }
}
.orange > .titleInfo {
  span:nth-child(1) {
    background: #0e88eb;
  }
}
.green > .titleInfo {
  span:nth-child(1) {
    background: #0e88eb;
  }
}
</style>
