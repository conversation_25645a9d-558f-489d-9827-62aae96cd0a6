<template>
  <el-container class="page-container">
    <SelectNumDialog :visible.sync="drawerVisible" drawerSize="100%" />
  </el-container>
</template>

<script>
import SelectNumDialog from '@/biz/components/SelectNumDialog'

export default {
  name: 'ThirdSelectNum',
  components: {
    SelectNumDialog
  },
  data() {
    return {
      drawerVisible: false
    }
  },
  methods: {
    initQuery(row) {
      this.$nextTick(function () {
        this.drawerVisible = true
      })
    }
  },
  activated() {},
  watch: {
    $route: {
      handler(newVal, oldVal) {
        console.log(newVal.path, 'newVal.path')
        if (newVal.path === '/selectNumDialog') {
          this.initQuery(newVal.query)
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
</style>
