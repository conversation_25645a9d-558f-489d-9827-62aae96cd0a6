/**
 * 业务系统全局常量、方法封装模块
 * 通过原型挂在到Vue属性
 * 通过 this.Global 调用
 */
// 路由白名单
// 示例:export const whiteList = ['/index/index']
// 其中/index/index对应菜单权限url:/views/index/index;对应文件路径:biz/views/Index/Index
// const whiteList = config.sso.onoff ? [
//   "/index/index",
//   "/resource/index",
//   "/catalog/index",
//   "/detailCatalog/index",
//   "/detailResource/index",
//   "/detail/index",
//   "/catalog/search",
//   "/resource/search",
//   "/ability/search"
// ] : [];
const whiteList = []
export const workFlowIp = config.workFlowIp
export const flowViewApi = '/tcwp/model/eos_flow_model/process_history_map_view'

export const bizHomeRedirect = '/home'
export const bizRouters = [
  {
    path: '/home',
    name: '首页111',
    component: (resolve) => require([`@/biz/views/Home/Index`], resolve)
  },
  {
    path: '/home/<USER>',
    name: '我的工作',
    component: (resolve) => require([`@/biz/views/Home/components/MyWork`], resolve)
  },
  {
    path: '/home/<USER>',
    name: '专题导航/系统分类',
    component: (resolve) => require([`@/biz/views/Home/components/TopicNav`], resolve)
  },
  {
    path: '/home/<USER>',
    name: '数据Tab',
    component: (resolve) => require([`@/biz/views/Home/components/ResourceTab`], resolve)
  },
  {
    path: '/home/<USER>',
    name: '能力Tab',
    component: (resolve) => require([`@/biz/views/Home/components/AbilityTab`], resolve)
  },
  {
    path: '/home/<USER>',
    name: '热门/最新',
    component: (resolve) => require([`@/biz/views/Home/components/HotAndNew`], resolve)
  },
  {
    path: '/index/index',
    name: '超市',
    component: (resolve) => require([`@/biz/views/Index/Index`], resolve)
  },
  {
    path: '/ability/search',
    name: '能力检索',
    component: (resolve) => require([`@/biz/components/AbilityList`], resolve)
  },
  {
    path: '/resource/index',
    name: '数据资源',
    component: (resolve) => require([`@/biz/views/Resource/Index`], resolve)
  },
  {
    path: '/resource/search',
    name: '数据资源检索',
    component: (resolve) => require([`@/biz/components/ResourceList`], resolve)
  },
  {
    path: '/catalog/index',
    name: '数据目录',
    component: (resolve) => require([`@/biz/views/Catalog/Index`], resolve)
  },
  {
    path: '/catalog/search',
    name: '数据目录检索',
    component: (resolve) => require([`@/biz/components/CatalogList`], resolve)
  },
  {
    path: '/detail/index',
    name: '能力详情',
    component: (resolve) => require([`@/biz/views/Detail/Index`], resolve)
  },
  {
    path: '/detailCatalog/index',
    name: '目录详情',
    component: (resolve) => require([`@/biz/views/detailCatalog/Index`], resolve)
  },
  {
    path: '/detailResource/index',
    name: '资源详情',
    component: (resolve) => require([`@/biz/views/detailResource/Index`], resolve)
  },
  {
    path: '/selectNumDialog',
    name: '选数车弹窗',
    component: (resolve) => require([`@/biz/views/third/thirdSelectNum`], resolve)
  },
  {
    path: '/collectDialog',
    name: '收藏弹窗',
    component: (resolve) => require([`@/biz/views/third/thirdCollect`], resolve)
  }
]

// 水印路由白名单 在白名单中的路由地址不会创建水印
const warterMarkWhiteList = ['/login', '/post/*', '/unit/index']

// 业务模块及操作按钮的权限编码
export const resourceCode = {
  unit: 'uims001001',
  unit_add: 'uims001001001',
  unit_eidt: 'uims001001002',
  unit_del: 'uims001001003',
  unit_view: 'uims001001004',
  post: 'uims001005',
  post_add: 'uims001005001',
  post_eidt: 'uims001005002',
  post_del: 'uims001005003',
  post_view: 'uims001005004',
  // 用户安全
  userInfo: `${config.systemCode}x98001`,
  userSafe: `${config.systemCode}x98002`,
  userSafe_editPass: `${config.systemCode}x98002001`,
  userSafe_eidtMobile: `${config.systemCode}x98002002`,
  userSafe_bindMobile: `${config.systemCode}x98002003`,
  userSafe_bindEmail: `${config.systemCode}x98002004`,
  userSafe_editEmail: `${config.systemCode}x98002005`,
  // 联合登录
  userUnion: `${config.systemCode}x98003`,
  userUnion_add: `${config.systemCode}x98003001`,
  userUnion_del: `${config.systemCode}x98003002`,
  // 委托权限
  userDelegatePriv: `${config.systemCode}x98004`,
  userDelegatePriv_add: `${config.systemCode}x98004001`,
  userDelegatePriv_view: `${config.systemCode}x98004002`,
  userDelegatePriv_stop: `${config.systemCode}x98004003`,
  // 以下为框架模块资源，需要与业务合并
  // 字典管理
  dict: `${config.systemCode}x99001`,
  dict_add: `${config.systemCode}x99001001`,
  dict_eidt: `${config.systemCode}x99001002`,
  dict_del: `${config.systemCode}x99001003`,
  dict_addC: `${config.systemCode}x99001004`,
  dict_eidtC: `${config.systemCode}x99001005`,
  dict_delC: `${config.systemCode}x99001006`,
  dict_reload: `${config.systemCode}x99001007`,
  dict_download: `${config.systemCode}x99001008`,
  // 服务日志
  log: `${config.systemCode}x99002001`,
  log_view: `${config.systemCode}x99002001001`,
  // 异常日志
  exceptionLog: `${config.systemCode}x99002002`,
  exceptionLog_view: `${config.systemCode}x99002002001`,
  // 空间日志
  space_log: `${config.systemCode}x99007001`,
  space_log_view: `${config.systemCode}x99007001001`,
  space_exceptionLog: `${config.systemCode}x99007002`,
  space_exceptionLog_view: `${config.systemCode}x99007002001`,
  // 参数管理
  config: `${config.systemCode}x99003`,
  config_eidt: `${config.systemCode}x99003001`,
  // 编码规则
  codeRule: `${config.systemCode}x99004`,
  codeRule_add: `${config.systemCode}x99004001`,
  codeRule_eidt: `${config.systemCode}x99004002`,
  codeRule_del: `${config.systemCode}x99004003`,
  codeRule_view: `${config.systemCode}x99004004`,
  // 通知公告
  msgNotice: `${config.systemCode}x99005001`,
  // 发件箱
  msgOutbox: `${config.systemCode}x99005002`,
  // 收件箱
  msgInbox: `${config.systemCode}x99005003`,
  // 业务消息配置
  msgBiz: `${config.systemCode}x99005004`,
  // 用户设置
  msgUserConfig: `${config.systemCode}x99006`,
  // 任务模块
  task_base: ``
}

export default {
  whiteList,
  warterMarkWhiteList,
  workFlowIp,
  flowViewApi,
  resourceCode,
  bizRouters,
  bizHomeRedirect
}
