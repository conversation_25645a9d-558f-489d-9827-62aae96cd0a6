<template>
  <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="800px" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :model="temp" label-position="right" label-width="100px">
                   <!--style="overflow: auto">-->
            <!--<el-input v-model="temp.logId" style="display: none"/>-->
            <div class="logView">
              <el-form-item label="系统名称">
                <el-input v-model="sysName" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="所属机构">
                <el-input v-model="temp.unitName" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="服务名称">
                <el-input v-model="temp.serviceId" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="服务IP">
                <el-input v-model="temp.serverIp" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="操作IP">
                <el-input v-model="temp.remoteIp" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="用户代理特性">
                <el-input v-model="temp.userAgent" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="方法类">
                <el-input v-model="temp.methodClass" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="操作参数">
                <el-input v-model="temp.requestParam" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                          :disabled="dialogDisabled"/>
              </el-form-item>
            </div>
            <div class="logView">
              <el-form-item label="操作用户">
                <el-input v-model="temp.userName" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="服务主机名">
                <el-input v-model="temp.serverHost" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="访问环境">
                <el-input v-model="temp.env" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="请求URI">
                <el-input v-model="temp.requestUri" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="请求方法">
                <el-input v-model="temp.requestMethod" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="方法名">
                <el-input v-model="temp.methodName" :disabled="dialogDisabled"/>
              </el-form-item>
              <el-form-item label="异常时间">
                <el-date-picker
                  v-model="temp.endTime"
                  value-format="yyyy/MM/dd hh:mm:ss"
                  type="datetime"
                  :disabled="dialogDisabled">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="请求body">
                <el-input v-model="temp.requestBody" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                          :disabled="dialogDisabled"/>
              </el-form-item>
            </div>
            <div class="logEx">
            <el-form-item label="异常信息">
              <el-input v-model="temp.message" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                        :disabled="dialogDisabled"/>
            </el-form-item>
            </div>
            <div class="logEx">
              <el-form-item label="详细异常" class="margin-bottom_0">
                <el-input v-model="temp.stacktrace" :autosize="{ minRows: 4, maxRows: 8}" type="textarea"
                          :disabled="dialogDisabled"/>
              </el-form-item>
            </div>
          </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button icon="uims-icon-cancel" @click="dialogMainFormVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { resourceCode } from "@/biz/http/settings";
  export default {
      name: "ErrorLogView",
      data() {
        return {
          textMap: {
            view: '查看错误日志'
          },
          sysName: `${config.headerBar.logo.title ? config.headerBar.logo.title : config.headerBar.logo.top_title}`,
          temp: {
            logId: undefined,
            sysName: undefined,
            userName: undefined,
            unitName: undefined,
            title: undefined,
            serviceId: undefined,
            serverHost: undefined,
            serviceIp: undefined,
            env: undefined,
            remoteIp: undefined,
            requestUri: undefined,
            userAgent: undefined,
            requestMethod: undefined,
            methodClass: undefined,
            methodName: undefined,
            requestParam: undefined,
            requestBody: undefined,
            startTime: undefined,
            endTime: undefined,
            cost: undefined,
            message: undefined
          },
          dialogMainFormVisible: false,
          resCode: ''
        }
      },
      props: {
        dialogStatus: String,
        dialogFormVisible: Boolean,
        dialogDisabled: Boolean,
        log: Array
      },
      methods: {
        destrory() {
          this.temp = {
            logId: undefined,
              sysName: undefined,
              userName: undefined,
              unitName: undefined,
              title: undefined,
              serviceId: undefined,
              serverHost: undefined,
              serviceIp: undefined,
              env: undefined,
              remoteIp: undefined,
              requestUri: undefined,
              userAgent: undefined,
              requestMethod: undefined,
              methodClass: undefined,
              methodName: undefined,
              requestParam: undefined,
              requestBody: undefined,
              startTime: undefined,
              endTime: undefined,
              cost: undefined,
              message: undefined
          }
        },
        timeFormatter(val) {
          if (val != null) {
            var date = new Date(val);
            return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
          }
        }
      },
      watch: {
        dialogFormVisible: function(newValue, oldValue) {
          this.dialogMainFormVisible = newValue
          if(newValue) {
            if(this.dialogStatus === 'view') {
              this.resCode = resourceCode.space_exceptionLog_view
              // // this.temp = this.log[0];
              // this.temp.startTime = this.timeFormatter(this.temp.startTime);
              // this.temp.endTime = this.timeFormatter(this.temp.endTime);
              this.$api.spaceLog.getErrorById({logId: this.log[0].logId}, this.resCode).then((res) => {
                this.temp = res.data
              })
            }
          }
        },
        dialogMainFormVisible: function(newV, oldV) {
          this.$emit('closeDialog', newV)
        }
      }
    }
</script>

<style scoped lang="scss">
  .logView{
    width: 50%;
    float: left
  }
  .logEx{
    width: 100%;
    float: left
  }
</style>
