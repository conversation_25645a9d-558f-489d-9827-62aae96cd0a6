/* Background */ .highlight-chroma { background-color: #ffffff }
/* Error */ .highlight-chroma .highlight-err { color: #a61717; background-color: #e3d2d2 }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; width: auto; overflow: auto; display: block; }
/* LineHighlight */ .highlight-chroma .highlight-hl { display: block; width: 100%;background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Keyword */ .highlight-chroma .highlight-k { color: #000080; font-weight: bold }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #000080; font-weight: bold }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #000080; font-weight: bold }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #000080; font-weight: bold }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #000080; font-weight: bold }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #000080; font-weight: bold }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #000080; font-weight: bold }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #ff0000 }
/* NameTag */ .highlight-chroma .highlight-nt { color: #000080; font-weight: bold }
/* LiteralString */ .highlight-chroma .highlight-s { color: #0000ff }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #0000ff }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #0000ff }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #800080 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #0000ff }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #0000ff }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #0000ff }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #0000ff }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #0000ff }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #0000ff }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #0000ff }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #0000ff }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #0000ff }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #0000ff }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #0000ff }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #0000ff }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #0000ff }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #0000ff }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #0000ff }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #0000ff }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #0000ff }
/* OperatorWord */ .highlight-chroma .highlight-ow { font-weight: bold }
/* Comment */ .highlight-chroma .highlight-c { color: #008800; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #008800; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #008800; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #008800; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #008800; font-weight: bold }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #008080 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #008080 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #000000; background-color: #ffdddd }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #aa0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #999999 }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #000000; background-color: #ddffdd }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #555555 }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #aaaaaa }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #aa0000 }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #bbbbbb }

/*

Lightfair style (c) Tristian Kelly <<EMAIL>>

*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
}

.hljs-name {
    color:#01a3a3;
}

.hljs-tag,.hljs-meta {
    color:#778899;
}

.hljs,
.hljs-subst {
    color: #444
}

.hljs-comment {
    color: #888888
}

.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta-keyword,
.hljs-doctag,
.hljs-name {
    font-weight: bold
}

.hljs-type,
.hljs-string,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
    color: #4286f4
}

.hljs-title,
.hljs-section {
    color: #4286f4;
    font-weight: bold
}

.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
    color: #BC6060
}

.hljs-literal {
    color: #62bcbc
}

.hljs-built_in,
.hljs-bullet,
.hljs-code,
.hljs-addition {
    color: #25c6c6
}

.hljs-meta-string {
    color: #4d99bf
}

.hljs-emphasis {
    font-style: italic
}

.hljs-strong {
    font-weight: bold
}

