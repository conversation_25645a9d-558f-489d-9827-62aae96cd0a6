hljs.registerLanguage("qml",function(r){var e={keyword:"in of on if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await import",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Behavior bool color coordinate date double enumeration font geocircle georectangle geoshape int list matrix4x4 parent point quaternion real rect size string url variant vector2d vector3d vector4dPromise"},t="[a-zA-Z_][a-zA-Z0-9\\._]*",a={cN:"keyword",b:"\\bproperty\\b",starts:{cN:"string",e:"(:|=|;|,|//|/\\*|$)",rE:!0}},n={cN:"keyword",b:"\\bsignal\\b",starts:{cN:"string",e:"(\\(|:|=|;|,|//|/\\*|$)",rE:!0}},o={cN:"attribute",b:"\\bid\\s*:",starts:{cN:"string",e:t,rE:!1}},i={b:t+"\\s*:",rB:!0,c:[{cN:"attribute",b:t,e:"\\s*:",eE:!0,r:0}],r:0},c={b:t+"\\s*{",e:"{",rB:!0,r:0,c:[r.inherit(r.TM,{b:t})]};return{aliases:["qt"],cI:!1,k:e,c:[{cN:"meta",b:/^\s*['"]use (strict|asm)['"]/},r.ASM,r.QSM,{cN:"string",b:"`",e:"`",c:[r.BE,{cN:"subst",b:"\\$\\{",e:"\\}"}]},r.CLCM,r.CBCM,{cN:"number",v:[{b:"\\b(0[bB][01]+)"},{b:"\\b(0[oO][0-7]+)"},{b:r.CNR}],r:0},{b:"("+r.RSR+"|\\b(case|return|throw)\\b)\\s*",k:"return throw case",c:[r.CLCM,r.CBCM,r.RM,{b:/</,e:/>\s*[);\]]/,r:0,sL:"xml"}],r:0},n,a,{cN:"function",bK:"function",e:/\{/,eE:!0,c:[r.inherit(r.TM,{b:/[A-Za-z$_][0-9A-Za-z$_]*/}),{cN:"params",b:/\(/,e:/\)/,eB:!0,eE:!0,c:[r.CLCM,r.CBCM]}],i:/\[|%/},{b:"\\."+r.IR,r:0},o,i,c],i:/#/}});