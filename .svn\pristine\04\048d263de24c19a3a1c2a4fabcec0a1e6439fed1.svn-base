let watermark = {}

let id = '1.23452384164.123412416';
let interval;
let onceFlag = false;

let guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    // eslint-disable-next-line one-var
    let r = Math.random() * 16 | 0,
      v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

let setWatermark = (str) => {
  let thumbnail = localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')) : {}
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
    id = guid()
  }

  // 创建一个画布
  let can = document.createElement('canvas');
  // 设置画布的长宽
  can.width = 900;
  can.height = 150;

  let cans = can.getContext('2d');
  // 旋转角度
  // cans.rotate(-15 * Math.PI / 180);
  cans.font = '18px Vedana';
  // 设置填充绘画的颜色、渐变或者模式
  if(thumbnail.thumbnailImg === "thinvent_darken") {
    cans.fillStyle = 'rgba(200, 200, 200, 0.10)';
  } else {
    if(!thumbnail.thumbnailImg && config.css === 'thinvent_darken') {
      cans.fillStyle = 'rgba(200, 200, 200, 0.10)';
    } else {
      cans.fillStyle = 'rgba(200, 200, 200, 0.30)';
    }
  }
  // 设置文本内容的当前对齐方式
  cans.textAlign = 'left';
  // 设置在绘制文本时使用的当前文本基线
  cans.textBaseline = 'Middle';
  // 在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）
  // cans.rect(20, 20, can.width, can.height);
  // cans.stroke();
  cans.fillText(str, 0, 50);
  cans.fillText(str, 300, 100);
  cans.fillText(str, 600, 150);

  let div = document.createElement('div');
  div.id = id;
  div.style.pointerEvents = 'none';
  div.style.top = '-400px';
  div.style.left = '-200px';
  div.style.transform = 'rotate(-20deg)';
  div.style.position = 'fixed';
  div.style.zIndex = '100000';
  div.style.width = (document.documentElement.clientWidth + 400) + 'px';
  div.style.height = (document.documentElement.clientHeight + 800) + 'px';
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat';
  document.body.appendChild(div);
  return id;
}

// 该方法只允许调用一次
watermark.set = (str) => {
  if(!onceFlag) {
    onceFlag = true
    if(config.openWatermark) {
      setWatermark(str);
      // 计时防止通过F12销毁dom
      interval = setInterval(() => {
        id = setWatermark(str);
      }, 1000);
      window.onresize = () => {
        setWatermark(str);
      };
    }
  }
}

watermark.removeWaterMark = (str) => {
  if(interval) {
    clearInterval(interval)
  }
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
    id = guid()
  }
  onceFlag = false
}

export default watermark;
