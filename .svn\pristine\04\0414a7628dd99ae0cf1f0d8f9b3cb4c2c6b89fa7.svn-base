<template>
  <div class="iframe-container">
    <iframe :src="src" scrolling="auto" frameborder="0" class="frame" :onload="onloaded()"></iframe>
  </div>
</template>

<script>
import { logout } from "@/core/utils/logout"
import { getIframeSrc } from '@/biz/utils/logic.js'
export default {
  name: 'iframe页面',
  data() {
    return {
      src: "",
      loading: null
    };
  },
  props: {
    pstr: String
  },
  methods: {
    // 获取路径
    resetSrc: function(url) {
      const skin = window.document.body.className;
      const targetUrl = getIframeSrc(url);
      const connector = targetUrl.indexOf('?') > -1 ? '&' : '?';
      this.src = targetUrl + connector + "from=iframe&skin=" + skin;
      this.load();
    },
    load: function() {
      this.loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.5)",
        fullscreen: false,
        target: document.querySelector(".iframe-container")
      });
    },
    onloaded: function() {
      if (this.loading) {
        this.loading.close();
        window.top.postMessage(JSON.stringify({BaseUIType: 'iframeLoaded', data: {}}), '*');
      }
    }
  },
  mounted() {
    this.resetSrc(this.$store.state.iframe.iframeUrl);
  },
  watch: {
    $route: {
      handler: function(val, oldVal) {
        // 如果是跳转到嵌套页面，切换iframe的url
        if(val.meta.isIframe) {
          this.resetSrc(this.$store.state.iframe.iframeUrl);
        }
      }
    }
  },
  created() {
    this.src = this.pstr;
    // console.log(this.$store.state.iframe.iframeUrl, this.pstr)
    window.addEventListener("message", (e) => {
      if(typeof e.data !== 'string') {
        return false;
      }
      let data = JSON.parse(e.data)
      const BaseUIType = data.BaseUIType || null;
      if(BaseUIType === 'logout') {
        logout();
      }
    })
  }
};
</script>

<style lang="scss">
.iframe-container {
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  .frame {
    width: 100%;
    height: 100%;
  }
}
</style>
