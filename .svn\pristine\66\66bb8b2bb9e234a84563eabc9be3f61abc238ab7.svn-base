<template>
  <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="600px" @open="initDialog" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :rules="dialogStatus === 'view'? {} : rules" :model="temp" label-position="right" label-width="108px">
            <el-form-item label="编码规则ID" prop="ruleId">
              <el-input v-model.trim="temp.ruleId" :disabled="dialogStatus !== 'create'" maxlength="32"/>
            </el-form-item>
            <el-form-item label="编码规则名称" prop="name">
              <el-input v-model.trim="temp.name" :disabled="contentVisible" maxlength="25"/>
            </el-form-item>
            <el-form-item label="编码规则分类" prop="type">
              <select-plus dictType="RULE_TYPE" v-model="temp.type" @change="typeChange" :disabled="contentVisible"></select-plus>
              <tip :tipInfo="tipInfo"></tip>
            </el-form-item>
            <el-form-item label="分类模板" prop="sortPattern">
              <el-input v-model="temp.sortPattern" :disabled="contentVisible || temp.type !== '3'" maxlength="50"/>
              <el-button @click="setSortYear" size="mini" :disabled="contentVisible || temp.type !== '3'" :class="(contentVisible || temp.type !== '3')?'btNotClick':''">年</el-button>
              <el-button @click="setSortMonth" size="mini" :disabled="contentVisible || temp.type !== '3'" :class="(contentVisible || temp.type !== '3')?'btNotClick':''">月</el-button>
              <el-button @click="setSortData" size="mini" :disabled="contentVisible || temp.type !== '3'" :class="(contentVisible || temp.type !== '3')?'btNotClick':''">日</el-button>
            </el-form-item>
            <el-form-item label="规则模板" prop="rulePattern">
              <el-input v-model="temp.rulePattern" :disabled="contentVisible" maxlength="50"/>
              <el-button @click="setRuleYear" size="mini" :disabled="contentVisible || temp.type === '2'" :class="(contentVisible || temp.type === '2')?'btNotClick':''">年</el-button>
              <el-button @click="setRuleMonth" size="mini" :disabled="contentVisible || temp.type === '2'" :class="(contentVisible || temp.type === '2')?'btNotClick':''">月</el-button>
              <el-button @click="setRuleData" size="mini" :disabled="contentVisible || temp.type === '2'" :class="(contentVisible || temp.type === '2')?'btNotClick':''">日</el-button>
              <el-button @click="setRuleSort" size="mini" :disabled="contentVisible || temp.type === '1' || temp.type === '2'"
                         :class="(contentVisible || temp.type === '1' || temp.type === '2')?'btNotClick':''">分类</el-button>
              <el-button @click="setRuleNum" size="mini" :disabled="contentVisible || temp.type === '2'"
                         :class="(contentVisible || temp.type === '2')?'btNotClick':''">流水号</el-button>
            </el-form-item>
            <el-form-item label="流水号长度">
              <el-input-number v-model="temp.serialLen" controls-position="right" :min="1" :max="10"
                               :disabled="contentVisible"></el-input-number>
            </el-form-item>
            <el-form-item label="流水号初始值">
              <el-input-number v-model="temp.serialStart" controls-position="right" :min="1" :max="2147483647"
                               :disabled="contentVisible"></el-input-number>
            </el-form-item>
            <el-form-item label="流水号步进值">
              <el-input-number v-model="temp.serialStep" controls-position="right" :min="1" :max="2147483647"
                               :disabled="contentVisible"></el-input-number>
            </el-form-item>
            <el-form-item label="备注" class="margin-bottom_0">
              <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                        :disabled="contentVisible" maxlength="125"/>
            </el-form-item>
          </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">
        {{dialogStatus==='view'?'关闭':'取消'}}
      </el-button>
      <el-button class="save-btn" type="primary" @click="dialogStatus==='create'?createData():updateData()" v-if="!contentVisible" :loading="okLoading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import SelectPlus from "@/core/components/SelectPlus";
  import Tip from "@/core/components/Tip";
  // import {checkCode} from "@/biz/utils/validate"
  import { resourceCode } from "@/biz/http/settings"
  export default {
    name: "CodeRuleMainDialog",
    components: {SelectPlus, Tip},
    data() {
      let checkId = (rule, value, callback) => {
        let patter = /^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$/;
        if(!value) {
          callback()
        }else {
          if (!patter.test(value)) {
            return callback(new Error('只能有字母、数字和下划线且不能以下划线开头和结尾'))
          } else {
            callback()
          }
        }
      };
      return {
        tipInfo: {
          titles: ["全局规则", "分段规则", "模板规则", "自定义规则"],
          infos: [
            "由可由时间或流水号组成。任意编码消费方使用该编码规则生成编码都将对流水号进行累加。",
            "该规则针对上下级结构数据。如规则“22333”第一级编码为两位数值组成编码，第二级编码由" +
            "两位数值并与第一级数值组成编码，第三级编码则由三位数值与第一级数值和第二级数值组成" +
            "，以此类推编码。",
            "由分类和流水号组成。将流水号进行分类，各个分类下的流水号步进不影响其他分类下的流水" +
            "号。该规则需配置分类模板。",
            "由分类和流水号组成。将流水号进行分类，各个分类下的流水号步进不影响其他分类下的流水" +
            "号。该规则分类由编码生成消费方提供，消费方许传入分类名称。"
          ]
        },
        okLoading: false,
        textMap: {
          update: '编辑编码规则',
          create: '新增编码规则',
          view: '查看编码规则'
        },
        temp: {
          ruleId: undefined,
          name: undefined,
          type: undefined,
          typeCode: undefined,
          sortPattern: undefined,
          rulePattern: undefined,
          serialLen: undefined,
          serialStart: undefined,
          serialStep: undefined,
          remark: undefined
        },
        // 表单校验规则
        rules: {
          ruleId: [{validator: checkId, trigger: 'blur'}],
          // code: [{required: true, message: '规则编码为必填项', trigger: 'blur'}, {validator: checkCode, trigger: 'blur'}],
          name: [{required: true, message: '规则名称为必填项', trigger: 'blur'}],
          type: [{required: true, message: '规则分类为必选项', trigger: 'change'}],
          sortPattern: [{max: 50, message: '分类模板最大长度为50', trigger: 'blur'}],
          rulePattern: [{max: 50, message: '规则模板最大长度为50', trigger: 'blur'}]
        },
        dialogMainFormVisible: false,
        // 字典
        dict: {
          ruleType: undefined
        },
        resCode: ''
        // once只能点击一次，无法实现功能，所以设置每个按钮的点击次数，默认为零
        // sortYearCount: 0,
        // sortMonthCount: 0,
        // sortDataCount: 0,
        // ruleYearCount: 0,
        // ruleMonthCount: 0,
        // ruleDataCount: 0,
        // ruleSortCount: 0,
        // ruleNumCount: 0
      }
    },
    props: {
      dialogStatus: String,
      dialogFormVisible: Boolean,
      codeRuleArray: Array,
      contentVisible: Boolean
      // systemId: String,
      // systemName: String
    },
    methods: {
      destrory() {
        this.resetTemp()
        this.okLoading = false;
      },
      setSortYear() {
        if(this.temp.sortPattern) {
          let data = this.temp.sortPattern + '`年`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '分类模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.sortPattern = data;
          }
        } else {
          this.temp.sortPattern = '`年`'
        }
      },
      setSortMonth() {
        if(this.temp.sortPattern) {
          let data = this.temp.sortPattern + '`月`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '分类模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.sortPattern = data;
          }
        } else {
          this.temp.sortPattern = '`月`'
        }
      },
      setSortData() {
        if(this.temp.sortPattern) {
          let data = this.temp.sortPattern + '`日`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '分类模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.sortPattern = data;
          }
        } else {
          this.temp.sortPattern = '`日`'
        }
      },
      setRuleYear() {
        if(this.temp.rulePattern) {
          let data = this.temp.rulePattern + '`年`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '规则模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.rulePattern = data;
          }
        } else {
          this.temp.rulePattern = '`年`'
        }
      },
      setRuleMonth() {
        if(this.temp.rulePattern) {
          let data = this.temp.rulePattern + '`月`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '规则模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.rulePattern = data;
          }
        } else {
          this.temp.rulePattern = '`月`'
        }
      },
      setRuleData() {
        if(this.temp.rulePattern) {
          let data = this.temp.rulePattern + '`日`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '规则模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.rulePattern = data;
          }
        } else {
          this.temp.rulePattern = '`日`'
        }
      },
      setRuleSort() {
        if(this.temp.rulePattern) {
          let data = this.temp.rulePattern + '`分类`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '规则模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.rulePattern = data;
          }
        } else {
          this.temp.rulePattern = '`分类`'
        }
      },
      setRuleNum() {
        if(this.temp.rulePattern) {
          let data = this.temp.rulePattern + '`流水号`';
          if(data.length > 25) {
            this.$notify({
              title: '提示',
              message: '规则模板长度不能超过50',
              duration: 2000
            });
          } else{
            this.temp.rulePattern = data;
          }
        } else {
          this.temp.rulePattern = '`流水号`'
        }
      },
      typeChange() {
        if(this.temp.type !== 3) {
          this.temp.sortPattern = ''
        }
        this.temp.rulePattern = ''
      },
      initDialog() {
        // this.loadDict()
      },
      createData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            this.$api.codeRule.add(this.temp, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false;
              this.$emit("getList");
              this.$notify({
                title: '操作成功',
                message: '新增编码规则成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      updateData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            const tempData = Object.assign({}, this.temp)
            this.okLoading = true
            this.$api.codeRule.update(tempData, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList");
              this.$notify({
                title: '操作成功',
                message: '编辑编码规则成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.temp = {
          ruleId: undefined,
          name: undefined,
          type: '1',
          typeCode: undefined,
          sortPattern: undefined,
          rulePattern: undefined,
          serialLen: undefined,
          serialStart: undefined,
          serialStep: undefined,
          remark: undefined
        }
      }
      // uuid() {
      //   var s = [];
      //   var hexDigits = "0123456789abcdef";
      //   for (var i = 0; i < 36; i++) {
      //     s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      //   }
      //   s[14] = "4";
      //   s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
      //   s[8] = s[13] = s[18] = s[23] = "-";
      //
      //   var uuid = s.join("");
      //   return uuid;
      // }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogStatus === 'create') {
            this.resCode = resourceCode.codeRule_add
            this.resetTemp();
            // this.temp.systemId = this.systemId;
            // this.temp.sysName = this.systemName;
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          } else if (this.dialogStatus === 'update') {
            this.resCode = resourceCode.codeRule_eidt
            this.$api.codeRule.get({ruleId: this.codeRuleArray[0].ruleId}, this.resCode).then((res) => {
              this.temp = Object.assign({}, res.data); // copy obj
              // this.temp.type = this.temp.typeCode
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          } else if (this.dialogStatus === 'view') {
            this.resCode = resourceCode.codeRule_view
            this.$api.codeRule.get({ruleId: this.codeRuleArray[0].ruleId}, this.resCode).then((res) => {
              this.temp = Object.assign({}, res.data); // copy obj
              // this.temp.type = this.temp.typeCode
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
  .display {
    display: none;
  }
  .btNotClick {
    color: #C0C4CC !important;
  }
</style>
