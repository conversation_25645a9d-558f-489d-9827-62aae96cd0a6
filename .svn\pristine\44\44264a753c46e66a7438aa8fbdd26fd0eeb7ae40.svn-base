hljs.registerLanguage("autohotkey",function(e){var r={b:"`[\\s\\S]"};return{cI:!0,aliases:["ahk"],k:{keyword:"Break Continue Critical Exit ExitApp Gosub Goto New OnExit Pause return SetBatchLines SetTimer Suspend Thread Throw Until ahk_id ahk_class ahk_pid ahk_exe ahk_group",literal:"A|0 true false NOT AND OR",built_in:"ComSpec Clipboard ClipboardAll ErrorLevel"},c:[{cN:"built_in",b:"A_[a-zA-Z0-9]+"},r,e.inherit(e.QSM,{c:[r]}),e.C(";","$",{r:0}),e.CBCM,{cN:"number",b:e.NR,r:0},{cN:"subst",b:"%(?=[a-zA-Z0-9#_$@])",e:"%",i:"[^a-zA-Z0-9#_$@]"},{cN:"built_in",b:"^\\s*\\w+\\s*,"},{cN:"meta",b:"^\\s*#w+",e:"$",r:0},{cN:"symbol",c:[r],v:[{b:'^[^\\n";]+::(?!=)'},{b:'^[^\\n";]+:(?!=)',r:0}]},{b:",\\s*,"}]}});