import Vue from 'vue'
import vuex from 'vuex'
import app from './modules/app'
import menu from './modules/menu'
import shortcut from './modules/shortcut'
import user from './modules/user'
import tab from './modules/tab'
import iframe from './modules/iframe'
import system from './modules/system'
import biz from '@/biz/store/biz'

Vue.use(vuex)

const store = new vuex.Store({
  modules: {
    system: system,
    app: app,
    menu: menu,
    shortcut: shortcut,
    user: user,
    tab: tab,
    iframe,
    biz
  }
})

export default store
