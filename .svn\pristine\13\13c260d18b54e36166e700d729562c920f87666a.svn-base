<template>
  <el-container class="page-container">
    <el-aside style="width: 268px" v-loading="treeLoading">
      <div class="toolbarTree-wrapper" style="height: 100%">
        <div class="searchPart">
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="resNameLike"
            @change="handleFilter"
            size="small"
          >
            <i class="el-icon-search el-input__icon" slot="suffix"> </i>
          </el-input>
        </div>

        <div class="searchTotal" v-show="!treeLoading">
          <span class="totalName"> 全部</span>
          <span class="totalNum">{{ powerTreeData.allNum }}个</span>
        </div>
        <div class="searchTotalWith withIn" v-show="!treeLoading">
          <div class="totalName">
            <img
              src="../../../assets/img/icon-withIn.svg"
              alt=""
              style="width: 23px; height: auto"
            />{{ sysConfig.innerItemName }}
          </div>
          <span class="totalNum"
            ><span>{{ powerTreeData.allNumIn }}</span
            >个</span
          >
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" v-show="!treeLoading">
          <el-tree
            :key="resNameLike ? '1' : '2'"
            class="icsp-el-tree-2"
            ref="treeIn"
            :lazy="true"
            :load="loadNodeIn"
            node-key="id"
            :highlight-current="true"
            accordion
            :data="powerTreeData.treeDataIn || []"
            :props="defaultProps"
            :indent="14"
            :expand-on-click-node="true"
            :auto-expand-parent="true"
            :check-on-click-node="true"
            @current-change="handleNodeClick1"
            :filter-node-method="filterNode"
            :default-expanded-keys="treeExpandData"
            :default-checked-keys="treeExpandNode"
            icon-class="el-icon-caret-right"
          >
            <span
              class="custom-tree-node"
              :class="{ active: data.id === filters.baseType }"
              slot-scope="{ data }"
            >
              <span class="custom-tree-name" :title="data.name">
                {{ data.name }}
              </span>
              <span class="custom-tree-btn">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="'目录总数：' + data.dirTotal"
                  placement="right"
                >
                  <el-link type="info" :underline="false">{{ data.dirTotal }}</el-link>
                </el-tooltip>
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
        <div class="searchTotalWith withOut" v-show="!treeLoading">
          <div class="totalName">
            <img
              src="../../../assets/img/icon-withOut.svg"
              alt=""
              style="width: 23px; height: auto"
            />{{ sysConfig.outItemName }}
          </div>
          <span class="totalNum"
            ><span>{{ powerTreeData.allNumOut }}</span
            >个</span
          >
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" v-show="!treeLoading">
          <el-tree
            :key="resNameLike ? '1' : '2'"
            class="icsp-el-tree-2"
            ref="treeOut"
            :lazy="true"
            :load="loadNodeOut"
            node-key="id"
            :highlight-current="true"
            accordion
            :data="powerTreeData.treeDataOut || []"
            :props="defaultProps"
            :indent="14"
            :expand-on-click-node="true"
            :check-on-click-node="true"
            @current-change="handleNodeClick2"
            :filter-node-method="filterNode"
            :default-expanded-keys="treeOutExpandData"
            icon-class="el-icon-caret-right"
          >
            <span
              class="custom-tree-node"
              :class="{ active: data.id === filters.baseType }"
              slot-scope="{ data }"
            >
              <span class="custom-tree-name" :title="data.name">
                {{ data.name }}
              </span>
              <span class="custom-tree-btn">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="'目录总数：' + data.dirTotal"
                  placement="right"
                >
                  <el-link type="info" :underline="false">{{ data.dirTotal }}</el-link>
                </el-tooltip>
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container>
      <el-main>
        <!--        <div style="width: 100%; margin: 8px 0;">-->
        <!--          <span style="color: #74767a">数据目录</span>-->
        <!--        </div>-->
        <div class="searchCont">
          <div class="searchList">
            <div class="searchInp" style="width: 100%">
              <el-row>
                <el-col :span="18">
                  <el-input
                    v-model.trim="filters.name"
                    clearable
                    placeholder="请输入目录名称或标签关键字进行搜索"
                    @change="handleFilter"
                    maxlength="20"
                  >
                    <el-button slot="append" type="primary" @click="handleFilter">搜索</el-button>
                  </el-input>
                </el-col>
                <el-button style="margin-left: 28px; height: 42px" @click="handleReset"
                  >重置</el-button
                >
              </el-row>
            </div>
          </div>
          <div class="searchList">
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="资源类型">
                  <el-radio-group v-model="filters.resFormatType" @change="handleFilter">
                    <el-radio-button
                      v-for="item in resourceTypeList"
                      :key="item.key"
                      :label="item.key"
                      >{{ item.value }}</el-radio-button
                    >
                  </el-radio-group>
                  <!-- <SelectPlus
                    collapseTags
                    multiple
                    dictType="ZYML_RESOURCE_TYPE"
                    mode="gxsl"
                    v-model="filters.resFormatType"
                    placeholder="资源类型"
                    @input="handleFilter"
                    clearable
                  ></SelectPlus> -->
                </el-form-item>
                <el-form-item label="共享类型">
                  <!-- <SelectPlus
                    collapseTags
                    multiple
                    dictType="SHARD_TYPE"
                    mode="gxsl"
                    v-model="filters.shareType"
                    @input="handleFilter"
                    clearable
                    placeholder="共享类型"
                  /> -->

                  <el-radio-group v-model="filters.shareType" @change="handleFilter">
                    <el-radio-button
                      v-for="item in shareTypeList"
                      :key="item.key"
                      :label="item.key"
                      >{{ item.value }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>

                <!-- <el-form-item label="发布时间">
                  <el-date-picker value-format="yyyy-MM-dd" v-model="filters.dateRange" type="daterange" @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
                  </el-date-picker>
                </el-form-item> -->
              </el-form>
            </div>
          </div>

          <el-collapse class="collapse-filter" v-model="activeCollapse">
            <el-collapse-item name="1">
              <div class="searchList" v-show="isExpanded">
                <div class="searchInp mg-b-12">
                  <el-form :inline="true" :model="filters">
                    <el-form-item label="来源系统">
                      <el-input
                        v-model.trim="filters.systemName"
                        clearable
                        placeholder="请输入来源系统"
                        @change="handleFilter"
                        maxlength="20"
                      />
                    </el-form-item>

                    <el-form-item label="更新周期">
                      <SelectPlus
                        collapseTags
                        multiple
                        dictType="ZYML_CATALOG_UPDATE_CYCLE"
                        mode="gxsl"
                        v-model="filters.updateCycle"
                        placeholder="请选择更新周期"
                        @input="handleFilter"
                        clearable
                      ></SelectPlus>
                    </el-form-item>
                    <!-- <el-form-item label="">
                  <SelectPlus
                    collapseTags
                    multiple
                    dictType="CATALOG_TYPE"
                    mode="gxsl"
                    v-model="filters.catalogType"
                    @input="handleFilter"
                    clearable
                    placeholder="数据目录类型"
                /></el-form-item> -->
                    <el-form-item label="提供单位">
                      <el-cascader
                        placeholder="请选择提供单位"
                        clearable
                        :filterable="true"
                        :options="options"
                        :props="{ checkStrictly: true }"
                        v-model="orgIds"
                        @change="handleOrgId"
                        :value="orgIds"
                        style="width: 100%"
                      >
                        <template slot-scope="{ data }">
                          <div class="nowrap" :title="data.label">
                            {{ data.label }}
                          </div>
                        </template>
                      </el-cascader>
                    </el-form-item>
                    <el-form-item label="发布时间">
                      <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="dateRange"
                        type="daterange"
                        @change="handleFilter"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 280px"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-form>
                  <!-- <CheckPlus dictType="ZYML_RESOURCE_TYPE" mode="gxsl" v-model="checkListresFormatType" :filterType="'resFormatType'" :num="'1'" @change="handleChecked" ref="resFormatType"></CheckPlus> -->
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>

          <div class="expand-toggle">
            <div class="expand-toggle-content" @click="toggleExpand">
              <span>{{ isExpanded ? '收起' : '展开更多选项' }}</span>
              <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
          </div>
        </div>
        <div class="dataCont" v-loading="listLoading">
          <div class="topSort">
            <el-checkbox
              v-model="filters.onlyShowNotOrder"
              @change="changeListStatus"
              :true-label="1"
              :false-label="0"
            >
              仅显示未订阅</el-checkbox
            >
            <div class="sort">
              <div v-for="(item, index) in sortItems" :key="index">
                <span class="iconLabel" :class="{ active: item.active }" @click="sort(item)">
                  {{ item.label }}
                </span>
                <span v-if="item.sort" class="iconList">
                  <i
                    class="el-icon-caret-top"
                    :class="{ active: item.order === 1 }"
                    @click="orderChange(item, 1)"
                  ></i>
                  <i
                    class="el-icon-caret-bottom"
                    :class="{ active: item.order === -1 }"
                    @click="orderChange(item, -1)"
                  ></i>
                </span>
              </div>
            </div>
          </div>
          <div class="borderSplit"></div>
          <div class="listCont">
            <div
              class="listItem"
              v-for="item in dataList || []"
              :key="item.id"
              @click="handleCheckDetail(item)"
            >
              <div class="line1">
                <div class="title">
                  <div class="ellis" :title="item.directoryName">
                    {{ item.directoryName }}
                  </div>
                  <span style="width: 23px; margin-left: 4px">
                    <img
                      v-if="item.rangeCode === '1'"
                      src="~@/assets/img/icon-withOut.svg"
                      alt=""
                    />
                    <img v-else src="~@/assets/img/icon-withIn.svg" alt="" />
                  </span>
                  <!-- 敏感等级标识 -->
                  <div
                    v-for="grade in catalogGradeList"
                    :key="grade.key"
                    :label="grade.key"
                    :class="
                      item.catalogGrade === '1'
                        ? 'grade-one'
                        : item.catalogGrade === '2'
                        ? 'grade-two'
                        : item.catalogGrade === '3'
                        ? 'grade-three'
                        : item.catalogGrade === '4'
                        ? 'grade-four'
                        : ''
                    "
                  >
                    <span v-if="item.catalogGrade && item.catalogGrade === grade.key">
                      {{ grade.value }}
                    </span>
                  </div>
                  <!-- 目录标识 -->
                  <!-- <div
                    v-if="item.catalogTypeCode && item.catalogTypeCode === '1'"
                    class="info-list"
                  >
                    政务信息目录
                  </div>
                  <div
                    v-if="item.catalogTypeCode && item.catalogTypeCode === '2'"
                    class="data-list"
                  >
                    政务数据目录
                  </div>
                  <div
                    v-if="item.catalogTypeCode && item.catalogTypeCode === '3'"
                    class="huiliu-list"
                  >
                    回流数据目录
                  </div> -->
                  <span class="info-list" v-if="item.isExistOrder && item.isExistOrder === '1'"
                    >已订阅</span
                  >
                </div>
                <div class="itemStatus">
                  <span class="info-tip" v-if="item.shardType && item.shardType !== ''">{{
                    item.shardType
                  }}</span>
                  <span class="info-tip" v-if="item.openType && item.openType !== ''">{{
                    item.openType
                  }}</span>
                </div>
              </div>
              <div class="line2">
                <div class="itemMes">
                  <span class="message"
                    >提供单位：<span>{{ item.providerDeptName }}</span></span
                  >
                  <span class="message"
                    >更新周期：<span>{{ item.updateCycle }}</span></span
                  >
                  <span class="message"
                    >发布时间：<span>{{ item.publishTime }}</span></span
                  >
                  <br />
                  <span class="message"
                    >来源系统：<span>{{ item.systemName }}</span></span
                  >
                  <span class="message"
                    >更新时间：<span>{{ item.lastUpdatedTime }}</span></span
                  >
                  <span class="message" v-if="item.rowChangeTime"
                    >业务数据更新时间：<span>{{ item.rowChangeTime }}</span></span
                  >
                  <span class="message" v-if="item.labels"
                  >标签：<span>{{ item.labels }}</span>
                  </span>
                  <!-- 数据量的展示 -->
               </br>
                  <template v-if="item.resourceType">
                  <!-- 含有库表 || 回流 -->
                  <span v-if="item.resourceType && (item.resourceType.indexOf('1') > -1 || item.resourceType.indexOf('4') > -1) && item.isExternalImport !== '9'" class="message">结构化数据量：<span>{{ showTotal(item.tdxTable) || 0 }}条</span></span>
                  <!-- 含有文件 || 文件夹 -->
                  <span v-if="item.resourceType && (item.resourceType.indexOf('2') > -1 || item.resourceType.indexOf('5') > -1) && item.isExternalImport !== '9'" class="message">非结构化数据量：<span>{{ (item.tdxFile / 1024 / 1024 ).toFixed(3) || 0 }}M</span></span>
                  </template>

                </div>
                <!-- 右侧资源类型 -->
                <div class="right-info">
                  <!-- 非回流数据目录 -->
                  <template v-if="item.catalogTypeCode !== '3'">
                    <div
                      class="type-info"
                      :class="{
                        active: item.resourceType && item.resourceType.indexOf('1') > -1
                      }"
                    >
                      <svg-icon icon-class="gxsl-kubiao" />
                      <p>库表</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active: item.resourceType && item.resourceType.indexOf('2') > -1
                      }"
                    >
                      <svg-icon icon-class="gxsl-wenjian" />
                      <p>文件</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active: item.resourceType && item.resourceType.indexOf('3') > -1
                      }"
                    >
                      <svg-icon icon-class="gxsl-jiekou" />
                      <p>接口</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active: item.resourceType && item.resourceType.indexOf('5') > -1
                      }"
                    >
                      <svg-icon icon-class="gxsl-wenjianjia" />
                      <p>文件夹</p>
                    </div>
                  </template>
                  <!-- 回流数据目录 -->
                  <template v-else>
                    <div
                      class="type-info"
                      :class="{
                        active: item.resourceType && item.resourceType.indexOf('4') > -1
                      }"
                    >
                      <svg-icon icon-class="gxsl-huiliu" />
                      <p>回流</p>
                    </div>
                  </template>
                </div>
              </div>
              <div class="line3">
                <div class="itemNum">
                  <span class="num"
                    >{{ !!item.orderSum ? item.orderSum : 0 }}次<span>订阅</span></span
                  >
                  <span class="num"
                    >{{ !!item.browseSum ? item.browseSum : 0 }}次<span>访问</span></span
                  >
                  <span class="num"
                    >{{ !!item.collectSum ? item.collectSum : 0 }}次<span>收藏</span></span
                  >
                </div>
                <div class="itemHandleBtn">
                  <el-tooltip
                    v-if="item.providerCode === unitData.unitCode"
                    placement="top"
                    effect="dark"
                    :append-to-body="true"
                    content="本单位数据不能发起订阅"
                  >
                    <el-button class="icsp-button-grey" disabled>
                      <svg-icon icon-class="gxsl-apply-btn" />
                      订阅
                    </el-button>
                  </el-tooltip>

                  <el-tooltip
                    v-else-if="!item.resourceType"
                    placement="top"
                    effect="dark"
                    :append-to-body="true"
                    content="目录未挂接资源不能发起订阅"
                  >
                    <el-button class="icsp-button-grey" :disabled="!item.resourceType">
                      <svg-icon icon-class="gxsl-apply-btn" />
                      订阅
                    </el-button>
                  </el-tooltip>

                  <el-button v-else class="icsp-button" plain @click.stop="handleCataApply(item)">
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>

                  <el-tooltip
                    v-if="item.providerCode === unitData.unitCode"
                    placement="top"
                    effect="dark"
                    :append-to-body="true"
                    content="本单位数据不能加入选数车"
                  >
                    <el-button class="icsp-button-grey" disabled>
                      <svg-icon icon-class="gxsl-shoppingCart" />
                      加入选数车
                    </el-button>
                  </el-tooltip>

                  <el-tooltip
                    v-else-if="!item.resourceType"
                    placement="top"
                    effect="dark"
                    :append-to-body="true"
                    content="目录未挂接资源不能加入选数车"
                  >
                    <el-button class="icsp-button-grey" :disabled="!item.resourceType">
                      <svg-icon icon-class="gxsl-shoppingCart" />
                      {{ item.addSelect === '1' ? '取消加入选数车' : '加入选数车' }}
                    </el-button>
                  </el-tooltip>

                  <el-button
                    v-else
                    :disabled="queryBt"
                    :class="item.addSelect === '1' ? 'icsp-button2' : 'icsp-button'"
                    plain
                    @click.stop="handleAddPowerToCart(item)"
                  >
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    {{ item.addSelect === '1' ? '取消加入选数车' : '加入选数车' }}
                  </el-button>

                  <el-tooltip
                    v-if="item.providerCode === unitData.unitCode"
                    placement="top"
                    effect="dark"
                    :append-to-body="true"
                    content="本单位数据不能收藏"
                  >
                    <el-button class="icsp-button-grey" disabled>
                      <svg-icon icon-class="gxsl-collect" />
                      收藏
                    </el-button>
                  </el-tooltip>

                  <el-button
                    v-else
                    :disabled="queryBt"
                    :class="item.collect ? 'icsp-button2' : 'icsp-button'"
                    plain
                    @click.stop="handleCollect(item)"
                  >
                    <svg-icon icon-class="gxsl-collect" />
                    {{ item.collect ? '取消收藏' : '收藏' }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-if="!listLoading && dataList && dataList.length === 0"
            description="暂无数据"
          ></el-empty>
          <div class="icsp-pagination-wrap">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="filters.currentPage"
              :page-size="filters.pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>

        <!-- 数据订阅 -->
        <SubscribeDialog
          :dialogVisible.sync="dialogVisibleApply"
          :type="type"
          :title="title"
          :data="dialogData"
        >
        </SubscribeDialog>
        <ToolBar @onRefreshList="getList" />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import ToolBar from '@/biz/components/common/toolbar'
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index.vue'
import CheckPlus from '@/biz/components/common/checkPlus'
import SubscribeDialog from '../../components/SubscribeDialog'
import Cookies from 'js-cookie'
export default {
  name: 'polyMonitor',
  components: {
    ToolBar,
    SelectPlus,
    CheckPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog
  },
  data() {
    return {
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      queryBt: false,
      checkAll: false,
      isIndeterminate: false,
      showList: true,
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      unitData: {
        userId: '',
        unitId: '',
        unitCode: ''
      },
      queryParamsList: [],
      options: [], // 所属部门
      orgIds: [],
      checkList: [
        {
          checkName: '一般',
          checkValue: 1
        },
        {
          checkName: '重要',
          checkValue: 2
        }
      ],
      checkOption: ['一般', '重要'],
      sortItems: [
        {
          label: '订阅量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 1
        },
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 2
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 8
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false,
          sortMode: 4
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      powerTreeData: {
        allNum: 0,
        allNumIn: 0,
        allNumOut: 0,
        treeDataIn: null, // 初始化为null，避免显示暂无数据
        treeDataOut: null // 初始化为null，避免显示暂无数据
      },
      dataList: null, // 初始化为null，避免显示暂无数据
      treeDataIn: [],
      treeDataOut: [],
      defaultProps: {
        children: 'childList',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      listLoading: true,
      treeLoading: true, // 树加载状态
      total: 0,
      checkListresFormatType: [1],
      resNameLike: '',
      showExistNode: '',
      treeExpandData: [],
      treeOutExpandData: [],
      treeExpandNode: [],
      dateRange: [],
      filters: {
        name: '',
        systemName: '',
        orgId: '',
        currentPage: 1,
        baseType: '',
        baseTypeInfo: '',
        onlyShowNotOrder: 0,
        pageSize: 10,
        sortMode: '',
        sortType: 'desc',
        catalogType: '1,2,3',
        updateCycle: '',
        shareType: '',
        resFormatType: ''
      },
      sysConfig: config,
      isExpanded: false,
      activeCollapse: [],
      resourceTypeList: [],
      shareTypeList: [],
      catalogGradeList: []
    }
  },
  methods: {
    handleChecked(val, type) {
      this.filters[type] = val
      this.handleFilter()
    },
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    // 改变所属部门
    handleOrgId(val) {
      if (val.length === 1) {
        this.filters.orgId = val[0]
      } else {
        this.filters.orgId = val[val.length - 1]
      }
      this.getList()
    },
    circle(data) {
      data.forEach((i) => {
        i.value = i.id
        i.label = i.name
        if (i.children && i.children.length > 0) {
          this.circle(i.children)
        }
      })
    },
    // 获取所属部门
    getOrgId() {
      this.$api.bizApi.common
        .getUnitsByUnitCode()
        .then((res) => {
          if (res.code === '200') {
            let a = res.data || []
            this.circle(a)
            this.options = a
            // 如果route中含有提供单位，需要反显选中
            if (this.filters.orgId) {
              let ary = this.getParentsById(this.options, this.filters.orgId)
              // console.log('ary' + ary)
              this.orgIds = ary
            }
          }
        })
        .catch((e) => {
          this.$message({
            message: e.message
          })
        })
    },
    // el-cascader数据提供部门多级回显
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          // console.log('node' + node)
          if (node !== undefined) {
            // 如能查询到把父节点加到数组前面
            node.unshift(list[i].id)
            return node
          }
        }
      }
    },
    getPowerTreeData() {
      this.treeLoading = true // 开始加载树数据
      let unitCode = this.unitData.unitCode
      this.$api.bizApi.pageRequest
        .getCatalogTree({
          resNameLike: this.resNameLike,
          ...this.filters,
          publishTimeStart: this.dateRange?.length ? this.dateRange[0] + ' 00:00:00' : undefined,
          publishTimeEnd: this.dateRange?.length ? this.dateRange[1] + ' 23:59:59' : undefined,
          unitCode
        })
        .then((res) => {
          if (res.data) {
            this.powerTreeData.allNum = res.data.dirTotal
            let targetNodeFound = false

            // 处理目标节点和父节点的函数
            const processTargetNode = async (treeType, item) => {
              if (!this.$route.query.id) return

              // 如果有pid值，说明已经有预设的路径
              if (this.$route.query.pid) {
                if (treeType === 'in') {
                  this.treeExpandData = this.$route.query.pid.split(',')
                } else {
                  this.treeOutExpandData = this.$route.query.pid.split(',')
                }

                // 设置过滤条件
                this.filters.baseType = this.$route.query.id
                this.filters.baseTypeInfo = this.$route.query.type
                targetNodeFound = true
              }
            }

            for (const item of res.data.childList) {
              if (item.name === config.innerItemName) {
                this.powerTreeData.treeDataIn = item.childList
                this.powerTreeData.allNumIn = item.dirTotal

                // 处理内部树的目标节点
                if (
                  this.$route.query.id &&
                  !targetNodeFound &&
                  item.childList.map((i) => i.id).includes(this.$route.query.pid.split(',')[0])
                ) {
                  processTargetNode('in', item)
                }
              }

              if (item.name === config.outItemName) {
                this.powerTreeData.treeDataOut = item.childList
                this.powerTreeData.allNumOut = item.dirTotal

                // 如果内部树没有找到，处理外部树的目标节点
                if (
                  this.$route.query.id &&
                  !targetNodeFound &&
                  item.childList.map((i) => i.id).includes(this.$route.query.pid.split(',')[0])
                ) {
                  processTargetNode('out', item)
                }
              }
            }

            this.treeLoading = false // 树数据加载完成
            setTimeout(() => {
              this.getList()
            }, 1000)
          } else {
            this.powerTreeData = {
              allNum: 0,
              allNumIn: 0,
              allNumOut: 0,
              treeDataIn: [],
              treeDataOut: []
            }
            this.treeLoading = false // 树数据加载完成
            this.getList()
          }
        })
        .catch((error) => {
          console.error('获取目录树数据失败:', error)
          // 确保在错误情况下也设置树数据为空数组
          this.powerTreeData = {
            allNum: 0,
            allNumIn: 0,
            allNumOut: 0,
            treeDataIn: [],
            treeDataOut: []
          }
          this.treeLoading = false // 树数据加载完成（错误情况）
          // 确保在错误情况下也调用getList，这样listLoading会被正确设置为false
          this.getList()
        })
    },
    handleCheckAllChange(val) {
      this.filters.checkList = val ? this.checkOption : []
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      let checkedCount = val.length
      this.checkAll = checkedCount === this.checkOption.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkOption.length
    },
    // 加载子树数据的方法，lazy为true时生效
    async loadNodeIn(node, resolve) {
      let data = node.data
      let resData = []
      if (data.id) {
        try {
          const res = await this.$api.bizApi.pageRequest.getBaseTypeTree({
            showExistNode: this.showExistNode,
            type: data.type,
            baseTypeId: data.id,
            rangeFlag: data.rangeFlag,
            ...this.filters,
            baseType: data.id
          })

          data.childList = res.data || []
          resData = res.data || []

          // 检查当前节点是否在展开路径中
          if (this.treeExpandData && this.treeExpandData.includes(data.id)) {
            // 如果当前节点是目标节点的父节点，继续展开
            if (this.$route.query.id) {
              // 检查子节点中是否包含目标节点
              const hasTargetNode = resData.some((item) => item.id === this.$route.query.id)

              if (hasTargetNode) {
                const targetNode = resData.find((item) => item.id === this.$route.query.id)
                if (targetNode) {
                  this.filters.baseTypeInfo = targetNode.type
                  this.$nextTick(() => {
                    this.$refs.treeIn.setCurrentKey(this.$route.query.id)
                  })
                }
              } else {
                if (this.$route.query.id === this.$route.query.pid) {
                  this.$nextTick(() => {
                    this.$refs.treeIn.setCurrentKey(data.id)
                  })
                }
              }
            }
          }
        } catch (error) {
          console.error('加载节点数据失败:', error)
        }
      }
      return resolve(resData)
    },
    async loadNodeOut(node, resolve) {
      let data = node.data
      let resData = []
      if (data.id) {
        try {
          const res = await this.$api.bizApi.pageRequest.getBaseTypeTree({
            resNameLike: '',
            showExistNode: this.showExistNode,
            type: data.type,
            baseTypeId: data.id,
            rangeFlag: data.rangeFlag,
            ...this.filters,
            baseType: data.id
          })

          data.childList = res.data || []
          resData = res.data || []

          // 检查当前节点是否在展开路径中
          if (this.treeOutExpandData && this.treeOutExpandData.includes(data.id)) {
            // 如果当前节点是目标节点的父节点，继续展开
            if (this.$route.query.id) {
              // 检查子节点中是否包含目标节点
              const hasTargetNode = resData.some((item) => item.id === this.$route.query.id)
              if (hasTargetNode) {
                const targetNode = resData.find((item) => item.id === this.$route.query.id)
                if (targetNode) {
                  this.filters.baseTypeInfo = targetNode.type
                  this.$nextTick(() => {
                    this.$refs.treeOut.setCurrentKey(this.$route.query.id)
                  })
                }
              } else {
                if (this.$route.query.id === this.$route.query.pid) {
                  this.$refs.treeOut.setCurrentKey(data.id)
                }
              }
            }
          }
        } catch (error) {
          console.error('加载节点数据失败:', error)
        }
      }
      return resolve(resData)
    },
    // 当前选中节点变化时触发的事件
    handleNodeClick1(node) {
      // console.log(node, 'nodeeee')
      // this.$refs.treeIn.setCurrentKey(null)
      // if (this.filters.baseType === node.id) {
      // this.filters.baseType = undefined
      // } else {
      this.filters.baseType = node.id
      this.filters.baseTypeInfo = node.type ? node.type : this.type
      this.$refs.treeOut.setCurrentKey(null)
      // }
      this.getList(node.rangeFlag)
    },
    handleNodeClick2(node) {
      // this.$refs.treeOut.setCurrentKey(null)
      // if (this.filters.baseType === node.id) {
      // this.filters.baseType = undefined
      // } else {
      this.filters.baseType = node.id
      this.filters.baseTypeInfo = node.type ? node.type : this.type
      this.$refs.treeIn.setCurrentKey(null)
      // }
      this.getList(node.rangeFlag)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    changeListStatus(val) {
      this.handleFilter()
    },
    handleReset() {
      this.filters.name = ''
      this.filters.resFormatType = ''
      this.filters.shareType = ''
      this.filters.systemName = ''
      this.filters.updateCycle = ''
      this.filters.orgId = ''
      this.filters.baseType = ''
      this.filters.baseTypeInfo = ''
      this.orgIds = []
      this.dateRange = []
      this.$nextTick(() => {
        this.getPowerTreeData()
      })
    },
    handleFilter() {
      this.filters.currentPage = 1
      this.$nextTick(() => {
        this.getPowerTreeData()
      })
      // this.getList()
    },
    getList(rangeFlag) {
      this.listLoading = true
      const params = {
        ...this.filters,
        rangeFlag: rangeFlag,
        publishTimeStart: this.dateRange?.length ? this.dateRange[0] + ' 00:00:00' : undefined,
        publishTimeEnd: this.dateRange?.length ? this.dateRange[1] + ' 23:59:59' : undefined
      }
      this.$api.bizApi.pageRequest
        .getCatalogPage(params)
        .then((res) => {
          this.listLoading = false
          this.dataList = res.data ? res.data.records : []

          this.total = res.data ? res.data.total : 0
        })
        .catch((res) => {
          this.listLoading = false
          this.dataList = [] // 确保错误时也设置为空数组
        })
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    // 查看详情
    handleCheckDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detailCatalog/index',
          query: {
            cataId: data.cataId
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataDir/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            cataId: data.cataId
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    // 能力收藏
    handleCollect(data) {
      if (this.isLogOn()) {
        if (!data.collect) {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addCatalogToFavorites({ cataId: data.cataId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('收藏成功')
                this.$set(data, 'collect', true)
                this.$set(data, 'collectSum', data.collectSum + 1)
                // this.handleFilter()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否取消收藏该数据目录?', '取消收藏', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .cancelCatalogToFavorites({ cataId: data.cataId })
              .then((res) => {
                if (res.code === '200') {
                  // this.handleFilter()
                  this.$message({
                    type: 'success',
                    message: '取消收藏成功!'
                  })
                  this.$set(data, 'collect', false)
                  this.$set(data, 'collectSum', data.collectSum - 1)
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 目录订阅
    handleCataApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = data
        this.dialogVisibleApply = true
        this.dialogData.cataId = data.cataId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart(data, index) {
      if (this.isLogOn()) {
        if (data.addSelect !== '1') {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addCatalogToCart({ cataId: data.cataId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('加入选数车成功')
                this.$set(data, 'addSelect', '1')
                // this.handleFilter()
                window.refreshCountNum()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否从选数车中取消该数据目录?', '取消确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .delCatalogFromCart({
                cataId: data.cataId // ,
                // userId,
                // unitId,
                // unitCode
              })
              .then((res) => {
                if (res.code === '200') {
                  // this.handleFilter()
                  this.$message.success('取消成功!')
                  this.$set(data, 'addSelect', '0')
                  window.refreshCountNum()
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded
      if (this.isExpanded) {
        this.activeCollapse = ['1']
      } else {
        this.activeCollapse = []
      }
    },
      showTotal(value) {
          if (value >= 100000000) {
              return (value / 100000000).toFixed(2) + "亿";
          } else if (value >= 10000) {
              return (value / 10000).toFixed(2) + "万";
          } else {
              return value;
          }
      }
  },
  watch: {
    filterText(val) {
      // 只有在树加载完成后才执行过滤
      if (!this.treeLoading) {
        this.$refs.treeIn && this.$refs.treeIn.filter(val)
        this.$refs.treeOut && this.$refs.treeOut.filter(val)
      }
    }
  },
  created() {
    let param = this.$route.query
    if (param.sortType) {
      this.filters.sortType = param.sortType
    }
    if (param.sortMode) {
      this.filters.sortMode = param.sortMode
    }
    if (param.systemName) {
      this.filters.systemName = param.systemName
    }

    if (param.id) {
      this.filters.baseType = param.id
    }

    this.getOrgId()
    // const { userId, unitId, unitCode } = JSON.parse(sessionStorage.getItem('user'))
    this.unitData.unitCode = JSON.parse(sessionStorage.getItem('user'))?.unitCode
    // this.getList()
    this.getPowerTreeData()

    this.$api.dict.getDictListByTypeWithGxsl({ type: 'ZYML_RESOURCE_TYPE' }).then((res) => {
      this.resourceTypeList = res.data.filter((item) => item.key !== '4')
      this.resourceTypeList.unshift({
        key: '',
        value: '全部'
      })
    })

    this.$api.dict.getDictListByTypeWithGxsl({ type: 'SHARD_TYPE' }).then((res) => {
      this.shareTypeList = res.data
      this.shareTypeList.unshift({
        key: '',
        value: '全部'
      })
    })

    this.$api.dict.getDictListByTypeWithGxsl({ type: 'BM_SENSITIVITY_LEVEL' }).then((res) => {
      this.catalogGradeList = res.data
    })
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  color: #3572ff;
  font-size: 14px;
  &-content {
    cursor: pointer;
  }
}

.expand-toggle i {
  margin-left: 5px;
}

.el-collapse {
  border: none;
}

.el-collapse-item >>> .el-collapse-item__header {
  display: none;
}

.el-collapse-item >>> .el-collapse-item__wrap {
  border-bottom: none;
}

.data-list {
  margin-left: 10px;
  font-size: 16px;
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  color: #fff;
  background-color: #ff8635;
}

.huiliu-list {
  @extend .data-list;
  background-color: #83c954;
}

.info-list {
  @extend .data-list;
  background-color: #3572ff;
}

.collapse-filter {
  /deep/ .el-collapse-item__content {
    padding-bottom: 0;
  }
}

.grade-one {
  margin-left: 10px;
  font-size: 16px;
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  color: #fff;
  background-color: #f56c6c;
}

.grade-two {
  @extend .grade-one;
  background-color: #ff8635;
}
.grade-three {
  @extend .grade-one;
  background-color: #3572ff;
}
.grade-four {
  @extend .grade-one;
  background-color: #83c954;
}
</style>
