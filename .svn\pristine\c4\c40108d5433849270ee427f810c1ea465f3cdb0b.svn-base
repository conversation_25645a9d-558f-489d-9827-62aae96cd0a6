<template>
  <div class="widgetMyWork">
    <h5 style="font-weight: 500;">我的工作</h5>
    <div class="mainCont">
      <div class="workItem">
        <img src="../../../../assets/img/pic-work1.png" alt="">
        <p class="itemName">数据库申请审核</p>
        <p class="itemValue"><span>1234</span>条</p>
      </div>
      <div class="workItem">
        <img src="../../../../assets/img/pic-work2.png" alt="">
        <p class="itemName">数据库目录审核</p>
        <p class="itemValue"><span>1234</span>条</p>
      </div>
      <div class="workItem">
        <img src="../../../../assets/img/pic-work3.png" alt="">
        <p class="itemName">能力注册审核</p>
        <p class="itemValue"><span>{{ pageData.powerWaitAuditTotal }}</span>条</p>
      </div>
      <div class="workItem">
        <img src="../../../../assets/img/pic-work4.png" alt="">
        <p class="itemName">资源订阅审核</p>
        <p class="itemValue"><span>{{ pageData.waitAuditOrderTotal }}</span>条</p>
      </div>
      <div class="workItem">
        <img src="../../../../assets/img/pic-work5.png" alt="">
        <p class="itemName">能力订阅审核</p>
        <p class="itemValue"><span>{{ pageData.orderWaitAuditTotal }}</span>条</p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'widgetMyWork',
  components: {},
  computed: {
    ...mapState({
      currentUser: (state) => state.user.currentUser
    })
  },
  props: {},
  data() {
    return {
      pageData: {
        orderWaitAuditTotal: 0,
        powerWaitAuditTotal: 0,
        waitAuditOrderTotal: 0
      }
    }
  },
  methods: {
    getCntFromAccept() {
      console.log(this.currentUser)
      this.$api.bizApi.pageRequest
        .getCntFromAccept({
          unitId: this.currentUser.unitId,
          userId: this.currentUser.userId,
          unitCode: this.currentUser.unitCode
        })
        .then((res) => {
          console.log(res)
          this.pageData.waitAuditOrderTotal = res.data.waitAuditOrderTotal
        })
    },
    getPowerCnt() {
      this.$api.bizApi.pageRequest.getPowerCnt({}).then((res) => {
        console.log(res)
        this.pageData.orderWaitAuditTotal = res.data.orderWaitAuditTotal
        this.pageData.powerWaitAuditTotal = res.data.powerWaitAuditTotal
      })
    }
  },
  mounted() {
    this.getCntFromAccept()
    this.getPowerCnt()
  }
}
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.widgetMyWork {
  width: 100%;

  h5 {
    //font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #333333;
    line-height: 20px;
    margin: 17px 0;
  }

  .mainCont {
    height: 208px;
    background: #ffffff;
    border-radius: 2px;
    display: flex;
    padding: 50px 31px 40px;

    .workItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 50px;

      .itemName {
        margin: 18px 0 15px 0;
        //font-family: Source Han Sans CN;
        font-size: 18px;
        font-size: 18px;
        color: rgba(51, 51, 51, 1);
        font-weight: 600;
        line-height: 20px;
      }

      .itemValue {
        font-family: D-DIN;
        font-weight: bold;
        color: #666666;
        line-height: 20px;
        font-size: 16px;

        span {
          font-size: 24px;
          color: $colors;
        }
      }
    }
  }
}
</style>
