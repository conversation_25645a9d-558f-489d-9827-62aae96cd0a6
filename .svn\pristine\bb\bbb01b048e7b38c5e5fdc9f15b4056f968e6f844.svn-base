@import "./mixiPortal.scss";
.igdp-portal-layout {
  min-width: 1360px;
}
// 门户菜单
.el-menu-portal {
  border-bottom: none !important;
}

.el-menu-portal {
  .el-menu-item {
    font-size: 18px;
    height: 50px;
    line-height: 50px !important;
  }
  .el-submenu__title {
    font-size: 18px;
    height: 50px !important;
    line-height: 50px !important;
  }
}
.el-menu.el-menu--popup {
  .el-menu-item,
  .el-submenu__title {
    font-weight: 400;
    font-size: 16px;
    height: 40px !important;
    line-height: 40px !important;
    &.is-active {
      background-color: rgba(255, 255, 255, .1) !important;
    }
  }
  .el-menu-item:hover,
  .el-menu-item:focus,
  .el-submenu__title:hover {
    background-color: rgba(255, 255, 255, .1) !important;
  }
}

.el-menu-portal {
  .el-menu-item.is-active,
  .el-submenu.is-active .el-submenu__title {
    border-bottom: none;
    position: relative;
    &:after {
      content: "";
      display: block;
      position: absolute;
      bottom: 0;
      width: calc(100% - 40px);
      height: 4px;
      background-color: var(--white);
      border-radius: 2px;
    }
  }
  .el-submenu.is-active .el-submenu__title {
    &:after {
      width: calc(100% - 60px);
    }
  }

  .el-submenu .el-submenu__title {
    &:hover {
      background-color: rgba(255, 255, 255, .1) !important;
    }
  }
  .el-menu-item:hover,
  .el-menu-item:focus {
    background-color: rgba(255, 255, 255, .1) !important;
  }
  .el-submenu__title i {
    color: #fff !important;
  }
}

.el-submenu-popper-portal {
}

/* 解决自定义滚动条 x 轴显示问题 */
.scrollbar-wrapper-y.el-scrollbar__wrap {
  overflow-x: hidden;
}

.thinvent-clear-float, .thinvent-clear-fix {
  position: relative;
  zoom:1;
  &:after {
    content: '';
    display:block;
    clear: both;
    height: 0;
    visibility: hidden;
  }
}
.thinvent-ellipsis-line, .thinvent-ellipsis-line {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.thinvent-scrollbar-wrapper {
  height: 100%;
  > .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }
}
// 弹出框 dialog
.thinvent-dialog {
  .el-dialog__header {
    border-bottom: 1px solid #dcdcdc;
  }
  .el-dialog__footer {
    border-top: 1px solid #dcdcdc;
  }
}
.thinvent-dialog-container {
  width: 100%;
}

// 操作按钮 -
.thinvent-el-button {
  padding: 10px 16px;
  font-size: 16px;
  [class^=thinvent-el-icon-] {
    margin-right: 5px;
  }
}

// tree 树
.thinvent-el-tree.el-tree {
  background-color: transparent;
  color: #333;
  & > .el-tree-node {
    background-color: #fff;
    box-shadow: 0px 3px 7px 0px rgba(112,112,112,0.1);
    margin-bottom: 10px;
    & > .el-tree-node__content {
      height: 60px;
      .custom-tree-node {
        .custom-tree-name {
          font-weight: bold;
        }
      }
    }
    &.is-expanded.is-expanded {
      & > .el-tree-node__content {
        color: #fff;
        background-color: var(--themeColor);
        .custom-tree-node.active {
          color: #fff;
        }
        .el-tree-node__expand-icon {
          color: #fff;
        }
      }
    }
    &.is-expanded {
      & > .el-tree-node__content {
        color: #fff;
        background-color: var(--themeColor);
        .custom-tree-node.active {
          color: #fff;
        }
      }
    }
    &.is-current > .el-tree-node__content {
      color: #fff;
      background-color: var(--themeColor);
      .custom-tree-node.active {
        color: #fff;
      }
    }
  }

  .el-tree-node__expand-icon.expanded {
    color: var(--themeColor);
  }
  .el-tree-node__content {
    height: 50px;
    flex-direction: row-reverse;
  }

  .custom-tree-node {
    box-sizing: border-box;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    font-size: 16px;
    /*color: #666;*/

    .custom-tree-name {
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex-grow: 1;
      padding-left: 10px;
    }

    .custom-tree-btn {
      flex-shrink: 0;
      visibility: visible;
    }

    &.active {
      color: var(--themeColor);
    }
  }
}

// list 列表
.thinvent-list-main-content {
  background-color: #fff;
  box-shadow: 0px 3px 7px 0px rgba(112,112,112,0.1);
  margin: 0 0 20px;
  .thinvent-list-main {
    .thinvent-list-item {
      border-bottom: .01rem solid #E5E5E5;
      color: var(--baseColor);
      line-height: 1.2em;
      padding: 20px;
      cursor: pointer;
      position: relative;

      .title-wrap {
        width: 100%;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        margin-bottom: 10px;
        line-height: 20px;
        .thinvent-portal-rate {
          margin-left: 6px;
        }
        &.flex-between {
          justify-content: space-between;
        }
        .left-title-wrap {
          flex-grow: 1;
          display: flex;
          align-items: center;
          overflow: hidden;
          .title {
            max-width: 50%;
          }
        }
        .right-title-wrap {
          flex-shrink: 0;
          margin-top: 0;
        }
      }
      .title {
        font-weight: bold;
        font-size: 18px;
        line-height: 1.2em;
      }
      .sub-title {
        font-size: 16px;
      }
      .p-info {
        margin-top: 18px;
        font-size: 14px;
        .p-info-item {
          color: var(--greyColor);
          margin-right: 40px;
          &:nth-last-child(1) {
            margin-right: 0;
          }
          .label {
            color: var(--greyColor);
          }
          .value {
            color: var(--black);
          }
          .lightColor {
            color: var(--lightColor);
          }
        }
      }
      .p-info.p-info-flex {
        display: flex;
        .p-info-item {
          flex-shrink: 0;
        }
      }
      .p-info-right {
        float: right;
        transform: translateY(50%);
        .p-info-item {
          margin-right: 40px;
          .count {
            color: var(--lightColor);
          }
        }
      }
      &:hover {
        //background-color: #DDECFF;
        //box-shadow: 0 1px 10px rgba(221, 236, 255, .6);
        box-shadow: 0 0 10px 5px rgba(204, 204, 204, 0.6);
      }
      // 资源类型 标签组
      .thinvent-resType-groups {
        margin-left: 10px;
      }
      // 操作按钮组
      .thinvent-el-button-group {
        margin-top: 20px;
      }
      .thinvent-el-button-group-right {
        position: absolute;
        right: 20px;
        bottom: 20px;
        display: inline-block;
        .el-button {
          display: block;
        }
        .el-button+.el-button {
          margin-left: 0;
          margin-top: 10px;
        }
      }
    }
  }
}
