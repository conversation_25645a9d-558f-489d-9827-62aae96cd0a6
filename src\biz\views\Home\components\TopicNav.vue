<template>
  <div class="widgetMyWork">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="专题导航" name="first">
        <template v-if="activeName === 'first'">

          <el-carousel class="mainConte" :autoplay="false" v-if="tabPaneDataFirst.length > itemLength">
            <el-carousel-item v-for="(list, index) in newTabtabPaneDataFirst" :key="index">
              <div class="workItem" v-for="(item, index1) in list" :key="index1" @click="routeToDetail2(item)">
                <img :src="imgList[index1 % 7].imgSrc" alt="" />
                <p class="itemName">{{ item.name }}</p>
              </div>
            </el-carousel-item>
          </el-carousel>
          <div key="3" class="mainConte" v-else>
            <div class="workItem" v-for="(item, index) in tabPaneDataFirst" :key="index" @click="routeToDetail2(item)">
              <img :src="imgList[index % 7].imgSrc" alt="" />
              <p class="itemName">{{ item.name }}</p>
            </div>
          </div>
        </template>
      </el-tab-pane>
      <el-tab-pane label="系统分类" name="second">
        <template v-if="activeName === 'second'">
          <div class="mainConte">
            <el-carousel key="2" class="mainConte" :autoplay="false" v-if="tabPaneDataSecond.length > itemLength">
              <el-carousel-item v-for="(list, index) in newTabtabPaneDataSecond" :key="index">
                <div class="workItem" v-for="(item, index1) in list" :key="index1" @click="routeToDetail(item)">
                  <img :src="imgList[index1 % 7].imgSrc" alt="" />
                  <p class="itemName">{{ item.systemName || item.name }}</p>
                </div>
              </el-carousel-item>
            </el-carousel>
            <template v-else>
              <div class="workItem" v-for="(item, index) in tabPaneDataSecond" :key="index" @click="routeToDetail(item)">
                <img :src="imgList[index % 7].imgSrc" alt="" />
                <p class="itemName">{{ item.systemName || item.name }}</p>
              </div>
            </template>
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {getBaseTypeTreeNotTypeIntro} from "../../../http/modules/pageRequest";

export default {
  name: 'widgetMyWork',
  components: {},
  props: {},
  data() {
    return {
      itemLength: 10,
      activeName: 'first',
      imgList: [
        {
          imgSrc: require('../../../../assets/img/pic-nav1.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav2.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav3.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav4.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav5.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav6.png')
        },
        {
          imgSrc: require('../../../../assets/img/pic-nav7.png')
        }
      ],
      tabPaneDataFirst: [],
      newTabtabPaneDataFirst: [],
      newTabtabPaneDataSecond: [],
      tabPaneDataSecond: []
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },
    handleNewTabtabPaneDataFirst() {
      let allData = JSON.parse(JSON.stringify(this.tabPaneDataFirst))
      this.newTabtabPaneDataFirst = [
        allData.slice(0, this.itemLength),
        allData.slice(this.itemLength, this.itemLength * 2)
      ]
      console.log(this.newTabtabPaneDataFirst)
    },
    handleNewTabtabPaneDataSecond() {
      let allData = JSON.parse(JSON.stringify(this.tabPaneDataFirst))
      this.newTabtabPaneDataSecond = [
        allData.slice(0, this.itemLength),
        allData.slice(this.itemLength, this.itemLength * 2)
      ]
      console.log(this.newTabtabPaneDataSecond)
    },
    getCataTypeCnt() {
      const params = {
        resNameLike: '',
        labelName: '',
        showExistNode: '',
        type: '2',
        baseTypeId: config.baseTypeId || '7',
        name: '',
        systemName: '',
        orgName: '',
        orgId: '',
        currentPage: 1,
        baseType: config.baseTypeId || '7',
        baseTypeInfo: '2',
        onlyShowNotOrder: 0,
        pageSize: 10,
        catalogType: '1,2,3',
        updateCycle: '',
        shareType: ''
      }
      this.$api.bizApi.pageRequest.getBaseTypeTreeNotTypeIntro(params).then((res) => {
        console.log(res)
        this.tabPaneDataFirst = res.data
        if (this.tabPaneDataFirst.length > this.itemLength) {
          this.handleNewTabtabPaneDataFirst()
        }
      })
    },
    getSystemList() {
      this.$api.bizApi.pageRequest.getSystemList({ isRelCataRankFlag: '1' }).then((res) => {
        this.tabPaneDataSecond = res.data.records
        if (this.tabPaneDataSecond.length > this.itemLength) {
          this.handleNewTabtabPaneDataSecond()
        }
      })
    },
    routeToDetail2(item) {
      const href = window.location.href
      let url = new URL(href)
      console.log(href, 'href')
      console.log(item, 'item')

      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/catalog/index',
          query: {
            id: item.id,
            type: item.type,
            pid: item.pid
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataDir',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            id: item.id,
            type: item.type,
            pid: item.pid
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    routeToDetail(item) {
      const href = window.location.href
      let url = new URL(href)
      console.log(item, 'item')
      console.log(href, 'href')
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/catalog/index',
          query: {
            systemName: item.name || item.systemName
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataDir',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            systemName: item.name || item.systemName
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    }
  },
  mounted() {
    this.getCataTypeCnt()
    this.getSystemList()
  }
}
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.widgetMyWork {
  width: 100%;

  >>> .el-tabs {
    // margin-top: 20px;

    .el-tabs__header {
      padding-left: 20px;
      margin: 0;
      background: #ffffff;
      border-bottom: 1px solid #ededed;

      .el-tabs__nav-wrap {
        &::after {
          background-color: transparent;
        }

        .el-tabs__item {
          //font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 24px;
          color: #333;
          line-height: 48px;
        }

        .el-tabs__item.is-active {
          color: #333;
        }

        .el-tabs__active-bar {
          width: 94px !important;
          height: 3px;
          background: $colors;
          border-radius: 2px;
        }

        .el-tabs__nav {
          height: 50px;
        }
      }
    }

    .el-tabs__content {
      .mainConte {
        height: 165px;
        background: #ffffff;
        border-radius: 2px;
        display: flex;
        align-items: center;
        padding: 0 30px;

        .workItem {
          cursor: pointer;
          width: 150px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 10px;
          // margin-right: 50px;

          .itemName {
            margin-top: 16px;
            //font-family: Source Han Sans CN;
            font-size: 18px;
            color: 333;
            font-weight: 600;
            line-height: 20px;
            height: 40px;
          }

          &:hover .itemName {
            color: $themeColor;
          }
        }

        &.flexLeft {
          justify-content: space-around;
        }
      }
    }
  }
}

>>> .el-carousel__container {
  width: 100%;
  height: 165px;

  .el-carousel__arrow {
    display: none;
  }

  .el-carousel__item {
    display: flex;
    align-items: center;
  }
}

>>> .el-carousel__indicator--horizontal {
  padding: 6px;
  background-color: transparent;
  border-radius: 50%;

  .el-carousel__button {
    width: 11px;
    height: 11px;
    background-color: #666666;
    border-radius: 50%;
  }

  &.is-active {
    .el-carousel__button {
      background-color: $colors;
    }
  }
}
</style>
