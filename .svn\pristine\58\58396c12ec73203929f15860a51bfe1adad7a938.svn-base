<template>
  <el-container class="page-container">
    <!-- 左侧树形 -->
    <el-aside v-if="systemCode === 'uims'? true : false">
      <system-tree :resourceCode="resourceCode" @systemTreeTransfer="getSelectSystemId" />
    </el-aside>
    <SplitBar v-if="systemCode === 'uims'? true : false" :value="200" :min="200" :max="500" :size="10" :id="resourceCode"></SplitBar>
    <!-- 右侧列表 -->
    <el-container>
      <!--      列表工具栏-->
      <el-header :class="systemCode === 'uims' ? 'marginLeft0' : null">
        <div class="toolbar-wrapper">
          <perm-button label="添加委托" type="text" icon="add" :perms="systemCode + 'x98004001'" @click="handleAdd" />
        </div>
        <!--      列表查询区-->
        <el-form :inline="true" :model="filters" :size="size">
          <el-form-item label="受托用户">
            <el-input v-model="filters.delegatedUserName" maxlength="100" placeholder="请输入受托用户" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="是否有效">
            <select-plus dictType="IS_FLAG" v-model="filters.valid" clearable style="width: 150px"></select-plus>
          </el-form-item>
          <perm-button type="primary" label="查询" icon="uims-icon-query" @click="handleFilter" />
        </el-form>
      </el-header>
      <!--      列表表格区-->
      <el-main :class="systemCode === 'uims' ? 'marginLeft0' : null">
        <table-plus :key="0" id="UserDelegate" @header-dragend="handleHeaderDrag" row-key="clientId" v-loading="listLoading" :data="list" border fit stripe highlight-current-row ref="multipleTable" @row-click="clickRow" @selection-change="selectMainTableRow">
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column label="委托用户" width="200" prop="delegateUserName" show-overflow-tooltip></el-table-column>
          <el-table-column label="受托用户" width="200" prop="delegatedUserName" show-overflow-tooltip></el-table-column>
          <el-table-column label="委托开始时间" width="200" prop="beginTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="委托结束时间" width="200" prop="endTime" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="是否有效" width="100" align="center" prop="valid"></el-table-column>
          <el-table-column label="操作" align="center" width="240" fixed="right">
            <template slot-scope="{row}">
              <perm-button-group :config="getButtons(row)" />
            </template>
          </el-table-column>
        </table-plus>
      </el-main>
      <el-footer :class="systemCode === 'uims' ? 'marginLeft0' : null">
        <table-footer ref="tableFooter" :showToolBar="true" excelName="委托权限" :showPage="true" :tableRef="this.$refs.multipleTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total"
        url="/delegatePriv/list"
        :filters="filters"
        :resourceCode="resourceCode">
        </table-footer>
      </el-footer>
      <delegate-main-dialog @closeDialog="closeDialog" @getList="getList" :dialogStatus="dialogStatus" :delegateArray="selectTableRow" :dialogFormVisible="dialogFormVisible" :dialogDisabled="dialogDisabled" :currentsyscode="systemCode" :systemName="selectSystemName" :systemId="selectSystemId">
      </delegate-main-dialog>
    </el-container>
  </el-container>
</template>
<script>
import SystemTree from "@/core/views/Common/Tree/SystemTree";
import PermButton from '@/core/components/PermButton'
import PermButtonGroup from '@/core/components/PermButtonGroup';
import TablePlus from "@/core/components/TablePlus";
import DelegateMainDialog from "./Dialog/DelegateMainDialog";
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SelectPlus from "@/core/components/SelectPlus";
import SplitBar from "@/core/components/SplitBar";

import { resourceCode } from "@/biz/http/settings"
export default {
  components: { SplitBar, SystemTree, TablePlus, PermButton, PermButtonGroup, DelegateMainDialog, TableFooter, SelectPlus },
  data() {
    return {
      queryBt: true,
      resourceCode: resourceCode.userDelegatePriv,
      systemCode: config.systemCode,
      currentRow: undefined,
      // form表单-table查询条件相关----👇👇👇👇
      size: 'mini',
      // 列结果
      list: [],
      // 结果总数
      total: 0,
      selectTableRow: undefined,
      // 是否加载中
      listLoading: false,
      selectSystemId: undefined,
      selectSystemName: undefined,
      // 查询过滤条件
      filters: {
        currentPage: 1,
        pageSize: 20,
        valid: undefined,
        delegateUserName: undefined,
        delegatedUserName: undefined,
        systemId: undefined,
        isUserCenter: undefined
      },
      dict: {
        isFlag: []
      },
      // form表单-table查询条件相关----👆👆👆👆
      // 查看窗口数据-👇👇👇👇
      // 查看窗口显示控制
      dialogFormVisible: false,
      dialogStatus: '',
      dialogDisabled: false
      // ---------👆👆👆👆新查看窗口相关👆👆👆👆---------
    }
  },
  methods: {
    getButtons(row) {
      let buttons = []
      if(row.validCode === '1') {
        buttons.push({label: "立刻终止委托", icon: "stop", clickFn: this.handleStop, perms: this.systemCode + 'x98004003'})
      }
      buttons.push({label: "查看", icon: "uims-icon-view", clickFn: this.handleView, perms: this.systemCode + 'x98004002'})
      return {row: row,
        buttons: buttons,
        showNums: 2}
    },
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    closeDialog(val) {
      this.dialogFormVisible = val
    },
    // 如当前为统一用户中心，则需选中系统进行添加
    handleAdd() {
      if (this.systemCode === 'uims') {
        if (this.selectSystemId) {
          // 新增
          this.dialogStatus = 'create';
          this.dialogFormVisible = true;
          this.dialogDisabled = false;
        } else {
          this.$notify({
            title: '提示',
            message: '请先选择系统',
            type: 'info',
            duration: 2000
          })
        }
      } else {
        // 新增
        this.dialogStatus = 'create';
        this.dialogFormVisible = true;
        this.dialogDisabled = false;
      }
    },

    // ---------👇👇👇👇查看相关👇👇👇👇---------
    handleView(row) {
      this.selectTableRow = [row]
      // 根据父级id找到父级资源名称
      this.dialogStatus = 'view'
      this.dialogFormVisible = true
      this.dialogDisabled = true
    },
    // ---------👆👆👆👆查看相关👆👆👆👆---------
    handleStop(row) {
      this.selectTableRow = [row]
      this.$confirm('您确认要终止当前选择的委托吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.userDelegatePriv.stopDelegate({ delegateId: row.delegateId }, resourceCode.userDelegatePriv_stop).then((res) => {
          this.getList()
          this.$notify({
            title: '操作成功',
            message: '终止委托成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消操作',
          type: 'info',
          duration: 2000
        })
      })
    },
    getList() {
      if(!this.queryBt) {
        return;
      }
      let userID = JSON.parse(sessionStorage.getItem('user')).userId;
      this.queryBt = false
      this.listLoading = true;
      this.filters.systemId = this.selectSystemId;
      this.filters.delegateUserId = userID;
      this.$api.userDelegatePriv.getList(this.filters, resourceCode.userDelegatePriv).then((res) => {
        this.queryBt = true
        this.listLoading = false;
        this.list = res.data.records;
        this.total = res.data.total
      }).catch(res => {
        this.queryBt = true
      })
    },
    // 查询
    handleFilter() {
      this.filters.currentPage = 1;
      this.getList()
    },
    // 点击前部checkbox
    selectMainTableRow(row, event, column) {
      this.selectTableRow = Object.assign([], row)
    },
    // 点击行选中checkbox
    clickRow(row) {
      if (this.currentRow === row) {
        this.currentRow = undefined;
        this.$refs.multipleTable.clearSelection();
      } else {
        this.currentRow = row;
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    handleSizeChange(val) {
      this.filters.pageSize = val;
      this.filters.currentPage = 1;
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val;
      this.getList()
    },
    // 树形回调函数
    getSelectSystemId(systemId, systemName) {
      this.selectSystemId = systemId;
      this.selectSystemName = systemName;
      this.filters = {
        currentPage: 1,
        pageSize: 20,
        valid: undefined,
        delegateUserName: undefined,
        delegatedUserName: undefined
      };
      this.getList();
    }
  },
  created() {
    this.getList()
    // this.loadDict()
  }
}
</script>
<style scoped lang="scss">
body .page-container .marginLeft0 {
  margin-left: 0;
}
.page-container >>>.el-aside .queryTree-wrapper .tree-wrapper.query {
  height: calc(100% - 50px);
}
</style>
