<template>
  <div :class="['headbar-wrapper', thumbnailPicture]" :style="backgroundColorStyle">
    <!-- LOGO -->
    <div class="system-icon" @mouseover="onDrawerSystem" v-if="headerConfig && headerConfig.system ? headerConfig.system.show : true">
      <svg-icon icon-class="more"/>
    </div>
    <div class="logo-wrapper" :style="headerConfig.system.show ? '' : 'left: 10px'" v-if="headerConfig && headerConfig.logo ? headerConfig.logo.show : true">
      <div class="logo"><img :src="headerConfig && headerConfig.logo && headerConfig.logo.logoUrl ? headerConfig.logo.logoUrl : `${BASE_URL}config/${thumbnailImg}/header_logo.png`"/></div>
      <p class="platTitle" style="padding-left:8px">{{ headerConfig.logo.top_title }}</p>
      <el-divider direction="vertical" v-if="headerConfig.logo.title"></el-divider>
      <p class="title">{{ headerConfig.logo.title }}</p>
      <p class="space-wrapper" style="padding-left:20px" v-if="spacesList && spacesList.length > 0">
        <el-tooltip class="item" effect="light" content="工作空间" placement="bottom">
          <svg-icon icon-class="wkspace"/>
        </el-tooltip>
        <el-select v-model="spaceId" placeholder="请选择" @change="changeSpace">
          <el-option
            v-for="item in spacesList"
            :key="item.spaceId"
            :label="item.spaceName"
            :value="item.spaceId">
          </el-option>
        </el-select>
      </p>
    </div>
    <!-- UserInfo -->
    <div class="userInfo-wrapper">
      <el-menu class="el-menu-demo" :background-color="backgroundColor" text-color="#14889A" active-text-color="#14889A" mode="horizontal">
        <el-menu-item index="1" v-popover:popover-personal v-if="headerConfig && headerConfig.userInfo ? headerConfig.userInfo.show : true">
          <!-- 用户信息 -->
          <span class="svg-container">
            <svg-icon icon-class="cur-user"/>
          </span>
          <span class="title" style="line-height:1;" v-if="realName[1].length > 0">{{ realName[0] }} <br/> {{ realName[1] }}</span>
          <span class="title" style="line-height:1;" v-else>{{ realName[0] }}</span><!-- <i class="el-icon-caret-bottom title"></i> -->
          <el-popover ref="popover-personal" placement="bottom-end" trigger="hover" :visible-arrow="false" v-model="personalVisible">
            <personal-panel :user="currentUser" @showChgUserDialog="showChgUserDialog" @showChgThemeDialog="showChgThemeDialog" @showChgPwdDialog="showChgPwdDialog"></personal-panel>
          </el-popover>
        </el-menu-item>
        <el-menu-item index="2" v-popover:popover-message style="width: 82px; text-align: center;" v-if="headerConfig && headerConfig.message ? headerConfig.message.show : true">
          <!-- 我的私信 -->
          <el-badge :value="msgTotal" :hidden="msgTotal === 0" :max="99" class="badge" type="danger">
            <i class="fa fa-envelope-o fa-lg"></i>
          </el-badge>
          <el-popover ref="popover-message" placement="bottom-end" trigger="hover" v-model="msgVisible">
            <component v-if="headerConfig.message.panelCompt" :is="messagePanelCompt"></component>
            <message-panel v-else @showMsgDetailPanel="showMsgDetailPanel" :initData="initData"></message-panel>
          </el-popover>
        </el-menu-item>
        <el-menu-item index="3" @click="onDrawerCopyright" v-on:mouseover="changeActive($event)"
                      v-on:mouseout="removeActive($event)" v-if="headerConfig && headerConfig.about ? headerConfig.about.show : true">
          <!--<i ref="dd" class="fa fa-map-signs fa-lg faa-lr animated-hover faa-slow"/><span class="title">关于</span>-->
          <span class="svg-container">
            <svg-icon icon-class="info"/>
          </span><span class="title">关于</span>
        </el-menu-item>
        <el-menu-item index="4" @click.native="logout">
          <!-- 用户退出 -->
          <span class="svg-container">
            <svg-icon icon-class="logout"/>
          </span><span class="title">退出</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
// eslint-disable-next-line no-unused-vars
// import mock from '@/mock/index'
import MessagePanel from '@/core/views/Core/MessagePanel'
import PersonalPanel from '@/core/views/Core/PersonalPanel'
import { logout } from "../utils/logout"
import Cookies from "js-cookie";
export default {
  components: {
    MessagePanel,
    PersonalPanel
  },
  data () {
    return {
      // 消息面板组件实例（业务自行定义），文件路径：src/biz/views/……/*.vue
      messagePanelCompt: null,
      headerConfig: config.headerBar,
      canvas: null,
      BASE_URL: process.env.BASE_URL,
      // logoSrc: `../../../static/config/${config.css}/logo.png`,
      // logoSrc: `../../static/config/${localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css}/header_logo.png`,
      sso: config.sso,
      drawer: false,
      personalVisible: false,
      msgVisible: false
    }
  },
  props: {
    initData: String,
    msgTotal: {type: Number, default: 0}
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      system: state => state.system.system,
      spacesList: state => state.system.spacesList,
      privModel: state => state.system.privModel,
      thumbnailPicture: state => state.app.thumbnail.thumbnailPicture
    }),
    realName: {
      get() {
        return this.measureText(this.currentUser.realName || "", "bold 14px 'Microsoft YaHei", 100)
      }
    },
    spaceId: {
      get() {
        return this.$store.state.system.spaceId
      },
      set(val) {
        this.$store.state.system.spaceId = val
      }
    },
    backgroundColor() {
      var bgColor = /^([0-9a-fA-F]{6}){1}$/g.test(this.$store.state.app.themeColor) ? '#' + this.$store.state.app.themeColor : this.$store.state.app.themeColor
      return bgColor
    },
    backgroundColorStyle() {
      var bgColor = {'background': /^([0-9a-fA-F]{6}){1}$/g.test(this.$store.state.app.themeColor) ? '#' + this.$store.state.app.themeColor : this.$store.state.app.themeColor}
      return bgColor
    },
    systemIconClass: {
      get() {
        return this.$store.state.app.systemDrawer
      },
      set(val) {
        this.$store.state.app.systemDrawer = val
      }
    },
    thumbnailImg() {
      return this.$store.state.app.thumbnail.thumbnailImg ? this.$store.state.app.thumbnail.thumbnailImg : config.css;
    }
  },
  methods: {
    measureText: function(text, font, width) {
      this.canvas = this.canvas || document.createElement("canvas");
      var context = this.canvas.getContext("2d");
      context.font = font;
      let idx = 0;
      while(context.measureText(text.substr(0, text.length - idx++)).width > width) {}
      idx = text.length - --idx;
      this.canvas.remove();
      this.canvas = null;
      return [text.substr(0, idx), text.substr(idx)];
    },
    // 空间变更
    changeSpace: function (newValue) {
      let spaceIdCookies = Cookies.get('spaceId');
      if(!spaceIdCookies) {
        spaceIdCookies = {}
      } else {
        spaceIdCookies = JSON.parse(Cookies.get('spaceId'));
      }
      spaceIdCookies[this.currentUser.userName] = newValue;
      Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
      // sessionStorage.setItem('spaceId', newValue) // 保存token过期时间到本地会话
      // 刷新页面
      this.global.beforeReload = true
      this.$router.push('/intro') // 先返回首页，否则404
      this.$store.state.tab.mainTabs = this.$store.state.tab.mainTabs.filter(item => item.name === '首页')
      location.reload()
    },
    // 折叠系统导航栏
    onDrawerSystem: function () {
      this.$store.commit('onDrawerSystem');
    },
    // 退出登录
    logout: function () {
      this.$confirm('您确认要退出吗？', '提示', {
        type: "warning"
      })
        .then(() => {
          logout()
        })
        .catch(() => {
        })
    },
    // 折叠系统关于导航栏
    onDrawerCopyright: function () {
      this.$store.commit('onDrawerCopyright')
    },
    selectNavBar (key, keyPath) {
    },
    // 鼠标移入加入class
    changeActive($event) {
      // $event.currentTarget.className = "active";
    },
    removeActive($event) {
      $event.currentTarget.className = "";
    },
    // 打开个人中心
    showChgUserDialog: function() {
      this.personalVisible = false
      this.$emit('showChgUserDialog', {})
    },
    // 切换风格
    showChgThemeDialog: function() {
      this.personalVisible = false
      this.$emit('showChgThemeDialog', {})
    },
    // 修改密码
    showChgPwdDialog: function() {
      this.personalVisible = true
      this.$emit('showChgPwdDialog', {})
    },
    // 查看消息
    showMsgDetailPanel: function(parms) {
      this.msgVisible = false
      if(parms) {
        // 复制，否则来新消息会被重复应用，导致消息查看数据别变更
        let newObj = Object.assign(parms)
        this.$emit('showMsgDetailPanel', newObj)
      }
    }
  },
  created() {
    if (this.headerConfig.message.panelCompt) {
      // 如业务上指定，则加载业务上自定义的消息面板
      let path = this.headerConfig.message.panelCompt.endsWith('.vue') ? this.headerConfig.message.panelCompt
              : this.headerConfig.message.panelCompt + '.vue';
      this.messagePanelCompt = resolve => require([`@/biz/views${path}`], resolve);
    }
  }
}
</script>
