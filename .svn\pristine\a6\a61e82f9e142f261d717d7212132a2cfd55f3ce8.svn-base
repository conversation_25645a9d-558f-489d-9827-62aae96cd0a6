<template>
  <div>
    <TDrawer :visible.sync="visible" :size="drawerSize ? drawerSize : 'calc(100vw - 200px)'" :center="true" :autoHeight="true" @open="handleOpen" @close="handleClose" title="选数车">
      <el-form ref="selectNumFormRef" label-width="70px" size="small" label-position="left">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="名称" style="margin-bottom: 0;">
              <el-input v-model="status.filters.name" clearable placeholder="请输入资源名称/能力名称" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="提供单位" style="margin-bottom: 0">
              <el-input v-model="status.filters.orgName" clearable placeholder="请输入提供单位" maxlength="20" />
            </el-form-item>
          </el-col>

          <el-col :span="4">
            <el-button type="primary" size="mini" @click="getList">查询</el-button>
            <el-button el-button class="close-btn" size="mini" @click="resetFilter">重置
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin-top: 20px">
          <el-col :span="19">
            <el-button type="primary" size="mini" icon="el-icon-delete" @click="handleDeleteGroup">删除</el-button>
          </el-col>
        </el-row>
        <div class="tip-box">
          <p><i class="el-icon-info"></i>&nbsp;已选择数量:<span class="blue">{{ status.allSelectTableRow.length }}&nbsp;&nbsp;&nbsp;</span><span class="smaller"><span class="orange">Tips:</span>最多只可一次批量提交30条数据</span></p>
        </div>
        <div class="titleInfo" style="margin: 20px 0">
          <span></span>
          <span>数据资源</span>
        </div>
        <table-plus ref="dataTableRef" :data="status.data1" :loading="status.tableLoading1" height="270" border fit stripe highlight-current-row @selection-change="selectDataMainTableRow" @header-dragend="handleDataHeaderDrag" :header-cell-style="{background:'#4EACFE'}">
          <el-table-column type="selection" width="60" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="providerDeptName" label="提供单位" header-align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="directoryName" label="目录名称" header-align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="resourceType" label="资源类型" align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="resourceName" label="资源名称" header-align="center"  show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.resourceName" class="clickName" @click="handleCheckResDetail(row)">{{ row.resourceName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="shareType" label="共享类型" align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="callOrgCount" label="附件" header-align="center"  width="220" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <el-link v-if="row.orderTipFile" type="primary" @click="downloadOrderTipFile(row)">{{ row.orderTipFile && row.orderTipFile.fileName
                }}<i class="el-icon-download"></i>
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160">
            <template slot-scope="{ row, $index }">
              <perm-button-group :config="getButtons1(row)" />
            </template>
          </el-table-column>
        </table-plus>
        <div class="titleInfo" style="margin: 20px 0">
          <span></span> <span>能力</span>
        </div>
        <TablePlus ref="abilityTableRef" :data="status.data2" :loading="status.tableLoading2" height="270" border fit stripe highlight-current-row @header-dragend="handleAbilityHeaderDrag" @selection-change="selectAbilityMainTableRow" :header-cell-style="{background:'#4EACFE'}">
          <el-table-column type="selection" width="60" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column prop="registerUnitName" label="提供单位" header-align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="powerShape" label="能力形态" align="center" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span>{{ row.powerShapeCode === '1' ? 'API' : row.powerShapeCode === '2' ? 'SDK' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="powerName" label="能力名称" header-align="center"  show-overflow-tooltip>
            <template slot-scope="{ row }">
              <span v-if="row.powerName" class="clickName" @click="handleCheckPowerDetail(row)">{{ row.powerName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="shareType" label="共享类型" align="center"  show-overflow-tooltip></el-table-column>
          <el-table-column prop="callOrgCount" label="附件" width="220" header-align="center"  show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div class="ellipsis-text">
                <el-link v-if="row.powerRemarkAnnexInfo" type="primary" @click="downloadPower(row)">{{
                    row.powerRemarkAnnexInfo && row.powerRemarkAnnexInfo.annexName
                  }}<i class="el-icon-download"></i>
                </el-link>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="160">
            <template slot-scope="{ row }">
              <perm-button-group :config="getButtons2(row)" />
            </template>
          </el-table-column>
        </TablePlus>
      </el-form>
      <template #footer>
        <div class="row">
          <div class="content">
            已选择<span class="num">{{ status.allSelectTableRow.length }}</span>个
          </div>
          <el-button type="primary" size="mini" @click="handleApply">订阅申请</el-button>
          <el-button class="close-btn" size="mini" @click="handleClose">取消</el-button>
        </div>
      </template>
    </TDrawer>

    <!-- 数据订阅 -->
    <SubscribeDialog :dialogVisible.sync="status.dialogVisibleApply" :type="status.type" :data="status.dialogData" :title="status.title" :step="status.step" @resetFilter="resetFilter"  ></SubscribeDialog>

    <SubInfoDialog :dialogVisible.sync="status.infoDialogVisibleApply" :type="status.type" :data="status.infoDialogData" :title="status.title" :step="status.step" @resetFilter="resetFilter"  ></SubInfoDialog>

  </div>
</template>

<script>
import { computed, defineComponent, reactive, getCurrentInstance, ref, nextTick } from 'vue'

import TDrawer from '@/biz/components/t-drawer'
import DialogPlus from '@/core/components/DialogPlus'
import PermButton from '@/core/components/PermButton'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import SelectPlus from '@/core/components/SelectPlus'
import TablePlus from '@/core/components/TablePlus'
import SubscribeDialog from '../components/SubscribeDialog'
import SubInfoDialog from './SubInfoDialog.vue'
import { FileUtils } from '@/biz/utils/download'

import { setDictCache, getDictCache } from '@/core/utils/tabCache'

export default defineComponent({
  name: 'SelectNumDialog',
  components: {
    DialogPlus,
    PermButton,
    PermButtonGroup,
    SelectPlus,
    TablePlus,
    SubscribeDialog,
    SubInfoDialog,
    TDrawer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    drawerSize: {
      type: String
    }
  },
  setup(props, { emit }) {
    const visible = computed({
      get: () => props.visible,
      set: (val) => {
        emit('update:visible', val)
      }
    })
    const { proxy } = getCurrentInstance()
    const dataTableRef = ref()
    const abilityTableRef = ref()
    const subscribeTable = ref()
    const valueTitle = ref('')
    const valueLabel = ref('')

    // const visible = true;
    // onMounted(() => {
    //   getList()
    // })

    const status = reactive({
      dialogData: undefined,
      infoDialogData: undefined,
      dialogVisibleApply: false,
      infoDialogVisibleApply: false,
      dialogVisibleSubscribe: false,
      dialogVisibleParam: false,
      dialogVisibleAdd: false,
      columnType: new Map(), // 字段类型字典映射
      selectTableRow1: [],
      subscribeListTemp: [], // 临时
      subscribeList: new Map(), // 格式: {资源id: 字段信息}
      selectTableRow2: [],
      paramListTemp: [], // 临时
      paramList: new Map(), // 格式: {资源id: 字段信息}
      nowIndex: undefined,
      nowIndex1: undefined,
      resourceList: [{}],
      title: '',
      type: '',
      allSelectTableRow: [],
      tableLoading1: false,
      tableLoading2: false,
      total1: 0,
      total2: 0,
      data1: [],
      data2: [],
      step: 1,
      selectDataTableRow: [],
      selectAbilityTableRow: [],
      filters: {
        currentPage: 1,
        pageSize: 100,
        name: '',
        orgName: ''
      }
    })
    const handleOpen = () => {
      getList()
    }
    const handleClose = () => {
      status.filters = {
        currentPage: 1,
        pageSize: 100,
        name: '',
        orgName: ''
      }
      status.selectDataTableRow = []
      status.selectAbilityTableRow = []
      status.allSelectTableRow = []
      visible.value = false
      let params = {
        IGDPType: 'IGDP_CLOSE_DRAWER'
      }
      window.parent.postMessage(JSON.stringify(params), '*')
    }

    const openDialogSubscribe = async (row, index) => {
      status.resourceList = row
      proxy.$api.bizApi.resApply.getResourcesInfo({ resourceIds: row.resourceId }).then((res) => {
        status.resourceList = res.data.resource || []
        console.log(status.resourceList, 'status.resourceList111')
        status.subscribeListTemp = status.resourceList[index].columnList
      })
      // 判断是否已经选中了，对列表数据进行选中
      if (status.subscribeList.get(row.resourceId)) {
        for (let temp in status.subscribeListTemp) {
          if (
            status.subscribeList.get(row.resourceId).indexOf(status.subscribeListTemp[temp].colId) >
            -1
          ) {
            status.$nextTick(() => {
              subscribeTable.toggleRowSelection(status.subscribeListTemp[temp], true)
            })
          }
        }
      } else {
        for (let temp in status.subscribeListTemp) {
          status.$nextTick(() => {
            subscribeTable.toggleRowSelection(status.subscribeListTemp[temp], true)
            // 此时选中所有同时要存入list中 防止用户直接点关闭而非保存导致无法存入colIds
            const columnIds = []
            for (let column of status.selectTableRow1) {
              columnIds.push(column.colId)
            }
            status.subscribeList.set(status.resourceList[status.nowIndex].resourceId, columnIds)
          })
        }
      }
      console.log(status.subscribeListTemp, 'status.subscribeListTemp')
      status.dialogVisibleSubscribe = true
      status.nowIndex = index
    }

    const getButtons1 = (row) => {
      let buttons = [
        {
          label: '订阅',
          icon: 'download',
          clickFn: batchApply
        },
        {
          label: '删除',
          icon: 'delete',
          clickFn: handleDelete1
        }
      ]
      return {
        row: row,
        buttons,
        showNums: 3
      }
    }

    const getButtons2 = (row) => {
      let buttons = [
        {
          label: '订阅',
          icon: 'download',
          clickFn: handleOpenDialog
        },
        {
          label: '删除',
          icon: 'delete',
          clickFn: handleDelete2
        }
      ]
      return {
        row: row,
        buttons,
        showNums: 3
      }
    }

    const handleApply = (row) => {
      handleOpenInfoDialog(row)
    }
    const handleOpenInfoDialog = () => {
      const allSelectedData = [...status.selectDataTableRow, ...status.selectAbilityTableRow]
      if (allSelectedData.length === 0) {
        proxy.$message({
          title: '提示',
          message: '请选择至少一条数据',
          type: 'warning',
          duration: 2000
        })
      } else {
        status.infoDialogData = {
          selectItems: allSelectedData
        }
        status.infoDialogVisibleApply = true
        let resourceIds = []
        let powerIds = []
        for (const tableRow of status.selectDataTableRow) {
          resourceIds.push(tableRow.resourceId)
        }
        status.infoDialogData.resourceId = resourceIds.toString()
        for (const tableRow of status.selectAbilityTableRow) {
          powerIds.push(tableRow.powerId)
        }
        status.infoDialogData.powerId = powerIds.toString()

        if (status.infoDialogData.resourceId && !status.infoDialogData.powerId) {
          status.type = 'apply'
        } else if (status.infoDialogData.powerId && !status.infoDialogData.resourceId) {
          status.type = 'power'
        } else if (status.infoDialogData.resourceId && status.infoDialogData.powerId) {
          status.type = 'together'
        }
      }
    }
    const handleOpenDialog = (row) => {
      if (row.powerId) {
        status.type = 'power'
        status.title = '能力订阅'
        status.dialogData = row
        status.dialogVisibleApply = true
        status.dialogData.powerId = row.powerId
        console.log('row', row)
      } else {
        if (status.selectAbilityTableRow.length === 0) {
          proxy.$message({
            title: '提示',
            message: '请选择一条数据',
            type: 'warning',
            duration: 2000
          })
        } else {
          status.type = 'power'
          status.dialogVisibleApply = true
          status.title = '能力批量订阅'
          status.dialogData = status.selectAbilityTableRow
          let resArr = []
          for (const tableRow of status.selectAbilityTableRow) {
            resArr.push(tableRow.powerId)
          }
          status.dialogData.powerId = resArr.toString()
        }
      }
    }

    const batchApply = (row) => {
      if (row.resourceId) {
        status.type = 'singleApply'
        status.title = '数据资源订阅'
        status.dialogData = {}
        status.dialogVisibleApply = true
        status.dialogData.resourceId = row.resourceId
      } else {
        // 批量订阅 弹出一个弹框
        if (status.selectDataTableRow.length === 0) {
          proxy.$message({
            title: '提示',
            message: '请选择一条数据',
            type: 'warning',
            duration: 2000
          })
        } else {
          status.step = 2
          status.type = 'batchApply'
          // const ids = this.selectTableRow.map(item => item.id);
          status.dialogVisibleApply = true
          status.title = '数据资源批量订阅'
          status.dialogData = {
            selectDataTableRow: status.selectDataTableRow
          }
          let resArr = []
          for (const tableRow of status.selectDataTableRow) {
            resArr.push(tableRow.resourceId)
          }
          status.dialogData.resourceId = resArr.toString()
        }
      }
    }

    // 表格固定方法
    const selectDataMainTableRow = (selection) => {
      status.selectDataTableRow = Object.assign([], selection)
      status.allSelectTableRow = [...status.selectDataTableRow, ...status.selectAbilityTableRow]
    }

    const downloadOrderTipFile = (row) => {
      proxy.$api.bizApi.resList
        .downLoadOrderTipFile({ id: row.attachId || row.annexId })
        .then((res) => {
          if (res && res.data && res.data.size > 0) {
            let blob = new Blob([res.data], {
              type: 'application/octet-stream;charset=utf-8'
            })
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.download = row.orderTipFile.fileName
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          }
        })
        .catch((e) => {
          proxy.$message.show({
            text: '下载文件失败',
            error: e
          })
        })
    }

    const downloadPower = (row) => {
      console.log(row, 'row')
      proxy.$api.bizApi.pageRequest
        .handleDownloadFile(row.powerRemarkAnnexInfo.annexId)
        .then((res) => {
          FileUtils.fileDownload([res.data], row.powerRemarkAnnexInfo.annexName)
        })
        .catch((e) => {
          proxy.$message.show({
            text: '下载文件失败',
            error: e
          })
        })
    }

    const handleDataHeaderDrag = (newWidth, oldWidth, column, event) => {
      nextTick(() => {
        dataTableRef.doLayout()
      })
    }

    const selectAbilityMainTableRow = (selection) => {
      status.selectAbilityTableRow = Object.assign([], selection)
      status.allSelectTableRow = [...status.selectDataTableRow, ...status.selectAbilityTableRow]
    }

    const handleAbilityHeaderDrag = (newWidth, oldWidth, column, event) => {
      nextTick(() => {
        abilityTableRef.doLayout()
      })
    }

    const handleDeleteGroup = () => {
      if (status.allSelectTableRow.length) {
        // '可以删除'
        proxy
          .$confirm(`您确认要删除选中的？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
          .then(() => {
            let ids = ''
            let ids2 = ''
            // 确定
            if (status.selectDataTableRow && status.selectDataTableRow.length > 0) {
              for (let i = 0; i < status.selectDataTableRow.length; i++) {
                ids += status.selectDataTableRow[i].resourceId
                if (i < status.selectDataTableRow.length - 1) {
                  ids += ','
                }
              }
              deleteResource(ids)
            }

            if (status.selectAbilityTableRow && status.selectAbilityTableRow.length > 0) {
              for (let i = 0; i < status.selectAbilityTableRow.length; i++) {
                ids2 += status.selectAbilityTableRow[i].id
                if (i < status.selectAbilityTableRow.length - 1) {
                  ids2 += ','
                }
              }
              deletePower(ids2)
            }

            emit('getRefresh')
          })
      } else {
        proxy.$notify({
          message: '未选中列表项',
          type: 'info',
          duration: 2000
        })
      }
    }

    const handleDelete1 = (row) => {
      if (row.resourceId) {
        proxy
          .$confirm('您确定要从选数车中删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            status.tableLoading1 = true
            let ids = row.resourceId
            deleteResource(ids)
            emit('getRefresh')
          })
      } else {
        return proxy.$message({
          title: '提示',
          message: '请选择一条数据',
          type: 'warning',
          duration: 2000
        })
      }
    }

    const deleteResource = (ids) => {
      proxy.$api.bizApi.resList
        .deleteCartResource({ resourceIds: ids })
        .then((res) => {
          if (res.code === '200') {
            status.tableLoading1 = false
            proxy.$message({
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            getList()
            emit('getRefresh')
          }
        })
        .catch((e) => {
          status.tableLoading1 = false
          proxy.$message({
            message: e.message,
            type: 'error',
            duration: 2000
          })
        })
    }

    const deletePower = (ids) => {
      proxy.$api.bizApi.resList
        .deleteCartPower({ id: ids })
        .then((res) => {
          if (res.code === '200') {
            status.tableLoading2 = false
            proxy.$message({
              message: '删除成功',
              type: 'success',
              duration: 2000
            })
            getList()
            emit('getRefresh')
          }
        })
        .catch((e) => {
          status.tableLoading2 = false
          proxy.$message({
            message: e.message,
            type: 'error',
            duration: 2000
          })
        })
    }

    const handleDelete2 = (row) => {
      if (row.id) {
        proxy
          .$confirm('您确定要从选数车中删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            status.tableLoading2 = true
            let ids = row.id
            deletePower(ids)
          })
      } else {
        return proxy.$message({
          title: '提示',
          message: '请选择一条数据',
          type: 'warning',
          duration: 2000
        })
      }
    }

    const getDataList = () => {
      const {
        filters: { currentPage, pageSize, name, orgName }
      } = status

      let params = {
        currentPage,
        pageSize,
        orgName,
        resourceName: name
      }
      status.data1 = []
      status.tableLoading1 = true

      proxy.$api.bizApi.resList
        .getCartResourceList(params)
        .then((res) => {
          status.data1 = res.data.records
          status.total1 = res.data.total
          status.data1.forEach((j) => {
            if (['1', '4'].includes(j.resourceTypeCode)) {
              let colIds = []
              ;(j.columnList || []).forEach((k, index) => {
                colIds.push(k.colId)
              })
              status.subscribeList.set(j.resourceId, colIds)
            }
            // if (j.isExternalImport === '9') {
            //   status.containsTheState = '1'
            // }
          })
          status.tableLoading1 = false
        })
        .catch((e) => {
          status.tableLoading1 = false
          proxy.$message({ message: e.message, type: 'error', duration: 2000 })
        })
    }

    const getPowerList = () => {
      const {
        filters: { currentPage, pageSize, name, orgName }
      } = status
      let params = {
        currentPage,
        pageSize,
        registerUnitName: orgName,
        powerName: name
      }
      status.data2 = []
      status.tableLoading2 = true

      proxy.$api.bizApi.resList
        .getCartPowerList(params)
        .then((res) => {
          status.data2 = res.data.records
          status.total2 = res.data.total
          status.tableLoading2 = false
        })
        .catch((e) => {
          status.tableLoading2 = false
          proxy.$message({ message: e.message, type: 'error', duration: 2000 })
        })
    }

    const closedDialogSubscribe = () => {
      status.dialogVisibleSubscribe = false
    }
    const clickRow1 = (row, event, column) => {
      subscribeTable.toggleRowSelection(row)
    }
    const selectMainTableRow1 = (selection) => {
      status.selectTableRow1 = Object.assign([], selection)
    }
    const saveDialogSubscribe = () => {
      // 判断是否选择了主键
      let hasPk = false.subscribeTable.selection.forEach((e) => {
        if (e.fieldIspkCode === '1') {
          hasPk = true
        }
      })
      if (!hasPk) {
        proxy.$message('请选择主键')
        return
      }
      if (status.selectTableRow1.length) {
        const columnIds = []
        for (let column of status.selectTableRow1) {
          columnIds.push(column.colId)
        }
        status.subscribeList.set(status.resourceList[status.nowIndex].resourceId, columnIds)
        closedDialogSubscribe()
      } else {
        status.subscribeList.delete(status.resourceList[status.nowIndex].resourceId)
        closedDialogSubscribe()
      }
    }

    const getColumnTypeMap = () => {
      let dictType = 'ZYML_FIELD_TYPE'
      if (getDictCache(dictType)) {
        let data = getDictCache(dictType)
        for (let key in data) {
          status.columnType.set(key, data[key])
        }
      } else {
        proxy.$api.dict.getDictByType({ type: dictType }).then((res) => {
          setDictCache(dictType, res.data)
          for (let key in res.data) {
            status.columnType.set(key, res.data[key])
          }
        })
      }
    }

    const columnTypeFormatter = (row, column, cellValue, index) => {
      return status.columnType.get(cellValue)
    }

    const getList = () => {
      getDataList()
      getPowerList()
      getColumnTypeMap()
    }

    const resetFilter = () => {
      status.filters = {
        currentPage: 1,
        pageSize: 100,
        name: '',
        orgName: ''
      }
      status.data1 = []
      status.data2 = []
      status.selectDataTableRow = []
      status.selectAbilityTableRow = []
      status.allSelectTableRow = []
      console.log(status.data2, 255)
      getList()
    }

    return {
      visible,
      status,
      closedDialogSubscribe,
      saveDialogSubscribe,
      selectMainTableRow1,
      columnTypeFormatter,
      clickRow1,
      handleOpen,
      handleClose,
      getButtons1,
      getButtons2,
      selectDataMainTableRow,
      selectAbilityMainTableRow,
      handleDataHeaderDrag,
      handleAbilityHeaderDrag,
      openDialogSubscribe,
      dataTableRef,
      abilityTableRef,
      subscribeTable,
      handleDeleteGroup,
      handleDelete1,
      handleDelete2,
      getList,
      resetFilter,
      batchApply,
      handleOpenDialog,
      handleApply,
      downloadPower,
      downloadOrderTipFile
    }
  },
  methods: {
    handleCheckResDetail(data) {
      console.log(11111)
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detailResource/index',
          query: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataRes/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    handleCheckPowerDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detail/index',
          query: {
            powerId: data.powerId
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/power/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            powerId: data.powerId
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    }
  }
})
</script>

<style scoped lang="scss">
.titleInfo {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;

  span:nth-child(1) {
    display: inline-block;
    width: 6px;
    height: 14px;
    opacity: 1;
    border-radius: 15px;
    background: #0084ff;
    margin-right: 8px;
  }
}

/deep/ .el-dialog {
  margin-top: 3vh !important;
  height: auto !important;
}

/deep/ .el-dialog__header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-size: 20px;
  font-weight: bold;
}

>>> .el-table th.el-table__cell > .cell {
  color: #ffffff;
}
>>> el-table td.el-table__cell {
  border-right: 1px solid #EBEEF5;
}
.row {
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    margin-right: 15px;

    .num {
      color: rgba(39, 117, 255, 1);
    }
  }
}

/deep/ .el-switch__core {
  width: 54px !important;
}
/deep/ .el-link {
  display: inline-block; /* 允许文本在一行内显示 */
  width: 100%; /* 或者其他固定宽度，取决于你的单元格宽度 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 用省略号表示被修剪的文本 */
  white-space: nowrap; /* 防止文本换行 */
  cursor: pointer; /* 鼠标悬停时显示指针样式，表明可点击 */
}

.tip-box {
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 10px 7px;
  background-color: #f5faff;
  p {
    font-size: 15px;
    font-weight: normal;
    i {
      color: #3572ff;
    }
    .blue {
      color: #3572ff;
    }
    .smaller {
      font-size: 14px;
      .orange {
        color: #ff8b03;
      }
    }
  }
}
.clickName {
  color: #707275;
  text-decoration: underline;
  cursor: pointer;
}
.clickName:hover {
  color: #1f91f3;
}
</style>
