const cfg = {
  gxslInvokeSwitch: false
}

/**
 * 系统参数设置
 */
const config = {
  // 业务系统编码
  systemCode: cfg.gxslInvokeSwitch ? 'gxsl' : 'sjnl',
  // nacos应用名称
  appCode: cfg.gxslInvokeSwitch ? 'gxslCz' : 'sjnl-igdp', // 'sjnl-yangting' / sjnl-yxh / sjnlxrx
  appCode_auth: "auth",
  appCode_uims: "admin",
  client_id: "app",
  client_secret: "secret",
  cookieName: "thinventToken", // 指定用户cookie名称
  // 设置系统默认皮肤（thinvent、thinvent_darken、orange、blue、green）
  css: "",
  // 设置登录页面风格 jian表示吉安登录背景；thinvent表示统一用户登录背景 shzl表示社会治理背景
  loginBg: "thinvent",
  // 导航菜单栏模式：1纵向导航；2横向导航
  navbar_mode: "1",
  // 共享协议触发模式：1打开弹窗倒计时 2下载共享协议
  trigger_mode: "2",
  // 侧边栏问题反馈是否显示
  showFeedback: true,
  baseTypeId: '7',
  // 页面模式： 'full' 为单页面模式， 不设置为原来的多tab模式
  page_mode: 'full',
  // 首页图标，不设置采用默认的
  // homeIcon: 'spmsHome',
  // 是否显示快捷工具栏
  showRightPanel: true,
  gxslInvokeSwitch: cfg.gxslInvokeSwitch,
  // 平台名称（用于登录页面显示）
  plat_title: "大数据平台",
  // JXYM: 江西云上密码
  certProvider: "JXYM",
  // 登录页版权文字
  copyright: "Copyright © 思创数码科技股份有限公司 版权所有",
  // 登录方式
  loginMode: {
    account: true,
    mobile: false,
    email: false,
    qq: false,
    wx: false,
    sina: false
  },
  // 是否中台应用，用于兼容中台部分功能，设置为false为中台模式，设置为true为非中台模式
  isOnUims: true,
  // 无权限的按钮是否可见：1-可见；0-不可见
  isShowNoPermBtn: 1,
  // token失效处理模式，login 弹出登录窗口;logout 直接退出;refresh 自动刷新
  tokenTimeOutMode: 'refresh',
  // 皮肤范围（配置皮肤缩略图，名称，及各皮肤配套的底图）
  theme: {
    version: "2.0.5",
    thumbnails: [
      {
        label: "默认风格",
        name: "thinvent",
        pictures: [
          { label: "无", name: "nopic2" },
          { label: "流动数据", name: "flow" },
          {
            label: "科技数据",
            name: "science"
          },
          { label: "蜂窝数据", name: "cellular" }
        ],
        picClass: "bgColor"
      },
      {
        label: "深色风格",
        name: "thinvent_darken",
        pictures: [
          { label: "无", name: "nopic" },
          { label: "流动数据", name: "flow" },
          {
            label: "科技数据",
            name: "science"
          },
          { label: "蜂窝数据", name: "cellular" }
        ]
      },
      {
        label: "橙色风格",
        name: "orange",
        pictures: [
          { label: "无", name: "nopic2" },
          { label: "流动数据", name: "flow" },
          {
            label: "科技数据",
            name: "science"
          },
          { label: "蜂窝数据", name: "cellular" }
        ],
        picClass: "bgColor"
      },
      {
        label: "蓝色风格",
        name: "blue",
        pictures: [
          { label: "无", name: "nopic2" },
          { label: "流动数据2", name: "flow2" },
          {
            label: "科技数据2",
            name: "science2"
          },
          { label: "蜂窝数据2", name: "cellular2" }
        ]
      },
      {
        label: "浅色风格",
        name: "green",
        pictures: [
          { label: "无", name: "nopic2" },
          { label: "流动数据2", name: "flow2" },
          {
            label: "科技数据2",
            name: "science2"
          },
          { label: "蜂窝数据2", name: "cellular2" }
        ]
      }
    ]
  },
  errMessage: {
    // API异常信息提醒方式：false-框架统-提醒，true-业务自定
    isCustomShowError: true,
    // API异常信息提醒时，是否允许显示异常信息：true-允许，false-不允许
    isShowErrorTrace: true
  },
  // 单点登录配置
  sso: {
    onoff: false, // true开启单点登录，false不开启单点登录
    url: "http://*************:5252/oauth/authorize", // 此处配置单点登录系统的系统选择页面的访问地址
    isCompatible: false, // 是否兼容老统一用户：false:不兼容，true:兼容
    logoutUrl: "http://*************:5252/#/logout" // model:true时，老统一用户的登出地址
  },
  timeout: 30000, // 超时时间
  wsUrl: "ws://localhost:7070/admin", // 消息接收服务地址
  codeDuration: 120, // 短信验证码发送间隔时间
  openWatermark: true, // 是否开启页面水印
  warnMessage: true, // 是否开启登陆警示标语
  // RSA公钥
  rsaPub:
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmhRWFfGcRc0jX76zrTt8\n" +
    "n4nd/D6dpMt+HW+M39U6Awq2GtZwSQ71YhmTItf0MrpbN84uitGiXj5B2nhtf9It\n" +
    "Cb5fiw4QFw5skpytVrSlAHCgbybVXnvx7kl7+bPPLKF29EroMAOIglFXHFCPaM6m\n" +
    "1AkNf8kF13AOsfHo63LVx9cb/dMO+mv3ia5KEGuBPj5hXY0XO3v7oUFxjVJAdR3j\n" +
    "qO9b+vcAO6JzJgM5xu58YzcAhX8Jnmvs1inMDXKQhxauxs0B/rNApuMHQTw78eW7\n" +
    "aifzRQt/RBargqdqWd+SCzdkxSKcaGSkesNqoX2/V7kuxl+WeIFg5FkyK588Bj3a\n" +
    "0wIDAQAB",
  // RAS公钥切换: 0 默认为当前rsaPub配置; 1 从远程获取
  rsaSwitch: 1,
  // 重载页面是否一次性加载所有字典到页面上 1 是 0 否
  initAllDict: 1,
  // 系统跳转模式，配置为1/true表示使用ticket模式，否则使用token模式
  departmentIn: '委内',
  departmentOut: '委外',
  ticketModel: true,
  // 开关：404时自动跳首页
  no404: false,
  // 是否显示赣政通业务消息配置 false 否 true 是
  gztSwitch: false,
  headerBar: {
    system: {
      show: true
    },
    logo: {
      show: true,
      // logoUrl: 不配用代码中默认的，指定风格下的logo图标
      logoUrl: "",
      // 平台名称（用于登录后TOP区显示）
      top_title: "统一用户V5.4.0",
      // 子系统名称
      title: ""
    },
    userInfo: {
      show: true,
      showThemeConfig: true,
      showChangePassword: true,
      showChangeUser: true,
      // 登录用户名显示宽度
      userNameWidth: 100
    },
    message: {
      show: true,
      showNotice: true,
      showMessage: true,
      showBusiness: true,
      // 消息面板组件路径，指定'src/biz/views'后的路径及文件名。如：'/message/index.vue'
      panelCompt: ""
    },
    about: {
      show: true
    }
  },
  innerItemName: '委内',
  outItemName: '委外',
  shareName: '江西省',
  shareTitle: '省发改委政务数据共享使用协议',
  sharingAgreementContent: function (aName, unitName) {
    return `

甲方：${aName}

乙方：${unitName || ""}

江西省11政务数据共享和开放平台（以下简称“共享开放平台”）主要实现全省政务数据资源整合汇聚和共享开放，提高行政效率，提升服务水平。为更好的规范部门数据资源共享工作，保障数据使用的安全性、规范性和有效性，经双方友好协商，签订如下协议。

一、协议依据

1.《国务院关于印发促进大数据发展行动纲要的通知》(国发〔2015〕50号)。

2.《国务院关于印发政务信息资源共享管理暂行办法的通知》(国发〔2016〕51号)。

3.《国务院关于加快推进“互联网+政务服务”工作的指导意见》(国发〔2016〕55号)。

4.《江西省人民政府关于印发政务信息资源共享管理实施细则的通知》(赣府发〔2017〕2号)。

5.《江西省人民政府关于印发加快推进“互联网+政务服务”工作实施方案的通知》(赣府字〔2017〕2号)。

6.《江西省人民政府办公厅印发关于加快推进全省政务数据共享工作方案的通知》(赣府厅字[2018]95号)。

二、保密信息

1. 在项目中所涉及的以一定形式记录、保存的文件、资料、图表和数据等各类信息资源。

2. 甲方在项目实施中为乙方及乙方工作人员提供必要的数据、文件、用户名、口令和资料等。

3. 其他甲方合理认为，并告知乙方属于保密的内容。

三、甲方的权力和义务

1．甲方为共享开放平台数据提供及使用单位。

2．甲方以共享为原则，不共享为例外，负责将各类政务信息资源按照“无条件共享”“有条件共享”“不予共享”等方式在共享开放平台上进行注册、挂接，涉及国家秘密和安全的，按相关法律法规执行。

3．甲方作为信息资源提供方，需要保证提供给共享开放平台信息资源的有效性、完整性、真实性。不能对共享开放平台正在使用的信息资源进行随意删除、修改，如有变更，应通过共享开放平台提前告知资源使用方。

4．甲方唯一拥有对共享开放平台中所提供信息资源的使用和审核的管理权。第三方订阅甲方信息资源经甲方审核通过后，才能通过乙方所建的共享开放平台提供给申请的第三方；对于为“无条件共享”的信息资源，第三方订阅甲方资源不需要经过审核；对于“不予共享”的信息资源，第三方不能订阅甲方资源。乙方会留存所有资源使用日志，供甲方查询。

5．甲方因工作需要有权向共享开放平台提出信息资源共享需求，在共享交换平台上进行订阅申请获取其他单位信息资源，乙方积极配合甲方和信息资源提供部门进行对接。

6．甲方唯一拥有对其提供在共享开放平台中的信息资源向互联网开放的权限，经甲方授权开放的信息资源的真实性、及时性，由甲方负责。

四、乙方的权力和义务

1．乙方为共享开放平台建设及运维管理单位, 负责共享开放平台稳定运行。

2．乙方应根据甲方需求，对其提供的信息资源进行传输和存储加密，以及敏感数据的脱敏服务，确保信息资源的安全性。

3．乙方需对共享开放平台的信息资源的使用者（包括甲方和乙方运维人员）进行严格的访问权限控制，加强日志审计等安全防护措施，同时建立安全应急处理和容灾恢复机制。

4．乙方作为共享开放平台项目建设及运维管理单位，有权要求甲方提供在履行职责过程中制作或获取的，以一定形式记录、保存的文件、资料、图表和数据等各类信息资源，包括政务部门直接或通过第三方依法采集的、依法授权管理的和因履行职责需要依托政务信息系统形成的信息资源等。

5．乙方有义务对甲方提供的各类信息资源进行有效保护。包括如下措施：

（1）签订保密协议。和本单位内部工作人员、项目承建公司及具体实施人员签订保密协议，从法律层面约束相关工作人员不得违规私自留存与数据共享和开放平台相关的任何形式的文本、电子资料。

（2）制定共享和开放的规则。属于无条件共享类的信息资源，第三方部门在共享平台上直接获取；属于有条件共享类的信息资源，第三方部门通过共享开放平台线上向甲方提出申请订阅，经过甲方审核通过后才能获取数据，对不予共享的，甲方应说明理由。

（3）乙方对甲方提出的共享、开放、加密、脱敏等需求，应及时响应，并提供服务。

五、数据使用范围

甲方对于其提供的所有信息资源，可限定其他使用部门的使用范围、使用期限、使用方式。任何使用方未经甲方授权不得超出甲方规定的使用范围以及给第三方使用。

六、数据保密责任范围

1．甲方保密范围为把资源和目录注册到开放平台上之前的保密和申请后获得的资源的保密。

2．乙方负责整个共享开放平台中所有信息资源的安全和保密工作。

3．甲方通过共享开放平台使用第三方的信息资源时，必须遵照第三方的约定，做好相关的信息资源安全和保密工作。

七、违约责任

1．甲方作为共享开放平台数据的使用者，如因数据被泄露而造成严重后果的，应追究部门领导及当事人法律责任。

2．乙方认真遵守国家保密法律、法规和规章制度及信息安全保护要求，履行保密义务。如因乙方原因造成甲方提供的信息泄密，应追究部门领导及当事人法律责任。

八、协议变更和终止条款

1．由于国家或省有关政策法规变动而必须修改或中止协议，双方协商后，按有关政策法规执行。

2．本协议的任何修改都应该以书面形式提出，经双方授权代表认可、签字盖章后，可与本协议享有同样的法律效力。

3．因地震等不可抗力因素导致乙方共享开放平台服务中断，乙方不承担相应事故责任。

九、其他事项

1．本协议书未尽事项，由双方协商解决。

2．本协议自双方签定之日起生效。

3．本协议书一式二份，甲方双方各一份。

4．如工作需要更改或终止本协议，需甲乙双方同时确认。



---------------[以下无正文，本协议书结束]---------------



`;
  }
};
