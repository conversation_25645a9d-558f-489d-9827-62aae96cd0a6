<template>
  <el-container class="page-container">
    <el-header v-if="pageMode==='full'"></el-header>
    <el-container>
      <el-aside>
        <div class="tree-wrapper" style="height: 100%;">
          <el-scrollbar wrap-class="scrollbar-wrapper" style="height: 100%; width:100%;">
            <el-tree class="list"
                     :props="defaultProps"
                     :data="treeList"
                     lazy
                     :load="loadTreeNodes"
                     node-key='id'
                     highlight-current
                     :expand-on-click-node="false"
                     @node-click="handleNodeClick"
                     ref="inBoxTree"
            >
            <span slot-scope="{ node, data }">
            <span :title="node.label">
              {{node.label}}
            </span>
          </span>
            </el-tree>
          </el-scrollbar>
        </div>
      </el-aside>
      <SplitBar :value="200" :min="200" :max="500" :size="10" :id="resourceCode"></SplitBar>
      <el-container>
        <el-header class="marginLeft0">
          <div class="toolbar-wrapper">
            <perm-button label="移入垃圾箱" type="text" :perms="systemCode + 'x99005003001'" icon="delete"
                         @click="handleDelete" v-if="delButFlag" />
            <perm-button label="彻底删除" type="text" :perms="systemCode + 'x99005003002'" icon="delete"
                         @click="handleRealDelete" />
            <perm-button label="标记已读" type="text" :perms="systemCode + 'x99005003003'" icon="edit"
                         @click="handleRead" v-if="readButFlag" />
            <perm-button label="收藏" type="text" :perms="systemCode + 'x99005003004'" icon="add"
                         @click="handleFile" v-if="fileButFlag" />
            <perm-button label="移出收藏" type="text" :perms="systemCode + 'x99005003006'" icon="edit"
                         @click="handleCancelFile"
                         v-if="cancelFileButFlag" />
            <perm-button label="移出垃圾箱" type="text" :perms="systemCode + 'x99005003007'" icon="edit"
                         @click="handleShiftOut" v-if="shiftOutButFlag" />
          </div>
          <!--列表查询区-->
          <el-form :inline="true" :model="filters" :size="size" @submit.native.prevent>
            <el-form-item label="发件人" v-if="showSenderFlag">
              <el-input style="width:150px;" v-model="filters.senderName" placeholder="请输入发件人" clearable
                        @keyup.enter.native="handleFilter"  maxlength="100"/>
            </el-form-item>
            <el-form-item label="消息名称">
              <el-input v-model="filters.subject" maxlength="100" placeholder="请输入消息名称" clearable @keyup.enter.native="handleFilter"/>
            </el-form-item>
            <el-form-item label="收件时间">
              <el-date-picker v-model="filters.receiveTimeInterVal" type="datetimerange"
                              :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"
                              range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" align="right">
              </el-date-picker>
            </el-form-item>
            <el-form-item v-if="showStatusFlag" label="收件状态">
              <select-extend :data="dictData['MSG_RCVR_STATUS']" v-model="filters.status" clearable :initFunction="initFunction"
                             style="width:150px;"></select-extend>
            </el-form-item>
            <perm-button label="查询" type="primary" icon="uims-icon-query" @click="handleFilter" />
            <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
          </el-form>
        </el-header>
        <!--列表表格区-->
        <el-main class="marginLeft0">
          <table-plus
            @header-dragend="handleHeaderDrag"
            :resourceCode="systemCode + 'x99005003'"
            id="msginbox"
            :data="list"
            ref="multipleTable"
            border
            fit
            default-expand-all
            v-loading="listLoading"
            tooltip-effect="light"
            stripe
            highlight-current-row
            @row-click="clickRow"
            @selection-change="onSelected">
            <el-table-column type="selection" width="60" align="center"></el-table-column>
            <el-table-column prop="senderName" label="发件人" width="200" v-if="showSenderFlag" show-overflow-tooltip></el-table-column>
            <el-table-column prop="spaceName" label="来源空间" width="200" v-if="showSpaceFlag && !isOnUims" show-overflow-tooltip></el-table-column>
            <el-table-column prop="subject" label="消息名称" width="280" show-overflow-tooltip></el-table-column>
            <el-table-column prop="receiveTime" label="收件时间" width="180" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="readTime" label="阅读时间" width="180" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="status" label="收件状态" width="100" align="center" show-overflow-tooltip
                             v-if="showStatusFlag"></el-table-column>
            <el-table-column prop="msgType" label="消息类型" width="100" align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="delTime" label="移入垃圾箱时间" width="180" align="center" v-if="showDelTimeFlag" show-overflow-tooltip>
            </el-table-column>
            <el-table-column width="100" label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <perm-button-group :config="getButtons(row)" />
              </template>
            </el-table-column>
          </table-plus>
        </el-main>
        <el-footer class="marginLeft0">
          <table-footer ref="tableFooter"
                        :showToolBar="true"
                        excelName="收件箱"
                        :showPage="true"
                        :tableRef="this.$refs.multipleTable"
                        @sizeChange="handleSizeChange"
                        @currentChange="handleCurrentChange"
                        :currentPage="filters.currentPage"
                        :pageSizes="[10, 20, 50, 100]"
                        :pageSize="filters.pageSize"
                        url="/msgInBox/list"
                        :filters="filters"
                        :resourceCode="resourceCode"
                        :total="total">
          </table-footer>
        </el-footer>
      </el-container>
    </el-container>
    <msg-in-box-main-dialog
      @closeDialog="closeDialog"
      :miId="msgInBoxMiId"
      :needReceipt="needReceipt"
      :readFlag="readFlag"
      :dialogModes="dialogModes"
      @getList="getList"
      :dialogFormVisible="dialogFormVisible">
    </msg-in-box-main-dialog>
  </el-container>
</template>

<script>
  import PermButton from '@/core/components/PermButton'
  import TablePlus from "@/core/components/TablePlus";
  import SelectExtend from "@/core/components/SelectExtend";
  import TableFooter from "@/core/components/TablePlus/TableFooter";
  import {resourceCode} from "@/biz/http/settings";
  import MsgInBoxMainDialog from "./Dialog/MsgInBoxMainDialog";
  import PermButtonGroup from '@/core/components/PermButtonGroup';
  import SplitBar from "@/core/components/SplitBar";

  export default {
    components: {SplitBar, MsgInBoxMainDialog, SelectExtend, TablePlus, PermButton, TableFooter, PermButtonGroup},
    data() {
      return {
        resourceCode: resourceCode.msgInBox,
        isOnUims: config.isOnUims,
        queryBt: true,
        // table参数
        systemCode: config.systemCode,
        size: 'mini',
        list: [],
        listLoading: true,
        total: 0,
        currentRow: undefined,
        treeList: [],
        defaultProps: {
          children: 'children',
          label: 'label',
          isLeaf: 'leaf'
        },
        dictData: {},
        oldClickTreeId: '',
        selectTreeName: '',
        selectTableRow: [],
        // table查询参数
        initFunction: function (data) {
          if(typeof data !== "undefined") {
            delete data["3"];
          }
        },
        filters: {
          currentPage: 1,
          pageSize: 20,
          receiveTimeInterVal: undefined,
          receiveTimeStart: undefined,
          receiveTimeEnd: undefined,
          senderName: undefined,
          subject: undefined,
          status: undefined, // 收件状态 0已收件 1已阅读 2已撤回 3已删除
          fileFlag: undefined, // 是否收藏 0否 1是
          msgType: undefined, // 消息类型 0通知公告 1站内消息 2业务消息
          msgBizType: undefined // 业务类型 （T_SYS_MSG_BIZ.MSG_BIZ_TYPE）
        },
        // 表单的参数设置
        dialogFormVisible: false,
        dialogModes: '',
        msgInBoxMiId: undefined,
        needReceipt: undefined,
        readFlag: '1',
        showSenderFlag: true,
        showSpaceFlag: true,
        showDelTimeFlag: false,
        showStatusFlag: true,
        readButFlag: true,
        delButFlag: true,
        fileButFlag: true,
        shiftOutButFlag: false,
        cancelFileButFlag: false,
        pickerOptions: {
          shortcuts: [{
            text: '最近一小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近十二小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 12);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        }
      }
    },
    computed: {
      pageMode() {
        return config.page_mode;
      }
    },
    // 执行方法
    methods: {
      getButtons(row) {
        return {row: row,
          buttons: [{
            label: "查看",
            icon: "see",
            clickFn: this.handleView,
            perms: this.systemCode + 'x99005003005',
            params: {}}],
          showNums: 2}
      },
      // 监听表头拖拽事件
      handleHeaderDrag(newWidth, oldWidth, column, event) {
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      },
      closeDialog(val) {
        this.dialogFormVisible = val
      },
      timeFormat(val) {
        if (val != null) {
          var date = new Date(val);
          return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
        }
      },
      handleFilter(resetFlag = false) {
        if (resetFlag === true) {
          this.filters.receiveTimeInterVal = undefined
          this.filters.receiveTimeStart = undefined
          this.filters.receiveTimeEnd = undefined
          this.filters.senderName = undefined
          this.filters.subject = undefined
          // 垃圾箱 重置不处理status
          if (this.filters.status !== '3') {
            this.filters.status = undefined
          }
        }
        this.filters.currentPage = 1
        if (this.filters.receiveTimeInterVal && this.filters.receiveTimeInterVal.length === 2) {
          this.filters.receiveTimeStart = this.timeFormat(this.filters.receiveTimeInterVal[0])
          this.filters.receiveTimeEnd = this.timeFormat(this.filters.receiveTimeInterVal[1])
        } else {
          this.filters.receiveTimeStart = undefined
          this.filters.receiveTimeEnd = undefined
        }
        this.getList();
      },
      async getInBoxTree() {
        let leftMenu = []
        let temp = {}
        temp.id = 'allmail'
        temp.label = '全部消息'
        temp.leaf = true
        leftMenu.push(temp)

        if(this.$store.state.app.configs.headerBar.message.showNotice) {
          temp = {}
          temp.id = 'notice'
          temp.label = '通知公告'
          temp.leaf = true
          leftMenu.push(temp)
        }

        if(this.$store.state.app.configs.headerBar.message.showMessage) {
          temp = {}
          temp.id = 'msg'
          temp.label = '站内消息'
          temp.leaf = true
          leftMenu.push(temp)
        }

        if(this.$store.state.app.configs.headerBar.message.showBusiness) {
          temp = {}
          let isFlag = true
          await this.$api.msgInBox.getTree(null, resourceCode.msgInBox).then((res) => {
            if (res && res.data && res.data.length > 0) {
              isFlag = false
            } else {
              isFlag = true
            }
          })
          temp.id = 'biz'
          temp.label = '业务消息'
          temp.leaf = isFlag
          leftMenu.push(temp)
        }

        temp = {}
        temp.id = 'file'
        temp.label = '收藏夹'
        temp.leaf = true
        leftMenu.push(temp)

        temp = {}
        temp.id = 'del'
        temp.label = '垃圾箱'
        temp.leaf = true
        leftMenu.push(temp)

        this.treeList = leftMenu
      },
      async loadTreeNodes(node, resolve) {
        if (node.level >= 1) {
          if (node.data && node.data.children && node.data.children.length) {
            return resolve(node.data.children)
          }else if (node.key === 'biz') {
            let msgBizType = []
            let fun = function (arrData, arr) {
              if (arrData && arrData.length) {
                arrData.forEach(el => {
                  let temp = {}
                  temp.id = el.dictId
                  temp.label = el.name
                  temp.code = el.dictId
                  if (el.nodes && el.nodes.length) {
                    temp.children = []
                    temp.leaf = false
                    fun(el.nodes, temp.children)
                  } else {
                    temp.leaf = true
                  }
                  arr.push(temp)
                })
              }
            }
            // 创建业务消息子节点
            this.$api.msgInBox.getTree(null, resourceCode.msgInBox).then((res) => {
              let tempData = res.data;
              fun(tempData, msgBizType)
              return resolve(msgBizType)
            })
          }
        } else {
          return resolve(this.treeList);
        }
      },
      handleView(row) {
        if (row.outStatus === '3') {
          this.$notify({
            title: '提示',
            message: '邮件已被撤回，无法查看',
            type: 'info',
            duration: 2000
          })
          return
        }
        this.msgInBoxMiId = row.miId
        this.needReceipt = row.needReceipt
        if (row.readTime) { // 该邮件已阅读
          this.readFlag = '0'
        } else {
          this.readFlag = '1'
        }
        this.dialogModes = 'view';
        this.dialogFormVisible = true;
      },
      resetFilter() {
        this.filters = {
          currentPage: 1,
          pageSize: 20,
          status: undefined, // 收件状态 0已收件 1已阅读 2已撤回 3已删除
          fileFlag: undefined, // 是否收藏 0否 1是
          msgType: undefined, // 消息类型 0通知公告 1站内消息 2业务消息
          msgBizType: undefined // 业务类型 （T_SYS_MSG_BIZ.MSG_BIZ_TYPE）
        }
        this.showSpaceFlag = true
        this.showSenderFlag = true
        this.showDelTimeFlag = false
        this.showStatusFlag = true
        this.cancelFileButFlag = false
        this.shiftOutButFlag = false
        this.readButFlag = true
        this.delButFlag = true
        this.fileButFlag = true
      },
      handleNodeClick(item, Node) {
        this.resetFilter()
        let id = item.id
        // if (this.oldClickTreeId === id) {
        //   // 取消选择
        //   this.$refs.inBoxTree.setCurrentKey();
        //   this.oldClickTreeId = ''
        // } else {
        //   this.oldClickTreeId = id
          if (id === 'allmail') {
            this.showSpaceFlag = true
          } else if (id === 'notice') {
            this.filters.msgType = '0'
            this.showSpaceFlag = false
          } else if (id === 'msg') {
            this.filters.msgType = '1'
            this.showSpaceFlag = false
          } else if (id === 'biz') {
            this.filters.msgType = '2'
            this.showSpaceFlag = true
            this.showSenderFlag = false
          } else if (id === 'file') {
            this.filters.fileFlag = '1'
            this.fileButFlag = false
            this.cancelFileButFlag = true
          } else if (id === 'del') {
            this.filters.status = '3'
            this.delButFlag = false
            this.readButFlag = false
            this.fileButFlag = false
            this.shiftOutButFlag = true
            this.showDelTimeFlag = true
            this.showStatusFlag = false
          } else {
            this.filters.msgBizType = id
            this.filters.msgBizType = item.code
            this.showSenderFlag = false
          }
        // }
        this.getList();
      },
      getList() {
        if (!this.queryBt) {
          return;
        }
        this.queryBt = false
        this.listLoading = true
        const filter = Object.assign({}, this.filters)
        delete filter.receiveTimeInterVal
        this.$api.msgInBox.getList(filter, resourceCode.msgInBox).then((res) => {
          this.queryBt = true
          this.listLoading = false
          this.list = res.data.records
          this.total = res.data.total
        }).catch(res => {
          this.queryBt = true
        })
      },
      clickRow(row, event, column) {
        if (this.currentRow === row) {
          this.currentRow = undefined
          this.$refs.multipleTable.clearSelection();
        } else {
          this.currentRow = row
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(row)
        }
      },
      onSelected(row, event, column) {
        this.selectTableRow = Object.assign([], row);
      },
      handleSizeChange(val) {
        this.filters.pageSize = val
        this.filters.currentPage = 1;
        this.getList()
      },
      handleCurrentChange(val) {
        this.filters.currentPage = val
        this.getList()
      },
      // 删除
      handleDelete() {
        if (this.selectTableRow.length > 0) {
          let miIds = ''
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (i === this.selectTableRow.length - 1) {
              miIds += this.selectTableRow[i].miId
            } else {
              miIds += this.selectTableRow[i].miId + ','
            }
          }
          this.$confirm('您确认要将选中的消息移入垃圾箱吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.del({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '消息成功移入垃圾箱',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消移入垃圾箱',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      },
      // 彻底删除
      handleRealDelete() {
        if (this.selectTableRow.length > 0) {
          let miIds = ''
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (i === this.selectTableRow.length - 1) {
              miIds += this.selectTableRow[i].miId
            } else {
              miIds += this.selectTableRow[i].miId + ','
            }
          }
          this.$confirm('您确认要彻底删除当前选中的消息吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.realDel({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '彻底删除消息成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消彻底删除',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      },
      // 标记已读
      handleRead() {
        if (this.selectTableRow.length > 0) {
          let miIds = []
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (this.selectTableRow[i].statusCode === '2') {
              continue
            }
            if (this.selectTableRow[i].statusCode === '1') {
              continue
            }
            miIds.push(this.selectTableRow[i].miId)
          }
          if(!miIds || miIds.length < 1) {
            this.$notify({
              title: '提示',
              message: '只能将未阅读状态的消息标记为已阅读',
              type: 'info',
              duration: 2000
            })
            return
          }
          miIds = miIds.join(',')
          this.$confirm('您确认要把当前选中的消息标记为已读吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.read({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '标记已读成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              title: '提示',
              type: 'info',
              message: '已取消标记已读'
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      },
      // 收藏
      handleFile() {
        if (this.selectTableRow.length > 0) {
          let miIds = ''
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (this.selectTableRow[i].statusCode === '2') {
              this.$notify({
                title: '提示',
                message: '存在已被撤回的邮件，无法收藏',
                type: 'info',
                duration: 2000
              })
              return
            }
            if (i === this.selectTableRow.length - 1) {
              miIds += this.selectTableRow[i].miId
            } else {
              miIds += this.selectTableRow[i].miId + ','
            }
          }
          this.$confirm('您确认要将选中的消息移入收藏夹吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.file({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '收藏成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消收藏',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      },
      handleCancelFile() {
        if (this.selectTableRow.length > 0) {
          let miIds = ''
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (i === this.selectTableRow.length - 1) {
              miIds += this.selectTableRow[i].miId
            } else {
              miIds += this.selectTableRow[i].miId + ','
            }
          }
          this.$confirm('您确认要将选中的消息移出收藏夹吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.cancelFile({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '取消收藏成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已撤消取消收藏操作',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      },
      handleShiftOut() {
        if (this.selectTableRow.length > 0) {
          let miIds = ''
          for (let i = 0; i < this.selectTableRow.length; i++) {
            if (i === this.selectTableRow.length - 1) {
              miIds += this.selectTableRow[i].miId
            } else {
              miIds += this.selectTableRow[i].miId + ','
            }
          }
          this.$confirm('您确认要将选中的消息移出垃圾箱吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgInBox.shiftOut({miIds: miIds}, resourceCode.msgInBox).then((res) => {
              this.getList();
              this.$notify({
                title: '操作成功',
                message: '移出成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消移出',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择一条记录',
            type: 'info',
            duration: 2000
          })
        }
      }
    },
    created() {
      this.$api.dict.getDictionaries({types: 'MSG_RCVR_STATUS'}).then((res) => {
        this.dictData = res.data
        this.getList();
        this.getInBoxTree();
      }).catch(e => {
        // 展示错误信息
        this.$errMsgBox.show({
          text: '加载信息出错',
          error: e
        });
      })
    }
  }
</script>

<style scoped lang="scss">
body .page-container .marginLeft0 {
  margin-left: 0;
}
</style>
