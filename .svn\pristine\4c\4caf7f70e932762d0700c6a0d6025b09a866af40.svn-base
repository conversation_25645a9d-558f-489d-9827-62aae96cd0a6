<template>
  <div class="instant-msg-container">
    <div class="top-container">
      <span class="newMsg">新消息</span>
      <span class="newItem">您有以下新事项</span>
    </div>
    <div class="msg-container">
      <span class="msgType">{{msgTypeName}}</span>
      <span class="msgTime">{{newMsg.receiveTime}}</span>
      <span class="msgTitle" @click="handleClick(newMsg.miId)">{{newMsg.subject}}</span>
    </div>
  </div>
</template>

<script>
import { singleNotify } from '@/core/utils/singleNotify'
export default {
  name: 'PersonalWebSocket',
  props: {
    newMsg: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed: {
    msgTypeName() {
      const MsgTypeName = ['通知公告', '私人发信', '业务消息'];
      return MsgTypeName[this.newMsg.msgType];
    }
  },
  methods: {
   /*  getSocketData(symbol) {
      let data = { "event": "subscription", "data": "market.kline." + symbol };
      this.sendMsg(data, ev => {
        console.log(JSON.parse(ev.data))
      })
    }, */
    // 查看消息
    handleClick(id) {
      this.$emit('showMsgDetailPanel', {msg: this.newMsg});
      singleNotify.close();
    }
  }
}
</script>
