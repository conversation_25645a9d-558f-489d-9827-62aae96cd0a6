<!--
 * @Description: 扩展el-select组件（支持多选合并模式下，第一个超长标签宽度自适应）
 * @Author: 刘芸
 * @Date: 2023-09-22 09:32:16
 * @LastEditTime: 2023-09-22 14:28:04
 * @LastEditors: 刘芸
-->
<script>
import { Select } from "element-ui";

export default {
  extends: Select, // 继承el-select
  name: "MultipleSelect",
  methods: {
    resetInputHeight() {
      this.$nextTick(() => {
        if (!this.multiple && !this.collapseTags && !this.$refs.reference) return;
        const tags = this.$refs.tags;
        if(tags.children.length === 0) return;
        const children = tags.children[0].children;
        if(children.length === 2) {
          let tagsWidth = tags.getBoundingClientRect().width
          let child0Width = children[0].getBoundingClientRect().width
          let child1Width = children[1].getBoundingClientRect().width
          if(child0Width + child1Width + 12 >= tagsWidth) {
            let w = tagsWidth - child1Width - 12
            let ratio = w / tagsWidth * 100
            children[0].style['max-width'] = ratio + '%'
          }
        } else {
          children[0].style['max-width'] = '100%'
        }
      });
    }
  }
};
</script>
