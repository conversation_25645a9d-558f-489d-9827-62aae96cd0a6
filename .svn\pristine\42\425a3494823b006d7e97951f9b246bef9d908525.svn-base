<script>
  import {Dialog} from 'element-ui'

  export default {
    extends: <PERSON><PERSON>, // 继承el-dialog
    name: 'DialogPlus',
    props: {
      autoHeight: {type: Boolean, default: false}
    },
    watch: {
      visible(val) {
        if(val && this.autoHeight) {
          let s = this.$el.querySelector('.el-dialog').style
          !s.height && s.setProperty('height', 'auto')
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
.el-dialog {
  display: flex;
  flex-direction: column;
  height: calc(100% - 30vh);
  .el-dialog__header {
    display: flex;
  }
  >>>.el-dialog__headerbtn {
    color: #909399;
    i.r180 {
      transform: rotate(180deg);
      top: -1px;
      position: relative;
    }
    &:hover {
      color: #409EFF;
    }
  }
  .el-dialog__body {
    max-height: unset;
    flex: 1;
    >>>.el-container {
      height: 100%;
    }
  }
}
</style>
