<template>
  <el-container class="page-container">
    <el-main>
      <div class="basicMes">
        <div class="title">{{ pageData.resourceName }}</div>
        <div class="centerPart">
          <div class="leftPart">
            <img src="~@/assets/img/pic-shujuziyuanshu.svg" alt="" />
          </div>
          <div class="middlePart">
            <el-row>
              <el-col :span="12" v-if="detailData.providerDeptName">
                <div class="singleMes">
                  提供单位：<span>{{ detailData.providerDeptName }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.publishTime">
                <div class="singleMes">
                  发布时间：<span>{{ pageData.publishTime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="detailData.sharindMethod">
                <div class="singleMes">
                  共享方式：<span>{{ detailData.sharindMethod }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.basicInfoUpdatetime">
                <div class="singleMes">
                  更新时间：<span>{{ pageData.basicInfoUpdatetime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="pageDataCataName">
                <div class="singleMes">
                  所属目录：<span>{{ pageDataCataName }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="detailData.updateCycle">
                <div class="singleMes">
                  更新周期：<span>{{ detailData.updateCycle }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="detailData.resClassification">
              <el-col :span="24">
                <div class="singleMes">
                  目录分类：<span>{{ detailData.resClassification }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="rightPart">
            <div class="itemStatus">
              <span
                style="color: #409eff; border: 1px solid #d9ecff; background-color: #ecf5ff"
                class="info-tip"
                v-if="detailData.shardType && detailData.shardType !== ''"
                >{{ detailData.shardType }}</span
              >
              <span
                style="color: #409eff; border: 1px solid #d9ecff; background-color: #ecf5ff"
                class="info-tip"
                v-if="detailData.openType && detailData.openType !== ''"
                >{{ detailData.openType }}</span
              >
            </div>
            <div class="right-info">
              <!-- 非回流数据目录 -->
              <template v-if="detailData.catalogTypeCode !== '3'">
                <div
                  class="type-info"
                  :class="{
                    active: detailData.resourceType && detailData.resourceType.indexOf('1') > -1
                  }"
                >
                  <svg-icon icon-class="gxsl-kubiao" />
                  <p>库表</p>
                </div>
                <div
                  class="type-info"
                  :class="{
                    active: detailData.resourceType && detailData.resourceType.indexOf('2') > -1
                  }"
                >
                  <svg-icon icon-class="gxsl-wenjian" />
                  <p>文件</p>
                </div>
                <div
                  class="type-info"
                  :class="{
                    active: detailData.resourceType && detailData.resourceType.indexOf('3') > -1
                  }"
                >
                  <svg-icon icon-class="gxsl-jiekou" />
                  <p>接口</p>
                </div>
                <div
                  class="type-info"
                  :class="{
                    active: detailData.resourceType && detailData.resourceType.indexOf('5') > -1
                  }"
                >
                  <svg-icon icon-class="gxsl-wenjianjia" />
                  <p>文件夹</p>
                </div>
              </template>
              <!-- 回流数据目录 -->
              <template v-else>
                <div
                  class="type-info"
                  :class="{
                    active: detailData.resourceType && detailData.resourceType.indexOf('4') > -1
                  }"
                >
                  <svg-icon icon-class="gxsl-huiliu" />
                  <p>回流</p>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div class="bottomPart">
          <div class="itemNum">
            <span class="num">{{ pageData.orderSum || 0 }}次<span>订阅</span></span>
            <span class="num">{{ pageData.browseSum || 0 }}次<span>访问</span></span>
            <span class="num">{{ pageData.collectSum || 0 }}次<span>收藏</span></span>
          </div>
          <div class="itemHandleBtn">
            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              disabled
              v-if="detailData.providerCode === (currentUser ? currentUser.unitCode : null)"
            >
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              v-else-if="!pageData.resourceType"
              :disabled="!pageData.resourceType"
            >
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              v-else
              class="icsp-button"
              plain
              @click.stop="handleResourceApply(pageData)"
            >
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>

            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              disabled
              v-if="detailData.providerCode === (currentUser ? currentUser.unitCode : null)"
            >
              <svg-icon icon-class="gxsl-shoppingCart" />
              加入选数车
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              :disabled="!pageData.resourceType"
              v-else-if="!pageData.resourceType"
            >
              <svg-icon icon-class="gxsl-shoppingCart" />
              {{ buttonText }}
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              v-else
              :disabled="queryBt"
              :class="detailData.resourceAddSelect === '1' ? 'icsp-button2' : 'icsp-button'"
              plain
              @click.stop="handleAddPowerToCart(detailData)"
            >
              <svg-icon icon-class="gxsl-shoppingCart" />
              {{ buttonText }}
            </el-button>

            <el-button
              class="icsp-button-grey"
              disabled
              v-if="detailData.providerCode === (currentUser ? currentUser.unitCode : null)"
            >
              <svg-icon icon-class="gxsl-collect" />
              收藏
            </el-button>
            <el-button
              v-else
              :disabled="queryBt"
              :class="pageData.collect ? 'icsp-button2' : 'icsp-button'"
              plain
              @click.stop="handleCollect(pageData)"
            >
              <svg-icon icon-class="gxsl-collect" />
              {{ pageData.collect ? '取消收藏' : '收藏' }}
            </el-button>

            <el-button
              @click.stop="handleLeaveAMessage(pageData)"
              :class="pageData.collect ? 'icsp-button2' : 'icsp-button'"
            >
              <svg-icon icon-class="feedback" />
              留言
            </el-button>
            <!--            <el-button class="icsp-button-grey" disabled v-if="pageData.providerCode === (currentUser ?  currentUser.unitCode : null)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
            <!--            <el-button v-else class="icsp-button" plain @click.stop="handleFeedback(pageData)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
          </div>
        </div>
      </div>
      <div class="otherMes">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="信息项" name="0" v-if="pageData.columnList">
            <div class="mainConte mainConteFirst">
              <div class="fullPart">
                <div class="fullContent">
                  <el-table
                    :data="pageData.columnList"
                    border
                    stripe
                    style="width: 100%"
                    class="projectTable"
                  >
                    <el-table-column
                      type="index"
                      label="序号"
                      width="120"
                      align="center"
                    ></el-table-column>
                    <el-table-column
                      prop="fieldName"
                      label="信息项名称"
                      min-width="140"
                      header-align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="fieldType"
                      label="数据类型"
                      align="center"
                      min-width="120"
                    ></el-table-column>
                    <el-table-column
                      prop="fieldIsopen"
                      label="是否向社会开放"
                      align="center"
                      header-align="center"
                      min-width="120"
                    >
                      <template slot-scope="scope">
                        <div v-if="scope.row.fieldIsopen === '1'">是</div>
                        <div v-else>否</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <template v-if="pageData.resourceTypeCode">
            <el-tab-pane
              label="库表"
              name="1"
              v-if="pageData.resourceTypeCode.split(',').includes('1')"
            >
              <div class="mainConte">
                <div class="leftPart">
                  <div
                    class="tableItem"
                    :title="item.tableName"
                    :class="{ active: fifthActiveId === item.tableId }"
                    v-for="item in fifthTableList"
                    :key="item.tableId"
                    @click="fifthPaneTableCheck(item)"
                  >
                    {{ item.tableName }}
                  </div>
                </div>
                <div class="rightPart">
                  <div class="rightContent" v-if="fifthActiveTableData">
                    <div class="rightMessage">
                      <div class="titlepart">
                        <p>
                          {{ fifthActiveTableData.tableName }}({{
                            fifthActiveTableData.tableNameCn
                          }})
                        </p>
                        <div class="line"></div>
                        <span class="message" v-if="fifthActiveTableData.providerDeptName"
                          >提供单位：<span>{{ fifthActiveTableData.providerDeptName }}</span></span
                        >
                        <span class="message" v-if="fifthActiveTableData.updateCycle"
                          >更新周期：<span>{{ fifthActiveTableData.updateCycle }}</span></span
                        >
                        <span class="message" v-if="fifthActiveTableData.dataUpdateTime"
                          >更新时间：<span>{{ fifthActiveTableData.dataUpdateTime }}</span></span
                        >
                        <br />
                        <span class="message" v-if="fifthActiveTableData.resourceType"
                          >资源分类：<span>{{ fifthActiveTableData.resourceType }}</span></span
                        >
                        <span class="message"
                          >数据量：<span>{{ fifthActiveTableData.dataSize }}条</span></span
                        >
                      </div>
                      <div class="bottomPart">
                        <div class="itemStatus">
                          <span>
                            {{ fifthActiveTableData.shareType }}
                          </span>
                          <span>
                            {{ fifthActiveTableData.openType }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <el-table
                      :data="fifthActiveTableData.columns"
                      border
                      stripe
                      :key="'dataVoList'"
                      style="width: 100%"
                      class="projectTable"
                    >
                      <el-table-column
                        type="index"
                        label="序号"
                        width="80"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldCode"
                        label="字段名称"
                        min-width="160"
                        show-overflow-tooltip
                        header-align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldName"
                        label="信息项名称"
                        min-width="160"
                        show-overflow-tooltip
                        header-align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldType"
                        label="字段类型"
                        width="160"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldLength"
                        label="字段长度"
                        width="100"
                        align="right"
                        header-align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldIspk"
                        label="是否主键"
                        width="100"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="fieldIsnull"
                        label="可否为空"
                        width="100"
                        align="center"
                      >
                        <template slot-scope="scope">
                          <div v-if="scope.row.fieldIsnull === '1'">是</div>
                          <div v-else>否</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="服务"
              name="3"
              v-if="pageData.resourceTypeCode.split(',').includes('3')"
            >
              <div class="mainConte">
                <div class="leftPart">
                  <div
                    class="tableItem"
                    :title="item.serviceName"
                    :class="{ active: sixthActiveId === item.serviceId }"
                    v-for="item in sixthTableList"
                    :key="item.serviceId"
                    @click="sixthPaneTableCheck(item)"
                  >
                    {{ item.serviceName }}
                  </div>
                </div>
                <div class="rightPart">
                  <div class="rightContent" v-if="sixthActiveTableData">
                    <div class="titlepart">
                      <p>{{ sixthActiveTableData.serviceName }}</p>
                      <div class="line"></div>
                    </div>
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="服务名称"
                        >{{ sixthActiveTableData.serviceName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务类型"
                        >{{ sixthActiveTableData.interfacetype }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务描述"
                        >{{ sixthActiveTableData.description }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务版本"
                        >{{ sixthActiveTableData.serviceVersion }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务注册者"
                        >{{ sixthActiveTableData.userName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="注册机构"
                        >{{ sixthActiveTableData.createOrgName }}
                      </el-descriptions-item>
                      <el-descriptions-item label="注册时间"
                        >{{ sixthActiveTableData.createTime }}
                      </el-descriptions-item>
                      <el-descriptions-item label="授权方式">
                        {{ sixthActiveTableData.authorizationMode }}
                      </el-descriptions-item>
                      <el-descriptions-item label="协议类型">
                        {{ sixthActiveTableData.protocol }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位"
                        >{{ sixthActiveTableData.supportUnit }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位联系人">
                        {{ sixthActiveTableData.supportUnitContact }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位联系电话">
                        {{ sixthActiveTableData.supportUnitPhone }}
                      </el-descriptions-item>
                      <el-descriptions-item label="附件">
                        <el-link
                          type="primary"
                          v-if="sixthActiveTableData.fileName"
                          @click="downLoadServer(sixthActiveTableData)"
                        >
                          {{ sixthActiveTableData.fileName }}
                        </el-link>
                      </el-descriptions-item>
                    </el-descriptions>
                    <div class="titlepart">
                      <p>方法列表</p>
                      <div class="line"></div>
                    </div>
                    <el-collapse
                      v-model="activeFunName"
                      accordion
                      v-if="sixthActiveTableData.funcList.length"
                    >
                      <el-collapse-item
                        v-for="(itemFun, itemFunIndex) in sixthActiveTableData.funcList"
                        :title="itemFun.funcName"
                        :name="itemFunIndex"
                        :key="'funcList' + itemFunIndex"
                      >
                        <div class="collapseCont">
                          <div class="fun-info-p">
                            <span class="label" v-if="itemFun.funcDesc">方法描述：</span>
                            <span class="content">{{ itemFun.funcDesc }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.requestMethod">
                            <span class="label">请求方式：</span>
                            <span class="content">{{ itemFun.requestMethod }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.returnType">
                            <span class="label">返回格式：</span>
                            <span class="content">{{ itemFun.returnType }}</span>
                          </div>
                          <div class="fun-info-table">
                            <div class="info-table">
                              <span class="label">输入参数：</span>
                              <el-table
                                :data="itemFun.requestParam"
                                border
                                stripe
                                max-height="300"
                                style="width: 100%"
                                class="projectTable"
                              >
                                <el-table-column
                                  prop="paramSort"
                                  label="排序"
                                  width="80"
                                  align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramName"
                                  label="参数名称"
                                  min-width="120"
                                  header-align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="paramStatus"
                                  label="是否必填"
                                  header-align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramLength"
                                  label="参数长度"
                                  align="right"
                                  header-align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramType"
                                  label="参数类型"
                                  align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramAddr"
                                  label="参数位置"
                                  header-align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="paramDesc"
                                  label="参数描述"
                                  min-width="350"
                                  header-align="center"
                                >
                                </el-table-column>
                                <!--                                <el-table-column prop="desc" label="参数描述" min-width="200" header-align="center">-->
                                <!--                                </el-table-column>-->
                              </el-table>
                            </div>
                            <div class="info-table">
                              <span class="label">输出参数：</span>
                              <el-table
                                :data="itemFun.responseParam"
                                border
                                stripe
                                max-height="300"
                                style="width: 100%"
                                class="projectTable"
                              >
                                <el-table-column
                                  prop="paramSort"
                                  label="排序"
                                  width="80"
                                  align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramName"
                                  label="参数名称"
                                  min-width="120"
                                  header-align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="paramStatus"
                                  label="是否必填"
                                  header-align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramLength"
                                  label="参数长度"
                                  align="right"
                                  header-align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramType"
                                  label="参数属性"
                                  header-align="center"
                                ></el-table-column>
                                <el-table-column
                                  prop="paramAddr"
                                  label="参数位置"
                                  header-align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  prop="paramDesc"
                                  label="参数描述"
                                  min-width="350"
                                  header-align="center"
                                >
                                </el-table-column>
                                <!--                                <el-table-column prop="desc" label="参数描述" min-width="200" header-align="center">-->
                                <!--                                </el-table-column>-->
                              </el-table>
                            </div>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.requestExample">
                            <span class="label">请求示例：</span>
                            <span class="content">{{ itemFun.requestExample }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.responseExampleSuccess">
                            <span class="label">成功响应示例：</span>
                            <span class="content">{{ itemFun.responseExampleSuccess }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.responseExampleFail">
                            <span class="label">失败响应示例：</span>
                            <span class="content">{{ itemFun.responseExampleFail }}</span>
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="文件"
              name="2"
              v-if="pageData.resourceTypeCode.split(',').includes('2')"
            >
              <div class="mainConte" v-if="sevenTableList.length">
                <div class="fullPart">
                  <div class="fullContent">
                    <el-table :data="sevenTableList" border stripe style="width: 100%">
                      <el-table-column
                        type="index"
                        label="序号"
                        width="80"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="resourceName"
                        label="资源名称"
                        min-width="180"
                        header-align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="fileName"
                        label="文件名称"
                        min-width="180"
                        header-align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="fileSize"
                        label="文件大小（单位：kb）"
                        width="200"
                        align="right"
                        header-align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="uploadtime"
                        label="上传时间"
                        width="200"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="description"
                        label="说明"
                        header-align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column prop="" label="操作" align="center">
                        <template slot-scope="scope">
                          <el-link
                            type="primary"
                            @click="downLoadFile(scope.row)"
                            v-if="
                              scope.row.downloadLicenseCode &&
                              scope.row.downloadLicenseCode !== '0' &&
                              isLogOn()
                            "
                            >下载<i class="el-icon-download"></i
                          ></el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="文件夹"
              name="5"
              v-if="pageData.resourceTypeCode.split(',').includes('5')"
            >
              <div class="mainConte" v-if="eightTableList.length">
                <div class="fullPart">
                  <div class="fullContent">
                    <el-table :data="eightTableList" border stripe style="width: 100%">
                      <el-table-column
                        type="index"
                        label="序号"
                        width="80"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="resourceName"
                        label="文件夹名称"
                        min-width="180"
                        header-align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="folderPath"
                        label="文件夹路径"
                        min-width="180"
                        header-align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="createTime"
                        label="创建时间"
                        width="200"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        prop="remark"
                        label="说明"
                        header-align="center"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>

        <!-- 数据订阅 -->
        <SubscribeDialog
          :dialogVisible.sync="dialogVisibleApply"
          :type="type"
          :title="title"
          :data="dialogData"
        ></SubscribeDialog>
        <!-- 问题反馈 -->
        <!--        <FeedbackDialog :dialogVisible.sync="feedbackVisible" :data="dialogData"></FeedbackDialog>-->
        <LeaveAMessage
          :dialogVisible.sync="dialogVisibleMessage"
          title="留言"
          :resourceId="pageData.resourceId"
          :cataId="pageData.cataId"
        ></LeaveAMessage>
      </div>
    </el-main>
  </el-container>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index'
import SubscribeDialog from '@/biz/components/SubscribeDialog'
import FeedbackDialog from '@/biz/components/FeedbackDialog'
import Cookies from 'js-cookie'
import { FileUtils } from '@/biz/utils/download'
import { cancelResourceCollecting } from '../../http/modules/collect'
import { addResourceToFavorites } from '../../http/modules/pageRequest'
import LeaveAMessage from '@/biz/components/LeaveAMessage'
export default {
  name: 'pageList',
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog,
    FeedbackDialog,
    LeaveAMessage
  },
  props: {},
  data() {
    return {
      pageData: {},
      queryBt: false,
      pageDataCataName: null,
      activeName: '0',
      tableList: [],
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      firstPaneActiveId: '1',
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      feedbackVisible: false,
      activeFunName: 0,
      fifthTableList: [],
      fifthActiveId: '',
      fifthActiveTableData: undefined,
      sixthTableList: [],
      sixthActiveId: '',
      sixthActiveTableData: undefined,
      sevenTableList: [],
      eightTableList: [],
      tableData: [],
      detailData: {},
      dialogVisibleMessage: false
    }
  },
  computed: {
    buttonText() {
      if (this.detailData.resourceAddSelect !== null && this.detailData.resourceAddSelect === '1') {
        return '取消加入选数车'
      } else if (this.detailData.resourceAddSelect === null && this.detailData.addSelect === '1') {
        return '取消加入选数车'
      } else {
        return '加入选数车'
      }
    }
  },
  methods: {
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    handleClick(tab, event) {
      if (this.activeName === '1') {
        this.getDirResourceDatabase()
      } else if (this.activeName === '3') {
        this.getDirResourceService()
      } else if (this.activeName === '2') {
        this.getDirResourceFile()
      } else if (this.activeName === '5') {
        this.getDirResourceFolder()
      }
    },
    getDirResourceDatabase() {
      this.$api.bizApi.pageRequest
        .getDirResourceDatabase({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          this.fifthTableList = res.data
          this.fifthActiveId = res.data[0].tableId
          this.fifthActiveTableData = res.data[0]
        })
    },
    getDirResourceService() {
      this.$api.bizApi.pageRequest
        .getDirResourceService({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          this.sixthTableList = res.data
          this.sixthActiveId = res.data[0].serviceId
          this.sixthActiveTableData = res.data[0]
        })
    },
    getDirResourceFile() {
      this.$api.bizApi.pageRequest
        .getDirResourceFile({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          this.sevenTableList = res.data
        })
    },
    getDirResourceFolder() {
      this.$api.bizApi.pageRequest
        .getDirResourceFolder({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          this.eightTableList = res.data
        })
    },
    fifthPaneTableCheck(item) {
      this.fifthActiveId = item.tableId
      this.fifthActiveTableData = item
    },
    sixthPaneTableCheck(item) {
      this.sixthActiveId = item.serviceId
      this.sixthActiveTableData = item
    },
    firstPaneTableCheck(data) {
      this.firstPaneActiveId = data.tableId
    },
    // 资源收藏
    handleCollect(data) {
      if (this.isLogOn()) {
        if (!data.collect) {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addResourceToFavorites({
              resourceId: this.pageData.resourceId
            })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('收藏成功')
                this.$set(data, 'collect', true)
                this.$set(data, 'collectSum', (data.collectSum += 1))
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否取消收藏该资源?', '取消收藏', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .cancelResourceToFavorites({ resourceId: this.pageData.resourceId })
              .then((res) => {
                if (res.code === '200') {
                  this.$message({
                    type: 'success',
                    message: '取消收藏成功!'
                  })
                  this.$set(data, 'collect', false)
                  this.$set(data, 'collectSum', (data.collectSum -= 1))
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 留言
    handleLeaveAMessage() {
      if (this.isLogOn()) {
        this.dialogVisibleMessage = true
      }
    },
    // 资源订阅
    handleResourceApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = data
        this.dialogVisibleApply = true
        this.dialogData.resourceId = data.resourceId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart() {
      if (this.isLogOn()) {
        if (this.detailData.resourceAddSelect !== '1') {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addResourceToCart({
              resourceIds: this.pageData.resourceId
            })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('加入选数车成功')
                this.$set(this.detailData, 'resourceAddSelect', '1')
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否从选数车中取消该资源?', '取消确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .delDataFromCart({
                resourceIds: this.pageData.resourceId
              })
              .then((res) => {
                if (res.code === '200') {
                  this.$message.success('取消成功!')
                  this.$set(this.detailData, 'resourceAddSelect', '0')
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // // 问题反馈
    // handleFeedback(data) {
    //   if (this.isLogOn()) {
    //     this.dialogData = data
    //     this.feedbackVisible = true
    //     this.dialogData.powerId = data.powerId
    //   }
    // },
    // 服务附件 下载
    downLoadFile(item) {
      this.$api.bizApi.pageRequest
        .dirDownloadFile({
          path: item.filePath,
          fileName: item.fileName,
          resourceId: item.resourceId,
          isExternalImport: 0,
          orderId: item.orderId
        })
        .then((res) => {
          let blob = res.data
          const link = document.createElement('a')
          link.href = URL.createObjectURL(blob)
          link.download = item.fileName
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          URL.revokeObjectURL(link.href)
          document.body.removeChild(link)
        })
        .catch((e) => {
          this.$message.warning('文件ID不存在，无法下载')
        })
    },
    downLoadServer(item) {
      this.$api.bizApi.resList
        .downLoadOrderTipFile({ id: item.attachId || item.annexId })
        .then((res) => {
          if (res && res.data && res.data.size > 0) {
            let blob = new Blob([res.data], {
              type: 'application/octet-stream;charset=utf-8'
            })
            const link = document.createElement('a')
            link.href = URL.createObjectURL(blob)
            link.download = item.fileName
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            URL.revokeObjectURL(link.href)
            document.body.removeChild(link)
          } else {
            this.$message({
              type: 'error',
              message: '提示附件资源不存在或者已被删除'
            })
          }
        })
        .catch((e) => {
          this.$message.warning('下载文件失败')
        })
    },
    getDetails() {
      this.$api.bizApi.pageRequest
        .getCatalogDetails({
          cataId: this.$route.query.cataId,
          type: this.$route.query.type || '1',
          resourceId: this.$route.query.resourceId
        })
        .then((res) => {
          this.detailData = res.data
        })
    },
    // 获取资源详情
    getResourceDetails() {
      this.$api.bizApi.pageRequest
        .getCatalogInfo({
          resourceId: this.$route.query.resourceId
        })
        .then((res) => {
          if (res.code === '200') {
            this.pageData = res.data.resource[0]
            this.pageDataCataName = res.data.directoryName
          }
        })
    }
  },
  mounted() {
    this.activeName = this.$route.query.resType
    this.getResourceDetails()
    this.getDetails()
    this.handleClick()
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';
@import '@/assets/detail.scss';

.tableItem {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 230px;
}
/deep/.el-table th.el-table__cell {
  background-color: #4eacfe;
  padding: 12px 0;
  .cell {
    background-color: #4eacfe;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
