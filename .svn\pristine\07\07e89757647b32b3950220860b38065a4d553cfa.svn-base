<template>
  <el-dropdown ref="dropDwnBtn" trigger="click" placement="bottom-start" :disabled="!hasPerms(perms)" v-if="hasPerms(perms) || isShowNoPermBtn">
    <el-button type="primary" :loading="loading" :disabled="!hasPerms(perms)">
      <template v-if="loading">
        <slot>下拉菜单</slot> <i class="el-icon-arrow-down el-icon--right"></i>
      </template>
      <template v-else>
        <svg-icon v-if="icon" :icon-class="icon"/><div v-if="icon" class="gap"></div><slot>下拉菜单</slot> <i class="el-icon-arrow-down el-icon--right"></i>
      </template>
    </el-button>
    <template v-if="!loading">
      <el-dropdown-menu slot="dropdown" class="baseUIDropdownBtn">
        <li v-for="item of optionsArr" :key="item.label"
          @click="item.disabled ? null : hdlClickMenuItem(item)"
          :class="item.divided ? 'item--divided' : ''">
          <!-- <el-dropdown-item :disabled="item.disabled"> -->
            <el-button type="text" :disabled="item.disabled" :class="item.icon ? '' : 'noIcon'">
              <svg-icon v-if="item.icon" :icon-class="item.icon"/><div v-if="item.icon" class="gap"></div> {{ item.label }}
            </el-button>
          <!-- </el-dropdown-item> -->
        </li>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<script>
import {hasPermission} from '@/core/utils/permission.js'
import SvgIcon from '@/core/components/SvgIcon/index.vue'
export default {
    name: 'DropDownButton',
    components: {
      SvgIcon
    },
    props: {
      icon: String,
      options: {
        type: Array,
        default: () => []
      },
      loading: { // 按钮加载标识
        type: Boolean,
        default: false
      },
      disabled: { // 按钮是否禁用
        type: Boolean,
        default: false
      },
      perms: { // 按钮权限标识，外部使用者传入
        type: String,
        default: null
      }
    },
    computed: {
      optionsArr() {
        return this.options
      }
    },
    data () {
      return {
        isShowNoPermBtn: config.isShowNoPermBtn
      }
    },
    methods: {
      hdlClickMenuItem(item) {
        this.$refs.dropDwnBtn.hide();
        if(typeof item.cb === "function") {
          item.cb();
        }
      },
      hasPerms: function (perms) {
        // 根据权限标识和外部指示状态进行权限判断
        if (perms) {
            return hasPermission(perms) & !this.disabled
        } else{
            return !this.disabled
        }
      }
    }
}
</script>

<style scoped lang="scss">
.el-dropdown-menu {
  border-radius: 2px;
  padding: 10px 10px 0 10px;
  li {
    margin-bottom: 10px;
    list-style: none;
    text-align: center;
    &.item--divided {
      position: relative;
      margin-top: 10px;
      border-top: 1px solid #ebeef5;
    }
    &.item--divided::before {
      content: "";
      height: 10px;
      display: block;
    }
    button {
      padding: 7px 10px;
      // border: 1px solid transparent;
      // background-color: transparent;
      height: 30px;
      box-sizing: border-box;
      border-radius: 2px;
      color: #FFF;
      >>>span {
        display: flex;
        justify-content: center;
      }
    }
    button.noIcon {
      width: 100%;
    }
  }
}
.thinvent_darken .el-dropdown-menu li.item--divided{
  border-top-color: #373c44
}
</style>
