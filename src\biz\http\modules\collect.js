import axios from "@/core/http/axios";

export function resourceList(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/collect/resourceList?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/collect/resourceList`,
    method: "get",
    params
  });
}

export function powerList(params) {
  return axios({
    url: `/${config.appCode}/powerSearch/getFavoritesList`,
    method: "get",
    params
  });
}

export function cancelCollect(params) {
  return axios({
    url: `/${config.appCode}/collect/cancel`,
    method: "post",
    params
  });
}

// 删除目录收藏
export function cancelCatalogCollecting(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceDir/cancelCatalogCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceDir/cancelCatalogCollecting`,
    method: "post",
    params
  });
}

// 删除资源收藏
export function cancelResourceCollecting(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/dataResourceApply/cancelResourceCollecting?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/dataResourceApply/cancelResourceCollecting`,
    method: "post",
    params
  });
}

export function cancelPowerToFavorites(params) {
  return axios({
    url: `/${config.appCode}/powerSearch/cancelPowerToFavorites`,
    method: "post",
    params
  });
}
