<template>
  <el-container class="page-container">
<!--    <el-container>-->
<!--      <el-main>-->
        <!-- <div class="searchCont">
          <div class="searchList">
            <h5>范围</h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="资源名称">
                  <el-input v-model.trim="filters.name" clearable placeholder="请输入资源名称" @input="handleFilter" maxlength="20" />
                </el-form-item>
                <el-form-item label="来源系统">
                  <el-input v-model.trim="filters.systemName" clearable placeholder="请输入来源系统" @input="handleFilter" maxlength="20" />
                </el-form-item>
                <el-form-item label="库表字段或表名">
                  <el-input clearable v-model="filters.libraryFields" @input="handleFilter" maxlength="20" placeholder="请输入库表字段或表名"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="searchList">
            <h5>更多筛选</h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="">
                  <SelectPlus collapseTags multiple dictType="SHARD_TYPE" mode="gxsl" v-model="filters.shareType" @change="handleFilter" clearable placeholder="共享属性" />
                </el-form-item>
                <el-form-item label="提供单位" style="margin-left: 30px;">
                  <el-cascader clearable :filterable="true" v-model="orgIds" :options="options" :props="{ checkStrictly: true }" @change="handleOrgId" :value="orgIds" style="width: 100%">
                    <template slot-scope="{ node, data }">
                      <div class="nowrap" :title="data.label">{{ data.label }}</div>
                    </template>
                  </el-cascader>
                </el-form-item>
                <el-form-item label="发布时间">
                  <el-date-picker value-format="yyyy-MM-dd" v-model="filters.dateRange1" type="daterange" @change="handleFilter" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="searchList">
            <h5>资源热度</h5>
            <div class="searchInp" style="display:flex;">
              <el-rate class="icsp-portal-rate" void-icon-class="iconfont icon-huomiao" void-color="#B3B3B3" :icon-classes="['iconfont icon-huomiao', 'iconfont icon-huomiao', 'iconfont icon-huomiao']" :colors="['#FF5E5E', '#E15555', '#E15555']" @change="changeScore" v-model="score"></el-rate>
              <button class="filterBtn1" @click="resetScore">重置</button>
            </div>
          </div>
          <div class="searchList">
            <h5>资源类型</h5>
            <div class="searchInp" style="display:flex;">
              <CheckPlus dictType="ZYML_RESOURCE_TYPE" mode="gxsl" v-model="checkListresFormatType" :filterType="'resFormatType'" :num="'1'" @change="handleChecked" ref="resFormatType"></CheckPlus>
            </div>
          </div>
          <div class="searchList">
            <h5>更新周期</h5>
            <div class="searchInp" style="display:flex;">
              <SelectPlus collapseTags multiple dictType="ZYML_CATALOG_UPDATE_CYCLE" mode="gxsl" v-model="filters.updateCycle" placeholder="请选择更新周期" @input="handleFilter" clearable></SelectPlus>

            </div>
          </div>
        </div> -->
        <div class="dataCont">
          <div class="topSort">
            <el-checkbox v-model="filters.onlyShowNotOrder" @change="changeListStatus" :true-label="1" :false-label="0">
              仅显示未订阅</el-checkbox>
            <div class="sort">
              <div v-for="(item, index) in sortItems" :key="index">
                <span class="iconLabel" :class="{ 'active': item.active }" @click="sort(item)">
                  {{ item.label }}
                </span>
                <span v-if="item.sort" class="iconList">
                  <i class="el-icon-caret-top" :class="{ 'active': item.order === 1 }" @click="orderChange(item, 1)"></i>
                  <i class="el-icon-caret-bottom" :class="{ 'active': item.order === -1 }" @click="orderChange(item, -1)"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="borderSplit"></div>
          <div class="listCont" v-loading="listLoading">
            <div class="listItem" v-for="item in dataList" :key="item.id" @click="handleCheckDetail(item)">
              <div class="line1">
                <div class="title">
                  <div class="ellis" :title="item.resourceName">{{ item.resourceName }} </div><span style="width: 23px; margin-left: 4px">
                    <img v-if="item.rangeCode === '1'" src="~@/assets/img/icon-withOut.svg" alt="">
                    <img v-else src="~@/assets/img/icon-withIn.svg" alt="">
                  </span><span class="info-list" v-if="item.orderResNum && item.orderResNum !== ''">已订阅</span>
                </div>
                <div class="itemStatus">
                  <span class="info-tip" v-if="item.shareType && item.shareType !== ''">{{ item.shareType }}</span>
                  <span class="info-tip" v-if="item.openType && item.openType !== ''">{{ item.openType }}</span>
                </div>
              </div>
              <div class="line2">
                <div class="itemMes">
                  <span class="message">提供单位：<span>{{ item.providerDeptName }}</span></span>
                  <span class="message">所属目录：<span>{{ item.directoryName }}</span></span>
                  <span class="message" v-if="item.systemName">来源系统：<span>{{ item.systemName }}</span></span>
                  </br>
                  <span class="message">发布时间：<span>{{ item.publishTime }}</span></span>
                  <span class="message" v-if="item.updateCycle">更新周期：<span>{{ item.updateCycle }}</span></span>
                  <span class="message" v-if="item.basicInfoUpdatetime">更新时间：<span>{{ item.basicInfoUpdatetime }}</span></span>
                  <template v-if="item.resourceTypeCode || item.resourceType">
                    <!-- 含有接口 -->
                    <span v-if="item.resourceTypeCode === '3' && item.isExternalImport !== '9'" class="p-info-item"><span class="label">累计调用量：</span>
                      <span class="value">{{ showTotal(item.callSum) || 0 }}次</span>
                    </span>
                    <!-- 含有库表 || 回流 -->
                    <span v-if="(item.resourceTypeCode === '1' || item.resourceTypeCode === '4')&& item.isExternalImport !== '9'" class="p-info-item"><span class="label">结构化数据量：</span>
                      <span class="value">{{ showTotal(item.tdxTable) || 0 }}条</span>
                    </span>
                    <!-- 含有文件 || 文件夹 -->
                    <span v-if="(item.resourceTypeCode === '2' || item.resourceTypeCode === '5') && item.isExternalImport !== '9'" class="p-info-item"><span class="label">非结构化数据量：</span>
                      <span class="value">{{ (item.tdxFile / 1024 / 1024 ).toFixed(3) || 0 }}M</span>
                    </span>
                  </template>
                </div>
                <div class="right-info">
                  <!-- 非回流数据目录 -->
                  <template v-if="(item.catalogTypeCode || item.catalogType) !== '3'">
                    <div class="type-info" :class="{
                active: (item.resourceTypeCode && item.resourceTypeCode.indexOf('1') > -1) || (item.resourceType && item.resourceType.indexOf('1') > -1) }" v-if="(item.resourceTypeCode && item.resourceTypeCode.indexOf('1') > -1) || (item.resourceType && item.resourceType.indexOf('1') > -1)">
                      <svg-icon icon-class="gxsl-kubiao" />
                      <p>库表</p>
                    </div>
                    <div class="type-info" :class="{
                active: (item.resourceTypeCode && item.resourceTypeCode.indexOf('2') > -1) || (item.resourceType && item.resourceType.indexOf('2') > -1) }" v-if="(item.resourceTypeCode && item.resourceTypeCode.indexOf('2') > -1) || (item.resourceType && item.resourceType.indexOf('2') > -1)">
                      <svg-icon icon-class="gxsl-wenjian" />
                      <p>文件</p>
                    </div>
                    <div class="type-info" :class="{
                active: (item.resourceTypeCode && item.resourceTypeCode.indexOf('3') > -1) || (item.resourceType && item.resourceType.indexOf('3') > -1) }" v-if="(item.resourceTypeCode && item.resourceTypeCode.indexOf('3') > -1) || (item.resourceType && item.resourceType.indexOf('3') > -1)">
                      <svg-icon icon-class="gxsl-jiekou" />
                      <p>接口</p>
                    </div>
                    <div class="type-info" :class="{
                active: (item.resourceTypeCode && item.resourceTypeCode.indexOf('5') > -1) || (item.resourceType && item.resourceType.indexOf('5') > -1) }" v-if="(item.resourceTypeCode && item.resourceTypeCode.indexOf('5') > -1) || (item.resourceType && item.resourceType.indexOf('5') > -1)">
                      <svg-icon icon-class="gxsl-wenjianjia" />
                      <p>文件夹</p>
                    </div>
                  </template>
                  <!-- 回流数据目录 -->
                  <template v-else>
                    <div class="type-info" :class="{
                active: (item.resourceTypeCode && item.resourceTypeCode.indexOf('4') > -1) || (item.resourceType && item.resourceType.indexOf('4') > -1) }" v-if="(item.resourceTypeCode && item.resourceTypeCode.indexOf('4') > -1) || (item.resourceType && item.resourceType.indexOf('4') > -1)">
                      <svg-icon icon-class="gxsl-huiliu" />
                      <p>回流</p>
                    </div>
                  </template>
                </div>
              </div>
              <div class="line3">
                <div class="itemNum">
                  <div style="display: flex; align-items: center;">
                    <el-rate :max="5" class="icsp-portal-rate" disabled-void-icon-class="iconfont icon-huomiao" void-icon-class="iconfont icon-huomiao" disabled-void-color="#B3B3B3" :icon-classes="['iconfont icon-huomiao', 'iconfont icon-huomiao', 'iconfont icon-huomiao']" :colors="['#FF5E5E', '#E15555', '#E15555']" v-model="item.score" disabled></el-rate>
                    <span class="num">{{ !!item.orderSum ? item.orderSum : 0 }}次<span>订阅</span></span>
                    <span class="num">{{ !!item.browseSum? item.browseSum : 0 }}次<span>访问</span></span>
                    <span class="num">{{ !!item.collectSum? item.collectSum : 0 }}次<span>收藏</span></span>
                  </div>
                </div>
                <div class="itemHandleBtn">
                  <el-button class="icsp-button-grey" disabled v-if="item.providerCode === currentUser.unitCode || (item.orderResNum && item.orderResNum !== '')">
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>
                  <el-button class="icsp-button-grey" v-else-if="!item.resourceType" :disabled="!item.resourceType">
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>
                  <el-button v-else class="icsp-button" plain @click.stop="handleResourceApply(item)">
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>

                  <el-button class="icsp-button-grey" disabled v-if="item.providerCode === currentUser.unitCode">
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    加入选数车
                  </el-button>
                  <el-button class="icsp-button-grey" :disabled="!item.resourceType" v-else-if="!item.resourceType">
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    {{ item.inCart ? "取消加入选数车" : "加入选数车" }}
                  </el-button>
                  <el-button v-else :disabled="queryBt" :class="item.inCart ? 'icsp-button2' : 'icsp-button'" plain @click.stop="
                     handleAddPowerToCart(item)
                    ">
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    {{ item.inCart ? "取消加入选数车" : "加入选数车" }}
                  </el-button>

                  <el-button class="icsp-button-grey" disabled v-if="item.providerCode === currentUser.unitCode">
                    <svg-icon icon-class="gxsl-collect" />
                    收藏
                  </el-button>
                  <el-button v-else :disabled="queryBt" :class="item.collect ? 'icsp-button2' : 'icsp-button'" plain @click.stop="
                    handleCollect(item)
                    ">
                    <svg-icon icon-class="gxsl-collect" />
                    {{ item.collect ? "取消收藏" : "收藏" }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-if="!listLoading && dataList.length === 0" description="暂无数据"></el-empty>
          <div class="icsp-pagination-wrap">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="filters.currentPage" :page-size="filters.pageSize" layout="total, prev, pager, next, sizes, jumper" :total="total"></el-pagination>
          </div>

        </div>

        <!-- 数据订阅 -->
        <SubscribeDialog :dialogVisible.sync="dialogVisibleApply" :type="type" :title="title" :data="dialogData"></SubscribeDialog>
        <ToolBar @onRefreshList="getList" />
<!--      </el-main>-->
<!--    </el-container>-->
  </el-container>
</template>

<script>
import ToolBar from '@/biz/components/common/toolbar'
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index.vue'
import CheckPlus from '@/biz/components/common/checkPlus'
import SubscribeDialog from './SubscribeDialog'
import Cookies from 'js-cookie'
export default {
  name: 'Resource',
  components: {
    ToolBar,
    SelectPlus,
    CheckPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog
  },
  data() {
    return {
      queryBt: false,
      checkAll: false,
      isIndeterminate: false,
      showList: true,
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      queryParamsList: [],
      checkList: [
        {
          checkName: '一般',
          checkValue: 11
        },
        {
          checkName: '重要',
          checkValue: 11
        }
      ],
      checkOption: ['一般', '重要'],
      sortItems: [
        {
          label: '订阅量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 1
        },
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 2
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 8
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false,
          sortMode: 4
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      powerTreeData: {
        allNum: 0,
        allNumIn: 0,
        allNumOut: 0,
        treeDataIn: [],
        treeDataOut: []
      },
      dataList: [],
      treeDataIn: [],
      treeDataOut: [],
      defaultProps: {
        children: 'childList',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      listLoading: false,
      total: 0,
      currentRow: undefined,
      options: [], // 所属部门
      orgIds: [],
      score: undefined,
      checkListresFormatType: [],
      checkListupdateCycle: [],
      filters: {
        name: '',
        score: undefined,
        systemName: '',
        updateCycle: undefined,
        dateRange1: [],
        orgId: '',
        currentPage: 1,
        onlyShowNotOrder: 0,
        baseType: '',
        baseTypeInfo: '',
        pageSize: 10,
        sortMode: '',
        sortType: 'desc',
        shareType: '',
        resFormatType: undefined,
        libraryFields: undefined
      }
    }
  },
  methods: {
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    // 重置 资源热度
    resetScore() {
      this.score = undefined
      this.filters.score = undefined
      this.handleFilter()
    },
    showTotal(value) {
      if (value >= 100000000) {
        return (value / 100000000).toFixed(2) + '亿'
      } else if (value >= 10000) {
        return (value / 10000).toFixed(2) + '万'
      } else {
        return value
      }
    },
    // 改变 资源热度
    changeScore(val) {
      this.filters.score = val * 10
      this.handleFilter()
    },
    handleChecked(val, type) {
      console.log(val, type, '1,2')
      console.log(this.checkListupdateCycle, 'checkListupdateCycle')
      this.filters[type] = val
      this.handleFilter()
    },
    // 改变所属部门
    handleOrgId(val) {
      // console.log(val, 'val')
      if (val.length === 1) {
        this.filters.orgId = val[0]
      } else {
        this.filters.orgId = val[val.length - 1]
      }
      this.getList()
    },
    circle(data) {
      data.forEach((i) => {
        i.value = i.id
        i.label = i.name
        if (i.children && i.children.length > 0) {
          this.circle(i.children)
        }
      })
    },
    // 获取所属部门
    getOrgId() {
      this.$api.bizApi.common
        .getUnitsByUnitCode()
        .then((res) => {
          if (res.code === '200') {
            let a = res.data || []
            this.circle(a)
            this.options = a
            // 如果route中含有提供单位，需要反显选中
            if (this.filters.orgId) {
              let ary = this.getParentsById(this.options, this.filters.orgId)
              // console.log('ary' + ary)
              this.orgIds = ary
            }
          }
        })
        .catch((e) => {
          this.$message({
            message: e.message
          })
        })
    },
    // el-cascader数据提供部门多级回显
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          // console.log('node' + node)
          if (node !== undefined) {
            // 如能查询到把父节点加到数组前面
            node.unshift(list[i].id)
            return node
          }
        }
      }
    },
    handleCheckAllChange(val) {
      console.log(val)
      this.filters.checkList = val ? this.checkOption : []
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      let checkedCount = val.length
      this.checkAll = checkedCount === this.checkOption.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkOption.length
    },
    handleNodeClick(data) {
      console.log(data, 'data')

      if (data.childList && data.childList.length) {
        console.log(this.filters.baseType, 'this.filters.baseType')
        if (data.type) {
          this.type = data.type
        }
        if (this.filters.baseType === data.id) {
          this.filters.baseType = undefined
          this.$refs.treeIn.setCurrentKey(null)
        } else {
          console.log(data.id, 'dataid')
          this.filters.baseType = data.id
          this.filters.baseTypeInfo = data.type ? data.type : this.type

          this.getList(data.rangeFlag)
        }
        const params = {
          resNameLike: '',
          labelName: '',
          showExistNode: '',
          type: data.type ? data.type : this.type,
          baseTypeId: data.id,
          catalogType: '1,2,3',
          rangeFlag: data.rangeFlag,
          ...this.filters
        }

        this.$api.bizApi.pageRequest.getBaseTypeTreeWithResCnt(params).then((res) => {
          data.childList = res.data || []
          console.log(data.childList, 'sss')
        })
      } else {
        console.log(this.type, 'this.type', data.id, 'data.id')
        this.filters.baseType = data.id
        this.filters.baseTypeInfo = data.type ? data.type : this.type
        this.getList(data.rangeFlag)
        const params = {
          resNameLike: '',
          labelName: '',
          showExistNode: '',
          type: data.type ? data.type : this.type,
          baseTypeId: data.id,
          catalogType: '1,2,3',
          rangeFlag: data.rangeFlag,
          ...this.filters
        }

        this.$api.bizApi.pageRequest.getBaseTypeTreeWithResCnt(params).then((res) => {
          data.childList = res.data || []
          console.log(data.childList, 'sss')
        })
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    changeListStatus(val) {
      this.handleFilter()
    },
    handleFilter() {
      this.filters.currentPage = 1
      this.getList()
    },
    getList(rangeFlag) {
      let param = this.$route.query
      if (param.portalSearchText) {
        this.filters.name = param.portalSearchText
      }
      this.listLoading = true
      const params = {
        ...this.filters,
        rangeFlag: rangeFlag,
        publishTimeStart: this.filters.dateRange1?.length
          ? this.filters.dateRange1[0] + ' 00:00:00'
          : undefined,
        publishTimeEnd: this.filters.dateRange1?.length
          ? this.filters.dateRange1[1] + ' 23:59:59'
          : undefined
      }
      this.$api.bizApi.pageRequest
        .getResourcePage(params)
        .then((res) => {
          this.listLoading = false
          this.dataList = res.data ? res.data.records : []
          this.dataList.forEach((item) => {
            item.score /= 10
          })
          this.total = res.data ? res.data.total : 0
        })
        .catch((res) => {
          this.listLoading = false
        })
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    // 查看详情
    handleCheckDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detailResource/index',
          query: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataRes/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    // 能力收藏
    handleCollect(data) {
      if (!data.collect) {
        this.queryBt = true
        if (!sessionStorage.getItem('user')) return
        console.log(sessionStorage.getItem('user'), 'ss')
        // const { userId, unitId, unitCode } = JSON.parse(sessionStorage.getItem('user'))
        this.$api.bizApi.pageRequest
          .addResourceToFavorites({
            resourceId: data.resourceId // ,
            // userId,
            // unitId,
            // unitCode
          })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('收藏成功')
              // this.handleFilter()
              this.$set(data, 'collect', true)
              this.$set(data, 'collectSum', data.collectSum + 1)
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false
          })
      } else {
        this.$confirm('是否取消收藏该数据资源?', '取消收藏', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .cancelResourceToFavorites({ resourceId: data.resourceId })
            .then((res) => {
              if (res.code === '200') {
                // this.handleFilter()
                this.$message({
                  type: 'success',
                  message: '取消收藏成功!'
                })
                this.$set(data, 'collect', false)
                this.$set(data, 'collectSum', data.collectSum - 1)
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    },
    // 资源订阅
    handleResourceApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = {}
        this.dialogVisibleApply = true
        this.dialogData.resourceId = data.resourceId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart(data) {
      if (!data.inCart) {
        this.queryBt = true
        if (!sessionStorage.getItem('user')) return
        console.log(sessionStorage.getItem('user'), 'ss')
        // const { userId, unitId, unitCode } = JSON.parse(sessionStorage.getItem('user'))
        this.$api.bizApi.pageRequest
          .addResourceToCart({
            resourceIds: data.resourceId // ,
            // userId,
            // unitId,
            // unitCode
          })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('加入选数车成功')
              this.handleFilter()
              window.refreshCountNum()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false;
          });
      } else {
        this.$confirm('是否从选数车中取消该数据资源?', '取消确定', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .delDataFromCart({ resourceIds: data.resourceId })
            .then((res) => {
              if (res.code === '200') {
                this.handleFilter()
                this.$message.success('取消成功!')
                window.refreshCountNum()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeIn.filter(val)
      this.$refs.treeOut.filter(val)
    },
    $route: {
      handler(newVal, oldVal) {
        console.log(newVal, 'newValWatch2')
        console.log(newVal.path.includes('/search'), '是否')
        if (newVal.path.includes('/search')) {
          this.getList()
          document.documentElement.scrollTop = 0
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getOrgId()
    this.getList()
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  margin-left: 0 !important;

  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 评分/热度
.icsp-portal-rate {
  height: auto;
  line-height: inherit;
  .icon-huomiao::before {
    font-size: 16px;
    margin-right: 0;
    vertical-align: -0.4em;
  }
}

.info-list {
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-color: #ff8635;
  background-color: #3572ff;
}
</style>
