<template>
  <div id="loginBox" class="loginPage-container">
    <div class="formBgMask">
      <div class="form-wrapper">
        <div v-if="warnMessage" class="warn-box">
          <div class="warn-icon">
            <span>警告</span>
          </div>
          <div class="warn-line">
          </div>
          <div class="warn-message">
            <span>警告：本系统所有数据均属于内部敏感数据，严禁非法查询外传泄露，一经查实必追究责任，造成违法行为后果自负！</span>
          </div>
        </div>
        <!--      <div :class="isShowFieldset ? 'logo' : ['logo', 'noFieldset']">-->
        <div class="logo">
          <!--        <div class="image-light2"><img :src="logoHeaderSrc" style="opacity: 0.8"/></div>-->
          <!--        <div style="height: 10px"></div>-->
          <!--        <div class="image-light1"><img :src="logoTextSrc" style="opacity: 0.8"/></div>-->
          <img :src="logoSrc">
        </div>
        <el-form size="small" v-if="loginMode.account" v-show="loginType === '1'" :model="accountLoginForm" :rules="accountRules" ref="accountLoginForm" label-position="right" label-width="0px" class="login-container">
          <div>
            <el-form-item prop="username" style="margin-top: 50px">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
              <div class="vLine"></div>
              <el-input type="text" v-model="accountLoginForm.username" placeholder="账号"></el-input>
            </el-form-item>
            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <div class="vLine"></div>
              <el-input type="password" v-model="accountLoginForm.password" placeholder="密码" ref="pwd"></el-input>
            </el-form-item>
            <el-form-item prop="captcha" class="captchaInput">
              <span class="svg-container">
                <svg-icon icon-class="captcha" />
              </span>
              <div class="vLine"></div>
              <el-input type="text" v-model="accountLoginForm.captcha" placeholder="验证码，单击图片刷新" @keyup.enter.native="accountLogin" maxlength="4"></el-input>
              <div style="float:right">
                <img class="captchaImg" :src="accountLoginForm.src" @click="refreshCaptcha">
              </div>
            </el-form-item>
            <el-button class="accountLoginBtn" size="small" type="primary" @click.native.prevent="accountLogin" :loading="loading">登录</el-button>
          </div>
        </el-form>
        <el-form size="small" v-if="loginMode.mobile" v-show="loginType === '2'" :model="mobileLoginForm" :rules="smsRules" ref="mobileLoginForm" label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>手机号登录</span>
            <hr class="hr_mobile" />
          </h2>
          <el-form-item prop="mobile" class="mobileLoginItem">
            <span class="svg-container">
              <svg-icon icon-class="mobile" />
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="mobileLoginForm.mobile" placeholder="请输入手机号" maxlength="11"></el-input>
          </el-form-item>
          <el-form-item prop="smsCaptcha" class="mobileLoginItem">
            <span class="svg-container" style="margin-left:-94px;">
              <svg-icon icon-class="captcha" />
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="mobileLoginForm.smsCaptcha" placeholder="请输入验证码" @keyup.enter.native="smsLogin" style="width: 56%;" maxlength="4"></el-input>
            <div style="float:right" v-show="!mobile_counter.show">
              <el-button size="mini" type="primary" class="captchaBtn" @click.native.prevent="sendCaptcha">发送验证码</el-button>
            </div>
            <span class="countDown" v-show="mobile_counter.show">倒计时还有<b>{{mobile_counter.counter}}</b>秒</span>
          </el-form-item>
          <el-button class="mobileLoginBtn" size="small" type="primary" @click.native.prevent="smsLogin" :loading="loading">
            登录
          </el-button>
        </el-form>
        <el-form size="small" v-if="loginMode.email" v-show="loginType === '3'" :model="emailLoginForm" :rules="emailRules" ref="emailLoginForm" label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>邮箱登录</span>
            <hr class="hr_email" />
          </h2>
          <el-form-item prop="email" class="emailLoginItem">
            <span class="svg-container">
              <svg-icon icon-class="email" />
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="emailLoginForm.email" placeholder="请输入邮箱地址"></el-input>
          </el-form-item>
          <el-form-item prop="captcha" class="emailLoginItem">
            <span class="svg-container" style="margin-left:-94px;">
              <svg-icon icon-class="captcha" />
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="emailLoginForm.captcha" placeholder="请输入验证码" @keyup.enter.native="emailLogin" style="width: 56%;" maxlength="4"></el-input>
            <div style="float:right" v-show="!email_counter.show">
              <el-button size="mini" type="primary" class="captchaBtn" @click.native.prevent="sendEmailCaptcha">发送验证码</el-button>
            </div>
            <span class="countDown" v-show="email_counter.show">倒计时还有<b>{{email_counter.counter}}</b>秒</span>
          </el-form-item>
          <el-button class="emailLoginBtn" size="small" type="primary" @click.native.prevent="emailLogin" :loading="loading">
            登录
          </el-button>
        </el-form>
        <fieldset v-show="isShowFieldset">
          <legend>其他登录方式</legend>
          <ul>
            <li @click="loginType='1'" v-if="loginMode.account">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-account" :class="loginType==='1'?'cur':''" />
                </span>
              </a>
            </li>
            <li @click="loginType='2'" v-if="loginMode.mobile">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-mobile" :class="loginType==='2'?'cur':''" />
                </span>
              </a>
            </li>
            <li @click="loginType='3'" v-if="loginMode.email">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-email" :class="loginType==='3'?'cur':''" />
                </span>
              </a>
            </li>
            <li class="dis" v-if="loginMode.qq"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-qq" />
                </span>
              </a>
            </li>
            <li class="dis" v-if="loginMode.wx"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-wx" />
                </span>
              </a>
            </li>
            <li class="dis" v-if="loginMode.sina"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-sina" />
                </span>
              </a>
            </li>
          </ul>
        </fieldset>
        <div class="copyright">{{this.copyright}}</div>
      </div>
    </div>
    <div class="ani">
      <SvgSignal></SvgSignal>
      <canvas id="myCanvas" width="1920px" height="1080px">
      </canvas>
    </div>
  </div>
</template>

<script>
import SvgIcon from '@/core/components/SvgIcon/index.vue'
import { validMobile, validEmail } from '@/core/utils/validate'
import { getSpaceId, loginInit } from "@/core/utils/logout"
import SvgSignal from '@/core/views/Login/shzl/SvgSignal.vue'
// eslint-disable-next-line
import Cookies from 'js-cookie'
import { rsa } from "../../../utils/utils"
  import { Message } from 'element-ui'

export default {
  name: 'Login',
  components: {
    SvgIcon, SvgSignal
  },
  data() {
    return {
      warnMessage: config.warnMessage,
      inputType: 'text',
        t: {
          token: this.$route.query ? this.$route.query.token : null,
          redirectPath: this.$route.query ? this.$route.query.redirectPath : null,
          query: this.$route.query
        },
      mobile_counter: {
        show: false,
        counter: '',
        timer: null
      },
      email_counter: {
        show: false,
        counter: '',
        timer: null
      },
      platTitle: config.plat_title,
      loginMode: config.loginMode,
      sso: config.sso,
      copyright: config.copyright,
      captcha: '',
      loginType: '1',
      loading: false,
      accountLoginForm: {
        username: '',
        password: '',
        captcha: '',
        src: ''
      },
      mobileLoginForm: {
        mobile: '',
        smsCaptcha: ''
      },
      emailLoginForm: {
        email: '',
        emailCaptcha: ''
      },
      accountRules: {
        username: [
          { required: true, message: '请输入账号！', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码！', trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 4, max: 4, message: '长度为4个字符', trigger: 'blur' }
        ]
      },
      smsRules: {
        mobile: [
          { required: true, trigger: 'blur', validator: validMobile }
        ]
      },
      emailRules: {
        email: [
          { required: true, trigger: 'blur', validator: validEmail }
        ]
      },
      checked: true
    }
  },
  computed: {
    isShowFieldset() {
      return Object.entries(this.loginMode).filter(o => o[1]).length > 1
    },
    logoHeaderSrc() {
      return `${process.env.BASE_URL}config/login/${config.loginBg}/logo_header.png`
    },
    logoTextSrc() {
      return `${process.env.BASE_URL}config/login/${config.loginBg}/logo_text.png`
    },
    logoSrc() {
      return `${process.env.BASE_URL}config/login/${config.loginBg}/login_logo.png`
    }
  },
  methods: {
    accountLogin() {
      this.loading = true
      let userInfo = {
        username: this.accountLoginForm.username,
        password: this.accountLoginForm.password,
        code: this.accountLoginForm.captcha,
        grant_type: 'password',
        scope: 'all',
        captcha: this.captcha,
        xep: new Date().getTime()
      }
      // 对密码加密
      userInfo.password = rsa(userInfo.password)
      this.$refs.accountLoginForm.validate((valid) => {
        if (valid) {
          this.getUserInfo(userInfo, 1)
        } else {
          this.loading = false
          return false;
        }
      })
    },
    smsLogin() {
      this.loading = true
      let userInfo = {
        grant_type: 'sms',
        scope: 'all',
        sms: this.mobileLoginForm.mobile,
        code: this.mobileLoginForm.smsCaptcha
      }
      let captcha = [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { min: 4, max: 4, message: '长度为4个字符', trigger: 'blur' }
      ]
      this.smsRules.smsCaptcha = captcha
      this.$refs.mobileLoginForm.validate((valid) => {
        if (valid) {
          this.getUserInfo(userInfo)
        } else {
          this.loading = false
          return false;
        }
      })
    },
    emailLogin() {
      this.loading = true
      let userInfo = {
        grant_type: 'email',
        scope: 'all',
        email: this.emailLoginForm.email,
        code: this.emailLoginForm.captcha
      }
      let captcha = [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { min: 4, max: 4, message: '长度为4个字符', trigger: 'blur' }
      ]
      this.emailRules.captcha = captcha
      this.$refs.emailLoginForm.validate((valid) => {
        if (valid) {
          this.getUserInfo(userInfo)
        } else {
          this.loading = false
          return false;
        }
      })
    },
    getUserInfo: function (userInfo, f) {
      Cookies.remove(config.cookieName)
      this.$api.login.login(userInfo).then((res) => {
        Cookies.set(config.cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
        sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
          // 获取系统列表
          getSpaceId(res.userName).then(() => {
            this.$api.authority.getSystemList().then((res2) => {
              let systemList = res2.data
              let privFlag = !systemList || systemList.filter(i => i.sysCode === config.systemCode).length === 0
              if(privFlag) {
                // 再从子集中寻找
                let pSystem = systemList.filter(i => i.childrenSystems && i.childrenSystems.length > 0)
                if (pSystem) {
                  for (let ele of pSystem) {
                    for (let cele of ele.childrenSystems) {
                      if (cele.sysCode === config.systemCode) {
                        privFlag = false
                        break
                      }
                    }
                  }
                }
              }
              if(privFlag) {
                Cookies.remove(config.cookieName)
                // sessionStorage.removeItem(config.cookieName);
                Message({message: '用户未授权，请联系系统管理员!', type: 'error'})
                if(f) this.refreshCaptcha()
              } else {
                loginInit({userInfo: res, sysList: systemList});
                // getSpaceId(res.userName).then(() => {
                  console.log(
                    "\n ______  __                                         __      \n" +
                    "/\\__  _\\/\\ \\      __                               /\\ \\__   \n" +
                    "\\/_/\\ \\/\\ \\ \\___ /\\_\\    ___   __  __     __    ___\\ \\ ,_\\  \n" +
                    "   \\ \\ \\ \\ \\  _ `\\/\\ \\ /' _ `\\/\\ \\/\\ \\  /'__`\\/' _ `\\ \\ \\/  \n" +
                    "    \\ \\ \\ \\ \\ \\ \\ \\ \\ \\/\\ \\/\\ \\ \\ \\_/ |/\\  __//\\ \\/\\ \\ \\ \\_ \n" +
                    "     \\ \\_\\ \\ \\_\\ \\_\\ \\_\\ \\_\\ \\_\\ \\___/ \\ \\____\\ \\_\\ \\_\\ \\__\\\n" +
                    "      \\/_/  \\/_/\\/_/\\/_/\\/_/\\/_/\\/__/   \\/____/\\/_/\\/_/\\/__/\n" +
                    "                                                            ")
                  if(this.t.redirectPath) {
                    this.$router.push({path: this.t.redirectPath, query: this.t.query}) // 登录成功，跳转到主页
                  } else {
                    this.$router.push({path: '/', query: this.t.query}) // 登录成功，跳转到主页
                  }
                // })
              }
            })
            this.loading = false
          })
        }).catch((res) => {
          this.loading = false
          if(f) this.refreshCaptcha()
        })
    },
    refreshCaptcha: function () {
      this.captcha = new Date().getTime()
      this.accountLoginForm.src = this.global.baseURL + '/auth/code/captcha?captcha=' + this.captcha
      this.accountLoginForm.captcha = null
    },
    getCounter: function (counter) {
      const TIME_COUNT = config.codeDuration;
      if (!counter.timer) {
        counter.counter = TIME_COUNT;
        counter.show = true;
        counter.timer = setInterval(() => {
          if (counter.counter > 0 && counter.counter <= TIME_COUNT) {
            counter.counter--;
          } else {
            counter.show = false;
            clearInterval(counter.timer);
            counter.timer = null;
          }
        }, 1000)
      }
    },
    sendCaptcha: function () {
      this.smsRules.smsCaptcha = undefined
      this.$refs.mobileLoginForm.validate((valid) => {
        if (valid) {
          this.$api.login.sendSMSCaptcha({ 'sms': this.mobileLoginForm.mobile }).then((res) => {
            this.getCounter(this.mobile_counter)
            this.$message({ message: '短信已发送，请注意查收短信！', type: 'success' })
            this.loading = false
          }).catch((res) => {
            this.loading = false
          })
        } else {
          this.loading = false
          return false;
        }
      })
    },
    sendEmailCaptcha: function () {
      this.$refs.emailLoginForm.validate((valid) => {
        if (valid) {
          this.getCounter(this.email_counter)
          this.$api.login.sendEmailCaptcha({ 'email': this.emailLoginForm.email }).then((res) => {
            if (res.code === "200") {
              this.$message({ message: '邮件已发送，请注意查收邮件！', type: 'success' })
            } else {
              this.$message({ message: res.msg, type: 'error' })
            }
            this.loading = false
          }).catch((res) => {
            this.loading = false
          })
        } else {
          this.loading = false
          return false;
        }
      })
    },
    handleFocus() {
      this.inputType = this.$refs.pwd.value ? 'password' : 'text'
    },
    handleBlur() {
      this.inputType = this.$refs.pwd.value ? 'password' : 'text'
    },
      clearMask() {
        document.querySelectorAll(['.v-modal', '.shadowbox']).forEach(node => node.remove());
      },
      // 清空表单
      resetForm() {
        let _this = this;
        ['account', 'mobile', 'email'].map(formName => {
          if(_this.loginMode[formName]) {
            _this.$refs[formName + 'LoginForm'].resetFields();
          }
        })
      }
  },
    activated () {
      this.clearMask();
      this.resetForm();
      Cookies.remove(config.cookieName);
      // sessionStorage.removeItem(config.cookieName);
      this.refreshCaptcha();
    },
  mounted() {
    this.resetForm();
    Cookies.remove(config.cookieName);
    // sessionStorage.removeItem(config.cookieName)
    this.refreshCaptcha()
  },
  created() {
    this.clearMask();
      if(this.sso.onoff) {
        if(this.t.token) {
          this.loading = true
          this.$api.authority.getUserInfoByToken({token: this.t.token}).then((res) => {
            loginInit({userInfo: res});
            getSpaceId(res.userName).then(() => {
              this.$router.push(typeof this.t.redirectPath === 'undefined' || this.t.redirectPath.indexOf("/login") !== -1 ? '/' : this.t.redirectPath) // 登录成功，跳转到主页
            })
          }).catch((res) => {
            this.loading = false
          })
        } else {
          this.$api.authority.getState().then(res => {
            let state = res.data
            window.location.href = config.sso.url + '?fsc=' + config.systemCode + '&client_id=' + config.client_id + '&state=' + state
          })
        }
      }
  }
}
</script>
<style scoped lang="scss">
.formBgMask {
  width: 488px;
  height: 541px;
  opacity: 0.8;
  background: #041424;
  left: calc((100% - 488px) / 2);
  top: calc((100% - 541px) / 2);
  position: absolute;
  z-index: 1;
}
/*父容器*/
.image-light1 {
  overflow: hidden;
  position: relative;
  /*设置为相对*/
}
.image-light2 {
  overflow: hidden;
  position: relative;
  width: 48px;
  margin-left: 44%;
  /*设置为相对*/
}
/*流光（采用伪类实现，当然也可以是子元素）*/
.image-light1:after {
  content: "";
  position: absolute;
  /*相对父级绝对定位*/
  width: 50px;
  height: 100%;
  top: 0;
  left: -100px;
  /*起始位置*/
  overflow: hidden;
  z-index: 9;
  /*背景渐变*/
  background: linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  -webkit-transform: skewX(0deg);
  /*倾斜*/
  -moz-transform: skewX(0deg);
  transform: skewX(0deg);
  animation: go 10s infinite linear;
}
@keyframes go {
  0% {
    left: -100px;
  }
  50% {
    left: 50%;
  }
  100% {
    left: calc(100% + 100px);
  }
}
.image-light2:after {
  content: "";
  position: absolute;
  /*相对父级绝对定位*/
  width: 50px;
  height: 100%;
  top: 0;
  left: -260px;
  /*起始位置*/
  overflow: hidden;
  z-index: 9;
  background: linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.7) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  -webkit-transform: skewX(0deg);
  /*倾斜*/
  -moz-transform: skewX(0deg);
  transform: skewX(0deg);
  animation: gogo 10s infinite linear;
}
@keyframes gogo {
  0% {
    left: -255px;
  }
  50% {
    left: 50%;
  }
  100% {
    left: calc(100% + 250px);
  }
}
.warn-box {
  position: absolute;
  bottom: -88px;
  height: 80px;
  margin-left: -3px;
  margin-right: 10px;
  padding: 0px 10px;
  border-radius: 4px;
  background: radial-gradient(#ffffff00, #06698400, #066984a8);
  border: 1px solid #066984;
  .warn-icon {
    width: 50px;
    float: left;
    height: 100%;
    span {
      color: red;
      line-height: 80px;
      font-weight: bold;
      font-size: 18px;
    }
  }
  .warn-line {
    float: left;
    border-left: 1px solid #066984;
    height: 50px;
    margin: 15px 18px 15px 10px;
  }
  .warn-message {
    font-weight: bold;
    width: calc(100% - 90px);
    float: left;
    padding-top: 18px;
    span {
      color: #00ddff;
      font-size: 14px;
    }
  }
}
.loginPage-container .form-wrapper fieldset {
  bottom: 90px !important;
}
.loginPage-container .form-wrapper .login-container .title {
  margin: 10px auto 10px auto !important;
}
/*<!--.loginPage-container .form-wrapper .logo.noFieldset{-->*/
/*<!--  margin-top: -515px !important;-->*/
/*<!--}-->*/
.loginPage-container .form-wrapper {
  top: calc((100% - 540px) / 2) !important;
}
.loginPage-container .form-wrapper .copyright {
  margin-top: calc(100vh / 2 - 92px) !important;
}

.night {
  position: relative;
  width: 100%;
  height: 100%;
  transform: rotateZ(88deg);
}
$shooting-time: 6000ms;
.shooting_star {
  position: absolute;
  left: 50%;
  top: 50%;
  background: linear-gradient(
    -45deg,
    rgba(64, 224, 208, 1),
    rgba(0, 0, 255, 0)
  );
  border-radius: 999px;
  filter: drop-shadow(0 0 6px rgba(105, 155, 255, 1));
  /*<!--animation:-->*/
  /*<!--  tail $shooting-time ease-in-out infinite,-->*/
  /*<!--  shooting $shooting-time ease-in-out infinite;-->*/
  &::after {
    @extend .shooting_star::before;
    transform: translateX(50%) rotateZ(-45deg);
  }

  @for $i from 1 through 20 {
    $rz: (((3 * $i)/8) - 3) + 0deg;
    @keyframes tail {
      0% {
        width: 0;
      }

      30% {
        width: 600px;
      }
      100% {
        width: 0;
      }
    }

    @keyframes #{shooting + $i} {
      0% {
        transform: rotateZ(#{$rz}) translateX(0);
      }

      100% {
        transform: rotateZ(#{$rz}) translateX(2000px);
      }
    }
    &:nth-child(#{$i}) {
      $shooting-timer: random(8000) + 4000ms;
      $delay: random(6000) + 0ms;
      $random: random(3);
      top: calc(200% - #{random(1) * 100 * $i - 0px});
      left: -600 - (200 * $random) + 0px;
      width: random(300) + 300px;
      height: random(1) - 0.05 * $i + 2px;
      filter: drop-shadow(0px -5px 6px rgba(105, 155, 255, 1)) brightness(0.6);
      animation: tail $shooting-timer ease-in-out infinite,
        #{shooting + $i} $shooting-timer ease-in-out infinite;
      animation-delay: $delay;
      // opacity: random(50) / 100 + 0.5;

      &::before,
      &::after {
        animation-delay: $delay;
      }
    }
  }
}
</style>
