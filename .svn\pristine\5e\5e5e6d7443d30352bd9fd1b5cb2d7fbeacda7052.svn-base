/**
 * 时间日期相关操作
 */

/**
 * 时间格式化
 * 将 2018-09-23T11:54:16.000+0000 格式化成 2018/09/23 11:54:16
 * @param datetime 国际化日期格式
 */
export function format (datetime) {
  return formatWithSeperator(datetime, '/', ':')
}

/**
 * 时间格式化
 * 将 2018-09-23T11:54:16.000+0000 格式化成类似 2018/09/23 11:54:16
 * 可以指定日期和时间分隔符
 * @param datetime 国际化日期格式
 */
export function formatWithSeperator (datetime, dateSeprator, timeSeprator) {
  if (datetime != null) {
    const dateMat = new Date(datetime)
    const year = dateMat.getFullYear()
    const month = (dateMat.getMonth() + 1).toString().padStart(2, '0')
    const day = dateMat.getDate().toString().padStart(2, '0')
    const hh = dateMat.getHours().toString().padStart(2, '0')
    const mm = dateMat.getMinutes().toString().padStart(2, '0')
    const ss = dateMat.getSeconds().toString().padStart(2, '0')
    const timeFormat = year + dateSeprator + month + dateSeprator + day + ' ' + hh + timeSeprator + mm + timeSeprator + ss
    return timeFormat
  }
}

// 格式化日期
export function formatDate (datetime, dateSeprator) {
  if (datetime != null) {
    const dateMat = new Date(datetime)
    const year = dateMat.getFullYear()
    const month = (dateMat.getMonth() + 1).toString().padStart(2, '0')
    const day = dateMat.getDate().toString().padStart(2, '0')
    const timeFormat = year + dateSeprator + month + dateSeprator + day
    return timeFormat
  }
}

// 获取当前月份的最后一天
export function getMonthLastDay(datetime) {
  var date = new Date(datetime);
  var currentMonth = date.getMonth();
  var nextMonth = ++currentMonth;
  var nextMonthFirstDay = new Date(date.getFullYear(), nextMonth, 1);
  var oneDay = 1000 * 60 * 60 * 24;
  return new Date(nextMonthFirstDay - oneDay);
}
