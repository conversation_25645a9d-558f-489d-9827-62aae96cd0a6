<template>
  <div class="widgetMyWork">
    <div class="basicMes">
      <div class="returnBack">
        <img src="~@/assets/img/pic-back.png" alt="">
        <span>返回</span>
      </div>
      <div class="title">{{ pageData.title }}</div>
      <div class="centerPart">
        <div class="leftPart">
          <img src="~@/assets/img/pic-emptyImg.png" alt="">
        </div>
        <div class="middlePart">
          <el-row>
            <el-col :span="12">
              <div class="singleMes">
                来源单位：<span>投资处</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="singleMes">
                来源单位：<span>投资处</span>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="rightPart"></div>
      </div>
      <div class="bottomPart"></div>
    </div>
    <div class="otherMes"></div>
  </div>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import SelectExtend from "@/core/components/SelectExtend";
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SplitBar from "@/core/components/SplitBar";
import SelectPlus from "@/core/components/SelectPlus/index.vue";
export default {
  name: "pageList",
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter
  },
  props: {
  },
  data () {
    return {
      pageData: {
        title: "投资建设的固定资产投资项目核准信息",
        dataImg: "",
        unit: "",
        system: "",
        mainType: "",
        label: "",
        constracts: "",
        pubTime: "",
        updateTime: "",
        updateCycle: "",
        quality: "",
        itemStatus: ['有条件共享', '不予开放'],
        sourceTypeList: ['1', '2'],
        visitNum: '2526',
        collectNum: '12'
      }
    }
  },
  methods: {
  },
  mounted () {
  }
};
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.basicMes {
  width: 100%;
  height: 300px;
  background: #FFFFFF;
  border-radius: 2px;
  margin-bottom: 20px;
  padding: 26px 20px 26px 30px;
  position: relative;

  .returnBack {
    position: absolute;
    right: 0;
    top: 0;
    width: 106px;
    height: 37px;
    display: flex;
    align-items: center;
    background-image: url('~@/assets/img/pic-backBg.png');
    background-repeat: no-repeat;
    background-size: 100%;

    img {
      margin-left: 30px;
      margin-right: 7px;
    }

    span {
      //font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #FFFFFF;
      line-height: 17px;
    }
  }

  .title {
    //font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 16px;
  }

  .centerPart {
    display: flex;

    .leftPart {
      width: 194px;
      height: 196px;
      padding: 41px 46px 59px 42px;
      background: #F1F5FA;
    }

    .middlePart {
      width: calc(100% - 400px);
      background: #f00;
    }

    .rightPart {
      width: 194px;
    }
  }

  .bottomPart {}
}

.otherMes {
  width: 100%;
  height: 840px;
  background: #FFFFFF;
  box-shadow: -1px 5px 10px 0px rgba(63, 78, 103, 0.05);
  border-radius: 2px;
}
</style>
