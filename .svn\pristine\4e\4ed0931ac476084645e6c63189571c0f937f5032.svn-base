<template>
  <el-container class="page-container">
    <el-main>
      <div class="basicMes">
        <div style="display: flex; align-items: center;">
          <div class="title">{{ pageData.directoryName }}</div>
          <span class="info-list" v-if="pageData.isExistOrder && pageData.isExistOrder === '1'">已订阅</span>
        </div>
        <div class="centerPart">
          <div class="leftPart">
            <img src="~@/assets/img/pic-shujumulu.svg" alt="">
          </div>
          <div class="middlePart">
            <el-row>
              <el-col :span="12">
                <div class="singleMes" v-if="pageData.providerDeptName">
                  提供单位：<span>{{ pageData.providerDeptName }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="singleMes" v-if="pageData.publishTime">
                  发布时间：<span>{{ pageData.publishTime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="singleMes" v-if="pageData.sharindMethod">
                  共享方式：<span>{{ pageData.sharindMethod }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="singleMes" v-if="pageData.dataUpdateTime">
                  更新时间：<span>{{ pageData.dataUpdateTime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="pageData.updateCycle">
                <div class="singleMes">
                  更新周期：<span>{{ pageData.updateCycle }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.resClassification">
                <div class="singleMes">
                  主题分类：<span>{{ pageData.resClassification }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="singleMes" v-if="pageData.labelList">
                  标签：<span>{{ handlelabelList(pageData.labelList) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="singleMes flex" v-if="pageData.appStatus">
                  目录质量：<div :class="{ mark: true, good: pageData.appStatus === '1', fine: pageData.appStatus === '2', bad: ['3'].includes(pageData.appStatus) }">{{ handleCataQuality(pageData.appStatus) }}</div>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12" v-if="pageData.resourceType && (pageData.resourceType.indexOf('1') > -1 || pageData.resourceType.indexOf('4') > -1) && pageData.isExternalImport !== '9'">
                <div class="singleMes">
                  结构化数据量：<span>{{ showTotal(pageData.tdxTable) || 0 }}条</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.resourceType && (pageData.resourceType.indexOf('2') > -1 || pageData.resourceType.indexOf('5') > -1) && pageData.isExternalImport !== '9'">
                <div class="singleMes">
                  非结构化数据量：<span>{{ (pageData.tdxFile / 1024 / 1024 ).toFixed(3) || 0 }}M</span>
                </div>
              </el-col>
            </el-row>

            <el-row v-if="pageData.rowChangeTime">
              <el-col :span="12">
                <div class="singleMes">
                  业务数据更新时间：<span>{{ pageData.rowChangeTime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="pageData.cataAbstract">
              <el-col :span="24">
                <div class="singleMes">
                  目录摘要：<span>{{ pageData.cataAbstract }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="rightPart">
            <div class="itemStatus">
              <span class="info-tip" v-if="pageData.shardType && pageData.shardType !== ''">{{ pageData.shardType }}</span>
              <span class="info-tip" v-if="pageData.openType && pageData.openType !== ''">{{ pageData.openType }}</span>
            </div>
            <div class="right-info">
              <!-- 非回流数据目录 -->
              <template v-if="pageData.catalogTypeCode !== '3'">
                <div class="type-info" :class="{ 'active':pageData.resourceType && pageData.resourceType.indexOf('1') > -1 }">
                  <svg-icon icon-class='gxsl-kubiao' />
                  <p>库表</p>
                </div>
                <div class="type-info" :class="{ 'active': pageData.resourceType && pageData.resourceType.indexOf('2') > -1 }">
                  <svg-icon icon-class='gxsl-wenjian' />
                  <p>文件</p>
                </div>
                <div class="type-info" :class="{ 'active': pageData.resourceType && pageData.resourceType.indexOf('3') > -1}">
                  <svg-icon icon-class='gxsl-jiekou' />
                  <p>接口</p>
                </div>
                <div class="type-info" :class="{ 'active': pageData.resourceType && pageData.resourceType.indexOf('5') > -1}">
                  <svg-icon icon-class='gxsl-wenjianjia' />
                  <p>文件夹</p>
                </div>
              </template>
              <!-- 回流数据目录 -->
              <template v-else>
                <div class="type-info" :class="{ 'active': pageData.resourceType && pageData.resourceType.indexOf('4') > -1}">
                  <svg-icon icon-class='gxsl-huiliu' />
                  <p>回流</p>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div class="bottomPart">
          <div class="itemNum">
            <span class="num">{{ pageData.orderSum || 0 }}次<span>订阅</span></span>
            <span class="num">{{ pageData.browseSum || 0 }}次<span>访问</span></span>
            <span class="num">{{ pageData.collectSum || 0 }}次<span>收藏</span></span>
          </div>
          <div class="itemHandleBtn">
            <el-button class="icsp-button-grey" disabled v-if="pageData.providerCode === (currentUser?currentUser.unitCode:null)">
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>
            <el-button class="icsp-button-grey" v-else-if="!pageData.resourceType" :disabled="!pageData.resourceType">
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>
            <el-button v-else class="icsp-button" plain @click.stop="handleCataApply(pageData)">
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>

            <el-button class="icsp-button-grey" disabled v-if="pageData.providerCode === (currentUser?currentUser.unitCode:null)">
              <svg-icon icon-class="gxsl-shoppingCart" />
              加入选数车
            </el-button>
            <el-button class="icsp-button-grey" :disabled="!pageData.resourceType" v-else-if="!pageData.resourceType">
              <svg-icon icon-class="gxsl-shoppingCart" />
              {{ pageData.addSelect === '1' ? "取消加入选数车" : "加入选数车" }}
            </el-button>
            <el-button v-else  :disabled="queryBt" :class="pageData.addSelect === '1' ? 'icsp-button2' : 'icsp-button'" plain @click.stop="
                     handleAddCatalogToCart()
                    ">
              <svg-icon icon-class="gxsl-shoppingCart" />
              {{ pageData.addSelect === '1' ? "取消加入选数车" : "加入选数车" }}
            </el-button>

            <el-button class="icsp-button-grey" disabled v-if="pageData.providerCode === (currentUser?currentUser.unitCode:null)">
              <svg-icon icon-class="gxsl-collect" />
              收藏
            </el-button>
            <el-button v-else :disabled="queryBt" :class="pageData.collect ? 'icsp-button2' : 'icsp-button'" plain @click.stop="
                    handleCollect(pageData)
                    ">
              <svg-icon icon-class="gxsl-collect" />
              {{ pageData.collect ? "取消收藏" : "收藏" }}
            </el-button>
            <el-button @click.stop="handleLeaveAMessage(pageData)" :class="pageData.collect ? 'icsp-button2' : 'icsp-button'">
              <svg-icon icon-class="feedback" />
              留言
            </el-button>

            <!--            <el-button class="icsp-button-grey" disabled v-if="pageData.providerCode === (currentUser?currentUser.unitCode:null)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
            <!--            <el-button v-else class="icsp-button" plain @click.stop="handleFeedback(pageData)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
          </div>
        </div>
      </div>
      <div class="otherMes">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="信息项" name="first" v-if="pageData.trsCatalogItems">
            <div class="mainConte mainConteFirst">
              <div class="fullPart">
                <div class="fullContent">
                  <el-table :data="pageData.trsCatalogItems" border stripe style="width: 100%" class="projectTable" ref="basicInfoTable">
                    <el-table-column type="index" label="序号" width="120" align="center"></el-table-column>
                    <el-table-column prop="itemName" label="信息项名称" min-width="140" header-align="center">
                    </el-table-column>
                    <el-table-column prop="dataType" label="数据类型" align="center" min-width="120"></el-table-column>
                    <el-table-column prop="isPublic" label="是否向社会开放" align="center" min-width="120">
                      <template slot-scope="scope">
                        <div v-if="scope.row.isPublic === '1'">是</div>
                        <div v-else>否</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="目录健康度" name="forth" v-if="pageData.resourceCataHealthVos">
            <div class="mainConte">
              <div class="fullPart">
                <div class="fullContent border">
                  <el-table :data="pageData.resourceCataHealthVos" border stripe style="width: 100%" class="projectTable" ref="healthyInfoTable" :span-method="objectSpanMethod" show-summary id="countTable" sum-text="合计：">
                    <el-table-column type="index" label="序号" align="center" width="100">
                    </el-table-column>
                    <el-table-column prop="checkContent" label="规则分类" header-align="center" width="220">
                    </el-table-column>
                    <el-table-column prop="ruleName" label="规则名称" header-align="center" align="left" width="220">
                    </el-table-column>
                    <el-table-column prop="ruleDesc" label="规则描述" header-align="center" align="left" min-width="220">
                    </el-table-column>
                    <el-table-column prop="score" label="分值占比" header-align="center" align="right" width="80">
                    </el-table-column>
                    <el-table-column prop="ratio" label="分值" header-align="center" align="right" width="80">
                    </el-table-column>
                    <el-table-column prop="lastScore" label="得分" header-align="center" align="right" width="80">
                    </el-table-column>
                    <el-table-column prop="scoreDesc" label="得分明细" header-align="center" align="left" width="220">
                    </el-table-column>
                  </el-table>
                  <p class="desc">根据评分区间规则，高：85分以上、中：60（含）-85（含）分、低：60分以下</p>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <template v-if="pageData.resourceType">
            <el-tab-pane label="库表" name="1" v-if="pageData.resourceType.split(',').includes('1')">
              <div class="mainConte">
                <div class="leftPart">
                  <div class="tableItem" :title="item.tableName" :class="{ 'active': fifthActiveId === item.tableId }" v-for="item in fifthTableList" :key="item.tableId" @click="fifthPaneTableCheck(item)">
                    {{ item.tableName }}
                  </div>
                </div>
                <div class="rightPart">
                  <div class="rightContent" v-if="fifthActiveTableData">
                    <div class="rightMessage">
                      <div class="titlepart">
                        <p>{{fifthActiveTableData.tableName}}({{fifthActiveTableData.tableNameCn}})</p>
                        <div class="line"></div>
                        <span class="message" v-if="fifthActiveTableData.providerDeptName">提供单位：<span>{{ fifthActiveTableData.providerDeptName }}</span></span>
                        <span class="message" v-if="fifthActiveTableData.updateCycle">更新周期：<span>{{ fifthActiveTableData.updateCycle }}</span></span>
                        <span class="message" v-if="fifthActiveTableData.dataUpdateTime">更新时间：<span>{{ fifthActiveTableData.dataUpdateTime }}</span></span>
                        <br>
                        <span class="message" v-if="fifthActiveTableData.resourceType">资源分类：<span>{{ fifthActiveTableData.resourceType }}</span></span>
                        <span class="message" ><span class="label">数据量：</span>{{fifthActiveTableData.dataSize}}条</span>
                      </div>
                      <div class="bottomPart">
                        <div class="itemStatus">
                          <span v-if="fifthActiveTableData.shareType">
                            {{ fifthActiveTableData.shareType }}
                          </span>
                          <span v-if="fifthActiveTableData.openType">
                            {{ fifthActiveTableData.openType }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <el-table :data="fifthActiveTableData.columns" border stripe :key="'dataVoList'" style="width: 100%" class="projectTable">
                      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                      <el-table-column prop="fieldCode" label="字段名称" min-width="160" show-overflow-tooltip header-align="center"></el-table-column>
                      <el-table-column prop="fieldName" label="信息项名称" min-width="160" show-overflow-tooltip header-align="center"></el-table-column>
                      <el-table-column prop="fieldType" label="字段类型" width="160" align="center"></el-table-column>
                      <el-table-column prop="fieldLength" label="字段长度" width="100" header-align="center" align="right"></el-table-column>
                      <el-table-column prop="fieldIspk" label="是否主键" width="100" align="center"></el-table-column>
                      <el-table-column prop="fieldIsnull" label="可否为空" width="100" align="center">
                        <template slot-scope="scope">
                          <div v-if="scope.row.fieldIsnull === '1'">是</div>
                          <div v-else>否</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="接口" name="3" v-if="pageData.resourceType.split(',').includes('3')">
              <div class="mainConte">
                <div class="leftPart">
                  <div class="tableItem" :title="item.serviceName" :class="{ 'active': sixthActiveId === item.serviceId }" v-for="item in sixthTableList" :key="item.serviceId" @click="sixthPaneTableCheck(item)">
                    {{ item.serviceName }}
                  </div>
                </div>
                <div class="rightPart">
                  <div class="rightContent" v-if="sixthActiveTableData">
                    <div class="titlepart">
                      <p>{{sixthActiveTableData.serviceName}}</p>
                      <div class="line"></div>
                    </div>
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="服务名称">{{
            sixthActiveTableData.serviceName
          }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务类型">{{
              sixthActiveTableData.interfacetype
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务描述">{{
              sixthActiveTableData.description
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务版本">{{
              sixthActiveTableData.serviceVersion
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="服务注册者">{{
              sixthActiveTableData.userName
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="注册机构">{{
              sixthActiveTableData.createOrgName
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="注册时间">{{
              sixthActiveTableData.createTime
            }}
                      </el-descriptions-item>
                      <el-descriptions-item label="授权方式">
                        {{ sixthActiveTableData.authorizationMode }}
                      </el-descriptions-item>
                      <el-descriptions-item label="协议类型">
                        {{ sixthActiveTableData.protocol }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位">{{
            sixthActiveTableData.supportUnit
          }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位联系人">
                        {{ sixthActiveTableData.supportUnitContact }}
                      </el-descriptions-item>
                      <el-descriptions-item label="技术支持单位联系电话">
                        {{ sixthActiveTableData.supportUnitPhone }}
                      </el-descriptions-item>
                      <el-descriptions-item label="附件">
                        <el-link type="primary" v-if="sixthActiveTableData.fileName" @click="downLoadServer(sixthActiveTableData)">
                          {{ sixthActiveTableData.fileName }}
                        </el-link>
                      </el-descriptions-item>
                    </el-descriptions>
                    <div class="titlepart">
                      <p>方法列表</p>
                      <div class="line"></div>
                    </div>
                    <el-collapse v-model="activeFunName" accordion v-if="sixthActiveTableData.funcList.length">
                      <el-collapse-item v-for="(itemFun, itemFunIndex) in sixthActiveTableData.funcList" :title="itemFun.funcName" :name="itemFunIndex" :key="'funcList' + itemFunIndex">
                        <div class="collapseCont">
                          <div class="fun-info-p" v-if="itemFun.funcDesc">
                            <span class="label">方法描述：</span>
                            <span class="content">{{ itemFun.funcDesc }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.requestMethod">
                            <span class="label">请求方式：</span>
                            <span class="content">{{ itemFun.requestMethod }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.returnType">
                            <span class="label">返回格式：</span>
                            <span class="content">{{ itemFun.returnType }}</span>
                          </div>
                          <div class="fun-info-table">
                            <div class="info-table">
                              <span class="label">输入参数：</span>
                              <el-table :data="itemFun.requestParam" border stripe max-height="300" style="width: 100%" class="projectTable">
                                <el-table-column type="paramSort" label="排序" width="80" align="center"></el-table-column>
                                <el-table-column prop="paramName" label="参数名称" min-width="120" header-align="center">
                                </el-table-column>
                                <el-table-column prop="paramStatus" label="是否必填" align="center"></el-table-column>
                                <el-table-column prop="paramLength" label="参数长度" align="right" header-align="center"></el-table-column>
                                <el-table-column prop="paramAttr" label="参数属性" header-align="center"></el-table-column>
                                <el-table-column prop="paramAddr" label="参数位置" header-align="center">
                                </el-table-column>
                                <el-table-column prop="paramDesc" label="参数描述" min-width="350" header-align="center">
                                </el-table-column>
                              </el-table>
                            </div>
                            <div class="info-table">
                              <span class="label">输出参数：</span>
                              <el-table :data="itemFun.responseParam" border stripe max-height="300" style="width: 100%" class="projectTable">
                                <el-table-column type="paramSort" label="排序" width="80" align="center"></el-table-column>
                                <el-table-column prop="paramName" label="参数名称" min-width="120" header-align="center">
                                </el-table-column>
                                <el-table-column prop="paramStatus" label="是否必填" align="center"></el-table-column>
                                <el-table-column prop="paramLength" label="参数长度" align="right" header-align="center"></el-table-column>
                                <el-table-column prop="paramAttr" label="参数属性" header-align="center"></el-table-column>
                                <el-table-column prop="paramAddr" label="参数位置" header-align="center">
                                </el-table-column>
                                <el-table-column prop="paramDesc" label="参数描述" min-width="350" header-align="center">
                                </el-table-column>
                              </el-table>
                            </div>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.requestExample">
                            <span class="label">请求示例：</span>
                            <span class="content">{{ itemFun.requestExample }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.responseExampleSuccess">
                            <span class="label">成功响应示例：</span>
                            <span class="content">{{ itemFun.responseExampleSuccess }}</span>
                          </div>
                          <div class="fun-info-p" v-if="itemFun.responseExampleFail">
                            <span class="label">失败响应示例：</span>
                            <span class="content">{{ itemFun.responseExampleFail }}</span>
                          </div>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="文件" name="2" v-if="pageData.resourceType.split(',').includes('2')">
              <div class="mainConte" v-if="sevenTableList.length">
                <div class="fullPart">
                  <div class="fullContent">
                    <el-table :data="sevenTableList" border stripe style="width: 100%">
                      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                      <el-table-column prop="resourceName" label="资源名称" min-width="180" header-align="center" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="fileName" label="文件名称" min-width="180" header-align="center" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="fileSize" label="文件大小（单位：kb）" width="200" align="right" header-align="center"></el-table-column>
                      <el-table-column prop="uploadtime" label="上传时间" width="200" align="center"></el-table-column>
                      <el-table-column prop="description" label="说明" header-align="center" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="" label="操作" align="center">
                        <template slot-scope="scope">
                          <el-link type="primary" @click="downLoadFile(scope.row)" v-if="scope.row.downloadLicenseCode && scope.row.downloadLicenseCode !== '0' && isLogOn()">下载<i class="el-icon-download"></i></el-link>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="文件夹" name="5" v-if="pageData.resourceType.split(',').includes('5')">
              <div class="mainConte" v-if="eightTableList.length">
                <div class="fullPart">
                  <div class="fullContent">
                    <el-table :data="eightTableList" border stripe style="width: 100%">
                      <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                      <el-table-column prop="resourceName" label="文件夹名称" min-width="180" header-align="center" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="folderPath" label="文件夹路径" min-width="180" header-align="center" show-overflow-tooltip></el-table-column>
                      <el-table-column prop="createTime" label="创建时间" width="200" align="center"></el-table-column>
                      <el-table-column prop="remark" label="说明" header-align="center" show-overflow-tooltip>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>

        <!-- 数据订阅 -->
        <SubscribeDialog :dialogVisible.sync="dialogVisibleApply" :type="type" :title="title" :data="dialogData"></SubscribeDialog>
        <!-- 问题反馈 -->
        <!--        <FeedbackDialog :dialogVisible.sync="feedbackVisible" :data="dialogData"></FeedbackDialog>-->
        <LeaveAMessage :dialogVisible.sync="dialogVisibleMessage" title="留言" :cataId="$route.query.cataId"></LeaveAMessage>
      </div>
    </el-main>
  </el-container>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index'
import Cookies from 'js-cookie'
import SubscribeDialog from '@/biz/components/SubscribeDialog'
import FeedbackDialog from '@/biz/components/FeedbackDialog'
import { FileUtils } from '@/biz/utils/download'
import LeaveAMessage from '@/biz/components/LeaveAMessage'

export default {
  name: 'pageList',
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog,
    FeedbackDialog,
    LeaveAMessage
  },
  props: {},
  data() {
    return {
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      pageData: {
        resourceCataHealthVosleData: []
      },
      queryBt: false,
      activeName: 'first',
      firstPaneActiveId: '',
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      feedbackVisible: false,
      secondTableList: [],
      fifthTableList: [],
      fifthActiveId: '',
      fifthActiveTableData: undefined,
      sixthTableList: [],
      sixthActiveId: '',
      sixthActiveTableData: undefined,
      sevenTableList: [],
      eightTableList: [],
      activeFunName: 0,
      tableData: [],
      dialogVisibleMessage: false,
      mergeObj: {},
      mergeArr: ['checkContent', 'ruleName'],
      tempInfo: {
        itemVoList: [
          {
            resourceName: '统一社会信用代码',
            dataType: '字符串',
            sensiLevel: '三级',
            fieldType: '无条件共享',
            openType: '无条件开放'
          },
          {
            resourceName: '法定代表人',
            dataType: '字符串',
            sensiLevel: '三级',
            fieldType: '无条件共享',
            openType: '无条件开放'
          }
        ],
        dataVoList: [
          {
            fieldName: '法定代表人',
            resourceName: '字符串',
            fieldLength: '三级',
            fieldType: '无条件共享',
            isPk: '无条件开放',
            nullable: '否'
          },
          {
            fieldName: '法定代表人',
            resourceName: '字符串',
            fieldLength: '三级',
            fieldType: '无条件共享',
            isPk: '无条件开放',
            nullable: '否'
          }
        ],
        serviceVoList: {
          resourceName: 'resourceName',
          interfaceType: 'interfaceType',
          description: 'description',
          serviceVersion: 'serviceVersion',
          userName: 'userName',
          createOrgName: 'createOrgName',
          createTime: 'createTime',
          authOrizattionMode: 'authOrizattionMode',
          procotol: '1',
          suppotUnit: 'suppotUnit',
          supportUnitContact: 'supportUnitContact',
          supportUnitPhone: 'supportUnitPhone',
          serviceOriginFile: null,
          funcList: [
            {
              funcName: 'funcName',
              desc: 'desc',
              requestMethod: 'requestMethod',
              returnType: 'returnType',
              paramInList: [
                {
                  name: 'name',
                  type: 'type',
                  length: 'length',
                  position: 'position',
                  isNull: 'isNull',
                  desc: 'desc'
                }
              ],
              paramOutList: [
                {
                  name: 'name',
                  type: 'type',
                  length: 'length',
                  position: 'position',
                  isNull: 'isNull',
                  desc: 'desc'
                }
              ],
              requestExample: '',
              responseExampleSucc: '',
              responseExampleFail: ''
            }
          ]
        },
        fileVoList: [
          {
            resourceName: 'resourceName',
            fileType: 'fileType',
            fileSize: 'fileSize',
            uploadtime: 'uploadtime',
            description: 'description',
            fileOrder: 'fileOrder',
            isUpLoad: 'isUpLoad'
          }
        ],
        fileFolderVoList: [
          {
            resourceName: 'resourceName',
            fileType: 'fileType',
            uploadtime: 'uploadtime',
            description: 'description'
          }
        ]
      }
    }
  },
  methods: {
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    handleClick(tab, event) {
      if (this.activeName === 'second') {
        this.getCatalogInfo()
      } else if (this.activeName === '1') {
        this.getDirResourceDatabase()
      } else if (this.activeName === '3') {
        this.getDirResourceService()
      } else if (this.activeName === '2') {
        this.getDirResourceFile()
      } else if (this.activeName === '5') {
        this.getDirResourceFolder()
      }
    },
    getCatalogInfo() {
      this.$api.bizApi.pageRequest
        .getCatalogInfo({
          cataId: this.$route.query.cataId
        })
        .then((res) => {
          console.log(res, 'sadajs')
          this.secondTableList = res.data.resource
        })
    },
    getDirResourceDatabase() {
      this.$api.bizApi.pageRequest
        .getDirResourceDatabase({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          console.log(res)
          this.fifthTableList = res.data
          this.fifthActiveId = res.data[0].tableId
          this.fifthActiveTableData = res.data[0]
        })
    },
    getDirResourceService() {
      this.$api.bizApi.pageRequest
        .getDirResourceService({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          console.log(res)
          this.sixthTableList = res.data
          this.sixthActiveId = res.data[0].serviceId
          this.sixthActiveTableData = res.data[0]
        })
    },
    getDirResourceFile() {
      this.$api.bizApi.pageRequest
        .getDirResourceFile({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          this.sevenTableList = res.data
        })
    },
    getDirResourceFolder() {
      this.$api.bizApi.pageRequest
        .getDirResourceFolder({
          catalogId: this.$route.query.cataId
        })
        .then((res) => {
          console.log(res)
          this.eightTableList = res.data
        })
    },
    fifthPaneTableCheck(item) {
      this.fifthActiveId = item.tableId
      this.fifthActiveTableData = item
    },
    sixthPaneTableCheck(item) {
      this.sixthActiveId = item.serviceId
      this.sixthActiveTableData = item
    },
    firstPaneTableCheck(data) {
      console.log(data)
      this.firstPaneActiveId = data.tableId
    },
    getSpanArr(data) {
      this.mergeArr.forEach((key, index1) => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
    },
    // 默认接受四个值 { 当前行的值, 当前列的值, 行的下标, 列的下标 }
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断列的属性
      // if (this.mergeArr.indexOf(column.property) !== -1) {
      //   // 判断其值是不是为0
      //   if (this.mergeObj[column.property][rowIndex]) {
      //     return [this.mergeObj[column.property][rowIndex], 1]
      //   } else {
      //     // 如果为0则为需要合并的行
      //     return [0, 0];
      //   }
      // }
      setTimeout(() => {
        if (this.$el.querySelector('#countTable')) {
          let tabelRef = this.$el
            .querySelector('#countTable')
            .querySelector('.el-table__footer-wrapper')
            .querySelector('.el-table__footer')
          const subTotalEl = tabelRef.rows[0]
          const COLNUM = 6
          // 合并 小计行
          subTotalEl.cells[0].colSpan = '6'
          subTotalEl.cells[0].style.textAlign = 'center'
          for (let index = 1; index < COLNUM; index++) {
            subTotalEl.cells[index].style.display = 'none'
          }
        }
      }, 50)
    },
    // 服务附件 下载
    downLoadFile(item) {
      console.log(item, 'item')
      if (item.filePath) {
        this.$api.bizApi.pageRequest
          .downloadFile({
            path: item.filePath,
            fileName: item.fileName,
            resourceId: item.resourceId,
            isExternalImport: 0,
            orderId: item.orderId
          })
          .then((res) => {
            // this.downLoadServerNext(item)
            console.log(res)
            FileUtils.fileDownload([res.data], item.fileName)
          })
      } else {
        this.$message.warning('文件ID不存在，无法下载')
      }
    },
    // 目录收藏
    handleCollect(data) {
      if (this.isLogOn()) {
        if (!this.pageData.collect) {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addCatalogToFavorites({ cataId: this.$route.query.cataId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('收藏成功')
                this.$set(data, 'collect', true)
                this.$set(data, 'collectSum', (data.collectSum += 1))
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否取消收藏该能力?', '取消收藏', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .cancelCatalogToFavorites({
                cataId: this.$route.query.cataId
              })
              .then((res) => {
                if (res.code === '200') {
                  // this.getCatalogDetails()
                  this.$message({
                    type: 'success',
                    message: '取消收藏成功!'
                  })
                  this.$set(data, 'collect', false)
                  this.$set(data, 'collectSum', (data.collectSum -= 1))
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 留言
    handleLeaveAMessage() {
      if (this.isLogOn()) {
        this.dialogVisibleMessage = true
      }
    },
    // 目录订阅
    handleCataApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = data
        this.dialogVisibleApply = true
        this.dialogData.cataId = data.cataId
      }
    },
    // 加入/取消选数车
    handleAddCatalogToCart() {
      if (this.isLogOn()) {
        if (this.pageData.addSelect !== '1') {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addCatalogToCart({ cataId: this.$route.query.cataId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('加入选数车成功')
                this.$set(this.pageData, 'addSelect', '1')
                // this.getCatalogDetails()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false;
            });
        } else {
          this.$confirm('是否从选数车中取消该目录?', '取消确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .delCatalogFromCart({
                cataId: this.$route.query.cataId
              })
              .then((res) => {
                if (res.code === '200') {
                  this.$message.success('取消成功!')
                  this.$set(this.pageData, 'addSelect', '0')
                  // this.getCatalogDetails()
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 问题反馈
    // handleFeedback(data) {
    //   if (this.isLogOn()) {
    //     this.dialogData = data
    //     this.feedbackVisible = true
    //     this.dialogData.powerId = data.powerId
    //   }
    // },
    // 获取目录详情
    getCatalogDetails() {
      this.$api.bizApi.pageRequest
        .getCatalogDetails({
          cataId: this.$route.query.cataId,
          type: this.$route.query.type
        })
        .then((res) => {
          console.log(res)
          // if (res.data.resourceCataHealthVos) {
          //   this.getSpanArr(this.tableData);
          // }
          console.log(this.pageData.resourceType)
          if (res.data.resourceCataHealthVos) {
            for (const item of res.data.resourceCataHealthVos) {
              item.lastScore = (item.score * item.ratio) / 100
            }
          }
          this.pageData = res.data
        })
    },
    handlelabelList(data) {
      if (data) {
        let str = ''
        for (const item of data) {
          str += item.labelName + ','
        }
        str = str.slice(0, str.length - 1)
        return str
      } else {
        return ''
      }
    },
    handleCataQuality(val) {
      switch (val) {
        case '1':
          return '优'
        case '2':
          return '良'
        case '3':
          return '差'
        default:
          break
      }
    },
    // 接口附件 下载
    downLoadServer(item) {
      console.log(item, 'item111')
      const { attachId: id, fileName: name } = item
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
      } else {
        this.$api.bizApi.resList
          .downLoadOrderTipFile({ id: id })
          .then((res) => {
            if (res && res.data && res.data.size > 0) {
              let blob = new Blob([res.data], {
                type: 'application/octet-stream;charset=utf-8'
              })
              const link = document.createElement('a')
              link.href = URL.createObjectURL(blob)
              link.download = name
              link.style.display = 'none'
              document.body.appendChild(link)
              link.click()
              URL.revokeObjectURL(link.href)
              document.body.removeChild(link)
            } else {
              this.$message({
                type: 'error',
                message: '提示附件资源不存在或者已被删除'
              })
            }
          })
          .catch((e) => {
            this.$message({
              type: 'error',
              message: e
            })
          })
      }
    },
      showTotal(value) {
          if (value >= 100000000) {
              return (value / 100000000).toFixed(2) + "亿";
          } else if (value >= 10000) {
              return (value / 10000).toFixed(2) + "万";
          } else {
              return value;
          }
      }
  },
  mounted() {
    // this.activeName = this.$route.query.resType;
    this.getCatalogDetails()
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';
@import '@/assets/detail.scss';

.tableItem {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 230px;
}

.data-list {
  margin-left: 10px;
  margin-bottom: 15px;
  font-size: 16px;
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  color: #fff;
  background-color: #ff8635;
}
.huiliu-list {
  @extend .data-list;
  background-color: #83c954;
}
.info-list {
  @extend .data-list;
  background-color: #3572ff;
}

.mark {
  width: 24px;
  height: 28px;
  border-radius: 4px;
  color: #fff;
  text-align: center;
  position: relative;
  font-weight: 300;
  margin-right: 9px;
  background: #83c954;
  &::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
    border-top: 7px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
  }
}
.good {
  background: #83c954;
}
.fine {
  background: #4abdff;
}
.bad {
  background: #949494;
}

.flex {
  display: flex;
  align-items: center;
}
/deep/.el-table th.el-table__cell {
  background-color: #4EACFE;
  padding: 12px 0;
  .cell {
    background-color: #4EACFE;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color:#f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
