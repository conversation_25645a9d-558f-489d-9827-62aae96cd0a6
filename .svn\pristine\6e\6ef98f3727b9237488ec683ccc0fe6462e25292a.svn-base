/*
 * 接口统一集成模块
 * export * from './modules/...
 */
// import * as listManagementInformation from './modules/listManagementInformation'
import * as system from './modules/system'
import * as pageRequest from './modules/pageRequest'
import * as questionFeedback from './modules/questionFeedback'
import * as resApply from "./modules/resApply";
import * as resList from "./modules/resList";
import * as common from "./modules/common";
import * as upload from "./modules/upload";
import * as collect from "./modules/collect";

export * from './modules/common'
export * from './modules/subscribed'
export * from './modules/collect'
export * from './modules/system'

// 默认全部导出
export { system, pageRequest, questionFeedback, resApply, resList, common, upload, collect };
