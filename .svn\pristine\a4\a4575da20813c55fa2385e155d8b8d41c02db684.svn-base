<template>
  <el-dialog title="用户自定义快捷工具栏" class="resource-dialog" :visible.sync="dialogVisible" :close-on-click-modal="false" :append-to-body="true" width="550px">
    <tree-transfer v-model="value" v-on:setValue="setValue" :initData="data" ref="treeTransfer"></tree-transfer>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click.native="handleClose">取消</el-button>
      <el-button size="mini" class="save-btn" type="primary" @click.native="handleChecked">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TreeTransfer from './TreeTransfer'
export default {
  name: "ResourceDialog",
  components: { TreeTransfer },
  data() {
    return {
      dialogVisible: false, // 对话框是否可见
      value: [], // 用户自定义的快捷菜单
      data: [], // 初始化用的数据
      original: [] // 原始数据
    };
  },
  methods: {
    // 设置可见性
    setDialogVisible: function (visible) {
      this.dialogVisible = visible;
    },
    // 关闭对话框
    handleClose: function () {
      this.dialogVisible = false;
      this.init(this.original);
      console.log('this.original: ', this.original)
      this.$refs.treeTransfer.cancel(this.original);
    },
    // 处理用户自定义快捷菜单结果
    handleChecked: function () {
      this.dialogVisible = false;
      this.$emit('setUserShortCut', this.value);
    },
    // 回调获取用户自定义快捷菜单结果
    setValue: function (data) {
      this.value = data;
    },
    init: function (data) {
      this.value = data;
      this.data = data;
      this.original = this.value;
    }
  }
};
</script>
