import { Base64 } from 'js-base64';

/**
 * 加密明文
 * @param {string} decodedData 明文
 * @returns string 密文
 */
export function encodeSql(decodedData) {
  if(typeof decodedData !== "string" || decodedData.length === 0) {
    return decodedData;
  }
  let base64Str = Base64.encode(decodedData);
  let arr = Array.from(base64Str)
  const n = Math.ceil(Math.random() * 9)
  let s = n + ''
  arr.forEach(ch => {
    s += String.fromCharCode(ch.charCodeAt() - n)
  })
  return s
}

/**
 * 解密密文
 * @param {string} encodeData 密文
 * @returns string 明文
 */
export function decodeSql(encodeData) {
  if(typeof encodeData !== "string" || encodeData.length === 0) {
    return encodeData;
  }
  const n = parseInt(encodeData.substring(0, 1))
  const base64Str = encodeData.substring(1)
  let arr = Array.from(base64Str)
  let s = ''
  arr.forEach(ch => {
    s += String.fromCharCode(ch.charCodeAt() + n)
  })
  return Base64.decode(s)
}
