<template>
  <el-container class="page-container">
    <el-aside style="width: 268px" v-loading="treeLoading && isInitialLoad">
      <div class="toolbarTree-wrapper" style="height: 100%">
        <div class="searchPart">
          <el-input
            placeholder="输入关键字进行过滤"
            v-model="resNameLike"
            @change="handleFilter"
            size="small"
          >
            <i class="el-icon-search el-input__icon" slot="suffix"> </i>
          </el-input>
        </div>
        <div class="searchTotal" v-show="!treeLoading || !isInitialLoad">
          <span class="totalName"> 全部</span>
          <span class="totalNum">{{ powerTreeData.allNum }}个</span>
        </div>
        <div class="searchTotalWith withIn" v-show="!treeLoading || !isInitialLoad">
          <span class="totalName"
            ><img
              src="../../../assets/img/icon-withIn.svg"
              alt=""
              style="width: 23px; height: auto"
            />{{ departmentIn }}</span
          >
          <span class="totalNum"
            ><span>{{ powerTreeData.allNumIn }}</span
            >个</span
          >
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" v-show="!treeLoading || !isInitialLoad">
          <el-tree
            class="icsp-el-tree-2"
            ref="treeIn"
            :lazy="true"
            :load="loadNode"
            node-key="id"
            :highlight-current="true"
            accordion
            :data="powerTreeData.treeDataIn || []"
            :props="defaultProps"
            :indent="14"
            :expand-on-click-node="true"
            :check-on-click-node="true"
            @current-change="handleNodeClick1"
            :filter-node-method="filterNode"
            :default-expanded-keys="treeExpandData"
            icon-class="el-icon-caret-right"
          >
            <span
              class="custom-tree-node"
              :class="{ active: data.id === filters.baseType }"
              slot-scope="{ data }"
            >
              <span class="custom-tree-name" :title="data.name">
                {{ data.name }}
              </span>
              <span class="custom-tree-btn">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="'资源总数：' + data.resourceTotal"
                  placement="right"
                >
                  <el-link type="info" :underline="false">{{ data.resourceTotal }}</el-link>
                </el-tooltip>
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
        <div class="searchTotalWith withOut" v-show="!treeLoading || !isInitialLoad">
          <span class="totalName"
            ><img
              src="../../../assets/img/icon-withOut.svg"
              alt=""
              style="width: 23px; height: auto"
            />{{ departmentOut }}</span
          >
          <span class="totalNum"
            ><span>{{ powerTreeData.allNumOut }}</span
            >个</span
          >
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" v-show="!treeLoading || !isInitialLoad">
          <el-tree
            class="icsp-el-tree-2"
            ref="treeOut"
            :lazy="true"
            :load="loadNode"
            node-key="id"
            :highlight-current="true"
            accordion
            :data="powerTreeData.treeDataOut || []"
            :props="defaultProps"
            :indent="14"
            :expand-on-click-node="true"
            :check-on-click-node="true"
            @current-change="handleNodeClick2"
            :filter-node-method="filterNode"
            :default-expanded-keys="treeExpandData"
            icon-class="el-icon-caret-right"
          >
            <span
              class="custom-tree-node"
              :class="{ active: data.id === filters.baseType }"
              slot-scope="{ data }"
            >
              <span class="custom-tree-name" :title="data.name">
                {{ data.name }}
              </span>
              <span class="custom-tree-btn">
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="'资源总数：' + data.resourceTotal"
                  placement="right"
                >
                  <el-link type="info" :underline="false">{{ data.resourceTotal }}</el-link>
                </el-tooltip>
              </span>
            </span>
          </el-tree>
        </el-scrollbar>
      </div>
    </el-aside>
    <el-container>
      <el-main>
        <!--        <div style="width: 100%; margin: 8px 0;">-->
        <!--          <span style="color: #74767a">数据资源</span>-->
        <!--        </div>-->
        <div class="searchCont">
          <div class="searchList">
            <div class="searchInp" style="width: 100%">
              <el-row>
                <el-col :span="18">
                  <el-input
                    v-model.trim="filters.name"
                    clearable
                    placeholder="请输入资源关键字进行搜索"
                    @change="handleFilter"
                    maxlength="20"
                  >
                    <el-button slot="append" type="primary" @click="handleFilter">搜索</el-button>
                  </el-input>
                </el-col>
                <el-button style="margin-left: 28px; height: 42px" @click="handleReset"
                  >重置</el-button
                >
              </el-row>
            </div>
          </div>
          <div class="searchList">
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="资源类型">
                  <el-radio-group v-model="filters.resFormatType" @change="handleFilter">
                    <el-radio-button
                      v-for="item in resourceTypeList"
                      :key="item.key"
                      :label="item.key"
                      >{{ item.value }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="共享类型">
                  <el-radio-group v-model="filters.shareType" @change="handleFilter">
                    <el-radio-button
                      v-for="item in shareTypeList"
                      :key="item.key"
                      :label="item.key"
                      >{{ item.value }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <!-- <el-form-item label="">
                  <SelectPlus
                    collapseTags
                    multiple
                    dictType="ZYML_RESOURCE_TYPE"
                    mode="gxsl"
                    v-model="filters.resFormatType"
                    @input="handleFilter"
                    clearable
                    placeholder="资源类型"
                  />
                </el-form-item>
                <el-form-item label="">
                  <SelectPlus
                    collapseTags
                    multiple
                    dictType="SHARD_TYPE"
                    mode="gxsl"
                    v-model="filters.shareType"
                    @input="handleFilter"
                    clearable
                    placeholder="共享属性"
                  />
                </el-form-item> -->
              </el-form>
            </div>
          </div>

          <el-collapse class="collapse-filter" v-model="activeCollapse">
            <el-collapse-item name="1">
              <div class="searchList" v-show="isExpanded">
                <div class="searchInp mg-b-12">
                  <el-form :inline="true" :model="filters">
                    <el-form-item label="来源系统">
                      <el-input
                        v-model.trim="filters.systemName"
                        clearable
                        placeholder="请输入来源系统"
                        @change="handleFilter"
                        maxlength="20"
                      />
                    </el-form-item>
                    <el-form-item label="库表字段">
                      <el-input
                        clearable
                        v-model="filters.libraryFields"
                        @change="handleFilter"
                        maxlength="20"
                        placeholder="请输入库表字段或表名"
                      ></el-input>
                    </el-form-item>
                    <el-form-item label="更新周期">
                      <SelectPlus
                        collapseTags
                        multiple
                        dictType="ZYML_CATALOG_UPDATE_CYCLE"
                        mode="gxsl"
                        v-model="filters.updateCycle"
                        @input="handleFilter"
                        clearable
                        placeholder="请选择更新周期"
                      />
                    </el-form-item>
                    <el-form-item label="提供单位">
                      <el-cascader
                        placeholder="请选择提供单位"
                        clearable
                        :filterable="true"
                        :options="options"
                        v-model="orgIds"
                        :props="{ checkStrictly: true }"
                        @change="handleOrgId"
                        :value="orgIds"
                        style="width: 100%"
                      >
                        <template slot-scope="{ data }">
                          <div class="nowrap" :title="data.label">
                            {{ data.label }}
                          </div>
                        </template>
                      </el-cascader>
                    </el-form-item>
                    <el-form-item label="请选择发布时间">
                      <el-date-picker
                        value-format="yyyy-MM-dd"
                        v-model="dateRange1"
                        type="daterange"
                        @change="handleFilter"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        style="width: 280px"
                      />
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>

          <div class="expand-toggle">
            <div class="expand-toggle-content" @click="toggleExpand">
              <span>{{ isExpanded ? '收起' : '展开更多选项' }}</span>
              <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
          </div>
          <!-- <div class="searchList">
            <div class="searchInp" style="display:flex;">
              <el-form :inline="true" :model="filters">
                <el-form-item label="">
                  <el-date-picker value-format="yyyy-MM-dd" v-model="dateRange1" type="daterange" @change="handleFilter"
                    range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </div>
          </div> -->
          <!-- <div class="searchList">
            <h5>资源类型</h5>
            <div class="searchInp" style="display:flex;">
              <CheckPlus dictType="ZYML_RESOURCE_TYPE" mode="gxsl" v-model="checkListresFormatType" :filterType="'resFormatType'" :num="'1'" @change="handleChecked" ref="resFormatType"></CheckPlus>
            </div>
          </div>
          <div class="searchList">
            <h5>更新周期</h5>
            <div class="searchInp" style="display:flex;">
              <CheckPlus dictType="ZYML_CATALOG_UPDATE_CYCLE" v-model="checkListupdateCycle" mode="gxsl" :filterType="'updateCycle'" :num="'9'" @change="handleChecked"></CheckPlus>
            </div>
          </div> -->
        </div>
        <div class="dataCont" v-loading="listLoading">
          <div class="topSort">
            <el-checkbox
              v-model="filters.onlyShowNotOrder"
              @change="changeListStatus"
              :true-label="1"
              :false-label="0"
            >
              仅显示未订阅</el-checkbox
            >
            <div class="sort">
              <div v-for="(item, index) in sortItems" :key="item.cataId">
                <span class="iconLabel" :class="{ active: item.active }" @click="sort(item)">
                  {{ item.label }}
                </span>
                <span v-if="item.sort" class="iconList">
                  <i
                    class="el-icon-caret-top"
                    :class="{ active: item.order === 1 }"
                    @click="orderChange(item, 1)"
                  ></i>
                  <i
                    class="el-icon-caret-bottom"
                    :class="{ active: item.order === -1 }"
                    @click="orderChange(item, -1)"
                  ></i>
                </span>
              </div>
            </div>
          </div>
          <div class="borderSplit"></div>
          <div class="listCont">
            <div
              class="listItem"
              v-for="item in dataList || []"
              :key="item.id"
              @click="handleCheckDetail(item)"
            >
              <div class="line1">
                <div class="title">
                  <div class="ellis" :title="item.resourceName">
                    {{ item.resourceName }}
                  </div>
                  <span style="width: 23px; margin-left: 4px">
                    <img
                      v-if="item.rangeCode === '1'"
                      src="~@/assets/img/icon-withOut.svg"
                      alt=""
                    />
                    <img v-else src="~@/assets/img/icon-withIn.svg" alt="" /> </span
                  ><span class="info-list" v-if="item.orderResNum && item.orderResNum !== ''"
                    >已订阅</span
                  >
                </div>
                <div class="itemStatus">
                  <span class="info-tip" v-if="item.shareType && item.shareType !== ''">{{
                    item.shareType
                  }}</span>
                  <span class="info-tip" v-if="item.openType && item.openType !== ''">{{
                    item.openType
                  }}</span>
                </div>
              </div>
              <div class="line2">
                <div class="itemMes">
                  <span class="message"
                    >提供单位：<span>{{ item.providerDeptName }}</span></span
                  >
                  <span class="message"
                    >所属目录：<span>{{ item.directoryName }}</span></span
                  >
                  <span class="message" v-if="item.systemName"
                    >来源系统：<span>{{ item.systemName }}</span></span
                  >
                  <br />
                  <span class="message"
                    >发布时间：<span>{{ item.publishTime }}</span></span
                  >
                  <span class="message" v-if="item.updateCycle"
                    >更新周期：<span>{{ item.updateCycle }}</span></span
                  >
                  <span class="message" v-if="item.basicInfoUpdatetime"
                    >更新时间：<span>{{ item.basicInfoUpdatetime }}</span></span
                  >
                  <!-- <span class="message">能力主题：<span>{{ handlePowerSceneCode(item.powerScene) }}</span></span> -->
                  <template v-if="item.resourceTypeCode || item.resourceType">
                    <!-- 含有接口 -->
                    <span
                      v-if="item.resourceTypeCode === '3' && item.isExternalImport !== '9'"
                      class="p-info-item"
                      ><span class="label">累计调用量：</span>
                      <span class="value">{{ showTotal(item.callSum) || 0 }}次</span>
                    </span>
                    <!-- 含有库表 || 回流 -->
                    <span
                      v-if="
                        (item.resourceTypeCode === '1' || item.resourceTypeCode === '4') &&
                        item.isExternalImport !== '9'
                      "
                      class="p-info-item"
                      ><span class="label">结构化数据量：</span>
                      <span class="value">{{ showTotal(item.tdxTable) || 0 }}条</span>
                    </span>
                    <!-- 含有文件 || 文件夹 -->
                    <span
                      v-if="
                        (item.resourceTypeCode === '2' || item.resourceTypeCode === '5') &&
                        item.isExternalImport !== '9'
                      "
                      class="p-info-item"
                      ><span class="label">非结构化数据量：</span>
                      <span class="value">{{ (item.tdxFile / 1024 / 1024).toFixed(3) || 0 }}M</span>
                    </span>
                  </template>
                </div>
                <div class="right-info">
                  <!-- 非回流数据目录 -->
                  <template v-if="(item.catalogTypeCode || item.catalogType) !== '3'">
                    <div
                      class="type-info"
                      :class="{
                        active:
                          (item.resourceTypeCode && item.resourceTypeCode.indexOf('1') > -1) ||
                          (item.resourceType && item.resourceType.indexOf('1') > -1)
                      }"
                      v-if="
                        (item.resourceTypeCode && item.resourceTypeCode.indexOf('1') > -1) ||
                        (item.resourceType && item.resourceType.indexOf('1') > -1)
                      "
                    >
                      <svg-icon icon-class="gxsl-kubiao" />
                      <p>库表</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active:
                          (item.resourceTypeCode && item.resourceTypeCode.indexOf('2') > -1) ||
                          (item.resourceType && item.resourceType.indexOf('2') > -1)
                      }"
                      v-if="
                        (item.resourceTypeCode && item.resourceTypeCode.indexOf('2') > -1) ||
                        (item.resourceType && item.resourceType.indexOf('2') > -1)
                      "
                    >
                      <svg-icon icon-class="gxsl-wenjian" />
                      <p>文件</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active:
                          (item.resourceTypeCode && item.resourceTypeCode.indexOf('3') > -1) ||
                          (item.resourceType && item.resourceType.indexOf('3') > -1)
                      }"
                      v-if="
                        (item.resourceTypeCode && item.resourceTypeCode.indexOf('3') > -1) ||
                        (item.resourceType && item.resourceType.indexOf('3') > -1)
                      "
                    >
                      <svg-icon icon-class="gxsl-jiekou" />
                      <p>接口</p>
                    </div>
                    <div
                      class="type-info"
                      :class="{
                        active:
                          (item.resourceTypeCode && item.resourceTypeCode.indexOf('5') > -1) ||
                          (item.resourceType && item.resourceType.indexOf('5') > -1)
                      }"
                      v-if="
                        (item.resourceTypeCode && item.resourceTypeCode.indexOf('5') > -1) ||
                        (item.resourceType && item.resourceType.indexOf('5') > -1)
                      "
                    >
                      <svg-icon icon-class="gxsl-wenjianjia" />
                      <p>文件夹</p>
                    </div>
                  </template>
                  <!-- 回流数据目录 -->
                  <template v-else>
                    <div
                      class="type-info"
                      :class="{
                        active:
                          (item.resourceTypeCode && item.resourceTypeCode.indexOf('4') > -1) ||
                          (item.resourceType && item.resourceType.indexOf('4') > -1)
                      }"
                      v-if="
                        (item.resourceTypeCode && item.resourceTypeCode.indexOf('4') > -1) ||
                        (item.resourceType && item.resourceType.indexOf('4') > -1)
                      "
                    >
                      <svg-icon icon-class="gxsl-huiliu" />
                      <p>回流</p>
                    </div>
                  </template>
                </div>
              </div>
              <div class="line3">
                <div class="itemNum">
                  <div style="display: flex; align-items: center">
                    <el-rate
                      :max="5"
                      class="icsp-portal-rate"
                      disabled-void-icon-class="iconfont icon-huomiao"
                      void-icon-class="iconfont icon-huomiao"
                      disabled-void-color="#B3B3B3"
                      :icon-classes="[
                        'iconfont icon-huomiao',
                        'iconfont icon-huomiao',
                        'iconfont icon-huomiao'
                      ]"
                      :colors="['#FF5E5E', '#E15555', '#E15555']"
                      v-model="item.score"
                      disabled
                    ></el-rate>
                    <span class="num"
                      >{{ !!item.orderSum ? item.orderSum : 0 }}次<span>订阅</span></span
                    >
                    <span class="num"
                      >{{ !!item.browseSum ? item.browseSum : 0 }}次<span>访问</span></span
                    >
                    <span class="num"
                      >{{ !!item.collectSum ? item.collectSum : 0 }}次<span>收藏</span></span
                    >
                  </div>
                </div>
                <div class="itemHandleBtn">
                  <el-button
                    v-permission="'sjnl003'"
                    class="icsp-button-grey"
                    disabled
                    v-if="
                      item.providerCode === (currentUser ? currentUser.unitCode : null) ||
                      (item.orderResNum && item.orderResNum !== '')
                    "
                  >
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>
                  <el-button
                    v-permission="'sjnl003'"
                    class="icsp-button-grey"
                    v-else-if="!item.resourceType"
                    :disabled="!item.resourceType"
                  >
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>
                  <el-button
                    v-permission="'sjnl003'"
                    v-else
                    class="icsp-button"
                    plain
                    @click.stop="handleResourceApply(item)"
                  >
                    <svg-icon icon-class="gxsl-apply-btn" />
                    订阅
                  </el-button>

                  <el-button
                    v-permission="'sjnl003'"
                    class="icsp-button-grey"
                    disabled
                    v-if="item.providerCode === (currentUser ? currentUser.unitCode : null)"
                  >
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    加入选数车
                  </el-button>
                  <el-button
                    v-permission="'sjnl003'"
                    class="icsp-button-grey"
                    :disabled="!item.resourceType"
                    v-else-if="!item.resourceType"
                  >
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    {{ item.addSelect === '1' ? '取消加入选数车' : '加入选数车' }}
                  </el-button>
                  <el-button
                    v-permission="'sjnl003'"
                    v-else
                    :disabled="queryBt"
                    :class="item.inCart ? 'icsp-button2' : 'icsp-button'"
                    plain
                    @click.stop="handleAddPowerToCart(item)"
                  >
                    <svg-icon icon-class="gxsl-shoppingCart" />
                    {{ item.inCart ? '取消加入选数车' : '加入选数车' }}
                  </el-button>

                  <el-button
                    class="icsp-button-grey"
                    disabled
                    v-if="item.providerCode === (currentUser ? currentUser.unitCode : null)"
                  >
                    <svg-icon icon-class="gxsl-collect" />
                    收藏
                  </el-button>
                  <el-button
                    v-else
                    :disabled="queryBt"
                    :class="item.collect ? 'icsp-button2' : 'icsp-button'"
                    plain
                    @click.stop="handleCollect(item)"
                  >
                    <svg-icon icon-class="gxsl-collect" />
                    {{ item.collect ? '取消收藏' : '收藏' }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-if="!listLoading && dataList && dataList.length === 0"
            description="暂无数据"
          ></el-empty>
          <div class="icsp-pagination-wrap">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="filters.currentPage"
              :page-size="filters.pageSize"
              layout="total, prev, pager, next, sizes, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>

        <!-- 数据订阅 -->
        <SubscribeDialog
          :dialogVisible.sync="dialogVisibleApply"
          :type="type"
          :title="title"
          :data="dialogData"
        >
        </SubscribeDialog>
        <ToolBar @onRefreshList="getList" />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import ToolBar from '@/biz/components/common/toolbar'
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index.vue'
import CheckPlus from '@/biz/components/common/checkPlus'
import SubscribeDialog from '../../components/SubscribeDialog'
import Cookies from 'js-cookie'
export default {
  name: 'Resource',
  components: {
    ToolBar,
    SelectPlus,
    CheckPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog
  },
  data() {
    return {
      queryBt: false,
      checkAll: false,
      isIndeterminate: false,
      showList: true,
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      queryParamsList: [],
      checkList: [
        {
          checkName: '一般',
          checkValue: 11
        },
        {
          checkName: '重要',
          checkValue: 11
        }
      ],
      checkOption: ['一般', '重要'],
      sortItems: [
        {
          label: '订阅量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 1
        },
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 2
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 8
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false,
          sortMode: ''
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      powerTreeData: {
        allNum: 0,
        allNumIn: 0,
        allNumOut: 0,
        treeDataIn: null, // 初始化为null，避免显示暂无数据
        treeDataOut: null // 初始化为null，避免显示暂无数据
      },
      dataList: null, // 初始化为null，避免显示暂无数据
      treeDataIn: [],
      treeDataOut: [],
      defaultProps: {
        children: 'childList',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      listLoading: true,
      treeLoading: true, // 树加载状态
      isInitialLoad: true, // 标识是否为初始加载
      total: 0,
      currentRow: undefined,
      options: [], // 所属部门
      orgIds: [],
      score: undefined,
      checkListresFormatType: [],
      checkListupdateCycle: [],
      unitData: {},
      resNameLike: '',
      showExistNode: '',
      treeExpandData: [],
      dateRange1: [],
      departmentIn: config.departmentIn,
      departmentOut: config.departmentOut,
      filters: {
        name: '',
        systemName: '',
        updateCycle: undefined,
        score: undefined,
        orgId: '',
        currentPage: 1,
        onlyShowNotOrder: 0,
        baseType: '',
        baseTypeInfo: '',
        pageSize: 10,
        sortMode: '',
        sortType: 'desc',
        shareType: '',
        resFormatType: '',
        libraryFields: undefined
      },
      isExpanded: false,
      activeCollapse: [],
      resourceTypeList: [],
      shareTypeList: []
    }
  },
  methods: {
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    // 重置 资源热度
    resetScore() {
      this.score = undefined
      this.filters.score = undefined
      this.handleFilter()
    },
    showTotal(value) {
      if (value >= 100000000) {
        return (value / 100000000).toFixed(2) + '亿'
      } else if (value >= 10000) {
        return (value / 10000).toFixed(2) + '万'
      } else {
        return value
      }
    },
    // 改变 资源热度
    changeScore(val) {
      this.filters.score = val * 10
      this.handleFilter()
    },
    handleChecked(val, type) {
      this.filters[type] = val
      this.handleFilter()
    },
    // 改变所属部门
    handleOrgId(val) {
      // console.log(val, 'val')
      if (val.length === 1) {
        this.filters.orgId = val[0]
      } else {
        this.filters.orgId = val[val.length - 1]
      }
      this.getList()
    },
    circle(data) {
      data.forEach((i) => {
        i.value = i.id
        i.label = i.name
        if (i.children && i.children.length > 0) {
          this.circle(i.children)
        }
      })
    },
    // 获取所属部门
    getOrgId() {
      this.$api.bizApi.common
        .getUnitsByUnitCode()
        .then((res) => {
          if (res.code === '200') {
            let a = res.data || []
            this.circle(a)
            this.options = a
            // 如果route中含有提供单位，需要反显选中
            if (this.filters.orgId) {
              let ary = this.getParentsById(this.options, this.filters.orgId)
              // console.log('ary' + ary)
              this.orgIds = ary
            }
          }
        })
        .catch((e) => {
          this.$message({
            message: e.message
          })
        })
    },
    // el-cascader数据提供部门多级回显
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          // console.log('node' + node)
          if (node !== undefined) {
            // 如能查询到把父节点加到数组前面
            node.unshift(list[i].id)
            return node
          }
        }
      }
    },
    async getPowerTreeData() {
      // 如果是初始加载，显示树的加载状态；如果是筛选，只显示列表加载状态
      if (this.isInitialLoad) {
        this.treeLoading = true
      }
      this.listLoading = true

      // if (!sessionStorage.getItem('user')) return
      let unitCode = JSON.parse(sessionStorage.getItem('user'))?.unitCode
      this.filters.baseType = ''
      this.filters.baseTypeInfo = ''

      try {
        const res = await this.$api.bizApi.pageRequest.getResourceTree({
          resNameLike: this.resNameLike,
          ...this.filters,
          unitCode,
          publishTimeStart: this.dateRange1?.length ? this.dateRange1[0] + ' 00:00:00' : undefined,
          publishTimeEnd: this.dateRange1?.length ? this.dateRange1[1] + ' 23:59:59' : undefined
        })

        if (res.data) {
          this.powerTreeData.allNum = res.data.resourceTotal
          for (const item of res.data.childList) {
            if (item.name === config.innerItemName) {
              this.powerTreeData.treeDataIn = item.childList
              this.powerTreeData.allNumIn = item.resourceTotal
            }
            if (item.name === config.outItemName) {
              this.powerTreeData.treeDataOut = item.childList
              this.powerTreeData.allNumOut = item.resourceTotal
            }
          }
        } else {
          this.powerTreeData = {
            allNum: 0,
            allNumIn: 0,
            allNumOut: 0,
            treeDataIn: [],
            treeDataOut: []
          }
        }

        // 树数据加载完成
        this.treeLoading = false
        this.isInitialLoad = false // 标记初始加载完成

        // 树数据加载完成后，立即开始加载列表数据（并行处理）
        await this.getList()
      } catch (error) {
        console.error('获取树数据失败:', error)
        // 确保在错误情况下也设置树数据为空数组
        this.powerTreeData = {
          allNum: 0,
          allNumIn: 0,
          allNumOut: 0,
          treeDataIn: [],
          treeDataOut: []
        }
        // 树数据加载完成（错误情况）
        this.treeLoading = false
        this.isInitialLoad = false // 标记初始加载完成
        // 确保在错误情况下也调用getList，这样listLoading会被正确设置为false
        await this.getList()
      }
    },
    handleCheckAllChange(val) {
      this.filters.checkList = val ? this.checkOption : []
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      let checkedCount = val.length
      this.checkAll = checkedCount === this.checkOption.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkOption.length
    },
    // 加载子树数据的方法，lazy为true时生效
    async loadNode(node, resolve) {
      let data = node.data
      let resData = []
      if (data.id) {
        await this.$api.bizApi.pageRequest
          .getBaseTypeTreeWithResCnt({
            showExistNode: this.showExistNode,
            type: data.type,
            baseTypeId: data.id,
            rangeFlag: data.rangeFlag,
            ...this.filters
          })
          .then((res) => {
            data.childList = res.data || []
            resData = res.data || []
          })
      }
      return resolve(resData)
    },
    // 当前选中节点变化时触发的事件
    handleNodeClick1(node) {
      // this.$refs.treeOut.setCurrentKey(null)
      // if (this.filters.baseType === node.id) {
      //   this.filters.baseType = undefined
      // } else {
      this.filters.baseType = node.id
      this.filters.baseTypeInfo = node.type ? node.type : this.type
      // }
      this.getList(node.rangeFlag)
    },
    handleNodeClick2(node) {
      // this.$refs.treeIn.setCurrentKey(null)
      // if (this.filters.baseType === node.id) {
      // this.filters.baseType = undefined
      // } else {
      this.filters.baseType = node.id
      this.filters.baseTypeInfo = node.type ? node.type : this.type
      // }
      this.getList(node.rangeFlag)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    changeListStatus(val) {
      this.handleFilter()
    },
    handleReset() {
      this.filters.name = ''
      this.filters.resFormatType = ''
      this.filters.shareType = ''
      this.filters.systemName = ''
      this.filters.libraryFields = ''
      this.filters.updateCycle = ''
      this.filters.orgId = ''
      this.orgIds = []
      this.dateRange1 = []
      this.$nextTick(() => {
        this.getPowerTreeData()
      })
    },
    handleFilter() {
      this.filters.currentPage = 1
      this.$nextTick(() => {
        this.getPowerTreeData()
      })
    },
    async getList(rangeFlag) {
      // 如果不是从getPowerTreeData调用，则设置loading状态
      if (!this.listLoading) {
        this.listLoading = true
      }

      const params = {
        ...this.filters,
        rangeFlag: rangeFlag,
        publishTimeStart: this.dateRange1?.length ? this.dateRange1[0] + ' 00:00:00' : undefined,
        publishTimeEnd: this.dateRange1?.length ? this.dateRange1[1] + ' 23:59:59' : undefined
      }

      try {
        const res = await this.$api.bizApi.pageRequest.getResourcePage(params)
        this.dataList = res.data ? res.data.records : []
        this.dataList.forEach((item) => {
          item.score /= 10
        })
        this.total = res.data ? res.data.total : 0
      } catch (error) {
        console.error('获取列表数据失败:', error)
        this.dataList = [] // 确保错误时也设置为空数组
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    // 查看详情
    handleCheckDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detailResource/index',
          query: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataRes/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            cataId: data.cataId,
            type: '1',
            resourceId: data.resourceId,
            resType: data.resourceTypeCode
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    // 能力收藏
    handleCollect(data) {
      if (this.isLogOn()) {
        if (!data.collect) {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addResourceToFavorites({
              resourceId: data.resourceId
            })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('收藏成功')
                this.$set(data, 'collect', true)
                this.$set(data, 'collectSum', data.collectSum + 1)
                // this.handleFilter()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否取消收藏该数据资源?', '取消收藏', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .cancelResourceToFavorites({ resourceId: data.resourceId })
              .then((res) => {
                if (res.code === '200') {
                  // this.handleFilter()
                  this.$message({
                    type: 'success',
                    message: '取消收藏成功!'
                  })
                  this.$set(data, 'collect', false)
                  this.$set(data, 'collectSum', data.collectSum - 1)
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 资源订阅
    handleResourceApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = {}
        this.dialogVisibleApply = true
        this.dialogData.resourceId = data.resourceId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart(data) {
      if (this.isLogOn()) {
        if (!data.inCart) {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addResourceToCart({
              resourceIds: data.resourceId
            })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('加入选数车成功')
                this.$set(data, 'inCart', true)
                // this.handleFilter()
                window.refreshCountNum()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否从选数车中取消该数据资源?', '取消确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .delDataFromCart({ resourceIds: data.resourceId })
              .then((res) => {
                // this.handleFilter()
                this.$message.success('取消成功!')
                this.$set(data, 'inCart', false)
                window.refreshCountNum()
              })
              .catch((e) => {
                this.$errMsgBox.show({
                  text: '取消失败',
                  error: e
                })
              })
          })
        }
      }
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded
      if (this.isExpanded) {
        this.activeCollapse = ['1']
      } else {
        this.activeCollapse = []
      }
    }
  },
  watch: {
    filterText(val) {
      // 只有在树加载完成后才执行过滤
      if (!this.treeLoading) {
        this.$refs.treeIn && this.$refs.treeIn.filter(val)
        this.$refs.treeOut && this.$refs.treeOut.filter(val)
      }
    }
  },
  created() {
    if (this.$route.query) {
      this.filters.sortType = this.$route.query.sortType
      this.filters.sortMode = this.$route.query.sortMode
    }
    this.getOrgId()
    this.getPowerTreeData()

    this.$api.dict.getDictListByTypeWithGxsl({ type: 'ZYML_RESOURCE_TYPE' }).then((res) => {
      this.resourceTypeList = res.data.filter((item) => item.key !== '4')
      this.resourceTypeList.unshift({
        key: '',
        value: '全部'
      })
    })

    this.$api.dict.getDictListByTypeWithGxsl({ type: 'SHARD_TYPE' }).then((res) => {
      this.shareTypeList = res.data
      this.shareTypeList.unshift({
        key: '',
        value: '全部'
      })
    })
  }
}
</script>

<style scoped lang="scss">
/deep/ .el-form-item {
  margin-right: 24px !important;
}
@import '../../../assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  color: #3572ff;
  font-size: 14px;
  &-content {
    cursor: pointer;
  }
}

.expand-toggle i {
  margin-left: 5px;
}

.el-collapse {
  border: none;
}

.el-collapse-item >>> .el-collapse-item__header {
  display: none;
}

.el-collapse-item >>> .el-collapse-item__wrap {
  border-bottom: none;
}

// 评分/热度
.icsp-portal-rate {
  height: auto;
  line-height: inherit;

  .icon-huomiao::before {
    font-size: 16px;
    margin-right: 0;
    vertical-align: -0.4em;
  }
}

.info-list {
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-color: #ff8635;
  background-color: #3572ff;
}

.collapse-filter {
  /deep/ .el-collapse-item__content {
    padding-bottom: 0;
  }
}
</style>
