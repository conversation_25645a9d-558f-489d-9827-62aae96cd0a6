export default {
  state: {
    // 主入口标签页
    mainTabs: [],
    // 当前标签页名
    mainTabsActiveName: '',
    // 默认打开的tab页——首页
    default: [{
      path: 'intro',
      name: '首页'
    }]
  },
  mutations: {
    updateMainTabs (state, tabs) {
      let ts = tabs.filter(t => (t.name && t.name !== "登录" && t.name !== "404"))
      state.mainTabs = ts
    },
    updateMainTabsActiveName (state, name) {
      state.mainTabsActiveName = name
    }
  }
}
