<template>
  <div class="ability-tab">
    <el-carousel class="mainConte" :autoplay="false" indicator-position="none">
      <el-carousel-item>
        <div class="workItem">
          <img :src="imgList[0].imgSrc" alt="" />
          <div class="content">
            <div class="name">能力总数</div>
            <div class="count">
              <span class="num">{{ abilityObj.powerTotal ? addUnitToNumber(abilityObj.powerTotal) : 0 }}</span>个
            </div>
          </div>
        </div>
        <div class="workItem">
          <img :src="imgList[3].imgSrc" alt="" />
          <div class="content">
            <div class="name">能力申请总数</div>
            <div class="count">
              <div style="display: flex; align-items: center">
                <span class="num">{{ abilityObj.powerApplyTotal ? addUnitToNumber(abilityObj.powerApplyTotal) : 0 }}</span>条
              </div>
            </div>
            <div class="new">
              今日新增+{{ abilityObj.todayAddPowerApplyTotal ? addUnitToNumber(abilityObj.todayAddPowerApplyTotal) : 0 }}
            </div>
          </div>
        </div>
        <div class="workItem">
          <img :src="imgList[4].imgSrc" alt="" />
          <div class="content">
            <div class="name">能力使用总数</div>
            <div class="count">
              <div style="display: flex; align-items: center">
                <span class="num">{{ abilityObj.apiUseCnt ? addUnitToNumber(abilityObj.apiUseCnt) : 0 }}</span>次
              </div>
            </div>
            <div class="new">
              今日新增+{{ abilityObj.todayAddPowerUseTotal ? addUnitToNumber(abilityObj.todayAddPowerUseTotal) : 0 }}
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
export default {
  name: 'ability-tab',
  components: {},
  props: {},
  data() {
    return {
      imgList: [
        {
          imgSrc: require('../../../../assets/img/nav-1.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-2.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-3.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-4.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-5.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-6.png')
        }
      ],
      abilityObj: {}
    }
  },
  methods: {
    addUnitToNumber(num) {
      if (num && num !== 0) {
        const numStr = num.toString()
        const length = numStr.length
        const units = ['', '万', '亿', '万亿', '兆']
        const thresholds = [0, 4, 8, 12, 16]

        // 找到适合的单位
        let unitIndex = 0
        while (unitIndex < thresholds.length - 1 && length > thresholds[unitIndex + 1]) {
          unitIndex++
        }

        let formattedNum = parseFloat(numStr)
        if (unitIndex > 0) {
          formattedNum = (num / Math.pow(10000, unitIndex)).toFixed(1)
        }
        return `${formattedNum}${units[unitIndex]}`
      } else {
        return 0
      }
    },
    getAbilityData() {
      this.$api.bizApi.pageRequest.getPowerCnt({}).then((res) => {
        this.abilityObj = res.data
      })
    }
  },
  mounted() {
    this.getAbilityData()
  }
}
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.ability-tab {
  width: 100%;
  background: #fff;
  padding: 0 20px;

  .mainConte {
    height: 132px;
    border-radius: 2px;
    display: flex;
    align-items: center;

    .workItem {
      width: calc((100% - 60px) / 6);
      min-width: calc((100% - 60px) / 6);
      max-width: calc((100% - 60px) / 6);
      height: 103px;
      background: linear-gradient(180deg, #f2f2f2 0%, #fafdff 100%);
      box-shadow: 0px 5px 9px 0px rgba(206, 206, 206, 0.43);
      border-radius: 4px;
      border: 1px solid rgba(206, 206, 206, 0.43);
      display: flex;
      align-items: center;
      padding: 18px 24px;
      margin-right: 12px;

      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        margin-left: 15px;

        .name {
          font-size: 18px;
          color: rgba(51, 51, 51, 1);
          font-weight: 600;
        }

        .count {
          color: rgba(102, 102, 102, 1);
          font-size: 16px;

          .num {
            color: rgba(39, 117, 255, 1);
            font-size: 24px;
            font-weight: bold;
          }
        }

        .new {
          height: 22px;
          min-width: 96px;
          font-size: 14px;
          color: rgba(32, 132, 246, 1);
          padding: 0 9px;
          margin-left: 7px;
          background: #cbdfff;
          border-radius: 11px;
        }
      }
    }

    &.flexLeft {
      justify-content: space-around;
    }
  }
}

>>> .el-carousel__container {
  width: 100%;
  height: 132px;

  .el-carousel__arrow {
    display: none;
  }

  .el-carousel__item {
    display: flex;
    align-items: center;
  }
}

>>> .el-carousel__indicator--horizontal {
  padding: 6px;
  background-color: transparent;
  border-radius: 50%;

  .el-carousel__button {
    width: 11px;
    height: 11px;
    background-color: #666666;
    border-radius: 50%;
  }

  &.is-active {
    .el-carousel__button {
      background-color: $colors;
    }
  }
}
</style>
