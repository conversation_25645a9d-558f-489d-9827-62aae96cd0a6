<template>
  <el-dialog v-dialogDrag title="任务日志" :visible.sync="dialogMainVisible" width="700px" @open="initList" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <table-plus id="CleanLogList" :data="list" ref="multipleTable" border fit stripe highlight-current-row v-loading="listLoading">
          <el-table-column prop="startTime" label="清理时间" width="180"></el-table-column>
          <el-table-column prop="runStatus" label="清理状态" align="center" width="100"></el-table-column>
          <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center">
            <template slot-scope="scope">
              <!-- <perm-button-group :config="getButtons(scope.row)"></perm-button-group> -->
              <el-popover
                placement="bottom"
                width="300"
                trigger="click"
                popper-class="cleanLogInfoContent"
              >
                <dl>
                  <dt>明细内容：</dt>
                  <dd v-for="item in infoList" :key="item.step">
                    <dl v-if="typeof item.count === 'number'">
                      <dt>步骤{{item.step}}：</dt>
                      <dd :title="'处理SQL '+ item.sql">处理SQL {{item.sql}}；</dd>
                      <dd>参数 {{item.param}} ；</dd>
                      <dd>处理数据 {{item.count}} 条；</dd>
                      <dd v-if="item.exp" :title="item.exp">{{item.exp}}</dd>
                    </dl>
                    <dl v-else>
                      <dt>步骤{{item.step}}：</dt>
                      <dd :title="'处理SQL '+ item.sql">处理SQL {{item.sql}}；</dd>
                      <dd>参数 {{item.param}} ；</dd>
                      <dd v-if="item.exp" :title="item.exp">{{item.exp}}</dd>
                    </dl>
                  </dd>
                </dl>
                <el-link slot="reference" type="primary" @click="handleView(scope.row)">处理明细</el-link>
              </el-popover>
            </template>
          </el-table-column>
        </table-plus>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import Tip from "@/core/components/Tip";
  import TablePlus from "@/core/components/TablePlus";
  import PermButtonGroup from "@/core/components/PermButtonGroup";
  import { resourceCode } from "@/biz/http/settings"
  export default {
    name: "CleanLogDialog",
    components: {Tip, TablePlus, PermButtonGroup},
    data() {
      return {
        listLoading: true,
        list: [],
        dialogMainVisible: false,
        infoList: []
      }
    },
    props: {
      dialogLogVisible: Boolean,
      codeRuleArray: Array
    },
    methods: {
      getButtons(row) {
        let buts = [
          {label: "处理明细", icon: "uims-icon-view", clickFn: this.handleView}
        ];
        return {
          row: row,
          buttons: buts,
          showNums: 2
        }
      },
      initList() {
        this.$api.cleanData.getCleanLogList({ cleanId: this.codeRuleArray[0].cleanId }, resourceCode.codeRule).then((res) => {
          this.listLoading = false
          this.list = res.data
        }).catch(e => {
          this.$errMsgBox.show({
            text: "获取数据出错",
            error: e
          })
        })
      },
      destrory() {
        this.listLoading = true;
        this.infoList = [];
        this.list = []
      },
      handleView(row) {
        this.$api.cleanData.getCleanLogInfo({ cleanLogId: row.cleanLogId }, resourceCode.codeRule).then((res) => {
          this.infoList = res.data && res.data.logDetails ? JSON.parse(res.data.logDetails) : []
        }).catch(e => {
          this.$errMsgBox.show({
            text: "获取明细出错",
            error: e
          })
        })
      }
    },
    watch: {
      dialogLogVisible: function (newValue, oldValue) {
        this.dialogMainVisible = newValue
      },
      dialogMainVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
  .cleanLogInfoContent {
    dl {
      margin: 0;
      dd {
        margin-left: 0;
        dd {
          margin-left: 28px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
</style>
