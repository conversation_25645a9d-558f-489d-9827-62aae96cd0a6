import axios from "@/core/http/axios";

export function subscribeList(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/list`,
    method: 'get',
    params
  });
}

export function cancelSubscribe(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/cancelSubscribe`,
    method: "post",
    params
  });
}

export function getApprovalRecord(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/approvalRecord`,
    method: "get",
    params
  });
}

export const getAccessInfo = params => {
  return axios({
    url: `/${config.appCode}/mySubscribe/getAccessInfo`,
    method: "get",
    params
  });
};

// 公用上传文件接口
export const uploadFile = (formData, resourceCode) => {
  return axios({
    url: `/${config.appCode}/annex/gxsl/upload`,
    method: "post",
    data: formData,
    transformRequest: function(data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 下载订阅附件
export const downloadFile = (params) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/attachment/downloadFile?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/attachment/downloadFile`,
    method: "get",
    params,
    responseType: "blob",
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": true
    }
  });
}

export function getServiceUsagePercent(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/serviceUsagePercent`,
    method: "get",
    params
  });
}

export function getServiceUsageCnt(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/serviceUsageCnt`,
    method: "get",
    params
  });
}

export function getServiceCallLog(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/getServiceCallLog`,
    method: "get",
    params
  })
}

export function getEncByPowerId(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/getEncByPowerId`,
    method: "get",
    params
  });
}

export function getPowerByIdToMySubscribe(params) {
  return axios({
    url: `/${config.appCode}/mySubscribe/getPowerById`,
    method: "get",
    params
  });
}
