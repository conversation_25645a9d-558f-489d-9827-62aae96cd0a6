<template>
  <div class="loginPage-container">
    <div class="form-wrapper">
      <div v-if="warnMessage" class="warn-box">
        <div class="warn-icon">
          <span>警告</span>
        </div>
        <div class="warn-line">
        </div>
        <div class="warn-message">
          <span>警告：本系统所有数据均属于内部敏感数据，严禁非法查询外传泄露，一经查实必追究责任，造成违法行为后果自负！</span>
        </div>
      </div>
      <div :class="isShowFieldset ? 'logo' : ['logo', 'noFieldset']">
        <img :src="logoSrc"/>
        <p>{{platTitle}}</p>
      </div>
      <div class="form-container">
        <el-form size="small" v-if="loginMode.account" v-show="loginType === '1' && password_form" :model="accountLoginForm" :rules="accountRules" ref="accountLoginForm"
                 label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>账号登录</span><hr class="hr_account"/></h2>
          <el-form-item prop="username">
          <span class="svg-container">
            <svg-icon icon-class="user"/>
          </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="accountLoginForm.username" autocomplete="off" placeholder="账号"></el-input>
          </el-form-item>
          <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password"/>
          </span>
            <div class="vLine"></div>
            <pwd-input ref="pwdInput" placeholder="密码" :value="accountLoginForm.passwordValue" v-model="accountLoginForm.password"></pwd-input>
<!--            <el-input type="password" autocomplete="off" v-model="accountLoginForm.password" placeholder="密码" ref="pwd"></el-input>-->
          </el-form-item>
          <el-form-item prop="captcha" class="captchaInput">
          <span class="svg-container">
            <svg-icon icon-class="captcha"/>
          </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="accountLoginForm.captcha" placeholder="验证码，单击图片刷新"
                      @keyup.enter.native="accountLogin" maxlength="4"></el-input>
            <div style="float:right">
              <img class="captchaImg" :src="accountLoginForm.src" @click="refreshCaptcha">
            </div>
          </el-form-item>
          <el-button class="accountLoginBtn" size="small" type="primary" @click.native.prevent="accountLogin" :loading="loading">登录</el-button>
        </el-form>
        <el-form size="small" v-if="loginMode.account" v-show="loginType === '1' && !password_form" :model="accountChangePwdForm" :rules="accountRules" ref="accountChangePwdForm"
                 label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>修改密码</span><hr class="hr_account"/></h2>
          <el-form-item prop="newPassword">
           <span class="svg-container">
              <svg-icon icon-class="password"/>
            </span>
            <div class="vLine"></div>
            <el-input type="password" v-model="accountChangePwdForm.newPassword" placeholder="新密码"></el-input>
          </el-form-item>
          <el-form-item prop="reNewPassword">
            <span class="svg-container">
              <svg-icon icon-class="password"/>
            </span>
            <div class="vLine"></div>
            <el-input type="password" v-model="accountChangePwdForm.reNewPassword" placeholder="确认密码"></el-input>
          </el-form-item>
          <el-button class="accountLoginChangePwdBtn" size="small" type="primary" @click.native.prevent="accountChangePwd" v-show="!password_form && change_password" :loading="loading">修改密码</el-button>
          <el-button class="accountLoginReLoginBtn" size="small" type="primary" @click.native.prevent="gotoReLogin" v-show="!password_form && !change_password" :loading="loading">返回登录</el-button>
          <em class="resetPwdReason">{{ resetPwdReason }}</em>
        </el-form>
        <el-form size="small" v-if="loginMode.mobile" v-show="loginType === '2'" :model="mobileLoginForm" :rules="smsRules" ref="mobileLoginForm"
                label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>手机号登录</span><hr class="hr_mobile"/></h2>
          <el-form-item prop="mobile" class="mobileLoginItem">
            <span class="svg-container">
              <svg-icon icon-class="mobile"/>
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="mobileLoginForm.mobile" placeholder="请输入手机号" maxlength="11"></el-input>
          </el-form-item>
          <el-form-item prop="smsCaptcha" class="mobileLoginItem">
            <span class="svg-container" style="margin-left:-94px;">
              <svg-icon icon-class="captcha"/>
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="mobileLoginForm.smsCaptcha" placeholder="请输入验证码"
                      @keyup.enter.native="smsLogin" style="width: 56%;" maxlength="4"></el-input>
            <div style="float:right" v-show="!mobile_counter.show">
              <el-button size="mini" type="primary" class="captchaBtn" @click.native.prevent="sendCaptcha">发送验证码</el-button>
            </div>
            <span class="countDown" v-show="mobile_counter.show">倒计时还有<b>{{mobile_counter.counter}}</b>秒</span>
          </el-form-item>
          <el-button class="mobileLoginBtn" size="small" type="primary" @click.native.prevent="smsLogin" :loading="loading">
            登录
          </el-button>
        </el-form>
        <el-form size="small" v-if="loginMode.email" v-show="loginType === '3'" :model="emailLoginForm" :rules="emailRules" ref="emailLoginForm"
                label-position="right" label-width="0px" class="login-container">
          <h2 class="title"><span>邮箱登录</span><hr class="hr_email"/></h2>
          <el-form-item prop="email" class="emailLoginItem">
            <span class="svg-container">
              <svg-icon icon-class="email"/>
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="emailLoginForm.email" placeholder="请输入邮箱地址"></el-input>
          </el-form-item>
          <el-form-item prop="emailCaptcha" class="emailLoginItem">
            <span class="svg-container" style="margin-left:-94px;">
              <svg-icon icon-class="captcha"/>
            </span>
            <div class="vLine"></div>
            <el-input type="text" v-model="emailLoginForm.emailCaptcha" placeholder="请输入验证码"
                      @keyup.enter.native="emailLogin" style="width: 56%;" maxlength="4"></el-input>
            <div style="float:right" v-show="!email_counter.show">
              <el-button size="mini" type="primary" class="captchaBtn" @click.native.prevent="sendEmailCaptcha">发送验证码</el-button>
            </div>
            <span class="countDown" v-show="email_counter.show">倒计时还有<b>{{email_counter.counter}}</b>秒</span>
          </el-form-item>
          <el-button class="emailLoginBtn" size="small" type="primary" @click.native.prevent="emailLogin" :loading="loading">
            登录
          </el-button>
        </el-form>
        <fieldset v-show="isShowFieldset">
          <legend>其他登录方式</legend>
          <ul>
            <li @click="loginType='1'" v-if="loginMode.account">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-account" :class="loginType==='1'?'cur':''"/>
                </span>
              </a>
            </li>
            <li @click="loginType='2'" v-if="loginMode.mobile">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-mobile" :class="loginType==='2'?'cur':''"/>
                </span>
              </a>
            </li>
            <li @click="loginType='3'" v-if="loginMode.email">
              <a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-email" :class="loginType==='3'?'cur':''"/>
                </span>
              </a>
            </li>
            <li class="dis" v-if="loginMode.qq"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-qq"/>
                </span>
            </a>
            </li>
            <li class="dis" v-if="loginMode.wx"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-wx"/>
                </span>
            </a>
            </li>
            <li class="dis" v-if="loginMode.sina"><a href="#">
                <span class="svg-container">
                  <svg-icon icon-class="login-sina"/>
                </span>
            </a>
            </li>
          </ul>
        </fieldset>
      </div>
      <div class="copyright">{{this.copyright}}</div>
    </div>
    <div class="ani">
      <canvas id="myCanvas" width="1920px" height="1080px"></canvas>
    </div>
    <div id="space"></div>
  </div>
</template>

<script>
  import SvgIcon from '@/core/components/SvgIcon/index.vue'
  import PwdInput from '@/core/components/PwdInput/index.vue'
  import {validMobile, validEmail} from '@/core/utils/validate'
  import { getSpaceId, loginInit, logout } from "@/core/utils/logout"
  import { getRsaPub } from '@/core/utils/token'
  // eslint-disable-next-line
  import Cookies from 'js-cookie'
  import {rsa} from "../../../utils/utils"
  import { Message } from 'element-ui'
  import { PopLoginBox } from '@/core/components/PopLoginBox'

  export default {
    name: 'Login',
    components: {
      SvgIcon,
      PwdInput
    },
    data() {
      var checkPassword = (rule, value, callback) => {
        value = this.accountChangePwdForm.newPassword
        if (this.accountChangePwdForm.newPassword === this.accountLoginForm.username) {
          callback(new Error('密码不能与用户名一致。'))
        }
        if (value.split('').reverse().join('') === this.accountLoginForm.username) {
          callback(new Error('密码不能与逆序用户名一致。'))
        }
        if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@!%*?#_|,.+-])[A-Za-z\d$@!%*?#_|,.+-]{8,18}$/.test(value)) {
          callback(new Error('密码必须包含大写字母,小写字母,数字和特殊字符(特殊字符包含:$@!%*?#_|,.+-)组成，位数为8-18'))
        }
        if (value.length < 8) {
          callback(new Error('密码最少需要8位'))
        } else {
          callback()
        }
      }
      var checkRePassword = (rule, value, callback) => {
        value = this.accountChangePwdForm.reNewPassword
        if (!value || value === 0) {
          callback(new Error('请再次输入密码'))
        } else if (value !== this.accountChangePwdForm.newPassword) {
          callback(new Error('两次密码输入不一致'))
        } else {
          callback()
        }
      }
      return {
        change_password: true,
        password_form: true,
        warnMessage: config.warnMessage,
        inputType: 'text',
        t: {
          token: this.$route.query ? this.$route.query.token : null,
          redirectPath: this.$route.query ? this.$route.query.redirectPath : null,
          query: this.$route.query
        },
        mobile_counter: {
          show: false,
          counter: '',
          timer: null
        },
        email_counter: {
          show: false,
          counter: '',
          timer: null
        },
        logoSrc: config.loginBg ? `${process.env.BASE_URL}config/login/${config.loginBg}/login_logo.png` : `${process.env.BASE_URL}config/login_logo.png`,
        platTitle: config.plat_title,
        loginMode: config.loginMode,
        sso: config.sso,
        copyright: config.copyright,
        captcha: '',
        loginType: '1',
        loading: false,
        accountLoginForm: {
          username: '',
          password: '',
          passwordValue: '',
          captcha: '',
          src: ''
        },
        accountChangePwdForm: {
          newPassword: '',
          reNewPassword: ''
        },
        mobileLoginForm: {
          mobile: '',
          smsCaptcha: ''
        },
        emailLoginForm: {
          email: '',
          emailCaptcha: ''
        },
        accountRules: {
          username: [
            {required: true, message: '请输入账号！', trigger: 'blur'}
          ],
          password: [
            {required: true, message: '请输入密码！', trigger: 'blur'},
            {max: 18, message: '最长18个字符', trigger: 'blur'}
          ],
          captcha: [
            {required: true, message: '请输入验证码', trigger: 'blur'},
            {min: 4, max: 4, message: '长度为4个字符', trigger: 'blur'}
          ],
          newPassword: [
            {required: true, message: '请输入新密码！', trigger: 'blur'},
            {validator: checkPassword, trigger: 'blur'}
          ],
          reNewPassword: [
            {required: true, message: '请输入确认密码！', trigger: 'blur'},
            {validator: checkRePassword, trigger: 'blur'}
          ]
        },
        smsRules: {
          mobile: [
            {required: true, trigger: 'blur', validator: validMobile}
          ]
        },
        emailRules: {
          email: [
            {required: true, trigger: 'blur', validator: validEmail}
          ]
        },
        checked: true,
        userId: "",
        rsaSwitch: config.rsaSwitch, // RSA取数方式
        rsaPub: config.rsaPub, // RSA默认公钥
        resetPwdReason: ''
      }
    },
    computed: {
      isShowFieldset() {
        return Object.entries(this.loginMode).filter(o => o[1]).length > 1
      }
    },
    methods: {
      getResetPwdReason(userId) {
        this.$api.authority.getResetPwdReason({userId}).then((res) => {
          this.resetPwdReason = res.data
        })
      },
      async accountLogin() {
        this.loading = true
        let userInfo = {
          username: this.accountLoginForm.username,
          password: this.accountLoginForm.password,
          code: this.accountLoginForm.captcha,
          grant_type: 'password',
          scope: 'all',
          captcha: this.captcha,
          xep: new Date().getTime()
        }
        // 对密码加密
        /* userInfo = encrypt({
          data: userInfo,
          key: "thinvent.incloud",
          param: ["password"]
        }) */
        await this.getRsaPub()
        userInfo.password = rsa(userInfo.password, this.rsaPub)
        this.$refs.accountLoginForm.validate((valid) => {
          if (valid) {
            this.getUserInfo(userInfo, 1)
          } else {
            this.loading = false
            return false;
          }
        })
      },
      // 获取RAS公钥
      getRsaPub: async function() {
        if(this.rsaSwitch) {
          await getRsaPub().then(res => {
            this.rsaPub = res.value
          }).catch(e => {})
        }
      },
      async gotoReLogin() {
        await this.toLogout()
        this.password_form = true
        this.change_password = true
        this.accountLoginForm = {
          username: '',
          password: '',
          captcha: '',
          src: ''
        }
        this.$refs.pwdInput.clearInputValue()
        this.accountChangePwdForm = {
          newPassword: '',
          reNewPassword: ''
        }
        this.refreshCaptcha()
      },
      toLogout: async function() {
        await logout()
      },
      async accountChangePwd() {
        this.$refs.accountChangePwdForm.validate((valid) => {
          if (valid) {
            this.loading = true
            // 获取RAS公钥 登录的时候已经获取了
            // this.getRsaPub()
            // 对密码加密
            let params = {
              password: rsa(this.accountLoginForm.password, this.rsaPub),
              newPassword: rsa(this.accountChangePwdForm.newPassword, this.rsaPub),
              confirmPassword: rsa(this.accountChangePwdForm.reNewPassword, this.rsaPub)
              // id: this.userId
            }
            this.$api.user.changePassword(params).then((res) => {
              this.$notify({
                message: '修改密码成功，请重新登录',
                type: 'success',
                duration: 2000
              })
              this.loading = false
              // this.change_password = false
              this.gotoReLogin()
            }).catch(() => {
              this.loading = false
              // this.gotoReLogin()
            })
          }
        })
      },
      smsLogin() {
        this.loading = true
        let userInfo = {
          grant_type: 'sms',
          scope: 'all',
          sms: this.mobileLoginForm.mobile,
          code: this.mobileLoginForm.smsCaptcha
        }
        let captcha = [
          {required: true, message: '请输入验证码', trigger: 'blur'},
          {min: 4, max: 4, message: '长度为4个字符', trigger: 'blur'}
        ]
        this.smsRules.smsCaptcha = captcha
        this.$refs.mobileLoginForm.validate((valid) => {
          if (valid) {
            this.getUserInfo(userInfo)
          } else {
            this.loading = false
            return false;
          }
        })
      },
      emailLogin() {
        this.loading = true
        let userInfo = {
          grant_type: 'email',
          scope: 'all',
          email: this.emailLoginForm.email,
          code: this.emailLoginForm.emailCaptcha
        }
        let captcha = [
          {required: true, message: '请输入验证码', trigger: 'blur'},
          {min: 4, max: 4, message: '长度为4个字符', trigger: 'blur'}
        ]
        this.emailRules.captcha = captcha
        this.$refs.emailLoginForm.validate((valid) => {
          if (valid) {
            this.getUserInfo(userInfo)
          } else {
            this.loading = false
            return false;
          }
        })
      },
      getUserInfo: function(userInfo, f) {
        Cookies.remove(config.cookieName)
        this.$api.login.login(userInfo).then((res) => {
          // Cookies.set(config.cookieName, res.access_token, { sameSite: 'strict', domatin: document.domain + ':' + window.location.port })
          Cookies.set(config.cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
          // 检查用户是否需要强制修改密码，显示密码修改表单
          if(res.pwdState === '1') {
            this.loading = false
            this.userId = res.userId
            this.password_form = false
            this.getResetPwdReason(this.userId)
          } else {
            this.loginGotoSys(res, f)
          }
        }).catch((res) => {
          this.loading = false
          Message({message: res.message, type: 'error'})
          if(f) this.refreshCaptcha()
        })
      },
      loginGotoSys(res, f) {
        // 获取系统列表
        getSpaceId(res.userName).then(() => {
          this.$api.authority.getSystemList().then((res2) => {
            for (let s of res2.data) {
              // 依据接入指南返回的值 前端兼容复制相关属性
              s.sysCode = s.systemCode
              s.sysName = s.systemName
              s.sysCat = s.systemCat
              s.sysUri = s.systemUri
            }
            let systemList = res2.data
            let privFlag = !systemList || systemList.filter(i => i.sysCode === config.systemCode).length === 0
            if(privFlag) {
              // 再从子集中寻找
              let pSystem = systemList.filter(i => i.childrenSystems && i.childrenSystems.length > 0)
              if (pSystem) {
                for (let ele of pSystem) {
                  for (let cele of ele.childrenSystems) {
                    cele.sysCode = cele.systemCode
                    cele.sysName = cele.systemName
                    cele.sysCat = cele.systemCat
                    cele.sysUri = cele.systemUri
                    if (cele.sysCode === config.systemCode) {
                      privFlag = false
                      break
                    }
                  }
                }
              }
            }
            if(privFlag) {
              Cookies.remove(config.cookieName)
              // sessionStorage.removeItem(config.cookieName);
              Message({message: '用户未授权，请联系系统管理员!', type: 'error'})
              if(f) this.refreshCaptcha()
            } else {
              loginInit({userInfo: res, sysList: systemList});
              // getSpaceId(res.userName).then(() => {
              console.log(
                "\n ______  __                                         __      \n" +
                "/\\__  _\\/\\ \\      __                               /\\ \\__   \n" +
                "\\/_/\\ \\/\\ \\ \\___ /\\_\\    ___   __  __     __    ___\\ \\ ,_\\  \n" +
                "   \\ \\ \\ \\ \\  _ `\\/\\ \\ /' _ `\\/\\ \\/\\ \\  /'__`\\/' _ `\\ \\ \\/  \n" +
                "    \\ \\ \\ \\ \\ \\ \\ \\ \\ \\/\\ \\/\\ \\ \\ \\_/ |/\\  __//\\ \\/\\ \\ \\ \\_ \n" +
                "     \\ \\_\\ \\ \\_\\ \\_\\ \\_\\ \\_\\ \\_\\ \\___/ \\ \\____\\ \\_\\ \\_\\ \\__\\\n" +
                "      \\/_/  \\/_/\\/_/\\/_/\\/_/\\/_/\\/__/   \\/____/\\/_/\\/_/\\/__/\n" +
                "                                                            ")
              this.$refs.pwdInput.clearInputValue()
              // 有redirectPath，使用ticket进行跳转
              let q = this.t.query
              if(q) {
                q.ticket = res.ticket
              } else {
                q = {ticket: res.ticket}
              }
              if(this.t.redirectPath) {
                this.$router.push({path: this.t.redirectPath, query: q}) // 登录成功，跳转到主页
              } else {
                this.$router.push({path: '/', query: q}) // 登录成功，跳转到主页
              }
            }
          }).catch((r) => {
            this.loading = false
          })
          this.loading = false
        })
      },
      refreshCaptcha: function () {
        this.captcha = new Date().getTime()
        this.accountLoginForm.src = this.global.baseURL + '/auth/code/captcha?captcha=' + this.captcha
        this.accountLoginForm.captcha = null
      },
      getCounter: function(counter) {
        const TIME_COUNT = config.codeDuration;
        if (!counter.timer) {
          counter.counter = TIME_COUNT;
          counter.show = true;
          counter.timer = setInterval(() => {
            if (counter.counter > 0 && counter.counter <= TIME_COUNT) {
              counter.counter--;
            } else {
              counter.show = false;
              clearInterval(counter.timer);
              counter.timer = null;
            }
          }, 1000)
        }
      },
      sendCaptcha: function () {
        this.smsRules.smsCaptcha = undefined
        this.$refs.mobileLoginForm.validate((valid) => {
          if (valid) {
            this.$api.login.sendSMSCaptcha({'sms': this.mobileLoginForm.mobile}).then((res) => {
              this.getCounter(this.mobile_counter)
              this.$message({message: '短信已发送，请注意查收短信！', type: 'success'})
              this.loading = false
            }).catch((res) => {
              this.loading = false
            })
          } else {
            this.loading = false
            return false;
          }
        })
      },
      sendEmailCaptcha: function () {
        this.$refs.emailLoginForm.validate((valid) => {
          if (valid) {
            this.getCounter(this.email_counter)
            this.$api.login.sendEmailCaptcha({'email': this.emailLoginForm.email}).then((res) => {
              this.$message({message: '邮件已发送，请注意查收邮件！', type: 'success'})
              this.loading = false
            }).catch((res) => {
              this.loading = false
            })
          } else {
            this.loading = false
            return false;
          }
        })
      },
      handleFocus() {
        this.inputType = this.$refs.pwd.value ? 'password' : 'text'
      },
      handleBlur() {
        this.inputType = this.$refs.pwd.value ? 'password' : 'text'
      },
      // thinvent风格动画
      tweenMaxAnimation() {
        function getRandomRange(min, max) {
          return Math.floor(Math.random() * (max - min + 1) + min)
        }

        function makeStar() {
          let space = document.getElementById("space");
          let spaceWidth = space.scrollWidth;
          let spaceHeight = space.scrollHeight;
          let perspective = 100;
          space.style.setProperty(`--perspective`, `${perspective}px`);
          const star = document.createElement(`time`);
          const starWidth = getRandomRange(1, 1.5);
          const starHeight = starWidth * getRandomRange(20, 40);
          const randomRotation = Math.random() * 360;
          const scaleModifier = Math.random();
          const visibleRangeMaximum = spaceWidth - spaceHeight > 0 ? spaceWidth / 2 : spaceHeight / 2;
          // eslint-disable-next-line
          TweenMax.set(star, {
            width: `${starWidth}px`,
            height: `${starHeight}px`,
            transform: `
              translateY(${starHeight / 2}px)
              rotate(${randomRotation}deg)
              rotateX(90deg)
              translate3d(0,0,0px)
              scaleX(${scaleModifier})
            `
          });
          // eslint-disable-next-line
          TweenMax.to(star, getRandomRange(5, 20), {
            transform: `
              translateY(${starHeight / 2}px)
              rotate(${randomRotation}deg)
              rotateX(90deg)
              translate3d(0,0,${perspective + visibleRangeMaximum}px)
              scaleX(${scaleModifier})
            `,
            repeat: -1,
            // eslint-disable-next-line
            ease: Power0.easeNone
          }).progress(Math.random());
          space.appendChild(star)
        }
        Promise.all([
          import('../../../../../static/config/thinvent/TweenMax.min.js')
        ]).then(() => {
          for (let i = 0; i < 50; i++) {
            makeStar();
          }
        })
      },
      clearMask() {
        document.querySelectorAll(['.v-modal', '.shadowbox']).forEach(node => node.remove());
      },
      // 清空表单
      resetForm() {
        let _this = this;
        ['account', 'mobile', 'email'].map(formName => {
          if(_this.loginMode[formName]) {
            _this.$refs[formName + 'LoginForm'].resetFields();
          }
        })
      }
    },
    activated () {
      this.clearMask();
      this.resetForm();
      Cookies.remove(config.cookieName);
      // sessionStorage.removeItem(config.cookieName);
      this.refreshCaptcha();
      PopLoginBox.close()
    },
    mounted() {
      this.resetForm();
      Cookies.remove(config.cookieName);
      // sessionStorage.removeItem(config.cookieName)
      this.refreshCaptcha()
      // thinvent风格动画
      this.tweenMaxAnimation()
    },
    created() {
      this.clearMask();
      if(this.sso.onoff) {
        if(this.t.token) {
          this.loading = true
          this.$api.authority.getUserInfoByToken({token: this.t.token}).then((res) => {
            loginInit({userInfo: res});
            getSpaceId(res.userName).then(() => {
              this.$router.push(typeof this.t.redirectPath === 'undefined' || this.t.redirectPath.indexOf("/login") !== -1 ? '/' : this.t.redirectPath) // 登录成功，跳转到主页
            })
          }).catch((res) => {
            this.loading = false
          })
        } else {
          this.$api.authority.getState().then(res => {
            let state = res.data
            window.location.href = config.sso.url + '?fsc=' + config.systemCode + '&client_id=' + config.client_id + '&state=' + state
          })
        }
      }
    }
  }
</script>

<style type="text/css">
.resetPwdReason {
  display: block;
  position: absolute;
  font-size: 14px;
  text-align: left;
  color: #F56C6C;
  font-style: normal;
}
</style>
