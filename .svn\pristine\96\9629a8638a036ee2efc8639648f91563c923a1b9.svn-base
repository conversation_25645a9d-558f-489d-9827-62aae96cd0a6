<template>
  <el-container class="page-container">
    <!-- 右侧列表 -->
<!--    <el-header v-if="pageMode==='full'"></el-header>-->
    <el-header>
      <!--      列表工具栏-->
<!--      <div class="toolbar-wrapper">-->
<!--        <perm-button label="查看" type="text" icon="see" :perms="systemCode + 'x99002002001'" @click="handleView" />-->
<!--      </div>-->
      <!--      列表查询区-->
      <div class="query-wrapper">
        <el-form :inline="true" :model="filters" :size="size">
          <el-form-item label="操作用户">
            <el-input v-model="filters.userName" maxlength="100" placeholder="请输入用户名称" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="所属机构">
            <el-input v-model="filters.unitName" maxlength="100" placeholder="请输入机构名称" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="服务IP">
            <el-input v-model="filters.serverIp" maxlength="100" placeholder="请输入服务IP" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="操作IP">
            <el-input v-model="filters.remoteIp" maxlength="100" placeholder="请输入操作IP" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="请求URI">
            <el-input v-model="filters.requestUri" maxlength="100" placeholder="请输入请求URI" clearable @keyup.enter.native="handleFilter" />
          </el-form-item>
          <el-form-item label="日志类型">
            <select-plus dictType="LOG_TYPE" v-model="filters.type" clearable :initFunction="initFunction" style="width: 150px"></select-plus>
          </el-form-item>
          <el-form-item label="异常时间">
            <el-date-picker v-model="filters.time"
                            value-format="yyyy/MM/dd HH:mm:ss"
                            type="datetimerange"
                            :picker-options="pickerOptions2"
                            range-separator="至"
                            start-placeholder="异常时间起"
                            end-placeholder="异常时间止"
                            :default-time="['00:00:00', '23:59:59']"
                            align="right">
            </el-date-picker>
          </el-form-item>
          <perm-button label="查询" type="primary" icon="uims-icon-query" @click="handleFilter" />
          <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
          <!--          <el-input v-model="filters.api" placeholder="机构名称" @keyup.enter.native="handleFilter" />-->
        </el-form>
      </div>
    </el-header>
    <el-main>
      <!--      列表表格区-->
      <table-plus id="Errorlog" :key="0" row-key="logId" @header-dragend="handleHeaderDrag" v-loading="listLoading" :data="list" @sort-change="sortChange" border fit stripe highlight-current-row ref="multipleTable" @row-click="clickRow" @selection-change="selectMainTableRow">
        <el-table-column type="selection" width="60" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作用户" width="160" prop="userName" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属机构" width="200" prop="unitName" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务IP" width="180" prop="serverIp" show-overflow-tooltip></el-table-column>
        <el-table-column label="日志名称" width="250" prop="title" show-overflow-tooltip></el-table-column>
        <el-table-column label="错误日志验签" prop="sign_error_log" labelClassName="baseUI-dynamicShow" align="center" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="日志类型" width="150" align="center" prop="type" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作IP" width="180" prop="remoteIp" show-overflow-tooltip></el-table-column>
        <el-table-column label="请求URI" width="250" prop="requestUri" show-overflow-tooltip></el-table-column>
        <el-table-column label="异常时间" width="180" align="center" prop="endTime" sortable="custom" show-overflow-tooltip></el-table-column>
        <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center" width="100">
          <template slot-scope="scope">
            <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
          </template>
        </el-table-column>
      </table-plus>
    </el-main>
    <el-footer>
      <table-footer ref="tableFooter" excelName="异常日志" :showToolBar="true" :showPage="true" :tableRef="this.$refs.multipleTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total"
      url="/log/errorList"
      :filters="filters">
      </table-footer>
    </el-footer>
    <error-log-view @closeDialog="closeDialog" @getList="getList" :dialogStatus="dialogStatus" :log="selectTableRow" :dialogFormVisible="dialogFormVisible" :dialogDisabled="dialogDisabled"
      :tableRef="this.$refs.multipleTable"
      :tableFooterRef="this.$refs.tableFooter"
      @handlePageChg="handleCurrentChange">
    </error-log-view>
  </el-container>
</template>
<script>
import PermButtonGroup from "@/core/components/PermButtonGroup";
import SelectPlus from "@/core/components/SelectPlus";
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus"
import ErrorLogView from "./Dialog/ErrorLogView"
import TableFooter from "@/core/components/TablePlus/TableFooter";

import { resourceCode } from "@/biz/http/settings"
export default {
  components: { PermButton, TablePlus, SelectPlus, ErrorLogView, TableFooter, PermButtonGroup },
  data() {
    return {
      systemCode: config.systemCode,
      pickerOptions2: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近十二小时',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 12);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      queryBt: true,
      currentRow: undefined,
      // form表单-table查询条件相关----👇👇👇👇
      size: 'mini',
      // 列结果
      list: [],
      initFunction: function (data) {
        if(data.findIndex(item => item.key === "EX_NULL") === -1) {
          data.push({"key": "EX_NULL", "value": "无类型日志"})
        }
      },
      // 结果总数
      total: 0,
      selectTableRow: undefined,
      // 是否加载中
      listLoading: true,
      // 查询过滤条件
      filters: {
        currentPage: 1,
        pageSize: 20,
        sysName: undefined,
        userName: undefined,
        unitName: undefined,
        serverIp: undefined,
        remoteIp: undefined,
        requestUri: undefined,
        type: undefined,
        startTimeStart: undefined,
        startTimeEnd: undefined,
        time: undefined
      },
      // form表单-table查询条件相关----👆👆👆👆
      // 查看窗口数据-👇👇👇👇
      // 查看窗口显示控制
      dialogFormVisible: false,
      dialogStatus: '',
      dialogDisabled: false
      // ---------👆👆👆👆新查看窗口相关👆👆👆👆---------
    }
  },
  computed: {
    pageMode() {
      return config.page_mode;
    }
  },
  methods: {
    // 排序
    sortChange(column) {
      if (column.order) {
        this.filters.orderType = column.order;
        this.filters.order = column.prop;
      } else {
        this.filters.orderType = undefined;
        this.filters.order = undefined;
      }
      this.handleFilter();
    },
    getButtons(row) {
      let buts = [
        {label: "查看", icon: "see", clickFn: this.handleView, perms: this.systemCode + 'x99002002001'}
      ];
      return {
        row: row,
        buttons: buts,
        showNums: 2
      }
    },
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    closeDialog(val) {
      this.dialogFormVisible = val
    },
    // ---------👇👇👇👇查看相关👇👇👇👇---------
    handleView(row) {
      this.selectTableRow = [row]
      this.temp = Object.assign({}, this.selectTableRow[0]) // copy obj
      // 根据父级id找到父级资源名称
      this.dialogStatus = 'view'
      this.dialogFormVisible = true
      this.dialogDisabled = true
    },
    // ---------👆👆👆👆查看相关👆👆👆👆---------
    timeFormatter(row, column) {
      let val
      if (column.property === 'startTime') {
        val = row.startTime
      } else {
        val = row.endTime
      }
      if (val != null) {
        var date = new Date(val);
        return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
      }
    },
    timeFormat(val) {
      if (val != null) {
        var date = new Date(val);
        return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
      }
    },
    getList() {
      if(!this.queryBt) {
        return;
      }
      this.queryBt = false
      this.listLoading = true
      if (this.filters.time && this.filters.time.length === 2) {
        this.filters.startTimeStart = this.timeFormat(this.filters.time[0])
        this.filters.startTimeEnd = this.timeFormat(this.filters.time[1])
      } else {
        this.filters.startTimeStart = undefined
        this.filters.startTimeEnd = undefined
      }
      const filter = Object.assign({}, this.filters)
      delete filter.time
      this.$api.log.errorLogList(filter, resourceCode.exceptionLog).then((res) => {
        this.queryBt = true
        this.listLoading = false
        this.list = res.data.records
        this.total = res.data.total
      }).catch(res => {
        this.queryBt = true
      })
    },
    // 查询
    handleFilter(resetFlag) {
      this.filters.currentPage = 1
       if(resetFlag === true) {
        this.filters.sysName = undefined
        this.filters.userName = undefined
        this.filters.unitName = undefined
        this.filters.serverIp = undefined
        this.filters.remoteIp = undefined
        this.filters.requestUri = undefined
        this.filters.type = undefined
        this.filters.startTimeStart = undefined
        this.filters.startTimeEnd = undefined
        this.filters.time = undefined
      }
      this.getList()
    },
    // 点击前部checkbox
    selectMainTableRow(row, event, column) {
      this.selectTableRow = Object.assign([], row)
    },
    // 点击行选中checkbox
    clickRow(row) {
      if (this.currentRow === row) {
        this.currentRow = undefined
        this.$refs.multipleTable.clearSelection();
      } else {
        this.currentRow = row
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1;
      this.getList()
    },
    async handleCurrentChange(val, cb) {
      this.filters.currentPage = val
      await this.getList()
      if(typeof cb === "function") {
        cb();
      }
    }
  },
  created() {
    this.getList()
  }
}
</script>
<style scoped lang="scss">
</style>
