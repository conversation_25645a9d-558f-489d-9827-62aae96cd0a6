<template>
  <div>
    <!-- 新增Form -->
    <el-dialog v-dialogDrag :title="modeMap[dialogModes]" :visible.sync="dialogMainFormVisible"
               width="calc(100% - 100px)"
               min-width="800px"
               @open="initDialog"
               @close="destrory()">
      <el-container>
        <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :model="temp" label-position="right" label-width="70px">
            <el-form-item label="消息名称" prop="subject">
              <el-input v-model="temp.subject" :disabled="dialogModes === 'view'" maxlength="50"/>
            </el-form-item>
            <el-form-item label="内容" prop="content" class="margin-bottom_0">
              <mark-down :content="temp.content"></mark-down>
              <!--        <el-input v-model="temp.content" :autosize="{ minRows: 5, maxRows: 10}" type="textarea"-->
              <!--                  :disabled="dialogModes === 'view'" />-->
            </el-form-item>
            <el-form-item>

            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMainFormVisible = false">{{dialogModes==='view'?'关闭':'取消'}}</el-button>
        <template v-if="temp.sysMsgBizActionList && temp.sysMsgBizActionList.length > 0">
          <el-button class="save-btn" type="primary" v-for="item in temp.sysMsgBizActionList" :key="item.actionCode"
                     @click="doAction(item.actionCode)">
            {{item.actionName}}
          </el-button>
        </template>
        <el-button class="save-btn" type="primary" @click="receipt()"
                   v-if="needReceipt === '1' && this.readFlag === '1'" :loading="okLoading">回执
        </el-button>
      </div>
    </el-dialog>
    <!--    <el-dialog title="test" custom-class="dialogYs" ref="childDialog"-->
    <!--               :append-to-body="true" :visible.sync="dialogOpenVisible" @closed="dialogOpenVisible = false">-->
<!--    <div id="ooolllccc" v-show="dialogOpenVisible">-->
    <component :is="compt" :params="params"></component>
<!--      <router-view :dialogOpenVisible="dialogOpenVisible" :params="params" @closeDialog="closeDialog"></router-view>-->
<!--    </div>-->
    <!--    </el-dialog>-->
  </div>
</template>

<script>
  import MarkDown from "@/core/components/MarkDown";
  import {resourceCode} from "@/biz/http/settings";
  // import { updateRouter } from '@/core/router'
  export default {
    components: {MarkDown},
    name: "MsgInBoxMainDialog",
    data() {
      return {
        compt: null,
        dialogOpenVisible: false,
        params: {},
        resCode: '',
        myComponent: null,
        okLoading: false,
        modeMap: {
          view: '查看收件箱信息'
        },
        // 查看列表
        temp: {
          subject: undefined,
          content: undefined,
          sysMsgBizActionList: undefined
        },
        dialogMainFormVisible: false
      }
    },
    props: {
      dialogModes: String,
      dialogFormVisible: Boolean,
      miId: String,
      needReceipt: String,
      readFlag: String
    },
    methods: {
      closeDialog(val) {
        this.dialogOpenVisible = val
      },
      destrory() {
        this.okLoading = false;
        if (this.readFlag === '1') { // 当前为第一次阅读，查看后更新阅读时间
          this.$emit("getList")
        }
      },
      initDialog() {
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      doAction(actionCode) {
        let action = {};
        for (let i = 0; i < this.temp.sysMsgBizActionList.length; i++) {
          if (this.temp.sysMsgBizActionList[i].actionCode === actionCode) {
            action = this.temp.sysMsgBizActionList[i]
            break;
          }
        }
        if (action.actionType === '1') {
          // 前端路由
          // this.$router.push(action.actionRoute).then(res => {
          //   this.dialogOpenVisible = true
          //   this.params = {}
          //   if (this.temp.params) {
          //     this.params = JSON.parse(this.temp.params)
          //   }
          // });
          // -------------
          this.compt = resolve => require([`@/biz${action.actionRoute}`], resolve)
          this.params = JSON.parse(this.temp.params)
        } else {
          // 后台接口
          // 点击接口需要二次确认
          this.$confirm(`您确认要执行${action.actionName}操作吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            let params = {
              actionCode: action.actionCode,
              params: this.temp.params
            }
            this.$api.msgInBox.process(params).then(res => {
              this.$notify({
                title: '操作成功',
                message: '操作成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消操作',
              type: 'info',
              duration: 2000
            })
          })
        }
      },
      receipt() {
        this.okLoading = true;
        this.resCode = resourceCode.msgInBox
        this.$api.msgInBox.receipt({miId: this.miId}, this.resCode).then((res) => {
          this.okLoading = false
          this.needReceipt = '0'
          this.$notify({
            title: '操作成功',
            message: '回执成功',
            type: 'success',
            duration: 2000
          })
        }).catch((res) => {
          this.okLoading = false
        })
      },
      initChildRouter() {
        if (this.temp.sysMsgBizActionList && this.temp.sysMsgBizActionList.length > 0) {
          let cRoute = []
          for (let i = 0; i < this.temp.sysMsgBizActionList.length; i++) {
            if (this.temp.sysMsgBizActionList[i].actionType === '1') {
              cRoute.push({
                path: this.temp.sysMsgBizActionList[i].actionRoute,
                component: resolve => require([`@/biz${this.temp.sysMsgBizActionList[i].actionRoute}`], resolve),
                name: this.temp.sysMsgBizActionList[i].actionName,
                isFromBtn: '1',
                meta: {isFromBtn: '1'}
              })
            }
          }
          this.$router.options.routes[0].children.filter(n => {
            if (n.name === "收件箱") {
              n.children = cRoute;
            }
          })
        }
        // updateRouter(this.$router.options.routes);
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogModes === 'view') {
            this.resCode = resourceCode.msgInBox
            this.$api.msgInBox.view({miId: this.miId}, this.resCode).then((res) => {
              this.temp = res.data
              // this.initChildRouter()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
