// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import Cookies from 'js-cookie'
import router from './core/router'
import api from './core/http'
import store from './core/store'
import global from '@/utils/global'
import '@/utils/dialog'
// import 'styles/index.scss' // global css
// 仅替换主题色
// import "./assets/element-variables.scss";

import './assets/iconfont/iconfont.css' // iconfont css
import './assets/global.scss' // global css
import './core/icons' // icon
import ElementUI from 'element-ui'
import ErrorMessageBox from "@/core/components/ErrorMessageBox/index" // 业务异常信息弹窗
import 'element-ui/lib/theme-chalk/index.css'
import 'font-awesome/css/font-awesome.min.css'
import 'font-awesome-animation/dist/font-awesome-animation.min.css'
import "styles/index.scss"; // login css
import "./assets/reset.css"; // reset css
import "./assets/globalPortal.scss"; // global css

// 注册组件
import * as directives from '@/biz/utils/directives'

import socket from '@/core/utils/websocket'
Vue.prototype.createSocket = socket.createSocket
Vue.prototype.sendMsg = socket.sendMsg
Vue.prototype.closeWS = socket.closeWS

// 全局修改默认配置:点击dialog外的地方不会关闭窗口
ElementUI.Dialog.props.closeOnClickModal.default = false;
// 全局修改默认配置：dialog遮罩层放在内层，防止浏览器回退无法点击页面
ElementUI.Dialog.props.modalAppendToBody.default = false;

Vue.prototype.Cookies = Cookies
Vue.config.productionTip = false

Vue.use(ElementUI)
Vue.use(api) // 引入API模块
Vue.use(ErrorMessageBox)

Vue.prototype.global = global // 挂载全局配置模块

// 防止手动修改localstorage
// window.addEventListener('storage', function(e) {
//   sessionStorage.setItem(e.key, e.oldValue) // 重新赋值修改前的值
// });
Vue.mixin({
  beforeRouteLeave(to, from, next) {
    let flag = true
    try {
      this.$store.state.tab.mainTabs.forEach(e => {
        if(from.name === e.name) {
          flag = false
        }
      })
    } catch(e) { flag = true }
    if(flag && this.$vnode.parent && this.$vnode.parent.componentInstance.cache) {
      let key = this.$vnode.key // 当前关闭的组件名
      let cache = this.$vnode.parent.componentInstance.cache // 缓存的组件
      let keys = this.$vnode.parent.componentInstance.keys // 缓存的组件名
      if(cache[key] != null) {
        delete cache[key]
        let index = keys.indexOf(key)
        if(index > -1) {
          keys.splice(index, 1)
        }
      }
    }
    if(to.path === '/login' && document.body.querySelectorAll('.el-dialog__wrapper').length > 0) {
      this.$destroy('home')
    }
    next()
  }
})

Object.keys(directives).forEach((key) => {
  Vue.directive(key, directives[key])
})

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  components: { App },
  template: '<App/>'
})
