<template>
  <div>
    <!-- 新增Form -->
    <el-dialog v-dialogDrag title="消息日志" :visible.sync="dialogMainFormVisible" width="calc(100% - 200px)"
               @open="initDialog" @close="destrory()">
      <el-container>
        <el-header>
          <!--列表查询区-->
          <el-form :inline="true" :model="filters" size="mini">
            <el-form-item label="收件人">
              <el-input v-model="filters.receiverName" maxlength="100" @keyup.enter.native="handleFilter"/>
            </el-form-item>
            <el-form-item label="发件时间">
              <el-date-picker v-model="filters.time" type="datetimerange" :picker-options="pickerOptions2"
                              range-separator="至" start-placeholder="开始日期起" end-placeholder="开始日期止" align="right">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="发件方式">
              <select-plus dictType="MSG_SEND_TYPE" v-model="filters.sendType" clearable
                           style="width: 150px"></select-plus>
            </el-form-item>
            <el-form-item label="发件状态">
              <select-plus dictType="MSG_SEND_STATUS" v-model="filters.status" clearable
                           style="width: 150px"></select-plus>
            </el-form-item>
            <el-form-item label="是否发件异常">
              <select-plus dictType="IS_FLAG" v-model="filters.excepFlag" clearable
                           style="width: 150px"></select-plus>
            </el-form-item>
            <perm-button type="primary" label="查询" icon="uims-icon-query" @click="handleFilter"/>
          </el-form>
        </el-header>
        <!--列表表格区-->
        <el-main>
          <table-plus
            :data="logList"
            ref="multipleTable"
            border
            id="1"
            fit
            highlight-current-row
            v-loading="listLoading"
            tooltip-effect="light"
          >
            <el-table-column width="300" show-overflow-tooltip prop="receiverName" label="收件人" header-align="center"
                             align="left"></el-table-column>
            <el-table-column width="120" show-overflow-tooltip prop="sendType" label="发件方式" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="120" show-overflow-tooltip prop="status" label="发件状态" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="150" show-overflow-tooltip prop="excepFlag" label="是否发件异常" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="180" show-overflow-tooltip prop="sendTime" label="发件时间" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="100" label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <perm-button label="查看" size="mini" icon="see" type="success" @click="handleViewLog(row)"/>
              </template>
            </el-table-column>
          </table-plus>
        </el-main>
        <el-footer>
          <table-footer ref="tableFooter"
                        :showToolBar="true"
                        :showPage="true"
                        :tableRef="this.$refs.multipleTable"
                        @sizeChange="handleSizeChange"
                        @currentChange="handleCurrentChange"
                        :currentPage="filters.currentPage"
                        :pageSizes="[10, 20, 50, 100]"
                        :pageSize="filters.pageSize"
                        :total="total">
          </table-footer>
        </el-footer>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMainFormVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    <subscript-view-log
      @closeDialog="closeLogDialog"
      :mhId="mhIdSelect"
      :dialogFormVisible="dialogLogVisible"
    >
    </subscript-view-log>
  </div>
</template>

<script>
  import SelectPlus from "@/core/components/SelectPlus";
  import PermButton from '@/core/components/PermButton'
  import TablePlus from "@/core/components/TablePlus"
  import TableFooter from "@/core/components/TablePlus/TableFooter"
  import SubscriptViewLog from "./SubscriptViewLog";
  import {resourceCode} from "@/biz/http/settings"
  export default {
    components: {SelectPlus, PermButton, TablePlus, TableFooter, SubscriptViewLog},
    name: "MainDialog",
    data() {
      return {
        dialogLogVisible: undefined,
        pickerOptions2: {
          shortcuts: [{
            text: '最近一小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近十二小时',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 12);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        listLoading: true,
        total: 0,
        mhIdSelect: undefined,
        filters: {
          currentPage: 1,
          pageSize: 20,
          mbId: undefined,
          order: "",
          receiverName: undefined,
          time: [],
          status: undefined,
          sendType: undefined,
          excepFlag: undefined
        },
        resCode: '',
        dialogMainFormVisible: false,
        logList: []
      }
    },
    props: {
      dialogFormVisible: Boolean,
      mbId: String
    },
    methods: {
      closeLogDialog(val) {
        this.dialogLogVisible = val
      },
      destrory() {
        this.mhIdSelect = undefined;
        this.listLoading = false;
        this.logList = [];
        this.filters = {
          currentPage: 1,
          pageSize: 20,
          mbId: undefined,
          order: "",
          receiverName: undefined,
          time: [],
          status: undefined,
          sendType: undefined
        }
      },
      initDialog() {
      },
      handleFilter() {
        this.filters.currentPage = 1
        this.getList()
      },
      timeFormat(val) {
        if (val != null) {
          var date = new Date(val);
          return date.getFullYear() + '-' +
            ((date.getMonth() + 1) < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1)) + '-' +
            (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ' +
            (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':' +
            (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':' +
            (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
        }
      },
      getList() {
        this.listLoading = true
        if (this.filters.time && this.filters.time.length === 2) {
          this.filters.sendTimeStart = this.timeFormat(this.filters.time[0])
          this.filters.sendTimeEnd = this.timeFormat(this.filters.time[1])
        } else {
          this.filters.sendTimeStart = undefined
          this.filters.sendTimeEnd = undefined
        }
        let filter = Object.assign({}, this.filters)
        filter.time = undefined
        // filter.tSysMsgOutboxVos = {}
        // filter.tSysMsgOutboxVos.status = this.filters.status
        this.$api.msgBiz.getBizHistory(filter, resourceCode.msgBiz).then((res) => {
          this.listLoading = false
          this.logList = res.data.records
          this.total = res.data.total
        })
      },
      handleSizeChange(val) {
        this.filters.pageSize = val
        this.filters.currentPage = 1;
        this.getList()
      },
      handleCurrentChange(val) {
        this.filters.currentPage = val
        this.getList()
      },
      handleViewLog(row) {
        this.mhIdSelect = row.mhId
        this.dialogLogVisible = true
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          this.filters.mbId = this.mbId
          this.getList()
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
  > > > .el-scrollbar {
    height: calc(100% - 0px);
    width: calc(100% - 15px);
  }
</style>
