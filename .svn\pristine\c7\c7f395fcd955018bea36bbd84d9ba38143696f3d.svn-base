import axios from "@/core/http/axios";

;

// 公用上传文件接口
export const annexUploadFile = (formData, resourceCode) => {
  return axios({
    url: `/${config.appCode}/annex/upload`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};

// 公用上传文件接口
export const uploadFile = (formData, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
    ? `/${config.appCode}/common/uploadFile?fromPowerInvoke=1`
    : `/${config.appCode}/annex/gxsl/upload`,
    method: "post",
    data: formData,
    transformRequest: function (data, headers) {
      const formData = new FormData();
      for (const key of Object.keys(data)) {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  });
};
