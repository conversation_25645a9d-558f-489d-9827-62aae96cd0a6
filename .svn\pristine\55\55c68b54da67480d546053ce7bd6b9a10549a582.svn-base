<template>
  <!-- 新增Form -->
  <el-dialog v-dialogDrag :title="modeMap[dialogModes]" :visible.sync="dialogMainFormVisible"
             min-width="900px" @opened="initDialog"
             @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <el-form ref="dataForm" :rules="dialogModes === 'view'? {} : rules" :model="temp" label-position="right"
                 label-width="68px">
          <el-form-item label="发送方式" v-if="isHadSpace()">
            <el-switch
              v-model="temp.isSpace"
              active-color="rgb(19, 206, 102)"
              inactive-color="#409eff"
              @change="toggleSendType"
              active-text="空间"
              inactive-text="机构"
              :disabled="dialogModes === 'view'">
            </el-switch>
          </el-form-item>
          <el-form-item label="收件人" prop="receiver">
            <el-select v-model="temp.receiver" :placeholder="placeholder"
                       :disabled="dialogModes === 'view' || initing"
                       style="width:100%"
                       @change="$forceUpdate()"
                       multiple
                       clearable
                       filterable>
              <template v-if="temp.isSpace">
                <el-option v-for="item in receiverSpaces" :key="item.key" :label="item.key" :value="item.key" />
              </template>
              <template v-else>
                <el-option v-for="item in receiverUnits" :key="item.key" :label="item.key"
                           :value="item.key">
                  <el-popover v-if="item.key.length >= 60" placement="top-start" width="400" trigger="hover">
                    <p>{{ item.key }}</p>
                    <span slot="reference">{{ item.key.slice(0,57) + '...' }}</span>
                  </el-popover>
                  <span v-else>{{ item.key }}</span>
                </el-option>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item label="主题" prop="subject">
            <el-input v-model="temp.subject" :disabled="dialogModes === 'view'" maxlength="50" />
          </el-form-item>
          <el-form-item label="有效期" prop="beginTimeToEndTime">
            <el-date-picker v-model="temp.beginTimeToEndTime" type="datetimerange" align="right"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="expireTimeOption"
                            :disabled="dialogModes === 'view'">
            </el-date-picker>
          </el-form-item>
                    <el-form-item label="定时发送" prop="sendTimer">
                      <el-date-picker v-model="temp.sendTimer" type="datetime" align="right"
                                      placeholder="选择定时发送时间"
                                      is-range
                                      :picker-options="sendTimeOption"
                                      :disabled="dialogModes === 'view'">
                      </el-date-picker>
                    </el-form-item>
          <el-form-item label="通知方式" prop="sendTypeArr">
            <el-checkbox-group v-model="sendTypeArr" @change="sendTypeChange" :disabled="dialogModes === 'view'">
              <el-checkbox label="0">消息通知</el-checkbox>
              <el-checkbox label="1">短信通知</el-checkbox>
              <el-checkbox label="2">邮件通知</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="内容" prop="content" class="margin-bottom_0">
            <mark-down v-if="dialogModes === 'view'" :content="temp.content"></mark-down>
            <div v-show="!(dialogModes === 'view')" id="vditor" style="height: 500px"></div>
            <el-input v-model="temp.content" :autosize="{ minRows: 5, maxRows: 10}" type="hidden"
                      :disabled="dialogModes === 'view'" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">{{dialogModes==='view'?'关闭':'取消'}}</el-button>
      <el-button class="commit-btn" type="success" @click="dialogModes==='create'?save(1):update(1)"
                 v-if="!(dialogModes === 'view')" :loading="okLoading">发送
      </el-button>
      <el-button class="save-btn" type="primary" @click="dialogModes==='create'?save(0):update(0)"
                 v-if="!(dialogModes === 'view')" :loading="okLoading">保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import Vditor from 'vditor'
  import MarkDown from "@/core/components/MarkDown";
  import "@/core/styles/vditor/scss/index.scss"
  import {resourceCode} from "@/biz/http/settings";

  export default {
    components: {MarkDown},
    name: "MsgNoticeMainDialog",
    data() {
      var checkTime = (rule, value, callback) => {
        if(this.temp.beginTimeToEndTime[1]) {
          if(typeof this.temp.beginTimeToEndTime[1] === 'string') {
            var arr1 = this.temp.beginTimeToEndTime[1].split(" ");
            var sdate = arr1[0].split('-');
            var mdate = arr1[1].split(':');
            var date = new Date(sdate[0], sdate[1] - 1, sdate[2], mdate[0], mdate[1], mdate[2]);
            if(new Date() > date) {
              callback(new Error('有效期截止时间不能小于当前时间1'))
            } else {
              callback()
            }
          } else {
            if(new Date() > this.temp.beginTimeToEndTime[1]) {
              callback(new Error('有效期截止时间不能小于当前时间2'))
            } else {
              callback()
            }
          }
        } else {
          callback()
        }
      }
      var checkSendType = (rule, value, callback) => {
        if (!this.sendTypeArr || !this.sendTypeArr.length) {
          callback(new Error('请选择通知类型'))
        } else {
          callback()
        }
      }
      return {
        sendTypeArr: [],
        contentEditor: undefined,
        resCode: '',
        okLoading: false,
        modeMap: {
          create: '新增通知公告',
          update: '编辑通知公告',
          view: '查看通知公告'
        },
        initing: true, // 正在初始化
        placeholder: '正在初始化数据，请稍后…',
        receiverUnits: [], // 收件人：机构
        receiverSpaces: [], // 收件人：空间
        // 新增/编辑列表
        temp: {
          isSpace: false,
          receiver: [],
          receiverNames: undefined,
          receiverIds: undefined,
          subject: undefined,
          status: undefined,
          content: undefined,
          effectiveDate: undefined,
          expireDate: undefined,
          sendTimer: undefined,
          sendType: undefined,
          createTime: undefined,
          beginTimeToEndTime: []
        },
        expireTimeOption: {
          disabledDate(date) {
            var now = new Date();
            var ny = now.getFullYear();
            var nm = now.getMonth();
            var nd = now.getDate();
            var y = date.getFullYear();
            var m = date.getMonth();
            var d = date.getDate();
            if (ny === y && nm === m && nd === d) {
              return false;
            }
            return (date.getTime() <= Date.now());
          }
        },
        sendTimeOption: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
        // 表单校验规则
        rules: {
          receiver: [
            {required: true, message: '请选择收件人', trigger: 'blur'}
          ],
          content: [
            {required: true, message: '请输入内容', trigger: 'blur'}
          ],
          subject: [
            {required: true, message: '请输入主题', trigger: 'blur'},
            {min: 1, max: 50, message: '长度为 1 到 50 个字符', trigger: 'blur'}
          ],
          beginTimeToEndTime: [
            {validator: checkTime, trigger: 'blur'},
            {required: true, message: '请选择有效期', trigger: 'blur'}
          ],
          sendTimer: [{validator: checkTime, trigger: 'blur'}],
          sendTypeArr: [
            {validator: checkSendType, trigger: 'change'}
          ]
          // sendType:[
          //   {required: true, message: '请选择通知类型', trigger: 'blur'}
          // ],
        },
        dialogMainFormVisible: false
      }
    },
    props: {
      dialogModes: String,
      dialogFormVisible: Boolean,
      msgNoticeArray: Array
    },
    computed: {
      unitUser() {
        return this.temp.isSpace ? this.receiverSpaces : this.receiverUnits
      }
    },
    methods: {
      sendTypeChange(val) {
        this.temp.sendType = val.toString()
      },
      // 判断是否存在空间
      isHadSpace() {
        let res = false;
        if (this.$store.state && this.$store.state.system && this.$store.state.system.spaceId) {
          res = true
        }
        return res;
      },
      toggleSendType(val) {
        this.temp.receiver = undefined
        this.placeholder = val ? '请选择空间' : '请选择单位/部门'
      },
      // 加载空间人员
      loadSpaceUser() {
        this.$api.msgOpen.getMsgSpaceReceiver({systemCode: config.systemCode}, resourceCode.msgNotice).then((res) => {
          this.receiverSpaces = Object.keys(res.data).map(key => {
            return {
              key: key,
              value: res.data[key]
            }
          })
        })
      },
      destrory() {
        this.okLoading = false;
        this.contentEditor.setValue('')
        this.resetTemp()
        this.$refs['dataForm'].clearValidate()
      },
      initEditor() {
        this.contentEditor = new Vditor('vditor', {
          cdn: 'vditor',
          toolbar: [
            'headings',
            'bold',
            'italic',
            'strike',
            'link',
            '|',
            'list',
            'ordered-list',
            'check',
            'outdent',
            'indent',
            '|',
            'line',
            'insert-before',
            'insert-after',
            '|',
            'table',
            '|',
            'undo',
            'redo'
          ],
          mode: 'wysiwyg', // 可选模式：sv, ir, wysiwyg
          width: '100%',
          toolbarConfig: {
            pin: true
          },
          cache: {
            enable: false
          },
          counter: {
            enable: true, // 是否启用计数器
            max: 3000, // 允许输入的最大值
            type: 'text' // 统计类型:md,text
          },
          after: () => {
            this.contentEditor.setValue(this.temp.content ? this.temp.content : '')
          }
        })
      },
      initDialog() {
        this.loadSpaceUser()
        this.loadMsgReceiver()
        if (!this.contentEditor) {
          this.initEditor();
        }
        if (this.dialogModes === 'view') {
          this.contentEditor.disabled()
        } else {
          this.contentEditor.enable()
        }
      },
      // 加载机构柜员
      loadMsgReceiver() {
        this.$api.msgOpen.getMsgReceiver({systemCode: config.systemCode}, resourceCode.msgNotice).then((res) => {
          this.receiverUnits = Object.keys(res.data).map(key => {
            return {
              key: key,
              value: res.data[key]
            }
          })
          this.initing = false
          this.placeholder = '请选择单位/部门'
        })
      },
      // 反显收件人
      viewMsgReceiver() {
        this.$api.msgOpen.getMsgReceiverUnit({userIds: this.temp.receiverIds}, this.resCode).then((res) => {
          this.temp.receiver = res.data
        });
      },
      save(statusVal) {
        let c = this.contentEditor.getValue()
        if (c === null || c.length <= 1 || c === undefined) {
          this.contentEditor.focus()
          this.$notify({
            title: '提示',
            message: '请输入内容',
            type: 'warning',
            duration: 2000
          })
          return;
        }
        if(c.length > 3001) {
          this.$notify({
            title: '提示',
            message: '消息内容操作限制，最多3000字',
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp.content = c
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            this.temp.status = statusVal
            let notifymsg = '保存通知公告成功'
            if (statusVal === 1) {
              notifymsg = '通知公告发送成功'
            }
            this.temp.effectiveDate = new Date(this.temp.beginTimeToEndTime[0])
            this.temp.expireDate = new Date(this.temp.beginTimeToEndTime[1])
            if (this.temp.sendTimer >= this.temp.expireDate) {
              this.okLoading = false
              this.$notify({
                title: '提示',
                message: '定时发送时间未在有效期内',
                type: 'success',
                duration: 2000
              })
              return;
            }

            let userId = '';
            let userName = '';
            let selectUnit = this.temp.receiver;
            for (let tempUnit in selectUnit) {
              let unitArry = this.unitUser.filter(item => item.key === selectUnit[tempUnit])[0]
              userId += unitArry.value.map(item => { return item.userId }).join(',') + ','
              userName += unitArry.value.map(item => { return item.realName + "(" + item.userName + ")" }).join(',') + ','
            }
            this.temp.receiverIds = userId.substring(0, userId.length - 1)
            this.temp.receiverNames = userName.substring(0, userName.length - 1)
            if (this.temp.receiverIds) {
              this.temp.msgType = 0;
              this.temp.sendType = this.sendTypeArr.toString()
              if (this.temp.deleted === '否') {
                this.temp.deleted = '0'
              } else {
                this.temp.deleted = '1'
              }
              if (this.temp.receiverType === '用户') {
                this.temp.receiverType = '0'
              }
              this.$api.msgNotice.save(this.temp, this.resCode).then((res) => {
                this.okLoading = false
                this.dialogMainFormVisible = false
                this.$emit("getList")
                this.$notify({
                  title: '操作成功',
                  message: notifymsg,
                  type: 'success',
                  duration: 2000
                })
              }).catch((res) => {
                this.okLoading = false
              })
            } else {
              this.$confirm('选择的机构下没有本系统授权用户？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
              }).then(() => {
                this.temp.msgType = 0
                this.temp.sendType = this.sendTypeArr.toString()
                if (this.temp.deleted === '否') {
                  this.temp.deleted = '0'
                } else {
                  this.temp.deleted = '1'
                }
                if (this.temp.receiverType === '用户') {
                  this.temp.receiverType = '0'
                }

                this.$api.msgNotice.save(this.temp, this.resCode).then((res) => {
                  this.okLoading = false
                  this.dialogMainFormVisible = false
                  this.$emit("getList")
                  this.$notify({
                    title: '操作成功',
                    message: notifymsg,
                    type: 'success',
                    duration: 2000
                  })
                }).catch((res) => {
                  this.okLoading = false
                })
              }).catch(() => {
                this.okLoading = false
              })
            }
          }
        })
      },
      update(statusVal) {
        let c = this.contentEditor.getValue()
        if (c === null || c.length <= 1 || c === undefined) {
          this.contentEditor.focus()
          this.$notify({
            title: '提示',
            message: '请输入内容',
            type: 'success',
            duration: 2000
          })
          return;
        }
        if(c.length > 3001) {
          this.$notify({
            title: '提示',
            message: '消息内容操作限制，最多3000字',
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp.content = c
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            this.temp.status = statusVal
            let notifymsg = '保存通知公告成功'
            if (statusVal === 1) {
              notifymsg = '通知公告发送成功'
            }

            this.temp.effectiveDate = new Date(this.temp.beginTimeToEndTime[0])
            this.temp.expireDate = new Date(this.temp.beginTimeToEndTime[1])
            if (this.temp.sendTimer >= this.temp.expireDate) {
              this.okLoading = false
              this.$notify({
                title: '提示',
                message: '定时发送时间未在有效期内',
                type: 'success',
                duration: 2000
              })
              return;
            }
            let userId = '';
            let userName = '';
            let selectUnit = this.temp.receiver;
            for (let tempUnit in selectUnit) {
              let unitArry = this.unitUser.filter(item => item.key === selectUnit[tempUnit])[0]
              userId += unitArry.value.map(item => { return item.userId }).join(',') + ','
              userName += unitArry.value.map(item => { return item.realName + "(" + item.userName + ")" }).join(',') + ','
            }
            this.temp.receiverIds = userId.substring(0, userId.length - 1)
            this.temp.receiverNames = userName.substring(0, userName.length - 1)
            this.temp.msgType = 0
            this.temp.sendType = this.sendTypeArr.toString()
            if (this.temp.deleted === '否') {
              this.temp.deleted = '0'
            } else {
              this.temp.deleted = '1'
            }
            if (this.temp.receiverType === '用户') {
              this.temp.receiverType = '0'
            }
            const tempData = Object.assign({}, this.temp)
            this.$api.msgNotice.save(tempData, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: notifymsg,
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.temp = {
          isSpace: false,
          receiver: [],
          receiverNames: undefined,
          receiverIds: undefined,
          subject: undefined,
          status: undefined,
          content: undefined,
          effectiveDate: undefined,
          expireDate: undefined,
          sendTimer: undefined,
          sendType: undefined,
          createTime: undefined,
          beginTimeToEndTime: []
        }
        this.sendTypeArr = ["0"]
        this.initing = true // 正在初始化
        this.placeholder = '正在初始化数据，请稍后…'
        this.receiverUnits = [] // 收件人：机构
        this.receiverSpaces = [] // 收件人：空间
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogModes === 'create') {
            this.resCode = resourceCode.msgNotice
            this.resetTemp()
          } else if (this.dialogModes === 'update') {
            this.resCode = resourceCode.msgNotice
            this.$api.msgNotice.getById({moId: this.msgNoticeArray[0].moId}, this.resCode).then((res) => {
              this.temp = res.data
              // 此处需将时间数据进行转化后再转回后台 否则更新失败
              this.temp.createTime = new Date(res.data.createTime)
              if (this.temp.sendType) {
                this.sendTypeArr = this.temp.sendType.split(",")
              }
              if (this.temp.sendTimer != null) {
                this.temp.sendTimer = new Date(res.data.sendTimer)
              } else {
                this.temp.sendTimer = undefined;
              }
              if (this.temp.effectiveDate != null && this.temp.expireDate != null) {
                this.temp.beginTimeToEndTime = [this.temp.effectiveDate, this.temp.expireDate]
              }
              this.temp = Object.assign({}, this.temp, this.temp)
              // this.viewMsgReceiver()
              if (this.contentEditor) {
                this.contentEditor.setValue(this.temp.content)
              } else {
                // this.initEditor();
                // 为了在编辑器初始化后给编辑器设置内容
                // setInterval(function () {
                //   if(this.contentEditor) {
                //     this.contentEditor.setValue(this.temp.content)
                //     clearInterval()
                //   }
                // }, 500)
              }
            })
          } else if (this.dialogModes === 'view') {
            this.resCode = resourceCode.msgNotice
            this.$api.msgNotice.getById({moId: this.msgNoticeArray[0].moId}, this.resCode).then((res) => {
              this.temp = res.data
              if (this.temp.sendTimer != null) {
                this.temp.sendTimer = new Date(res.data.sendTimer)
              } else {
                this.temp.sendTimer = undefined;
              }
              if (this.temp.sendType) {
                this.sendTypeArr = this.temp.sendType.split(",")
              }
              if (this.temp.effectiveDate != null && this.temp.expireDate != null) {
                this.temp.beginTimeToEndTime = [this.temp.effectiveDate, this.temp.expireDate]
              }
              // if(this.contentEditor) {
              //   this.contentEditor.setValue(this.temp.content)
              // } else {
              //   this.initEditor();
              //   this.contentEditor.setValue(this.temp.content)
              // }
              // this.viewMsgReceiver()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      },
      unitUser: {
        handler(newVal, oldVal) {
          if(newVal && this.temp.receiverGroupIds) {
            this.temp.receiver = []
            let gids = this.temp.receiverGroupIds.split(',')
            gids.forEach(gid => {
              let obj = newVal.filter(item => item.key.indexOf(gid) > -1)
              if(obj && obj.length > 0) {
                this.temp.receiver.push(obj[0].key)
              }
            })
          }
        },
        immediate: true,
        deep: true
      }
    }
  }
</script>

<style scoped lang="scss">
//>>>.vditor-panel.vditor-panel--none {
//  display: none !important;
//}
>>> button[data-type="remove"] {
  display: none !important;
}
</style>
