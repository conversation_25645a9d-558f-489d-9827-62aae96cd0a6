import axios from '@/core/http/axios'

export function getUnitsByUnitCode(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/common/getUnitsByUnitCode?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/common/getUnitsByUnitCode`,
    method: "get",
    params
  });
}

// 获取是否上链
export function getIsBaas(params) {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/common/getIsBaas?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/common/getIsBaas`,
    method: 'get',
    params
  })
}
