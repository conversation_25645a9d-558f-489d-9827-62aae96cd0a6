<template>
  <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;" ref="myScrollbar">
    <div>
      <el-card>
        <div class="maxbox">
          <p class="title">
            基本信息
            <perm-button label="编辑" class="save-btn" type="primary" size="mini" icon="edit" @click="editMode = true" v-show="!editMode" />
            <perm-button label="保存" class="save-btn" type="primary" size="mini" icon="uims-icon-save" @click="updateUserInfo()" v-show="editMode" :loading="okLoading" />
            <perm-button label="取消" class="cancel-btn" size="mini" icon="uims-icon-cancel" @click="cancelUserInfo()" v-show="editMode" />
          </p>
          <el-form ref="dataForm" :model="temp" :rules="rules" label-position="right" label-width="125px">
            <el-form-item label="用户名">
              <el-input v-model="temp.userName" :disabled="true" />
            </el-form-item>
            <el-form-item label="所属机构">
              <el-input v-model="temp.unitName" :disabled="true" />
            </el-form-item>
  <!--          <el-form-item label="所属部门">-->
  <!--            <el-input v-model="temp.deptName" :disabled="true" />-->
  <!--          </el-form-item>-->
            <el-form-item label="证件号码">
              <el-input v-model="temp.idCard" :disabled="!editMode" maxLength="32"/>
            </el-form-item>
            <el-form-item label="地址">
              <el-input v-model="temp.address" :disabled="!editMode" maxLength="200"/>
            </el-form-item>
            <el-form-item label="电话号码" prop="mobile">
              <el-input v-model="temp.mobile" :disabled="!editMode" maxLength="50"/>
            </el-form-item>
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="temp.email" :disabled="!editMode" maxLength="50"/>
            </el-form-item>
            <el-form-item label="自我介绍">
              <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" style="resize: none" :disabled="!editMode" maxLength="200" />
            </el-form-item>
          </el-form>
        </div>
        <div class="maxbox">
          <p class="title">
            授权信息
          </p>
          <el-form ref="dataForm2" label-position="right" label-width="125px">
            <el-form-item label="已授权系统">
              <template v-if="privSystem && privSystem.length > 0">
                <el-tag class="tag-margin" :title="item.label" v-for="item of privSystem" :key="item.key" effect="plain">
                  {{ item.label }}
                </el-tag>
              </template>
            </el-form-item>
            <el-form-item label="已授权角色">
              <table-plus :key="0" :data="list" border fit stripe highlight-current-row style="width: 100%;" height="200">
                <el-table-column label="角色名称" width="400" prop="roleName" show-overflow-tooltip></el-table-column>
                <el-table-column label="角色编码" width="200" prop="roleCode" show-overflow-tooltip></el-table-column>
              </table-plus>
            </el-form-item>
            <el-form-item label="已授权岗位">
              <template v-if="privPost && privPost.length > 0">
                <el-tag class="tag-margin" :title="item.label" v-for="item of privPost" :key="item.key" effect="plain">
                  {{ item.label }}
                </el-tag>
              </template>
            </el-form-item>
            <el-form-item label="已授权用户组">
              <template v-if="privGroup && privGroup.length > 0">
                <el-tag class="tag-margin" :title="item.label" v-for="item of privGroup" :key="item.key" effect="plain">
                  {{ item.label }}
                </el-tag>
              </template>
            </el-form-item>
            <el-form-item label="已授权行权限" v-if="false">
              <template v-if="privRow && privRow.length > 0">
                <el-tag class="tag-margin" :title="item.label" v-for="item of privRow" :key="item.key" effect="plain">
                  {{ item.label }}
                </el-tag>
              </template>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>
  </el-scrollbar>
</template>
<script>
import { checkEmail, checkPhone } from "@/core/utils/validate"
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus"
import {resourceCode} from "@/biz/http/settings";

export default {
  components: { PermButton, TablePlus },
  data() {
    return {
      editMode: false,
      rules: {
        mobile: [{ validator: checkPhone, trigger: 'blur' }],
        email: [{ validator: checkEmail, trigger: 'blur' }]
      },
      okLoading: false,
      tableData: [],
      oldTemp: {},
      temp: {
        userId: undefined,
        unionId: undefined,
        userName: undefined,
        password: undefined,
        rePassword: undefined,
        idCard: undefined,
        userType: undefined,
        unitId: undefined,
        unitName: undefined,
        deptId: undefined,
        deptName: undefined,
        mobile: undefined,
        address: undefined,
        email: undefined,
        passwordStatus: undefined,
        enabled: '1',
        deleted: '0',
        createUserId: undefined,
        createTime: undefined,
        modifyUserId: undefined,
        modifyTime: undefined,
        remark: undefined
      },
      list: [],
      privSystem: [],
      privPost: [],
      privGroup: [],
      privRow: []
    }
  },
  methods: {
    loadUser() {
      let userId = JSON.parse(sessionStorage.getItem('user')).userId
      this.$api.user.getById({ userId: userId }, resourceCode.userInfo).then((res) => {
        this.temp = Object.assign({}, res.data)
        this.oldTemp = Object.assign({}, res.data)
      })
    },
    cancelUserInfo() {
      this.editMode = false
      this.temp = Object.assign({}, this.oldTemp)
      this.$refs['dataForm'].clearValidate()
    },
    updateUserInfo() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.okLoading = true
          let userInfo = {
            address: this.temp.address,
            idCard: this.temp.idCard,
            mobile: this.temp.mobile,
            email: this.temp.email,
            remark: this.temp.remark
          }
          this.$api.user.updateUserInfo(userInfo, resourceCode.userInfo).then((res) => {
            this.loadUser()
            this.$notify({
              title: '操作成功',
              message: '编辑基本信息成功',
              type: 'success',
              duration: 2000
            })
            this.editMode = false
            this.okLoading = false
          }).catch(() => {
            this.okLoading = false
          })
        }
      })
    },
    getPriv() {
      let userId = JSON.parse(sessionStorage.getItem('user')).userId
      this.$api.user.getPriv({ userId: userId }, resourceCode.userInfo).then((res) => {
        if (res.data) {
          if (res.data.sysSystems && res.data.sysSystems.length > 0) {
            for (let item of res.data.sysSystems) {
              this.privSystem.push({ key: item.systemId, label: item.sysName })
            }
          }
          if (res.data.roles && res.data.roles.length > 0) {
            for (let item of res.data.roles) {
              this.list.push(item)
            }
          }
          if (res.data.groups && res.data.groups.length > 0) {
            for (let item of res.data.groups) {
              this.privGroup.push({ key: item.groupId, label: item.groupName })
            }
          }
          if (res.data.posts && res.data.posts.length > 0) {
            for (let item of res.data.posts) {
              this.privPost.push({ key: item.postId, label: item.postName })
            }
          }
          if (res.data.dataRules && res.data.dataRules.length > 0) {
            for (let item of res.data.dataRules) {
              this.privRow.push({ key: item.drrId, label: item.dataruleName })
            }
          }
        }
      })
    }
  },
  created() {
    this.loadUser()
    this.getPriv()
  }
}
</script>
<style scoped lang="scss">
.maxbox {
  .title {
    font-weight: bold;
    font-size: 14px;
    height: 35px;
    line-height: 35px;
    border-bottom: 1px solid #bac0cc;
    button {
      float: right;
    }
  }
  .tag-margin {
    margin-right: 10px;
    max-width: 100%;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
  }
}
</style>
