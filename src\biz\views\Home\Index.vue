<template>
  <el-container class="page-container">
    <el-main>
      <div class="contain">
        <div class="myWork">
          <MyWork></MyWork>
        </div>
        <div class="movings"></div>
      </div>
      <div class="contain">
        <ResourceTab />
        <AbilityTab />
      </div>
      <div class="contain">
        <TopicNav></TopicNav>
      </div>
      <div class="contain">
        <HotAndNew></HotAndNew>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import PermButton from '@/core/components/PermButton'
import TablePlus from '@/core/components/TablePlus'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '../../../core/components/SelectPlus/index.vue'
import MyWork from './components/MyWork.vue'
import TopicNav from './components/TopicNav.vue'
import HotAndNew from './components/HotAndNew.vue'
import ResourceTab from './components/ResourceTab.vue'
import AbilityTab from './components/AbilityTab.vue'
export default {
  name: 'polyMonitor',
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    TablePlus,
    PermButton,
    TableFooter,
    PermButtonGroup,
    MyWork,
    TopicNav,
    HotAndNew,
    ResourceTab,
    AbilityTab
  },
  data() {
    return {
      queryBt: true,
      queryParamsList: [
        {
          queryName: '长江流域',
          queryValue: 86
        },
        {
          queryName: '长江流域',
          queryValue: 86
        },
        {
          queryName: '长江流域',
          queryValue: 86
        },
        {
          queryName: '长江流域',
          queryValue: 86
        },
        {
          queryName: '长江流域',
          queryValue: 86
        }
      ],
      checkList: [
        {
          checkName: '一般',
          checkValue: 20
        },
        {
          checkName: '重要',
          checkValue: 10
        }
      ],
      checked: false,
      sortItems: [
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      dataList: [
        {
          id: 1,
          itemTitle: '投资建设的固定资产投资表',
          itemStatus: ['有条件共享', '不予开放'],
          itemType: '1',
          unit: '投资处',
          updateCycle: '实时',
          pubTime: '2023-01-02 23:45:50',
          sourceSys: '投资管理系统',
          label: '1',
          sourceTypeList: ['1', '2'],
          visitNum: '2526',
          collectNum: '12'
        },
        {
          id: 2,
          itemTitle: '投资建设的固定资产投资表2',
          itemStatus: ['有条件共享'],
          itemType: '2',
          unit: '投资处2',
          updateCycle: '实时1',
          pubTime: '2023-02-02 23:45:50',
          sourceSys: '投资管理系统2',
          label: '1',
          sourceTypeList: ['2'],
          visitNum: '25264',
          collectNum: '121'
        },
        {
          id: 3,
          itemTitle: '投资建设的固定资产投资表3',
          itemStatus: ['不予开放'],
          itemType: '1',
          unit: '投资处3',
          updateCycle: '实时2',
          pubTime: '2023-03-02 23:45:50',
          sourceSys: '投资管理系统3',
          label: '2',
          sourceTypeList: ['1', '2'],
          visitNum: '25263',
          collectNum: '122'
        },
        {
          id: 4,
          itemTitle: '投资建设的固定资产投资表4',
          itemStatus: ['有条件共享', '不予开放'],
          itemType: '1',
          unit: '投资处4',
          updateCycle: '实时3',
          pubTime: '2023-04-02 23:45:50',
          sourceSys: '投资管理系统4',
          label: '2',
          sourceTypeList: ['1'],
          visitNum: '25262',
          collectNum: '123'
        },
        {
          id: 5,
          itemTitle: '投资建设的固定资产投资表5',
          itemStatus: ['有条件共享'],
          itemType: '2',
          unit: '投资处5',
          updateCycle: '实时4',
          pubTime: '2023-05-02 23:45:50',
          sourceSys: '投资管理系统5',
          label: '1',
          sourceTypeList: ['2'],
          visitNum: '25261',
          collectNum: '124'
        }
      ],
      treeDataIn: [
        {
          labelId: '1',
          labelName: '投资处',
          labelValue: '45',
          children: [
            {
              labelId: '11',
              labelName: '投资处1',
              labelValue: '145',
              children: [
                {
                  labelId: '111',
                  labelName: '投资处11',
                  labelValue: '145'
                },
                {
                  labelId: '211',
                  labelName: '投资处21',
                  labelValue: '145'
                }
              ]
            },
            {
              labelId: '21',
              labelName: '投资处2',
              labelValue: '145',
              children: []
            },
            {
              labelId: '31',
              labelName: '投资处3',
              labelValue: '145',
              children: []
            }
          ]
        },
        {
          labelId: '2',
          labelName: '计划处',
          labelValue: '45',
          children: [
            {
              labelId: '12',
              labelName: '计划处2',
              labelValue: '145',
              children: []
            },
            {
              labelId: '22',
              labelName: '计划处2',
              labelValue: '145',
              children: []
            },
            {
              labelId: '32',
              labelName: '计划处3',
              labelValue: '145',
              children: []
            }
          ]
        },
        {
          labelId: '3',
          labelName: '内事处',
          labelValue: '45',
          children: [
            {
              labelId: '13',
              labelName: '内事处1',
              labelValue: '145',
              children: []
            },
            {
              labelId: '23',
              labelName: '内事处2',
              labelValue: '145',
              children: []
            },
            {
              labelId: '33',
              labelName: '内事处3',
              labelValue: '145',
              children: []
            }
          ]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'labelName'
      },
      listLoading: false,
      total: 0,
      currentRow: undefined,
      filters: {
        currentPage: 1,
        pageSize: 20,
        checkList: []
      }
    }
  },
  methods: {
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        console.log(item)
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        console.log(item)
      })
    },
    handleNodeClick(data) {
      console.log(data)
    },
    handleFilter() {
      this.$nextTick(() => {
        this.getList()
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.labelName.includes(value)
    },
    getList() {
      if (!this.queryBt) {
        return
      }
      this.queryBt = false
      this.listLoading = false
      this.$api.polyMonitor
        .getList(this.filters)
        .then((res) => {
          this.queryBt = true
          this.listLoading = false
          this.list = res.data ? res.data.records : []
          this.total = res.data ? res.data.total : 0
        })
        .catch((res) => {
          this.queryBt = true
        })
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeIn.filter(val)
      this.$refs.treeOut.filter(val)
    }
  },
  created() {}
}
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';

.page-container {
  width: 1600px;
  margin: 0 auto;
  >>> .el-main {
    padding: 0;
  }
  .contain {
    width: 100%;
    .myWork {
      width: 1156px;
      margin-right: 20px;
    }
  }
}
</style>
