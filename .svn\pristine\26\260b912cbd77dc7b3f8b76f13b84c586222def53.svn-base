import Cookies from 'js-cookie'

export default {
  state: {
    configs: config,
    appName: '', // 应用名称
    themeColor: '#4b4b4b', // 主题颜色
    menuColor: '#282828', // 菜单栏颜色
    oldThemeColor: '#4b4b4b', // 上一次主题颜色
    collapse: false, // 导航栏收缩状态
    menuRouteLoaded: false, // 菜单和路由是否已经加载
    systemDrawer: false, // 系统选择
    systemListDrawer: false, // 系统列表选择
    copyrightDrawer: false, // 系统关于
    thumbnail: localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')) : {}
  },
  getters: {
    collapse (state) { // 对应着上面state
      return state.collapse
    },
    configs (state) { // 对应着上面state
      return state.configs
    }
  },
  mutations: {
    onCollapse (state) { // 改变收缩状态
      state.collapse = !state.collapse
    },
    onDrawerSystem (state) { // 改变系统导航栏收缩状态
      state.systemDrawer = !state.systemDrawer
    },
    onDrawerListSystem (state) { // 改变系统导航栏收缩状态
      state.systemListDrawer = !state.systemListDrawer
    },
    closeDrawerSystem(state) {
      state.systemDrawer = false;
    },
    closeDrawerListSystem(state) {
      state.systemListDrawer = false;
    },
    onDrawerCopyright (state) { // 改变系统关于收缩状态
      state.copyrightDrawer = !state.copyrightDrawer
    },
    /**
     * 设置颜色
     * @param state
     * @param obj {themeColor,flag是否需要更新Cookie（false:不需要；true:需要）}
     */
    setThemeColor (state, obj) { // 改变主题颜色
      if (typeof obj.themeColor !== 'undefined') {
        state.oldThemeColor = state.themeColor
        // state.themeColor = (/^([0-9a-fA-F]{6}){1}$/g.test(themeColor) ? '#' + themeColor : themeColor)
        state.themeColor = obj.themeColor
        if (obj.flag) {
          Cookies.set('themeColor', obj.themeColor, {expires: 7})
        }
      }
    },
    menuRouteLoaded (state, menuRouteLoaded) { // 改变菜单和路由的加载状态
      state.menuRouteLoaded = menuRouteLoaded
    },
    appName (state, appName) { // 设置应用名称
      state.appName = appName
    },
    setConfigs (state, object) {
      state.configs = object
    },
    // 设置皮肤风格缩略图
    setThumbnail (state, thumbnail) {
      state.thumbnail = thumbnail;
    }
  },
  actions: {}
}
