<template>
  <el-button :size="size" :type="type" :title="title ? title : ''" :loading="loading" :disabled="!hasPerms(perms)" @click.stop="handleClick" v-if="hasPerms(perms) || isShowNoPermBtn">
    <template v-if="loading">
      {{label}}
    </template>
    <template v-else>
      <svg-icon v-if="icon" :icon-class="icon" /><div v-if="icon" class="gap"></div>{{label}}
    </template>
  </el-button>
</template>

<script>
  import {hasPermission} from '@/core/utils/permission.js'
  import {hasForbidProjectPermission} from '@/biz/utils/permission.js'

  export default {
    name: 'index',
    props: {
      title: {
        type: String,
        default: ''
      },
      label: { // 按钮显示文本
        type: String,
        default: ''
      },
      icon: { // 按钮显示图标
        type: String,
        default: ''
      },
      size: { // 按钮尺寸
        type: String,
        default: 'mini'
      },
      type: { // 按钮类型
        type: String,
        default: null
      },
      loading: { // 按钮加载标识
        type: Boolean,
        default: false
      },
      disabled: { // 按钮是否禁用
        type: Boolean,
        default: false
      },
      perms: { // 按钮权限标识，外部使用者传入
        type: String,
        default: null
      },
      projectId: { // 项目ID（若不为空，则按项目上的逻辑判断按钮是否有权限）
        type: String,
        default: null
      }
    },
    data () {
      return {
        isShowNoPermBtn: config.isShowNoPermBtn
      }
    },
    methods: {
      handleClick: function () {
        // 按钮操作处理函数
        this.$emit('click', {})
      },
      hasPerms: function (perms) {
        // 根据权限标识和外部指示状态进行权限判断
        if(this.projectId || String(this.projectId) === '0') {
          // 若项目优先，则由项目返回权限控制结果
          return hasForbidProjectPermission(perms, this.projectId)
        } else {
          // 按统一用户资源权限控制
          if (perms) {
              return hasPermission(perms) & !this.disabled
          } else{
              return !this.disabled
          }
        }
      }
    },
    mounted () {
    }
  }
</script>

<style scoped>
  .gap {
    display:inline-block;
    width: 2px;
  }
</style>
