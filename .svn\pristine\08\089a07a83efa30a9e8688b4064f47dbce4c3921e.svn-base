import axios from '@/core/http/axios'

/*
 * 日志模块
 */

// 日志列表
export const logList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/log/spaceLogList`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

// 异常日志列表
export const errorLogList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/log/spaceErrorList`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getLogById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/log/getLogById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getErrorById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/log/getErrorById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
