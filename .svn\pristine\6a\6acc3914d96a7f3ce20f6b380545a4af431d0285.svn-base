import axios from '@/core/http/axios'

export const getDictsByType = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/bizDict/getDictsByType`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/bizDict/list`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const save = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/bizDict/save`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const del = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/bizDict/del`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/bizDict/getById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
