<template>
  <el-container class="page-container">
    <el-main>
      <div class="basicMes">
        <div class="title">
          <span :title="pageData.powerName">{{ pageData.powerName }}</span>
        </div>
        <div class="centerPart">
          <div class="leftPart">
            <img src="~@/assets/img/pic-nengli.svg" alt="" />
          </div>
          <div class="middlePart">
            <el-row>
              <el-col :span="12" v-if="pageData.registerUnitName">
                <div class="singleMes">
                  提供单位：<span :title="pageData.registerUnitName">{{
                    pageData.registerUnitName
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.releaseTime">
                <div class="singleMes">
                  发布时间：<span>{{ pageData.releaseTime }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="pageData.updateTime">
                <div class="singleMes">
                  更新时间：<span>{{ pageData.updateTime }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.applicableTerminalNames">
                <div class="singleMes">
                  适用终端：<span>{{ pageData.applicableTerminalNames }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="pageData.powerTheme">
                <div class="singleMes">
                  能力主题：<span>{{ handlePowerTheme(pageData.powerTheme) }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="pageData.powerLabelNames">
                <div class="singleMes">
                  标签：<span>{{ pageData.powerLabelNames }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="pageData.powerScene">
                <div class="singleMes">
                  应用场景：<span :title="pageData.powerScene" style="max-width: 350px">{{
                    pageData.powerScene
                  }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="pageData.powerShape === 'API' && pageData.powerRemarkAnnexInfo">
              <el-col :span="12">
                <div class="singleMes" style="display: -webkit-box">
                  使用指南：
                  <div>
                    <el-tooltip
                      v-if="
                        pageData.isOrderFalg !== '1' &&
                        pageData.registerUnitCode !== (currentUser ? currentUser.unitCode : null)
                      "
                      content="请先订阅能力后下载"
                      placement="top"
                      effect="dark"
                    >
                      <el-link
                        :disabled="
                          pageData.isOrderFalg !== '1' &&
                          pageData.registerUnitCode !== (currentUser ? currentUser.unitCode : null)
                        "
                        type="primary"
                        @click="handleSDKDownloadFile(pageData.powerRemarkAnnexInfo)"
                      >
                        {{ pageData.powerRemarkAnnexInfo.annexName
                        }}<i class="el-icon-download"></i>
                      </el-link>
                    </el-tooltip>
                    <el-link
                      v-else
                      type="primary"
                      @click="handleSDKDownloadFile(pageData.powerRemarkAnnexInfo)"
                    >
                      {{ pageData.powerRemarkAnnexInfo.annexName }}<i class="el-icon-download"></i>
                    </el-link>
                  </div>
                </div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="16" v-if="pageData.powerRemark">
                <div class="singleMes">
                  能力简介：<span :title="pageData.powerRemark">{{ pageData.powerRemark }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="rightPart">
            <div class="itemStatus">
              <span
                style="color: #409eff; border: 1px solid #d9ecff; background-color: #ecf5ff"
                v-if="pageData.shareType"
                >{{ pageData.shareType }}</span
              >
            </div>
            <div class="itemSourceType" v-if="pageData.powerShape">
              <div class="itemSourceItem">
                <div class="iconSchema" v-if="pageData.powerShape === 'API'"></div>
                <div class="iconTable" v-else-if="pageData.powerShape === 'SDK'"></div>
                <span>{{ pageData.powerShape.toUpperCase() }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="bottomPart">
          <div class="itemNum">
            <span class="num">{{ pageData.orderSum || 0 }}次<span>订阅</span></span>
            <span class="num">{{ pageData.browseSum || 0 }}次<span>访问</span></span>
            <span class="num">{{ pageData.collectSum || 0 }}次<span>收藏</span></span>
          </div>
          <div class="itemHandleBtn">
            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              disabled
              v-if="
                pageData.registerUnitCode === (currentUser ? currentUser.unitCode : null) ||
                pageData.isOrderFalg === '1'
              "
            >
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              v-else
              class="icsp-button"
              plain
              @click.stop="handlePowerApply(pageData)"
            >
              <svg-icon icon-class="gxsl-apply-btn" />
              订阅
            </el-button>

            <el-button
              v-permission="'sjnl003'"
              class="icsp-button-grey"
              disabled
              v-if="pageData.registerUnitCode === (currentUser ? currentUser.unitCode : null)"
            >
              <svg-icon icon-class="gxsl-shoppingCart" />
              加入选数车
            </el-button>
            <el-button
              v-permission="'sjnl003'"
              v-else
              :disabled="queryBt"
              :class="pageData.isAddCartFalg === '1' ? 'icsp-button2' : 'icsp-button'"
              plain
              @click.stop="handleAddPowerToCart()"
            >
              <svg-icon icon-class="gxsl-shoppingCart" />
              {{ pageData.isAddCartFalg === '1' ? '取消加入选数车' : '加入选数车' }}
            </el-button>

            <el-button
              class="icsp-button-grey"
              disabled
              v-if="pageData.registerUnitCode === (currentUser ? currentUser.unitCode : null)"
            >
              <svg-icon icon-class="gxsl-collect" />
              收藏
            </el-button>
            <el-button
              v-else
              :class="pageData.isCollectFalg === '1' ? 'icsp-button2' : 'icsp-button'"
              plain
              :disabled="queryBt"
              @click.stop="handleCollect()"
            >
              <svg-icon icon-class="gxsl-collect" />
              {{ pageData.isCollectFalg === '1' ? '取消收藏' : '收藏' }}
            </el-button>

            <!--            <el-button class="icsp-button-grey" disabled v-if="pageData.registerUnitCode === (currentUser?currentUser.unitCode:null)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
            <!--            <el-button v-else class="icsp-button" plain @click.stop="handleFeedback(pageData)">-->
            <!--              <svg-icon icon-class="feedback" />-->
            <!--              问题反馈-->
            <!--            </el-button>-->
          </div>
        </div>
      </div>
      <div class="otherMes">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="使用指南" name="first" v-if="pageData.powerShape === 'API'">
            <div class="mainConte mainConteFirst">
              <div class="leftPart">
                <div
                  class="tableItem"
                  :title="item.encName"
                  :class="{ active: firstPaneActiveId === item.encVerId }"
                  v-for="item in pageData.apiEncList"
                  :key="item.encVerId"
                  @click="firstPaneTableCheck(item)"
                >
                  {{ item.encName }}
                </div>
              </div>
              <div class="rightPart">
                <div class="rightContent">
                  <div class="middleTitle" v-if="firstPaneActiveData.encName">
                    {{ firstPaneActiveData.encName }}
                  </div>
                  <div class="line"></div>
                  <div class="messageContent">
                    <div class="fun-info-p">
                      <span class="label"
                        >接口类型：<span class="content" v-if="pageData.apiType">{{
                          pageData.apiType
                        }}</span></span
                      >
                      <span
                        class="label"
                        style="margin-left: 50px"
                        v-if="firstPaneActiveData.respDataFormat"
                        >响应数据格式：<span class="content">{{
                          firstPaneActiveData.respDataFormat
                        }}</span></span
                      >
                    </div>
                    <div class="fun-info-p">
                      <span class="label" v-if="firstPaneActiveData.reqMethod"
                        >请求方式：<span class="content">{{
                          firstPaneActiveData.reqMethod
                        }}</span></span
                      >
                    </div>
                    <div
                      class="fun-info-p"
                      style="margin-bottom: 10px"
                      v-if="firstPaneActiveData && firstPaneActiveData.selectedAnnex"
                    >
                      <span class="label">接口说明文档：</span>
                      <template>
                        <el-tooltip
                          v-if="
                            pageData.isOrderFalg !== '1' &&
                            pageData.registerUnitCode !==
                              (currentUser ? currentUser.unitCode : null)
                          "
                          content="请先订阅能力后下载"
                          placement="top"
                          effect="dark"
                        >
                          <el-link
                            :disabled="pageData.isOrderFalg !== '1'"
                            type="primary"
                            @click="handleSDKDownloadFile(firstPaneActiveData.selectedAnnex)"
                          >
                            {{ firstPaneActiveData.selectedAnnex.annexName
                            }}<i class="el-icon-download"></i>
                          </el-link>
                        </el-tooltip>
                        <el-link
                          v-else
                          type="primary"
                          @click="handleSDKDownloadFile(firstPaneActiveData.selectedAnnex)"
                        >
                          {{ firstPaneActiveData.selectedAnnex.annexName
                          }}<i class="el-icon-download"></i>
                        </el-link>
                      </template>
                      <input
                        v-show="false"
                        type="file"
                        id="fileInput"
                        ref="fileInput"
                        @change="handleFileChange($event)"
                        accept="fileType"
                      />
                    </div>
                    <div
                      class="fun-info-p"
                      style="margin-bottom: 10px"
                      v-if="firstPaneActiveData.apiRemark"
                    >
                      <span class="label">接口说明：</span>
                      <el-input
                        style="margin-bottom: 2px"
                        v-model="firstPaneActiveData.apiRemark"
                        clearable
                        readonly
                        :autosize="{ minRows: 1, maxRows: 10 }"
                        type="textarea"
                        maxlength="500"
                      />
                    </div>
                    <div class="fun-info-table">
                      <div class="info-table">
                        <span class="label">请求参数：</span>
                        <el-table
                          :data="requestParams"
                          border
                          stripe
                          max-height="300"
                          class="projectTable"
                        >
                          <el-table-column
                            type="index"
                            label="排序"
                            width="80"
                            align="center"
                          ></el-table-column>
                          <el-table-column
                            prop="paramKey"
                            label="参数名称"
                            min-width="120"
                            header-align="center"
                          >
                          </el-table-column>
                          <el-table-column label="是否必填" align="center">
                            <template #default="scope">
                              <span>{{ scope.row.isMandatory === '0' ? '否' : '是' }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            prop="paramType"
                            label="参数类型"
                            align="center"
                          ></el-table-column>
                          <el-table-column
                            prop="paramPosition"
                            label="参数位置"
                            header-align="center"
                          >
                          </el-table-column>
                          <el-table-column
                            prop="paramRemark"
                            label="参数描述"
                            min-width="350"
                            header-align="center"
                          >
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                    <div
                      class="fun-info-p"
                      style="margin-bottom: 10px"
                      v-if="firstPaneActiveData.reqExample"
                    >
                      <span class="label">请求示例：</span>
                      <el-input
                        style="margin-bottom: 2px"
                        v-model="firstPaneActiveData.reqExample"
                        clearable
                        readonly
                        :autosize="{ minRows: 2, maxRows: 10 }"
                        type="textarea"
                        maxlength="500"
                      />
                    </div>
                    <div class="fun-info-table">
                      <div class="info-table">
                        <span class="label">返回参数：</span>
                        <el-table
                          :data="responseParams"
                          border
                          stripe
                          max-height="300"
                          class="projectTable"
                        >
                          <el-table-column
                            type="index"
                            label="排序"
                            width="80"
                            align="center"
                          ></el-table-column>
                          <el-table-column
                            prop="paramKey"
                            label="参数名称"
                            min-width="120"
                            header-align="center"
                          >
                          </el-table-column>
                          <el-table-column label="是否必填" align="center">
                            <template #default="scope">
                              <span>{{ scope.row.isMandatory === '0' ? '否' : '是' }}</span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            prop="paramType"
                            label="参数类型"
                            align="center"
                          ></el-table-column>
                          <el-table-column
                            prop="paramPosition"
                            label="参数位置"
                            header-align="center"
                          >
                          </el-table-column>
                          <el-table-column
                            prop="paramRemark"
                            label="参数描述"
                            min-width="350"
                            header-align="center"
                          >
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                    <div class="fun-info-p" v-if="firstPaneActiveData.respExampleSuccess">
                      <span class="label">返回成功示例：</span>
                      <el-input
                        style="margin-bottom: 2px"
                        v-model="firstPaneActiveData.respExampleSuccess"
                        clearable
                        readonly
                        :autosize="{ minRows: 2, maxRows: 10 }"
                        type="textarea"
                        maxlength="500"
                      />
                    </div>
                    <div class="fun-info-p" v-if="firstPaneActiveData.respExampleError">
                      <span class="label">返回失败示例：</span>
                      <el-input
                        style="margin-bottom: 2px"
                        v-model="firstPaneActiveData.respExampleError"
                        clearable
                        readonly
                        :autosize="{ minRows: 2, maxRows: 10 }"
                        type="textarea"
                        maxlength="500"
                      />
                    </div>
                    <div
                      class="fun-info-p"
                      style="margin-bottom: 10px"
                      v-if="firstPaneActiveData.useExample"
                    >
                      <span class="label">使用示例：</span>
                      <el-input
                        style="margin-bottom: 2px"
                        v-model="firstPaneActiveData.useExample"
                        clearable
                        readonly
                        :autosize="{ minRows: 2, maxRows: 10 }"
                        type="textarea"
                        maxlength="500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="能力下载" name="first" v-if="pageData.powerShape === 'SDK'">
            <div class="mainConte">
              <div class="fullPart">
                <div class="fullContent">
                  <el-table :data="pageData.fileList" border stripe style="width: 100%">
                    <el-table-column
                      type="index"
                      label="序号"
                      width="80"
                      align="center"
                    ></el-table-column>
                    <el-table-column
                      prop="name"
                      label="文件类型"
                      min-width="80"
                      align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="createTime"
                      label="上传日期"
                      min-width="180"
                      align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column
                      prop="annexName"
                      label="附件名称"
                      min-width="180"
                      header-align="center"
                      show-overflow-tooltip
                    ></el-table-column>
                    <el-table-column prop="" label="操作" align="center">
                      <template slot-scope="scope">
                        <el-link
                          v-if="pageData.isOrderFalg === '1'"
                          type="primary"
                          @click="handleSDKDownloadFile(scope.row)"
                          >下载<i class="el-icon-download"></i>
                        </el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <!-- 数据订阅 -->
        <SubscribeDialog
          :dialogVisible.sync="dialogVisibleApply"
          :type="type"
          :title="title"
          :data="dialogData"
        ></SubscribeDialog>
      </div>
    </el-main>
  </el-container>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index'
import SubscribeDialog from '@/biz/components/SubscribeDialog'
import FeedbackDialog from '@/biz/components/FeedbackDialog'
import Cookies from 'js-cookie'
import { FileUtils } from '@/biz/utils/download'
import { parseTime } from '@/core/utils/utils'
export default {
  name: 'pageList',
  components: {
    SelectPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog,
    FeedbackDialog
  },
  props: {},
  data() {
    return {
      currentUser: JSON.parse(sessionStorage.getItem('user')),
      queryBt: false,
      pageData: {},
      activeName: 'first',
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      feedbackVisible: false,
      tableList: [],
      firstPaneActiveId: '',
      firstPaneActiveData: null,
      activeFunName: 0,
      annexList: {},
      tableData: [],
      mergeObj: {},
      mergeArr: ['dataIndex', 'ruleType'],
      tempInfo: {
        itemVoList: [
          {
            resourceName: '统一社会信用代码',
            dataType: '字符串',
            sensiLevel: '三级',
            fieldType: '无条件共享',
            openType: '无条件开放'
          },
          {
            resourceName: '法定代表人',
            dataType: '字符串',
            sensiLevel: '三级',
            fieldType: '无条件共享',
            openType: '无条件开放'
          }
        ],
        dataVoList: [
          {
            fieldName: '法定代表人',
            resourceName: '字符串',
            fieldLength: '三级',
            fieldType: '无条件共享',
            isPk: '无条件开放',
            nullable: '否'
          },
          {
            fieldName: '法定代表人',
            resourceName: '字符串',
            fieldLength: '三级',
            fieldType: '无条件共享',
            isPk: '无条件开放',
            nullable: '否'
          }
        ],
        serviceVoList: {
          resourceName: 'resourceName',
          interfaceType: 'interfaceType',
          description: 'description',
          serviceVersion: 'serviceVersion',
          userName: 'userName',
          createOrgName: 'createOrgName',
          createTime: 'createTime',
          authOrizattionMode: 'authOrizattionMode',
          procotol: '1',
          suppotUnit: 'suppotUnit',
          supportUnitContact: 'supportUnitContact',
          supportUnitPhone: 'supportUnitPhone',
          serviceOriginFile: null,
          funcList: [
            {
              funcName: 'funcName',
              desc: 'desc',
              requestMethod: 'requestMethod',
              returnType: 'returnType',
              paramInList: [
                {
                  name: 'name',
                  type: 'type',
                  length: 'length',
                  position: 'position',
                  isNull: 'isNull',
                  desc: 'desc'
                }
              ],
              paramOutList: [
                {
                  name: 'name',
                  type: 'type',
                  length: 'length',
                  position: 'position',
                  isNull: 'isNull',
                  desc: 'desc'
                }
              ],
              requestExample: '',
              responseExampleSucc: '',
              responseExampleFail: ''
            }
          ]
        },
        fileVoList: [
          {
            resourceName: 'resourceName',
            fileType: 'fileType',
            fileSize: 'fileSize',
            uploadtime: 'uploadtime',
            description: 'description',
            fileOrder: 'fileOrder',
            isUpLoad: 'isUpLoad'
          }
        ],
        fileFolderVoList: [
          {
            resourceName: 'resourceName',
            fileType: 'fileType',
            uploadtime: 'uploadtime',
            description: 'description'
          }
        ]
      }
    }
  },
  computed: {
    requestParams() {
      return (this.firstPaneActiveData.apiEncParamVerList || []).filter(
        (item) => item.paramStyleCode === 'REQ'
      )
    },
    responseParams() {
      return (this.firstPaneActiveData.apiEncParamVerList || []).filter(
        (item) => item.paramStyleCode === 'RESP'
      )
    }
  },
  methods: {
    handleClick(tab, event) {},
    firstPaneTableCheck(data) {
      this.firstPaneActiveId = data.encVerId
      this.firstPaneActiveData = data
      const annexList = this.pageData.annexList || []
      const foundAnnex = annexList.find((annex) => {
        return annex.annexId === this.firstPaneActiveData.apiRemarkAnnex
      })
      if (foundAnnex) {
        this.firstPaneActiveData.selectedAnnex = foundAnnex
      }
    },
    // 能力收藏
    handleCollect() {
      if (this.isLogOn()) {
        if (this.pageData.isCollectFalg !== '1') {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addPowerToFavorites({ powerId: this.pageData.powerId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('收藏成功')
                this.$set(this.pageData, 'isCollectFalg', '1')
                this.$set(this.pageData, 'collectSum', (this.pageData.collectSum += 1))
                // this.getPowerDetail()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否取消收藏该能力?', '取消收藏', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .cancelPowerToFavorites({ powerId: this.pageData.powerId })
              .then((res) => {
                if (res.code === '200') {
                  // this.getPowerDetail()
                  this.$message({
                    type: 'success',
                    message: '取消收藏成功!'
                  })
                  this.$set(this.pageData, 'isCollectFalg', '0')
                  this.$set(this.pageData, 'collectSum', (this.pageData.collectSum -= 1))
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    // 能力订阅
    handlePowerApply(data) {
      if (this.isLogOn()) {
        if (data.isEnabled === '0') {
          this.$confirm('当前订阅能力是停用状态，订阅通过后也无法使用，是否确定继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(async () => {
            this.type = 'power'
            this.title = '能力订阅'
            this.dialogData = data
            this.dialogVisibleApply = true
            this.dialogData.powerId = data.powerId
          })
        } else {
          this.type = 'power'
          this.title = '能力订阅'
          this.dialogData = data
          this.dialogVisibleApply = true
          this.dialogData.powerId = data.powerId
        }
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart() {
      if (this.isLogOn()) {
        if (this.pageData.isAddCartFalg !== '1') {
          this.queryBt = true
          this.$api.bizApi.pageRequest
            .addPowerToCart({ powerId: this.pageData.powerId })
            .then((res) => {
              if (res.code === '200') {
                this.$message.success('加入选数车成功')
                this.$set(this.pageData, 'isAddCartFalg', '1')
                // this.getPowerDetail()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((res) => {
              this.$message.error(res)
            })
            .finally(() => {
              this.queryBt = false
            })
        } else {
          this.$confirm('是否从选数车中取消该能力?', '取消确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.bizApi.pageRequest
              .delPowerFromCart({ powerId: this.pageData.powerId })
              .then((res) => {
                if (res.code === '200') {
                  // this.getPowerDetail()
                  this.$message.success('取消成功!')
                  this.$set(this.pageData, 'isAddCartFalg', '0')
                } else {
                  this.$message.error(res.message)
                }
              })
              .catch((e) => {
                this.$message.error(e)
              })
          })
        }
      }
    },
    // 问题反馈
    handleFeedback(data) {
      if (this.isLogOn()) {
        this.dialogData = data
        this.feedbackVisible = true
        this.dialogData.powerId = data.powerId
      }
    },
    handlePowerTheme(data) {
      if (data) {
        let str = ''
        for (const item of data.split(',')) {
          str += this.handlePowerSceneCode(item) + ','
        }
        str = str.slice(0, str.length - 1)
        return str
      } else {
        return ''
      }
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    handlePowerScene(val) {
      switch (val) {
        case '1':
          return '政务服务'
        case '2':
          return '公共服务'
        case '3':
          return '监管'
        case '4':
          return '其他'
        default:
          break
      }
    },
    // 获取能力详情
    getPowerDetail() {
      this.$api.bizApi.pageRequest
        .getPowerDetail({
          powerId: this.$route.query.powerId
        })
        .then((res) => {
          this.pageData = res.data
          if (res.data.powerShape === 'API') {
            this.firstPaneActiveId = res.data.apiEncList[0].encVerId
            this.firstPaneActiveData = res.data.apiEncList[0]
            if (res.data.annexList.length > 0) {
              this.annexList = res.data.annexList[0]
            }
            const annexList = this.pageData.annexList || []
            const foundAnnex = annexList.find((annex) => {
              return annex.annexId === this.firstPaneActiveData.apiRemarkAnnex
            })
            if (foundAnnex) {
              this.firstPaneActiveData.selectedAnnex = foundAnnex
            }
          } else {
            let fileList = []
            fileList.push({
              ...res.data.sdkEncList[0].encFileInfo,
              name: '封装文件'
            })
            fileList.push({
              ...res.data.sdkEncList[0].useExampleInfo,
              name: '使用示例'
            })
            fileList.push({
              ...res.data.sdkEncList[0].useGuideInfo,
              name: '使用指南'
            })
            fileList.forEach((item) => {
              item.createTime = parseTime(item.createTime)
            })
            this.pageData.fileList = fileList
          }
        })
    },
    // 附件下载
    handleDownloadFile(data) {
      if (this.pageData.isEnabled === '0') {
        this.$message.info('当前能力已停用，无法下载。')
        return
      }
      this.$api.bizApi.pageRequest.handleDownloadFile(data.annexId).then((res) => {
        FileUtils.fileDownload([res.data], data.annexName)
      })
    },
    // SDK相关下载
    handleSDKDownloadFile(data) {
      if (this.pageData.isEnabled === '0') {
        this.$message.info('当前能力已停用，无法下载。')
        return
      }
      if (data.name && data.name === '封装文件') {
        this.$api.bizApi.pageRequest
          .downloadPowerSdk({ applyId: this.pageData.applyId, annexId: data.annexId })
          .then((res) => {
            FileUtils.fileDownload([res.data], data.annexName)
          })
      } else {
        this.$api.bizApi.pageRequest.handleDownloadFile(data.annexId).then((res) => {
          FileUtils.fileDownload([res.data], data.annexName)
        })
      }
    }
  },
  mounted() {
    this.getPowerDetail()
  }
}
</script>

<style scoped lang="scss">
@import '../../../assets/global.scss';
@import '@/assets/detail.scss';

.tableItem {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 230px;
}

.powerRemark {
  width: 800px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.messageContent {
  padding: 15px 20px;

  .fun-info-p {
    font-size: 16px;
    line-height: 40px;
    margin-bottom: 10px;

    .label {
      color: var(--black);
    }

    .content {
      color: #666666;
    }
  }

  .fun-info-table {
    font-size: 16px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    .info-table {
      display: flex;
      margin-bottom: 10px;
    }

    .label {
      color: var(--black);
    }

    .content {
      color: #666666;
    }
  }
}
.singleMes {
  display: flex;
  span {
    max-width: 420px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
/deep/.el-table th.el-table__cell {
  background-color: #4eacfe;
  padding: 12px 0;
  .cell {
    background-color: #4eacfe;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
