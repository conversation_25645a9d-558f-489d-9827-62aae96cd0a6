/*
 * 接口统一集成模块
 */
import * as authority from './modules/authority'
import * as login from './modules/login'
import * as user from './modules/user'
import * as userUnion from './modules/userUnion'
import * as dict from './modules/dict'
import * as log from './modules/log'
import * as spaceLog from './modules/spaceLog'
import * as config from './modules/config'
import * as codeRule from './modules/codeRule'
import * as bizApi from 'biz/http/api'
import * as version from './modules/version'
import * as userDelegatePriv from './modules/userDelegatePriv'
import * as system from './modules/system'
import * as bizDict from './modules/bizDict'
import * as msgBiz from './modules/msgBiz'
import * as msgNotice from './modules/msgNotice'
import * as msgOpen from './modules/msgOpen'
import * as msgOutBox from './modules/msgOutBox'
import * as msgInBox from './modules/msgInBox'
import * as msgUserConfig from './modules/msgUserConfig'
import * as cleanData from './modules/cleanData'
// 默认全部导出
export default {
  authority,
  login,
  user,
  userUnion,
  dict,
  log,
  spaceLog,
  config,
  codeRule,
  bizApi,
  version,
  userDelegatePriv,
  system,
  bizDict,
  msgBiz,
  msgOpen,
  msgNotice,
  msgOutBox,
  msgInBox,
  msgUserConfig,
  cleanData
}
