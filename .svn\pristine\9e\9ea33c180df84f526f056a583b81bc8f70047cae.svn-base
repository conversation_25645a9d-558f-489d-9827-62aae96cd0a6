/*!
 * Vditor v3.3.3 - A markdown editor written in TypeScript.
 *   
 * MIT License
 * 
 * Copyright (c) 2018-present B3log 开源, b3log.org
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * 
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Vditor=t():e.Vditor=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=73)}([function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return s})),n.d(t,"e",(function(){return l})),n.d(t,"h",(function(){return c})),n.d(t,"c",(function(){return d})),n.d(t,"g",(function(){return u})),n.d(t,"f",(function(){return p})),n.d(t,"d",(function(){return h}));var r=n(2),i=n(1),o=n(3),a=function(e){var t;return getSelection().rangeCount>0&&(t=getSelection().getRangeAt(0),e.isEqualNode(t.startContainer)||e.contains(t.startContainer))||(e.focus(),(t=e.ownerDocument.createRange()).setStart(e,0),t.collapse(!0)),t},s=function(e){var t=window.getSelection().getRangeAt(0);if(!e.contains(t.startContainer)&&!Object(o.e)(t.startContainer,"vditor-panel--none"))return{left:0,top:0};var n,r=e.parentElement.getBoundingClientRect();if(0===t.getClientRects().length)if(3===t.startContainer.nodeType){var i=t.startContainer.parentElement;if(!(i&&i.getClientRects().length>0))return{left:0,top:0};n=i.getClientRects()[0]}else{var a=t.startContainer.children;if(a[t.startOffset]&&a[t.startOffset].getClientRects().length>0?n=a[t.startOffset].getClientRects()[0]:t.startContainer.childNodes.length>0?(t.selectNode(t.startContainer.childNodes[Math.max(0,t.startOffset-1)]),n=t.getClientRects()[0],t.collapse(!1)):n=t.startContainer.getClientRects()[0],!n){for(var s=t.startContainer.childNodes[t.startOffset];!s.getClientRects||s.getClientRects&&0===s.getClientRects().length;)s=s.parentElement;n=s.getClientRects()[0]}}else n=t.getClientRects()[0];return{left:n.left-r.left,top:n.top-r.top}},l=function(e,t){if(!t){if(0===getSelection().rangeCount)return!1;t=getSelection().getRangeAt(0)}var n=t.commonAncestorContainer;return e.isEqualNode(n)||e.contains(n)},c=function(e){var t=window.getSelection();t.removeAllRanges(),t.addRange(e)},d=function(e,t){var n={end:0,start:0};if(!t){if(0===getSelection().rangeCount)return n;t=window.getSelection().getRangeAt(0)}if(l(e,t)){var r=t.cloneRange();e.childNodes[0]&&e.childNodes[0].childNodes[0]?r.setStart(e.childNodes[0].childNodes[0],0):r.selectNodeContents(e),r.setEnd(t.startContainer,t.startOffset),n.start=r.toString().length,n.end=n.start+t.toString().length}return n},u=function(e,t,n){var r=0,i=0,o=n.childNodes[i],a=!1,s=!1;e=Math.max(0,e),t=Math.max(0,t);var l=n.ownerDocument.createRange();for(l.setStart(o||n,0),l.collapse(!0);!s&&o;){var d=r+o.textContent.length;if(!a&&e>=r&&e<=d&&(0===e?l.setStart(o,0):3===o.childNodes[0].nodeType?l.setStart(o.childNodes[0],e-r):o.nextSibling?l.setStartBefore(o.nextSibling):l.setStartAfter(o),a=!0,e===t)){s=!0;break}a&&t>=r&&t<=d&&(0===t?l.setEnd(o,0):3===o.childNodes[0].nodeType?l.setEnd(o.childNodes[0],t-r):o.nextSibling?l.setEndBefore(o.nextSibling):l.setEndAfter(o),s=!0),r=d,o=n.childNodes[++i]}return!s&&n.childNodes[i-1]&&l.setStartBefore(n.childNodes[i-1]),c(l),l},p=function(e,t){var n=e.querySelector("wbr");if(n){if(n.previousElementSibling)if(n.previousElementSibling.isSameNode(n.previousSibling)){if(n.previousElementSibling.lastChild)return t.setStartBefore(n),t.collapse(!0),c(t),!Object(i.c)()||"EM"!==n.previousElementSibling.tagName&&"STRONG"!==n.previousElementSibling.tagName&&"S"!==n.previousElementSibling.tagName||(t.insertNode(document.createTextNode(r.a.ZWSP)),t.collapse(!1)),void n.remove();t.setStartAfter(n.previousElementSibling)}else t.setStart(n.previousSibling,n.previousSibling.textContent.length);else n.previousSibling?t.setStart(n.previousSibling,n.previousSibling.textContent.length):n.nextSibling?3===n.nextSibling.nodeType?t.setStart(n.nextSibling,0):t.setStartBefore(n.nextSibling):t.setStart(n.parentElement,0);t.collapse(!0),n.remove(),c(t)}},h=function(e,t){var n=document.createElement("div");n.innerHTML=e;var r=n.querySelectorAll("p");1!==r.length||r[0].previousSibling||r[0].nextSibling||("wysiwyg"===t.currentMode&&t.wysiwyg.element.children.length>0||"ir"===t.currentMode&&t.ir.element.children.length>0)&&(e=r[0].innerHTML.trim());var i=document.createElement("template");i.innerHTML=e;var s=a(t[t.currentMode].element);""!==s.toString()&&"sv"!==t.currentMode&&(t[t.currentMode].preventInput=!0,document.execCommand("delete",!1,""));var l=Object(o.c)(s.startContainer);i.content.firstElementChild&&"0"===i.content.firstElementChild.getAttribute("data-block")&&l?l.insertAdjacentHTML("afterend",e):(s.insertNode(i.content.cloneNode(!0)),s.collapse(!1))}},function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"e",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"d",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"c",(function(){return c}));var r=function(){return navigator.userAgent.indexOf("Safari")>-1&&-1===navigator.userAgent.indexOf("Chrome")},i=function(){return navigator.userAgent.toLowerCase().indexOf("firefox")>-1},o=function(){try{return"undefined"!=typeof localStorage}catch(e){return!1}},a=function(){return navigator.userAgent.indexOf("iPhone")>-1?"touchstart":"click"},s=function(e){return navigator.platform.toUpperCase().indexOf("MAC")>=0?!(!e.metaKey||e.ctrlKey):!(e.metaKey||!e.ctrlKey)},l=function(e){return/Mac/.test(navigator.platform)||"iPhone"===navigator.platform?(e=e.replace("ctrl","⌘").replace("shift","⇧").replace("alt","⌥")).indexOf("⇧")>-1&&(e=i()?e.replace(";",":").replace("=","+"):e.replace(":",";").replace("+","=").replace("_","-")):(e=e.replace("⌘","ctrl").replace("⇧","shift").replace("⌥","alt")).indexOf("shift")>-1&&(e=e.replace(";",":").replace("=","+")),e},c=function(){return/Chrome/.test(navigator.userAgent)&&/Google Inc/.test(navigator.vendor)}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r="3.3.3",i=function(){function e(){}return e.ZWSP="​",e.MOBILE_WIDTH=520,e.CLASS_MENU_DISABLED="vditor-menu--disabled",e.EDIT_TOOLBARS=["emoji","headings","bold","italic","strike","link","list","ordered-list","outdent","indent","check","line","quote","code","inline-code","insert-after","insert-before","upload","record","table"],e.CONTENT_THEME=["dark","light","wechat"],e.CODE_THEME=["abap","algol","algol_nu","arduino","autumn","borland","bw","colorful","dracula","emacs","friendly","fruity","github","igor","lovelace","manni","monokai","monokailight","murphy","native","paraiso-dark","paraiso-light","pastie","perldoc","pygments","rainbow_dash","rrt","solarized-dark","solarized-dark256","solarized-light","swapoff","tango","trac","vim","vs","xcode"],e.CODE_LANGUAGES=["mermaid","echarts","mindmap","abc","graphviz","apache","bash","cs","cpp","css","coffeescript","diff","xml","http","ini","json","java","javascript","js","makefile","markdown","nginx","objectivec","php","perl","properties","python","ruby","sql","shell","dart","erb","go","gradle","julia","kotlin","less","lua","matlab","rust","scss","typescript","ts","yaml"],e}()},function(e,t,n){"use strict";n.d(t,"g",(function(){return i})),n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"f",(function(){return s})),n.d(t,"e",(function(){return l})),n.d(t,"h",(function(){return c})),n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return u}));var r=n(6),i=function(e,t){for(var n=l(e,t),r=!1,i=!1;n&&!n.classList.contains("vditor-reset")&&!i;)(r=l(n.parentElement,t))?n=r:i=!0;return n||!1},o=function(e,t,n){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var r=e,i=!1;r&&!i&&!r.classList.contains("vditor-reset");)r.getAttribute(t)===n?i=!0:r=r.parentElement;return i&&r},a=function(e){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);var t=e,n=!1,r=o(e,"data-block","0");if(r)return r;for(;t&&!n&&!t.classList.contains("vditor-reset");)"H1"===t.tagName||"H2"===t.tagName||"H3"===t.tagName||"H4"===t.tagName||"H5"===t.tagName||"H6"===t.tagName||"P"===t.tagName||"BLOCKQUOTE"===t.tagName||"OL"===t.tagName||"UL"===t.tagName?n=!0:t=t.parentElement;return n&&t},s=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)n.nodeName===t?r=!0:n=n.parentElement;return r&&n},l=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)n.classList.contains(t)?r=!0:n=n.parentElement;return r&&n},c=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);var n=Object(r.b)(e,t),i=!1;n&&(i=Object(r.b)(n.parentElement,t));for(var o=!1;n&&!n.classList.contains("vditor-reset")&&!o;)i?(n=Object(r.b)(n.parentElement,t))&&(i=Object(r.b)(n.parentElement,t)):o=!0;return n||!1},d=function(e){var t=c(e,"UL"),n=c(e,"OL"),r=t;return n&&(!t||t&&n.contains(t))&&(r=n),r},u=function(e){for(;e&&e.lastChild;)e=e.lastChild;return e}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r={en_US:{alignCenter:"Center",alignLeft:"Left",alignRight:"Right",alternateText:"Alternate text",bold:"Blod",both:"editor & preview",check:"Task List",code:"Code Block","code-theme":"Code Block Theme Preview",column:"Column",confirm:"Confirm","content-theme":"Content Theme Preview",copied:"Copied",copy:"Copy","delete-column":"Delete Row","delete-row":"Delete Column",devtools:"DevTools",down:"Down",downloadTip:"The browser does not support the download function",edit:"Edit","edit-mode":"Toggle Edit Mode",emoji:"Emoji",export:"Export",fileTypeError:"file type is error",footnoteRef:"Footnote Ref",format:"Format",fullscreen:"Toggle Fullscreen",generate:"Generating",headings:"Headings",help:"Help",imageURL:"image URL",indent:"Indent",info:"Info","inline-code":"Inline Code","insert-after":"Insert line after","insert-before":"Insert line Before","insert-column":"Insert Column","insert-row":"Insert Row",instantRendering:"Instant Rendering",italic:"Italic",language:"Language",line:"Line",link:"Link",linkRef:"Link Ref",list:"List",more:"More",nameEmpty:"Name is empty","ordered-list":"Order List",outdent:"Outdent",outline:"Outline",over:"over",performanceTip:"Real-time preview requires ${x}ms, you can close it",preview:"Preview",quote:"Quote",record:"Start Record/End Record","record-tip":"The device does not support recording",recording:"recording...",redo:"Redo",remove:"Remove",row:"Row",splitView:"Split View",strike:"Strike",table:"Table",textIsNotEmpty:"text(no empty)",tooltipText:"Tooltip text",undo:"Undo",up:"Up",update:"Update",upload:"Upload image or file",uploadError:"upload error",uploading:"uploading...",wysiwyg:"WYSIWYG"},ko_KR:{alignCenter:"가운데",alignLeft:"왼쪽",alignRight:"오른쪽",alternateText:"이미지 태그",bold:"굵게",both:"에디터 & 미리보기",check:"체크박스",code:"코드블럭삽입","code-theme":"코드블럭테마",column:"행",confirm:"확인","content-theme":"컨텐츠테마",copied:"복사완료",copy:"복사","delete-column":"열 삭제","delete-row":"행 삭제",devtools:"개발툴",down:"다운",downloadTip:"브라우저가 다운로드 기능을 지원하지 않습니다",edit:"수정","edit-mode":"편집모드",emoji:"이모지",export:"내보내기",fileTypeError:"지원하지않습니다.",footnoteRef:"각주참조",format:"형식",fullscreen:"전체화면",generate:"생성",headings:"제목크기",help:"도움말",imageURL:"이미지 URL",indent:"들여쓰기",info:"정보","inline-code":"인라인코드","insert-after":"블락 뒤로 입력","insert-before":"블락 앞으로 입력","insert-column":"열 삽입","insert-row":"행 삽입",instantRendering:"타이포라",italic:"기울임꼴",language:"언어",line:"문단나눔",link:"링크",linkRef:"링크 참조",list:"순서없는 목록",more:"더보기",nameEmpty:"이름이 비어있습니다.","ordered-list":"순서있는 목록",outdent:"내어쓰기",outline:"개요",over:"오버",performanceTip:"실시간 미리보기에는 ${x}ms가 필요하며 에디터/미리보기 버튼을 클릭하여 닫을 수 있습니다.",preview:"미리보기",quote:"인용단락",record:"녹음시작/녹음종료","record-tip":"녹음을 지원하지 않습니다.",recording:"녹음중...",redo:"되돌리기",remove:"삭제",row:"열",splitView:"마크다운",strike:"취소선",table:"표삽입",textIsNotEmpty:"텍스트(no empty)",tooltipText:"툴팁",undo:"취소하기",up:"위로",update:"업데이트",upload:"이미지 업로드하기",uploadError:"업로드 실패",uploading:"업로드중...",wysiwyg:"위지위그"},zh_CN:{alignCenter:"居中",alignLeft:"居左",alignRight:"居右",alternateText:"替代文本",bold:"粗体",both:"编辑 & 预览",check:"任务列表",code:"代码块","code-theme":"代码块主题预览",column:"列",confirm:"确定","content-theme":"内容主题预览",copied:"已复制",copy:"复制","delete-column":"删除列","delete-row":"删除行",devtools:"开发者工具",down:"下",downloadTip:"该浏览器不支持下载功能",edit:"编辑","edit-mode":"切换编辑模式",emoji:"表情",export:"导出",fileTypeError:"文件类型不允许上传",footnoteRef:"脚注标识",format:"格式化",fullscreen:"全屏切换",generate:"生成中",headings:"标题",help:"帮助",imageURL:"图片地址",indent:"列表缩进",info:"关于","inline-code":"行内代码","insert-after":"末尾插入行","insert-before":"起始插入行","insert-column":"插入列","insert-row":"插入行",instantRendering:"即时渲染",italic:"斜体",language:"语言",line:"分隔线",link:"链接",linkRef:"引用标识",list:"无序列表",more:"更多",nameEmpty:"文件名不能为空","ordered-list":"有序列表",outdent:"列表反向缩进",outline:"大纲",over:"超过",performanceTip:"实时预览需 ${x}ms，可点击编辑 & 预览按钮进行关闭",preview:"预览",quote:"引用",record:"开始录音/结束录音","record-tip":"该设备不支持录音功能",recording:"录音中...",redo:"重做",remove:"删除",row:"行",splitView:"分屏预览",strike:"删除线",table:"表格",textIsNotEmpty:"文本（不能为空）",tooltipText:"提示文本",undo:"撤销",up:"上",update:"更新",upload:"上传图片或文件",uploadError:"上传错误",uploading:"上传中...",wysiwyg:"所见即所得"}}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r=function(e,t){if(document.getElementById(t))return!1;var n=new XMLHttpRequest;n.open("GET",e,!1),n.setRequestHeader("Accept","text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01"),n.send("");var r=document.createElement("script");r.type="text/javascript",r.text=n.responseText,r.id=t,document.head.appendChild(r)},i=function(e,t){return new Promise((function(n,r){if(document.getElementById(t))return n(),!1;var i=document.createElement("script");i.src=e,i.async=!0,document.head.appendChild(i),i.onload=function(){if(document.getElementById(t))return i.remove(),n(),!1;i.id=t,n()}}))}},function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var r=function(e,t){if(!e)return!1;3===e.nodeType&&(e=e.parentElement);for(var n=e,r=!1;n&&!r&&!n.classList.contains("vditor-reset");)0===n.nodeName.indexOf(t)?r=!0:n=n.parentElement;return r&&n},i=function(e){var t=r(e,"H");return!(!t||2!==t.tagName.length||"HR"===t.tagName)&&t}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e,t){if(!document.getElementById(t)){var n=document.createElement("link");n.id=t,n.rel="stylesheet",n.type="text/css",n.href=e,document.getElementsByTagName("head")[0].appendChild(n)}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){return e.replace(/\u00a0/g," ")}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(7),o=function(e,t){if(void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b),r.a.CONTENT_THEME.includes(e)){var n=document.getElementById("vditorContentTheme"),o=t+"/dist/css/content-theme/"+e+".css";"light"!==e?n?n.href!==o&&(n.remove(),Object(i.a)(o,"vditorContentTheme")):Object(i.a)(o,"vditorContentTheme"):n&&n.remove()}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b);var n=e.querySelectorAll(".language-abc");n.length>0&&Object(i.a)(t+"/dist/js/abcjs/abcjs_basic.min.js","vditorAbcjsScript").then((function(){n.forEach((function(e){var t=document.createElement("div");t.style.backgroundColor="var(--preview-background-color)",e.parentNode.replaceChild(t,e),ABCJS.renderAbc(t,e.textContent.trim()),t.style.overflowX="auto"}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b);var n=e.querySelectorAll(".language-echarts");n.length>0&&Object(i.a)(t+"/dist/js/echarts/echarts.min.js","vditorEchartsScript").then((function(){n.forEach((function(e){var t=e.innerText.trim();if(t)try{if("true"===e.getAttribute("data-processed"))return;var n=JSON.parse(t);echarts.init(e).setOption(n),e.setAttribute("data-processed","true")}catch(t){e.className="vditor-reset--error",e.innerHTML="echarts render error: <br>"+t}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(25),i=n.n(r),o=n(4),a=n(8),s=function(e,t){void 0===t&&(t="zh_CN"),e.querySelectorAll("pre > code").forEach((function(n,r){if(!(n.classList.contains("language-mermaid")||n.classList.contains("language-echarts")||n.classList.contains("language-mindmap")||n.classList.contains("language-abc")||n.classList.contains("language-graphviz")||n.style.maxHeight.indexOf("px")>-1||e.classList.contains("vditor-preview")&&r>5)){var s=n.innerText;if(n.classList.contains("highlight-chroma")){var l=document.createElement("code");l.innerHTML=n.innerHTML,l.querySelectorAll(".highlight-ln").forEach((function(e){e.remove()})),s=l.innerText}var c=document.createElement("div");c.className="vditor-copy",c.innerHTML='<span aria-label="'+o.a[t].copy+"\"\nonmouseover=\"this.setAttribute('aria-label', '"+o.a[t].copy+"')\"\nclass=\"vditor-tooltipped vditor-tooltipped__w\"\nonclick=\"this.previousElementSibling.select();document.execCommand('copy');this.setAttribute('aria-label', '"+o.a[t].copied+"')\">"+i.a+"</span>";var d=document.createElement("textarea");d.value=Object(a.a)(s),c.insertAdjacentElement("afterbegin",d),n.before(c),n.style.maxHeight=window.outerHeight-40+"px"}}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b);var n=e.querySelectorAll(".language-graphviz");0!==n.length&&Object(i.a)(t+"/dist/js/graphviz/viz.js","vditorGraphVizScript").then((function(){n.forEach((function(e){if("true"!==e.getAttribute("data-processed")){try{var t=new Blob(["importScripts('"+document.getElementById("vditorGraphVizScript").src.replace("viz.js","full.render.js")+"');"],{type:"application/javascript"}),n=(window.URL||window.webkitURL).createObjectURL(t),r=new Worker(n);new Viz({worker:r}).renderSVGElement(e.textContent).then((function(t){e.innerHTML=t.outerHTML})).catch((function(t){e.innerHTML="graphviz render error: <br>"+t,e.className="vditor-math vditor-reset--error"}))}catch(e){console.error("graphviz error",e)}e.setAttribute("data-processed","true")}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var r=n(2),i=n(5),o=n(7),a=function(e,t,n){void 0===t&&(t=document),void 0===n&&(n="https://cdn.jsdelivr.net/npm/vditor@"+r.b);var a=e.style;r.a.CODE_THEME.includes(a)||(a="github");var s=document.getElementById("vditorHljsStyle"),l=n+"/dist/js/highlight.js/styles/"+a+".css";(s&&s.href!==l&&s.remove(),Object(o.a)(n+"/dist/js/highlight.js/styles/"+a+".css","vditorHljsStyle"),!1!==e.enable)&&(0!==t.querySelectorAll("pre > code").length&&Object(i.a)(n+"/dist/js/highlight.js/highlight.pack.js","vditorHljsScript").then((function(){t.querySelectorAll("pre > code").forEach((function(t){if(!t.parentElement.classList.contains("vditor-ir__marker--pre")&&!t.parentElement.classList.contains("vditor-wysiwyg__pre")&&!(t.classList.contains("language-mermaid")||t.classList.contains("language-echarts")||t.classList.contains("language-mindmap")||t.classList.contains("language-abc")||t.classList.contains("language-graphviz"))&&(hljs.highlightBlock(t),e.lineNumber)){t.classList.add("vditor-linenumber");var n=t.querySelector(".vditor-linenumber__temp");n||((n=document.createElement("div")).className="vditor-linenumber__temp",t.insertAdjacentElement("beforeend",n));var r=getComputedStyle(t).whiteSpace,i=!1;"pre-wrap"!==r&&"pre-line"!==r||(i=!0);var o="",a=t.textContent.split(/\r\n|\r|\n/g);a.pop(),a.map((function(e){var t="";i&&(n.textContent=e||"\n",t=' style="height:'+n.getBoundingClientRect().height+'px"'),o+="<span"+t+"></span>"})),n.style.display="none",o='<span class="vditor-linenumber__rows">'+o+"</span>",t.insertAdjacentHTML("beforeend",o)}}))})))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n(2),i=n(5),o=n(7),a=n(8),s=function(e,t){var n=e.querySelectorAll(".vditor-math");if(0!==n.length){var s={cdn:"https://cdn.jsdelivr.net/npm/vditor@"+r.b,math:{engine:"KaTeX",inlineDigit:!1,macros:{}}};t&&t.math&&(t.math=Object.assign({},s.math,t.math)),"KaTeX"===(t=Object.assign({},s,t)).math.engine?(Object(o.a)(t.cdn+"/dist/js/katex/katex.min.css","vditorKatexStyle"),Object(i.a)(t.cdn+"/dist/js/katex/katex.min.js","vditorKatexScript").then((function(){n.forEach((function(e){if(!e.getAttribute("data-math")){var t=Object(a.a)(e.textContent);e.setAttribute("data-math",t);try{e.innerHTML=katex.renderToString(t,{displayMode:"DIV"===e.tagName,output:"html"})}catch(t){e.innerHTML=t.message,e.className="vditor-math vditor-reset--error"}e.addEventListener("copy",(function(e){e.stopPropagation(),e.preventDefault();var t=e.currentTarget.closest(".vditor-math");e.clipboardData.setData("text/html",t.innerHTML),e.clipboardData.setData("text/plain",t.getAttribute("data-math"))}))}}))}))):"MathJax"===t.math.engine&&(window.MathJax||(window.MathJax={loader:{paths:{mathjax:t.cdn+"/dist/js/mathjax"}},tex:{macros:t.math.macros}}),Object(i.b)(t.cdn+"/dist/js/mathjax/tex-svg.js","vditorMathJaxScript"),setTimeout((function(){n.forEach((function(e){if(!e.getAttribute("data-math")){var t=Object(a.a)(e.textContent);e.setAttribute("data-math",t),window.MathJax.texReset();var n=window.MathJax.getMetricsFor(e);n.display="DIV"===e.tagName,window.MathJax.tex2svgPromise(t,n).then((function(t){e.innerHTML="",e.append(t),window.MathJax.startup.document.clear(),window.MathJax.startup.document.updateDocument();var n=e.querySelector("mjx-container").textContent.trim();""!==n&&(e.innerHTML=n,e.className="vditor-math vditor-reset--error")}))}}))})))}}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t,n){void 0===t&&(t=".language-mermaid"),void 0===n&&(n="https://cdn.jsdelivr.net/npm/vditor@"+r.b),0!==e.querySelectorAll(t).length&&Object(i.a)(n+"/dist/js/mermaid/mermaid.min.js","vditorMermaidScript").then((function(){mermaid.init({noteMargin:10},t)}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(5),o=function(e,t){void 0===e&&(e=document),void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b);var n=e.querySelectorAll(".language-mindmap");n.length>0&&Object(i.a)(t+"/dist/js/echarts/echarts.min.js","vditorEchartsScript").then((function(){n.forEach((function(e){var t=e.getAttribute("data-code");if(t)try{if("true"===e.getAttribute("data-processed"))return;var n={series:[{data:[JSON.parse(decodeURIComponent(t))],initialTreeDepth:-1,itemStyle:{borderWidth:0,color:"#4285f4"},label:{backgroundColor:"#f6f8fa",borderColor:"#d1d5da",borderRadius:5,borderWidth:.5,color:"#586069",lineHeight:20,offset:[-5,0],padding:[0,5],position:"insideRight"},lineStyle:{color:"#d1d5da",width:1},roam:!0,symbol:function(e,t){var n;return(null===(n=null==t?void 0:t.data)||void 0===n?void 0:n.children)?"circle":"path://"},type:"tree"}],tooltip:{trigger:"item",triggerOn:"mousemove"}};echarts.init(e).setOption(n),e.setAttribute("data-processed","true")}catch(t){e.className="vditor-reset--error",e.innerHTML="mindmap render error: <br>"+t}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){e&&e.querySelectorAll("a").forEach((function(e){var t=e.getAttribute("href");t&&(t.match(/^.+.(mp4|m4v|ogg|ogv|webm)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<video controls="controls" src="'+t+'"></video>'),e.remove()}(e,t):t.match(/^.+.(mp3|wav|flac)$/)?function(e,t){e.insertAdjacentHTML("afterend",'<audio controls="controls" src="'+t+'"></audio>'),e.remove()}(e,t):function(e,t){var n=t.match(/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?/),r=t.match(/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/),i=t.match(/\/\/v\.qq\.com\/x\/cover\/.*\/([^\/]+)\.html\??.*/),o=t.match(/(?:www\.|\/\/)coub\.com\/view\/(\w+)/),a=t.match(/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/),s=t.match(/.+dailymotion.com\/(video|hub)\/(\w+)\?/),l=t.match(/(?:www\.|\/\/)bilibili\.com\/video\/(\w+)/),c=t.match(/(?:www\.|\/\/)ted\.com\/talks\/(\w+)/);n&&11===n[1].length?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//www.youtube.com/embed/'+n[1]+(n[2]?"?start="+n[2]:"")+'"></iframe>'),e.remove()):r&&r[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//player.youku.com/embed/'+r[1]+'"></iframe>'),e.remove()):i&&i[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="https://v.qq.com/txp/iframe/player.html?vid='+i[1]+'"></iframe>'),e.remove()):o&&o[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="//coub.com/embed/'+o[1]+'?muted=false&autostart=false&originalSize=true&startWithHD=true"></iframe>'),e.remove()):a&&a[0]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.facebook.com/plugins/video.php?href='+encodeURIComponent(a[0])+'"></iframe>'),e.remove()):s&&s[2]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="https://www.dailymotion.com/embed/video/'+s[2]+'"></iframe>'),e.remove()):l&&l[1]?(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video"\n src="//player.bilibili.com/player.html?bvid='+l[1]+'"></iframe>'),e.remove()):c&&c[1]&&(e.insertAdjacentHTML("afterend",'<iframe class="iframe__video" src="//embed.ted.com/talks/'+c[1]+'"></iframe>'),e.remove())}(e,t))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(2),i=n(7),o=function(e,t){void 0===t&&(t="https://cdn.jsdelivr.net/npm/vditor@"+r.b),r.a.CODE_THEME.includes(e)||(e="github");var n=document.getElementById("vditorHljsStyle"),o=t+"/dist/js/highlight.js/styles/"+e+".css";n?n.href!==o&&(n.remove(),Object(i.a)(o,"vditorHljsStyle")):Object(i.a)(o,"vditorHljsStyle")}},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M6 0v32l20-15.977-20-16.023z"></path> </svg> '},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(6),i=function(e,t,n){var i="";Array.from(e.children).forEach((function(e,t){if(Object(r.a)(e)){var o=parseInt(e.tagName.substring(1),10),a=new Array(2*(o-1)).fill("&emsp;").join(""),s="";s=n&&"ir"===n.currentMode?e.textContent.substring(o+1).trim():e.textContent.trim();var l=e.id.lastIndexOf("_"),c=e.id.substring(0,-1===l?void 0:l);e.id=c+"_"+t,i+='<div data-id="'+e.id+'" class="vditor-outline__item">'+a+s+"</div>"}})),t.innerHTML=i,t.querySelectorAll(".vditor-outline__item").forEach((function(r){r.addEventListener("click",(function(i){var o=document.getElementById(r.getAttribute("data-id"));if(o){if(n)if("auto"===n.options.height){var a=o.offsetTop+n.element.offsetTop;n.options.toolbarConfig.pin||(a+=n.toolbar.element.offsetHeight),window.scrollTo(window.scrollX,a)}else n.element.offsetTop<window.scrollY&&window.scrollTo(window.scrollX,n.element.offsetTop),n.preview.element.contains(e)?e.parentElement.scrollTop=o.offsetTop:e.scrollTop=o.offsetTop;else window.scrollTo(window.scrollX,o.offsetTop);t.querySelectorAll(".vditor-outline__item").forEach((function(e){e.classList.remove("vditor-outline__item--current")})),r.classList.add("vditor-outline__item--current")}}))}))}},function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){var t=Lute.New();return t.PutEmojis(e.emojis),t.SetEmojiSite(e.emojiSite),t.SetHeadingAnchor(e.headingAnchor),t.SetInlineMathAllowDigitAfterOpenMarker(e.inlineMathDigit),t.SetAutoSpace(e.autoSpace),t.SetToC(e.toc),t.SetFootnotes(e.footnotes),t.SetChinesePunct(e.chinesePunct),t.SetFixTermTypo(e.fixTermTypo),t.SetVditorCodeBlockPreview(e.codeBlockPreview),t.SetSetext(e.setext),t.SetSanitize(e.sanitize),t.SetChineseParagraphBeginningSpace(e.paragraphBeginningSpace),t.SetRenderListStyle(e.listStyle),t.SetLinkBase(e.linkBase),e.lazyLoadImage&&t.SetImageLazyLoading(e.lazyLoadImage),t}},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M11 0h-6c-0.553 0-1 0.448-1 1v30c0 0.553 0.447 1 1 1h6c0.553 0 1-0.447 1-1v-30c0-0.552-0.447-1-1-1zM27 0h-6c-0.553 0-1 0.448-1 1v30c0 0.553 0.447 1 1 1h6c0.553 0 1-0.447 1-1v-30c0-0.552-0.447-1-1-1z"></path> </svg> '},function(e,t){function n(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32}n.Diff,n.prototype.diff_main=function(e,t,n,r){void 0===r&&(r=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout);var i=r;if(null==e||null==t)throw new Error("Null input. (diff_main)");if(e==t)return e?[[0,e]]:[];void 0===n&&(n=!0);var o=n,a=this.diff_commonPrefix(e,t),s=e.substring(0,a);e=e.substring(a),t=t.substring(a),a=this.diff_commonSuffix(e,t);var l=e.substring(e.length-a);e=e.substring(0,e.length-a),t=t.substring(0,t.length-a);var c=this.diff_compute_(e,t,o,i);return s&&c.unshift([0,s]),l&&c.push([0,l]),this.diff_cleanupMerge(c),c},n.prototype.diff_compute_=function(e,t,n,r){var i;if(!e)return[[1,t]];if(!t)return[[-1,e]];var o=e.length>t.length?e:t,a=e.length>t.length?t:e,s=o.indexOf(a);if(-1!=s)return i=[[1,o.substring(0,s)],[0,a],[1,o.substring(s+a.length)]],e.length>t.length&&(i[0][0]=i[2][0]=-1),i;if(1==a.length)return[[-1,e],[1,t]];var l=this.diff_halfMatch_(e,t);if(l){var c=l[0],d=l[1],u=l[2],p=l[3],h=l[4],f=this.diff_main(c,u,n,r),m=this.diff_main(d,p,n,r);return f.concat([[0,h]],m)}return n&&e.length>100&&t.length>100?this.diff_lineMode_(e,t,r):this.diff_bisect_(e,t,r)},n.prototype.diff_lineMode_=function(e,t,n){e=(d=this.diff_linesToChars_(e,t)).chars1,t=d.chars2;var r=d.lineArray,i=this.diff_main(e,t,!1,n);this.diff_charsToLines_(i,r),this.diff_cleanupSemantic(i),i.push([0,""]);for(var o=0,a=0,s=0,l="",c="";o<i.length;){switch(i[o][0]){case 1:s++,c+=i[o][1];break;case-1:a++,l+=i[o][1];break;case 0:if(a>=1&&s>=1){i.splice(o-a-s,a+s),o=o-a-s;for(var d,u=(d=this.diff_main(l,c,!1,n)).length-1;u>=0;u--)i.splice(o,0,d[u]);o+=d.length}s=0,a=0,l="",c=""}o++}return i.pop(),i},n.prototype.diff_bisect_=function(e,t,n){for(var r=e.length,i=t.length,o=Math.ceil((r+i)/2),a=o,s=2*o,l=new Array(s),c=new Array(s),d=0;d<s;d++)l[d]=-1,c[d]=-1;l[a+1]=0,c[a+1]=0;for(var u=r-i,p=u%2!=0,h=0,f=0,m=0,v=0,g=0;g<o&&!((new Date).getTime()>n);g++){for(var b=-g+h;b<=g-f;b+=2){for(var y=a+b,w=(k=b==-g||b!=g&&l[y-1]<l[y+1]?l[y+1]:l[y-1]+1)-b;k<r&&w<i&&e.charAt(k)==t.charAt(w);)k++,w++;if(l[y]=k,k>r)f+=2;else if(w>i)h+=2;else if(p){if((S=a+u-b)>=0&&S<s&&-1!=c[S])if(k>=(M=r-c[S]))return this.diff_bisectSplit_(e,t,k,w,n)}}for(var E=-g+m;E<=g-v;E+=2){for(var M,S=a+E,O=(M=E==-g||E!=g&&c[S-1]<c[S+1]?c[S+1]:c[S-1]+1)-E;M<r&&O<i&&e.charAt(r-M-1)==t.charAt(i-O-1);)M++,O++;if(c[S]=M,M>r)v+=2;else if(O>i)m+=2;else if(!p){if((y=a+u-E)>=0&&y<s&&-1!=l[y]){var k;w=a+(k=l[y])-y;if(k>=(M=r-M))return this.diff_bisectSplit_(e,t,k,w,n)}}}}return[[-1,e],[1,t]]},n.prototype.diff_bisectSplit_=function(e,t,n,r,i){var o=e.substring(0,n),a=t.substring(0,r),s=e.substring(n),l=t.substring(r),c=this.diff_main(o,a,!1,i),d=this.diff_main(s,l,!1,i);return c.concat(d)},n.prototype.diff_linesToChars_=function(e,t){var n=[],r={};function i(e){for(var t="",i=0,o=-1,a=n.length;o<e.length-1;){-1==(o=e.indexOf("\n",i))&&(o=e.length-1);var s=e.substring(i,o+1);i=o+1,(r.hasOwnProperty?r.hasOwnProperty(s):void 0!==r[s])?t+=String.fromCharCode(r[s]):(t+=String.fromCharCode(a),r[s]=a,n[a++]=s)}return t}return n[0]="",{chars1:i(e),chars2:i(t),lineArray:n}},n.prototype.diff_charsToLines_=function(e,t){for(var n=0;n<e.length;n++){for(var r=e[n][1],i=[],o=0;o<r.length;o++)i[o]=t[r.charCodeAt(o)];e[n][1]=i.join("")}},n.prototype.diff_commonPrefix=function(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var n=0,r=Math.min(e.length,t.length),i=r,o=0;n<i;)e.substring(o,i)==t.substring(o,i)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},n.prototype.diff_commonSuffix=function(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var n=0,r=Math.min(e.length,t.length),i=r,o=0;n<i;)e.substring(e.length-i,e.length-o)==t.substring(t.length-i,t.length-o)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},n.prototype.diff_commonOverlap_=function(e,t){var n=e.length,r=t.length;if(0==n||0==r)return 0;n>r?e=e.substring(n-r):n<r&&(t=t.substring(0,n));var i=Math.min(n,r);if(e==t)return i;for(var o=0,a=1;;){var s=e.substring(i-a),l=t.indexOf(s);if(-1==l)return o;a+=l,0!=l&&e.substring(i-a)!=t.substring(0,a)||(o=a,a++)}},n.prototype.diff_halfMatch_=function(e,t){if(this.Diff_Timeout<=0)return null;var n=e.length>t.length?e:t,r=e.length>t.length?t:e;if(n.length<4||2*r.length<n.length)return null;var i=this;function o(e,t,n){for(var r,o,a,s,l=e.substring(n,n+Math.floor(e.length/4)),c=-1,d="";-1!=(c=t.indexOf(l,c+1));){var u=i.diff_commonPrefix(e.substring(n),t.substring(c)),p=i.diff_commonSuffix(e.substring(0,n),t.substring(0,c));d.length<p+u&&(d=t.substring(c-p,c)+t.substring(c,c+u),r=e.substring(0,n-p),o=e.substring(n+u),a=t.substring(0,c-p),s=t.substring(c+u))}return 2*d.length>=e.length?[r,o,a,s,d]:null}var a,s,l,c,d,u=o(n,r,Math.ceil(n.length/4)),p=o(n,r,Math.ceil(n.length/2));return u||p?(a=p?u&&u[4].length>p[4].length?u:p:u,e.length>t.length?(s=a[0],l=a[1],c=a[2],d=a[3]):(c=a[0],d=a[1],s=a[2],l=a[3]),[s,l,c,d,a[4]]):null},n.prototype.diff_cleanupSemantic=function(e){for(var t=!1,n=[],r=0,i=null,o=0,a=0,s=0,l=0,c=0;o<e.length;)0==e[o][0]?(n[r++]=o,a=l,s=c,l=0,c=0,i=e[o][1]):(1==e[o][0]?l+=e[o][1].length:c+=e[o][1].length,i&&i.length<=Math.max(a,s)&&i.length<=Math.max(l,c)&&(e.splice(n[r-1],0,[-1,i]),e[n[r-1]+1][0]=1,r--,o=--r>0?n[r-1]:-1,a=0,s=0,l=0,c=0,i=null,t=!0)),o++;for(t&&this.diff_cleanupMerge(e),this.diff_cleanupSemanticLossless(e),o=1;o<e.length;){if(-1==e[o-1][0]&&1==e[o][0]){var d=e[o-1][1],u=e[o][1],p=this.diff_commonOverlap_(d,u),h=this.diff_commonOverlap_(u,d);p>=h?(p>=d.length/2||p>=u.length/2)&&(e.splice(o,0,[0,u.substring(0,p)]),e[o-1][1]=d.substring(0,d.length-p),e[o+1][1]=u.substring(p),o++):(h>=d.length/2||h>=u.length/2)&&(e.splice(o,0,[0,d.substring(0,h)]),e[o-1][0]=1,e[o-1][1]=u.substring(0,u.length-h),e[o+1][0]=-1,e[o+1][1]=d.substring(h),o++),o++}o++}},n.prototype.diff_cleanupSemanticLossless=function(e){function t(e,t){if(!e||!t)return 6;var r=e.charAt(e.length-1),i=t.charAt(0),o=r.match(n.nonAlphaNumericRegex_),a=i.match(n.nonAlphaNumericRegex_),s=o&&r.match(n.whitespaceRegex_),l=a&&i.match(n.whitespaceRegex_),c=s&&r.match(n.linebreakRegex_),d=l&&i.match(n.linebreakRegex_),u=c&&e.match(n.blanklineEndRegex_),p=d&&t.match(n.blanklineStartRegex_);return u||p?5:c||d?4:o&&!s&&l?3:s||l?2:o||a?1:0}for(var r=1;r<e.length-1;){if(0==e[r-1][0]&&0==e[r+1][0]){var i=e[r-1][1],o=e[r][1],a=e[r+1][1],s=this.diff_commonSuffix(i,o);if(s){var l=o.substring(o.length-s);i=i.substring(0,i.length-s),o=l+o.substring(0,o.length-s),a=l+a}for(var c=i,d=o,u=a,p=t(i,o)+t(o,a);o.charAt(0)===a.charAt(0);){i+=o.charAt(0),o=o.substring(1)+a.charAt(0),a=a.substring(1);var h=t(i,o)+t(o,a);h>=p&&(p=h,c=i,d=o,u=a)}e[r-1][1]!=c&&(c?e[r-1][1]=c:(e.splice(r-1,1),r--),e[r][1]=d,u?e[r+1][1]=u:(e.splice(r+1,1),r--))}r++}},n.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,n.whitespaceRegex_=/\s/,n.linebreakRegex_=/[\r\n]/,n.blanklineEndRegex_=/\n\r?\n$/,n.blanklineStartRegex_=/^\r?\n\r?\n/,n.prototype.diff_cleanupEfficiency=function(e){for(var t=!1,n=[],r=0,i=null,o=0,a=!1,s=!1,l=!1,c=!1;o<e.length;)0==e[o][0]?(e[o][1].length<this.Diff_EditCost&&(l||c)?(n[r++]=o,a=l,s=c,i=e[o][1]):(r=0,i=null),l=c=!1):(-1==e[o][0]?c=!0:l=!0,i&&(a&&s&&l&&c||i.length<this.Diff_EditCost/2&&a+s+l+c==3)&&(e.splice(n[r-1],0,[-1,i]),e[n[r-1]+1][0]=1,r--,i=null,a&&s?(l=c=!0,r=0):(o=--r>0?n[r-1]:-1,l=c=!1),t=!0)),o++;t&&this.diff_cleanupMerge(e)},n.prototype.diff_cleanupMerge=function(e){e.push([0,""]);for(var t,n=0,r=0,i=0,o="",a="";n<e.length;)switch(e[n][0]){case 1:i++,a+=e[n][1],n++;break;case-1:r++,o+=e[n][1],n++;break;case 0:r+i>1?(0!==r&&0!==i&&(0!==(t=this.diff_commonPrefix(a,o))&&(n-r-i>0&&0==e[n-r-i-1][0]?e[n-r-i-1][1]+=a.substring(0,t):(e.splice(0,0,[0,a.substring(0,t)]),n++),a=a.substring(t),o=o.substring(t)),0!==(t=this.diff_commonSuffix(a,o))&&(e[n][1]=a.substring(a.length-t)+e[n][1],a=a.substring(0,a.length-t),o=o.substring(0,o.length-t))),0===r?e.splice(n-i,r+i,[1,a]):0===i?e.splice(n-r,r+i,[-1,o]):e.splice(n-r-i,r+i,[-1,o],[1,a]),n=n-r-i+(r?1:0)+(i?1:0)+1):0!==n&&0==e[n-1][0]?(e[n-1][1]+=e[n][1],e.splice(n,1)):n++,i=0,r=0,o="",a=""}""===e[e.length-1][1]&&e.pop();var s=!1;for(n=1;n<e.length-1;)0==e[n-1][0]&&0==e[n+1][0]&&(e[n][1].substring(e[n][1].length-e[n-1][1].length)==e[n-1][1]?(e[n][1]=e[n-1][1]+e[n][1].substring(0,e[n][1].length-e[n-1][1].length),e[n+1][1]=e[n-1][1]+e[n+1][1],e.splice(n-1,1),s=!0):e[n][1].substring(0,e[n+1][1].length)==e[n+1][1]&&(e[n-1][1]+=e[n+1][1],e[n][1]=e[n][1].substring(e[n+1][1].length)+e[n+1][1],e.splice(n+1,1),s=!0)),n++;s&&this.diff_cleanupMerge(e)},n.prototype.diff_xIndex=function(e,t){var n,r=0,i=0,o=0,a=0;for(n=0;n<e.length&&(1!==e[n][0]&&(r+=e[n][1].length),-1!==e[n][0]&&(i+=e[n][1].length),!(r>t));n++)o=r,a=i;return e.length!=n&&-1===e[n][0]?a:a+(t-o)},n.prototype.diff_prettyHtml=function(e){for(var t=[],n=/&/g,r=/</g,i=/>/g,o=/\n/g,a=0;a<e.length;a++){var s=e[a][0],l=e[a][1].replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(o,"&para;<br>");switch(s){case 1:t[a]='<ins style="background:#e6ffe6;">'+l+"</ins>";break;case-1:t[a]='<del style="background:#ffe6e6;">'+l+"</del>";break;case 0:t[a]="<span>"+l+"</span>"}}return t.join("")},n.prototype.diff_text1=function(e){for(var t=[],n=0;n<e.length;n++)1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},n.prototype.diff_text2=function(e){for(var t=[],n=0;n<e.length;n++)-1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")},n.prototype.diff_levenshtein=function(e){for(var t=0,n=0,r=0,i=0;i<e.length;i++){var o=e[i][0],a=e[i][1];switch(o){case 1:n+=a.length;break;case-1:r+=a.length;break;case 0:t+=Math.max(n,r),n=0,r=0}}return t+=Math.max(n,r)},n.prototype.diff_toDelta=function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n][0]){case 1:t[n]="+"+encodeURI(e[n][1]);break;case-1:t[n]="-"+e[n][1].length;break;case 0:t[n]="="+e[n][1].length}return t.join("\t").replace(/%20/g," ")},n.prototype.diff_fromDelta=function(e,t){for(var n=[],r=0,i=0,o=t.split(/\t/g),a=0;a<o.length;a++){var s=o[a].substring(1);switch(o[a].charAt(0)){case"+":try{n[r++]=[1,decodeURI(s)]}catch(e){throw new Error("Illegal escape in diff_fromDelta: "+s)}break;case"-":case"=":var l=parseInt(s,10);if(isNaN(l)||l<0)throw new Error("Invalid number in diff_fromDelta: "+s);var c=e.substring(i,i+=l);"="==o[a].charAt(0)?n[r++]=[0,c]:n[r++]=[-1,c];break;default:if(o[a])throw new Error("Invalid diff operation in diff_fromDelta: "+o[a])}}if(i!=e.length)throw new Error("Delta length ("+i+") does not equal source text length ("+e.length+").");return n},n.prototype.match_main=function(e,t,n){if(null==e||null==t||null==n)throw new Error("Null input. (match_main)");return n=Math.max(0,Math.min(n,e.length)),e==t?0:e.length?e.substring(n,n+t.length)==t?n:this.match_bitap_(e,t,n):-1},n.prototype.match_bitap_=function(e,t,n){if(t.length>this.Match_MaxBits)throw new Error("Pattern too long for this browser.");var r=this.match_alphabet_(t),i=this;function o(e,r){var o=e/t.length,a=Math.abs(n-r);return i.Match_Distance?o+a/i.Match_Distance:a?1:o}var a=this.Match_Threshold,s=e.indexOf(t,n);-1!=s&&(a=Math.min(o(0,s),a),-1!=(s=e.lastIndexOf(t,n+t.length))&&(a=Math.min(o(0,s),a)));var l,c,d=1<<t.length-1;s=-1;for(var u,p=t.length+e.length,h=0;h<t.length;h++){for(l=0,c=p;l<c;)o(h,n+c)<=a?l=c:p=c,c=Math.floor((p-l)/2+l);p=c;var f=Math.max(1,n-c+1),m=Math.min(n+c,e.length)+t.length,v=Array(m+2);v[m+1]=(1<<h)-1;for(var g=m;g>=f;g--){var b=r[e.charAt(g-1)];if(v[g]=0===h?(v[g+1]<<1|1)&b:(v[g+1]<<1|1)&b|(u[g+1]|u[g])<<1|1|u[g+1],v[g]&d){var y=o(h,g-1);if(y<=a){if(a=y,!((s=g-1)>n))break;f=Math.max(1,2*n-s)}}}if(o(h+1,n)>a)break;u=v}return s},n.prototype.match_alphabet_=function(e){for(var t={},n=0;n<e.length;n++)t[e.charAt(n)]=0;for(n=0;n<e.length;n++)t[e.charAt(n)]|=1<<e.length-n-1;return t},n.prototype.patch_addContext_=function(e,t){if(0!=t.length){for(var n=t.substring(e.start2,e.start2+e.length1),r=0;t.indexOf(n)!=t.lastIndexOf(n)&&n.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)r+=this.Patch_Margin,n=t.substring(e.start2-r,e.start2+e.length1+r);r+=this.Patch_Margin;var i=t.substring(e.start2-r,e.start2);i&&e.diffs.unshift([0,i]);var o=t.substring(e.start2+e.length1,e.start2+e.length1+r);o&&e.diffs.push([0,o]),e.start1-=i.length,e.start2-=i.length,e.length1+=i.length+o.length,e.length2+=i.length+o.length}},n.prototype.patch_make=function(e,t,r){var i,o;if("string"==typeof e&&"string"==typeof t&&void 0===r)i=e,(o=this.diff_main(i,t,!0)).length>2&&(this.diff_cleanupSemantic(o),this.diff_cleanupEfficiency(o));else if(e&&"object"==typeof e&&void 0===t&&void 0===r)o=e,i=this.diff_text1(o);else if("string"==typeof e&&t&&"object"==typeof t&&void 0===r)i=e,o=t;else{if("string"!=typeof e||"string"!=typeof t||!r||"object"!=typeof r)throw new Error("Unknown call format to patch_make.");i=e,o=r}if(0===o.length)return[];for(var a=[],s=new n.patch_obj,l=0,c=0,d=0,u=i,p=i,h=0;h<o.length;h++){var f=o[h][0],m=o[h][1];switch(l||0===f||(s.start1=c,s.start2=d),f){case 1:s.diffs[l++]=o[h],s.length2+=m.length,p=p.substring(0,d)+m+p.substring(d);break;case-1:s.length1+=m.length,s.diffs[l++]=o[h],p=p.substring(0,d)+p.substring(d+m.length);break;case 0:m.length<=2*this.Patch_Margin&&l&&o.length!=h+1?(s.diffs[l++]=o[h],s.length1+=m.length,s.length2+=m.length):m.length>=2*this.Patch_Margin&&l&&(this.patch_addContext_(s,u),a.push(s),s=new n.patch_obj,l=0,u=p,c=d)}1!==f&&(c+=m.length),-1!==f&&(d+=m.length)}return l&&(this.patch_addContext_(s,u),a.push(s)),a},n.prototype.patch_deepCopy=function(e){for(var t=[],r=0;r<e.length;r++){var i=e[r],o=new n.patch_obj;o.diffs=[];for(var a=0;a<i.diffs.length;a++)o.diffs[a]=i.diffs[a].slice();o.start1=i.start1,o.start2=i.start2,o.length1=i.length1,o.length2=i.length2,t[r]=o}return t},n.prototype.patch_apply=function(e,t){if(0==e.length)return[t,[]];e=this.patch_deepCopy(e);var n=this.patch_addPadding(e);t=n+t+n,this.patch_splitMax(e);for(var r=0,i=[],o=0;o<e.length;o++){var a,s,l=e[o].start2+r,c=this.diff_text1(e[o].diffs),d=-1;if(c.length>this.Match_MaxBits?-1!=(a=this.match_main(t,c.substring(0,this.Match_MaxBits),l))&&(-1==(d=this.match_main(t,c.substring(c.length-this.Match_MaxBits),l+c.length-this.Match_MaxBits))||a>=d)&&(a=-1):a=this.match_main(t,c,l),-1==a)i[o]=!1,r-=e[o].length2-e[o].length1;else if(i[o]=!0,r=a-l,c==(s=-1==d?t.substring(a,a+c.length):t.substring(a,d+this.Match_MaxBits)))t=t.substring(0,a)+this.diff_text2(e[o].diffs)+t.substring(a+c.length);else{var u=this.diff_main(c,s,!1);if(c.length>this.Match_MaxBits&&this.diff_levenshtein(u)/c.length>this.Patch_DeleteThreshold)i[o]=!1;else{this.diff_cleanupSemanticLossless(u);for(var p,h=0,f=0;f<e[o].diffs.length;f++){var m=e[o].diffs[f];0!==m[0]&&(p=this.diff_xIndex(u,h)),1===m[0]?t=t.substring(0,a+p)+m[1]+t.substring(a+p):-1===m[0]&&(t=t.substring(0,a+p)+t.substring(a+this.diff_xIndex(u,h+m[1].length))),-1!==m[0]&&(h+=m[1].length)}}}}return[t=t.substring(n.length,t.length-n.length),i]},n.prototype.patch_addPadding=function(e){for(var t=this.Patch_Margin,n="",r=1;r<=t;r++)n+=String.fromCharCode(r);for(r=0;r<e.length;r++)e[r].start1+=t,e[r].start2+=t;var i=e[0],o=i.diffs;if(0==o.length||0!=o[0][0])o.unshift([0,n]),i.start1-=t,i.start2-=t,i.length1+=t,i.length2+=t;else if(t>o[0][1].length){var a=t-o[0][1].length;o[0][1]=n.substring(o[0][1].length)+o[0][1],i.start1-=a,i.start2-=a,i.length1+=a,i.length2+=a}if(0==(o=(i=e[e.length-1]).diffs).length||0!=o[o.length-1][0])o.push([0,n]),i.length1+=t,i.length2+=t;else if(t>o[o.length-1][1].length){a=t-o[o.length-1][1].length;o[o.length-1][1]+=n.substring(0,a),i.length1+=a,i.length2+=a}return n},n.prototype.patch_splitMax=function(e){for(var t=this.Match_MaxBits,r=0;r<e.length;r++)if(!(e[r].length1<=t)){var i=e[r];e.splice(r--,1);for(var o=i.start1,a=i.start2,s="";0!==i.diffs.length;){var l=new n.patch_obj,c=!0;for(l.start1=o-s.length,l.start2=a-s.length,""!==s&&(l.length1=l.length2=s.length,l.diffs.push([0,s]));0!==i.diffs.length&&l.length1<t-this.Patch_Margin;){var d=i.diffs[0][0],u=i.diffs[0][1];1===d?(l.length2+=u.length,a+=u.length,l.diffs.push(i.diffs.shift()),c=!1):-1===d&&1==l.diffs.length&&0==l.diffs[0][0]&&u.length>2*t?(l.length1+=u.length,o+=u.length,c=!1,l.diffs.push([d,u]),i.diffs.shift()):(u=u.substring(0,t-l.length1-this.Patch_Margin),l.length1+=u.length,o+=u.length,0===d?(l.length2+=u.length,a+=u.length):c=!1,l.diffs.push([d,u]),u==i.diffs[0][1]?i.diffs.shift():i.diffs[0][1]=i.diffs[0][1].substring(u.length))}s=(s=this.diff_text2(l.diffs)).substring(s.length-this.Patch_Margin);var p=this.diff_text1(i.diffs).substring(0,this.Patch_Margin);""!==p&&(l.length1+=p.length,l.length2+=p.length,0!==l.diffs.length&&0===l.diffs[l.diffs.length-1][0]?l.diffs[l.diffs.length-1][1]+=p:l.diffs.push([0,p])),c||e.splice(++r,0,l)}}},n.prototype.patch_toText=function(e){for(var t=[],n=0;n<e.length;n++)t[n]=e[n];return t.join("")},n.prototype.patch_fromText=function(e){var t=[];if(!e)return t;for(var r=e.split("\n"),i=0,o=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;i<r.length;){var a=r[i].match(o);if(!a)throw new Error("Invalid patch string: "+r[i]);var s=new n.patch_obj;for(t.push(s),s.start1=parseInt(a[1],10),""===a[2]?(s.start1--,s.length1=1):"0"==a[2]?s.length1=0:(s.start1--,s.length1=parseInt(a[2],10)),s.start2=parseInt(a[3],10),""===a[4]?(s.start2--,s.length2=1):"0"==a[4]?s.length2=0:(s.start2--,s.length2=parseInt(a[4],10)),i++;i<r.length;){var l=r[i].charAt(0);try{var c=decodeURI(r[i].substring(1))}catch(e){throw new Error("Illegal escape in patch_fromText: "+c)}if("-"==l)s.diffs.push([-1,c]);else if("+"==l)s.diffs.push([1,c]);else if(" "==l)s.diffs.push([0,c]);else{if("@"==l)break;if(""!==l)throw new Error('Invalid patch mode "'+l+'" in: '+c)}i++}}return t},n.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},n.patch_obj.prototype.toString=function(){for(var e,t=["@@ -"+(0===this.length1?this.start1+",0":1==this.length1?this.start1+1:this.start1+1+","+this.length1)+" +"+(0===this.length2?this.start2+",0":1==this.length2?this.start2+1:this.start2+1+","+this.length2)+" @@\n"],n=0;n<this.diffs.length;n++){switch(this.diffs[n][0]){case 1:e="+";break;case-1:e="-";break;case 0:e=" "}t[n+1]=e+encodeURI(this.diffs[n][1])+"\n"}return t.join("").replace(/%20/g," ")},e.exports=n,e.exports.diff_match_patch=n,e.exports.DIFF_DELETE=-1,e.exports.DIFF_INSERT=1,e.exports.DIFF_EQUAL=0},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 32 32" width=32px height=32px> <path d="M28.681 11.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-11.5c-1.379 0-2.5 1.121-2.5 2.5v23c0 1.378 1.121 2.5 2.5 2.5h19c1.378 0 2.5-1.122 2.5-2.5v-15.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 9.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268v0zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-19c-0.271 0-0.5-0.229-0.5-0.5v-23c0-0.271 0.229-0.5 0.5-0.5 0 0 11.499-0 11.5 0v7c0 0.552 0.448 1 1 1h7v15.5zM18.841 1.319c-1.612-1.182-2.393-1.319-2.841-1.319h-11.5c-1.378 0-2.5 1.121-2.5 2.5v23c0 1.207 0.86 2.217 2 2.45v-25.45c0-0.271 0.229-0.5 0.5-0.5h15.215c-0.301-0.248-0.595-0.477-0.873-0.681z"></path> </svg>'},function(e,t,n){"use strict";n.r(t);var r=n(10),i=n(11),o=n(12),a=n(13),s=n(14),l=function(e){void 0===e&&(e=document);var t=function(e){var t=document.createElement("img");t.src=e.getAttribute("data-src"),t.addEventListener("load",(function(){e.getAttribute("style")||e.getAttribute("class")||e.getAttribute("width")||e.getAttribute("height")||t.naturalHeight>t.naturalWidth&&t.naturalWidth/t.naturalHeight<document.querySelector(".vditor-reset").clientWidth/(window.innerHeight-40)&&t.naturalHeight>window.innerHeight-40&&(e.style.height=window.innerHeight-40+"px"),e.src=t.src})),e.removeAttribute("data-src")};if(!("IntersectionObserver"in window))return e.querySelectorAll("img").forEach((function(e){e.getAttribute("data-src")&&t(e)})),!1;window.vditorImageIntersectionObserver?(window.vditorImageIntersectionObserver.disconnect(),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)}))):(window.vditorImageIntersectionObserver=new IntersectionObserver((function(e){e.forEach((function(e){(void 0===e.isIntersecting?0!==e.intersectionRatio:e.isIntersecting)&&e.target.getAttribute("data-src")&&t(e.target)}))})),e.querySelectorAll("img").forEach((function(e){window.vditorImageIntersectionObserver.observe(e)})))},c=n(15),d=n(18),u=n(16),p=n(17),h=n(21),f=n(2),m=n(9),v=n(5),g=n(22),b=n(23),y=n.n(b),w=n(20),E=n.n(w),M=n(0),S=function(e,t){if(void 0===t&&(t="zh_CN"),"undefined"!=typeof speechSynthesis&&"undefined"!=typeof SpeechSynthesisUtterance){var n=document.querySelector(".vditor-speech");if(!n){(n=document.createElement("div")).className="vditor-speech",document.body.insertAdjacentElement("beforeend",n);var r=function(){var e,n;return speechSynthesis.getVoices().forEach((function(r){r.lang===t.replace("_","-")&&(e=r),r.default&&(n=r)})),e||(e=n),e};void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=r);var i=r();n.onclick=function(){if("vditor-speech"===n.className){var e=new SpeechSynthesisUtterance(n.getAttribute("data-text"));e.voice=i,e.onend=function(){n.className="vditor-speech",speechSynthesis.cancel(),n.innerHTML=E.a},speechSynthesis.speak(e),n.className="vditor-speech vditor-speech--current",n.innerHTML=y.a}else speechSynthesis.speaking&&(speechSynthesis.paused?(speechSynthesis.resume(),n.innerHTML=y.a):(speechSynthesis.pause(),n.innerHTML=E.a));Object(M.h)(window.vditorSpeechRange)},document.body.addEventListener("click",(function(){""===getSelection().toString().trim()&&"block"===n.style.display&&(n.className="vditor-speech",speechSynthesis.cancel(),n.style.display="none")}))}e.addEventListener("mouseup",(function(e){var t=getSelection().toString().trim();if(speechSynthesis.cancel(),""!==getSelection().toString().trim()){window.vditorSpeechRange=getSelection().getRangeAt(0).cloneRange();var r=getSelection().getRangeAt(0).getBoundingClientRect();n.innerHTML=E.a,n.style.display="block",n.style.top=r.top+r.height+document.querySelector("html").scrollTop-20+"px",n.style.left=e.screenX+2+"px",n.setAttribute("data-text",t)}else"block"===n.style.display&&(n.className="vditor-speech",n.style.display="none")}))}},O=function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{l(r.next(e))}catch(e){o(e)}}function s(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}l((r=r.apply(e,t||[])).next())}))},k=function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},x=function(e){var t={anchor:0,cdn:"https://cdn.jsdelivr.net/npm/vditor@"+f.b,customEmoji:{},emojiPath:(e&&e.emojiPath||"https://cdn.jsdelivr.net/npm/vditor@"+f.b)+"/dist/images/emoji",hljs:{enable:!0,lineNumber:!1,style:"github"},lang:"zh_CN",markdown:{autoSpace:!1,chinesePunct:!1,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",listStyle:!1,paragraphBeginningSpace:!1,sanitize:!0,setext:!1,theme:"light",toc:!1},math:{engine:"KaTeX",inlineDigit:!1,macros:{}},speech:{enable:!1}};return(null==e?void 0:e.hljs)&&(e.hljs=Object.assign({},t.hljs,e.hljs)),(null==e?void 0:e.speech)&&(e.speech=Object.assign({},t.speech,e.speech)),(null==e?void 0:e.math)&&(e.math=Object.assign({},t.math,e.math)),(null==e?void 0:e.markdown)&&(e.markdown=Object.assign({},t.markdown,e.markdown)),Object.assign(t,e)},T=function(e,t){var n=x(t);return Object(v.a)(n.cdn+"/dist/js/lute/lute.min.js","vditorLuteScript").then((function(){var r=Object(g.a)({autoSpace:n.markdown.autoSpace,chinesePunct:n.markdown.chinesePunct,codeBlockPreview:n.markdown.codeBlockPreview,emojiSite:n.emojiPath,emojis:n.customEmoji,fixTermTypo:n.markdown.fixTermTypo,footnotes:n.markdown.footnotes,headingAnchor:0!==n.anchor,inlineMathDigit:n.math.inlineDigit,lazyLoadImage:n.lazyLoadImage,linkBase:n.markdown.linkBase,listStyle:n.markdown.listStyle,paragraphBeginningSpace:n.markdown.paragraphBeginningSpace,sanitize:n.markdown.sanitize,setext:n.markdown.setext,toc:n.markdown.toc});return(null==t?void 0:t.renderers)&&r.SetJSRenderers({renderers:{Md2HTML:t.renderers}}),r.Md2HTML(e)}))},L=function(e,t,n){return O(void 0,void 0,void 0,(function(){var h,f;return k(this,(function(v){switch(v.label){case 0:return h=x(n),[4,T(t,h)];case 1:return f=v.sent(),h.transform&&(f=h.transform(f)),e.innerHTML=f,e.classList.add("vditor-reset"),Object(m.a)(h.markdown.theme,h.cdn),1===h.anchor&&e.classList.add("vditor-reset--anchor"),Object(o.a)(e,h.lang),Object(s.a)(h.hljs,e,h.cdn),Object(c.a)(e,{cdn:h.cdn,math:h.math}),Object(u.a)(e,".language-mermaid",h.cdn),Object(a.a)(e,h.cdn),Object(i.a)(e,h.cdn),Object(p.a)(e,h.cdn),Object(r.a)(e,h.cdn),Object(d.a)(e),h.speech.enable&&S(e,h.lang),0!==h.anchor&&(g=h.anchor,document.querySelectorAll(".vditor-anchor").forEach((function(e){1===g&&e.classList.add("vditor-anchor--left"),e.onclick=function(){var t=e.getAttribute("href").substr(1),n=document.getElementById("vditorAnchor-"+t).offsetTop;document.querySelector("html").scrollTop=n}})),window.onhashchange=function(){var e=document.getElementById("vditorAnchor-"+decodeURIComponent(window.location.hash.substr(1)));e&&(document.querySelector("html").scrollTop=e.offsetTop)}),h.after&&h.after(),h.lazyLoadImage&&l(e),[2]}var g}))}))},j=n(19),_=function(){function e(){}return e.codeRender=o.a,e.graphvizRender=a.a,e.highlightRender=s.a,e.mathRender=c.a,e.mermaidRender=u.a,e.chartRender=i.a,e.abcRender=r.a,e.mindmapRender=p.a,e.outlineRender=h.a,e.mediaRender=d.a,e.speechRender=S,e.lazyLoadImageRender=l,e.md2html=T,e.preview=L,e.setCodeTheme=j.a,e.setContentTheme=m.a,e}();t.default=_},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M32 6.857v-2.286c0-0.625-0.518-1.143-1.143-1.143h-29.714c-0.625 0-1.143 0.518-1.143 1.143v2.286c0 0.625 0.518 1.143 1.143 1.143h29.714c0.625 0 1.143-0.518 1.143-1.143zM25.143 13.714v-2.286c0-0.625-0.518-1.143-1.143-1.143h-16c-0.625 0-1.143 0.518-1.143 1.143v2.286c0 0.625 0.518 1.143 1.143 1.143h16c0.625 0 1.143-0.518 1.143-1.143zM29.714 20.571v-2.286c0-0.625-0.518-1.143-1.143-1.143h-25.143c-0.625 0-1.143 0.518-1.143 1.143v2.286c0 0.625 0.518 1.143 1.143 1.143h25.143c0.625 0 1.143-0.518 1.143-1.143zM22.857 27.429v-2.286c0-0.625-0.518-1.143-1.143-1.143h-11.429c-0.625 0-1.143 0.518-1.143 1.143v2.286c0 0.625 0.518 1.143 1.143 1.143h11.429c0.625 0 1.143-0.518 1.143-1.143z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M21.334 16.532q0-0.233-0.15-0.384l-5.867-5.867q-0.15-0.15-0.384-0.15t-0.384 0.15l-5.85 5.85q-0.167 0.2-0.167 0.399 0 0.233 0.15 0.384t0.384 0.15h3.733v5.867q0 0.217 0.159 0.375t0.375 0.159h3.2q0.217 0 0.375-0.159t0.159-0.375v-5.867h3.734q0.217 0 0.375-0.159t0.159-0.375zM32 21.332q0 2.65-1.875 4.525t-4.525 1.875h-18.133q-3.083 0-5.275-2.192t-2.192-5.275q0-2.166 1.167-4t3.134-2.75q-0.034-0.5-0.034-0.717 0-3.533 2.5-6.033t6.033-2.5q2.6 0 4.759 1.45t3.142 3.849q1.184-1.033 2.767-1.033 1.767 0 3.017 1.25t1.25 3.017q0 1.267-0.683 2.3 2.166 0.516 3.558 2.258t1.392 3.975z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M6.857 10.857v10.286c0 0.304-0.268 0.571-0.571 0.571-0.143 0-0.304-0.054-0.411-0.161l-5.143-5.143c-0.107-0.107-0.161-0.268-0.161-0.411s0.054-0.304 0.161-0.411l5.143-5.143c0.107-0.107 0.268-0.161 0.411-0.161 0.304 0 0.571 0.268 0.571 0.571zM32 24.571v3.429c0 0.304-0.268 0.571-0.571 0.571h-30.857c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h30.857c0.304 0 0.571 0.268 0.571 0.571zM32 17.714v3.429c0 0.304-0.268 0.571-0.571 0.571h-19.429c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h19.429c0.304 0 0.571 0.268 0.571 0.571zM32 10.857v3.429c0 0.304-0.268 0.571-0.571 0.571h-19.429c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h19.429c0.304 0 0.571 0.268 0.571 0.571zM32 4v3.429c0 0.304-0.268 0.571-0.571 0.571h-30.857c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h30.857c0.304 0 0.571 0.268 0.571 0.571z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M32 25.143v2.286c0 0.625-0.518 1.143-1.143 1.143h-29.714c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h29.714c0.625 0 1.143 0.518 1.143 1.143zM25.143 18.286v2.286c0 0.625-0.518 1.143-1.143 1.143h-22.857c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h22.857c0.625 0 1.143 0.518 1.143 1.143zM29.714 11.429v2.286c0 0.625-0.518 1.143-1.143 1.143h-27.429c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h27.429c0.625 0 1.143 0.518 1.143 1.143zM22.857 4.571v2.286c0 0.625-0.518 1.143-1.143 1.143h-20.571c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h20.571c0.625 0 1.143 0.518 1.143 1.143z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M32 25.143v2.286c0 0.625-0.518 1.143-1.143 1.143h-29.714c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h29.714c0.625 0 1.143 0.518 1.143 1.143zM32 18.286v2.286c0 0.625-0.518 1.143-1.143 1.143h-22.857c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h22.857c0.625 0 1.143 0.518 1.143 1.143zM32 11.429v2.286c0 0.625-0.518 1.143-1.143 1.143h-27.429c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h27.429c0.625 0 1.143 0.518 1.143 1.143zM32 4.571v2.286c0 0.625-0.518 1.143-1.143 1.143h-20.571c-0.625 0-1.143-0.518-1.143-1.143v-2.286c0-0.625 0.518-1.143 1.143-1.143h20.571c0.625 0 1.143 0.518 1.143 1.143z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M9.598 29.866v-16.005c0-0.589-0.478-1.067-1.067-1.067s-1.067 0.478-1.067 1.067v1.067h-4.268v-4.268h1.067c0.589 0 1.067-0.478 1.067-1.067s-0.478-1.067-1.067-1.067h-1.067c-1.178 0-2.134 0.954-2.134 2.134v19.206c0 1.18 0.956 2.134 2.134 2.134h4.268c1.178 0 2.134-0.954 2.134-2.134zM3.196 23.464v-6.402h4.268v6.402h-4.268zM3.196 29.866v-4.268h4.268v4.268h-4.268zM20.268 9.593c0 0.589 0.478 1.067 1.067 1.067h1.067v4.268h-4.268v-1.067c0-0.589-0.478-1.067-1.067-1.067s-1.067 0.478-1.067 1.067v16.005c0 1.18 0.956 2.134 2.134 2.134h10.67c1.18 0 2.134-0.954 2.134-2.134v-19.206c0-1.18-0.954-2.134-2.134-2.134h-7.469c-0.589 0-1.067 0.478-1.067 1.067zM28.804 25.598v4.268h-4.268v-4.268h4.268zM22.402 25.598v4.268h-4.268v-4.268h4.268zM28.804 17.062v6.402h-4.268v-6.402h4.268zM22.402 17.062v6.402h-4.268v-6.402h4.268zM28.804 10.66v4.268h-4.268v-4.268h4.268zM12.799 3.951l-3.651-3.651c-0.397-0.397-1.041-0.397-1.438 0-0.399 0.399-0.399 1.041 0 1.44l3.651 3.651-3.587 3.587c-0.397 0.397-0.397 1.041 0 1.438 0.397 0.399 1.041 0.399 1.438 0l3.587-3.589 3.587 3.587c0.397 0.399 1.041 0.399 1.438 0 0.397-0.397 0.397-1.041 0-1.438l-3.587-3.589 3.649-3.649c0.399-0.399 0.399-1.041 0-1.44-0.397-0.397-1.041-0.397-1.438 0l-3.649 3.653z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M2.134 9.598h16.005c0.589 0 1.067-0.478 1.067-1.067s-0.478-1.067-1.067-1.067h-1.067v-4.268h4.268v1.067c0 0.589 0.478 1.067 1.067 1.067s1.067-0.478 1.067-1.067v-1.067c0-1.178-0.954-2.134-2.134-2.134h-19.206c-1.18 0-2.134 0.956-2.134 2.134v4.268c0 1.178 0.954 2.134 2.134 2.134zM8.536 3.196h6.402v4.268h-6.402v-4.268zM2.134 3.196h4.268v4.268h-4.268v-4.268zM22.407 20.268c-0.589 0-1.067 0.478-1.067 1.067v1.067h-4.268v-4.268h1.067c0.589 0 1.067-0.478 1.067-1.067s-0.478-1.067-1.067-1.067h-16.005c-1.18 0-2.134 0.956-2.134 2.134v10.67c0 1.18 0.954 2.134 2.134 2.134h19.206c1.18 0 2.134-0.954 2.134-2.134v-7.469c0-0.589-0.478-1.067-1.067-1.067zM6.402 28.804h-4.268v-4.268h4.268v4.268zM6.402 22.402h-4.268v-4.268h4.268v4.268zM14.938 28.804h-6.402v-4.268h6.402v4.268zM14.938 22.402h-6.402v-4.268h6.402v4.268zM21.34 28.804h-4.268v-4.268h4.268v4.268zM28.049 12.799l3.651-3.651c0.397-0.397 0.397-1.041 0-1.438-0.399-0.399-1.041-0.399-1.44 0l-3.651 3.651-3.587-3.587c-0.397-0.397-1.041-0.397-1.438 0-0.399 0.397-0.399 1.041 0 1.438l3.589 3.587-3.587 3.587c-0.399 0.397-0.399 1.041 0 1.438 0.397 0.397 1.041 0.397 1.438 0l3.589-3.587 3.649 3.649c0.399 0.399 1.041 0.399 1.44 0 0.397-0.397 0.397-1.041 0-1.438l-3.653-3.649z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M31.628 11.185l-14.734 14.714c-0.496 0.496-1.291 0.496-1.787 0l-14.734-14.714c-0.496-0.496-0.496-1.311 0-1.807l3.296-3.276c0.496-0.496 1.291-0.496 1.787 0l10.544 10.544 10.544-10.544c0.496-0.496 1.291-0.496 1.787 0l3.296 3.276c0.496 0.496 0.496 1.311 0 1.807z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M9.601 29.867v-19.197c0-1.18-0.956-2.133-2.133-2.133h-4.266c-1.177 0-2.133 0.953-2.133 2.133v19.197c0 1.18 0.956 2.133 2.133 2.133h4.266c1.177 0 2.133-0.953 2.133-2.133zM3.202 14.936v-4.266h4.266v4.266h-4.266zM3.202 23.468v-6.399h4.266v6.399h-4.266zM3.202 29.867v-4.266h4.266v4.266h-4.266zM16 10.669v19.197c0 1.18 0.956 2.133 2.133 2.133h10.665c1.18 0 2.133-0.953 2.133-2.133v-19.197c0-1.18-0.953-2.133-2.133-2.133h-10.665c-1.177 0-2.133 0.953-2.133 2.133zM28.798 25.601v4.266h-4.266v-4.266h4.266zM22.399 25.601v4.266h-4.266v-4.266h4.266zM28.798 17.069v6.399h-4.266v-6.399h4.266zM22.399 17.069v6.399h-4.266v-6.399h4.266zM28.798 10.669v4.266h-4.266v-4.266h4.266zM22.399 10.669v4.266h-4.266v-4.266h4.266zM7.713 2.276l3.75 5.606c0.621 0.926 2.058 0.926 2.679 0l3.75-5.606c0.661-0.99-0.094-2.276-1.34-2.276h-7.5c-1.246 0-2.001 1.288-1.34 2.276z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M2.133 9.601h19.197c1.18 0 2.133-0.956 2.133-2.133v-4.266c0-1.177-0.953-2.133-2.133-2.133h-19.197c-1.18 0-2.133 0.956-2.133 2.133v4.266c0 1.177 0.953 2.133 2.133 2.133zM17.064 3.202h4.266v4.266h-4.266v-4.266zM8.532 3.202h6.399v4.266h-6.399v-4.266zM2.133 3.202h4.266v4.266h-4.266v-4.266zM21.331 16h-19.197c-1.18 0-2.133 0.956-2.133 2.133v10.665c0 1.18 0.953 2.133 2.133 2.133h19.197c1.18 0 2.133-0.953 2.133-2.133v-10.665c0-1.177-0.953-2.133-2.133-2.133zM6.399 28.798h-4.266v-4.266h4.266v4.266zM6.399 22.399h-4.266v-4.266h4.266v4.266zM14.931 28.798h-6.399v-4.266h6.399v4.266zM14.931 22.399h-6.399v-4.266h6.399v4.266zM21.331 28.798h-4.266v-4.266h4.266v4.266zM21.331 22.399h-4.266v-4.266h4.266v4.266zM29.724 7.713l-5.606 3.75c-0.926 0.621-0.926 2.058 0 2.679l5.606 3.75c0.99 0.661 2.276-0.094 2.276-1.34v-7.5c0-1.246-1.288-2.001-2.276-1.34z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M5.76 28.16c0 2.12 1.719 3.84 3.84 3.84h12.8c2.12 0 3.84-1.72 3.84-3.84l2.56-20.48h-25.6l2.56 20.48zM19.84 11.52h2.56v16.64h-2.56v-16.64zM14.72 11.52h2.56v16.64h-2.56v-16.64zM9.6 11.52h2.56v16.64h-2.56v-16.64zM28.16 2.56h-8.32c0 0-0.573-2.56-1.28-2.56h-5.12c-0.708 0-1.28 2.56-1.28 2.56h-8.32c-1.061 0-1.92 0.859-1.92 1.92s0 1.92 0 1.92h28.16c0 0 0-0.859 0-1.92s-0.86-1.92-1.92-1.92z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M31.628 22.622l-3.296 3.276c-0.496 0.496-1.291 0.496-1.787 0l-10.544-10.544-10.544 10.544c-0.496 0.496-1.291 0.496-1.787 0l-3.296-3.276c-0.496-0.496-0.496-1.311 0-1.807l14.734-14.714c0.496-0.496 1.291-0.496 1.787 0l14.734 14.714c0.496 0.496 0.496 1.311 0 1.807z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M6.927 17.719s-3.040-3.431-2.915-6.942c0.16-4.453 4.738-10.257 11.359-10.257 1.884 0 5.653 0 10.328 5.52 0.249 0.302-15.075-3.84-18.772 11.679z"></path> <path d="M17.477 9.301s3.946-1.298 7.271-0.178c4.222 1.422 8.693 6.826 6.809 13.182-0.533 1.804-1.609 5.413-8.231 8.32-0.356 0.16 10.613-13.351-5.849-21.323z"></path> <path d="M10.944 24.332c-1.938 2.035-3.751 1.742-3.751 1.742l0.578-3.191c-5.235-3.44-6.373-10.328-6.453-10.106-2.444 6.817-0.916 11.377 0.027 13.004 3.315 5.733 11.982 7.351 17.484 3.893 2.969-1.867 4.533-7.057 4.533-7.057-5.298 2.338-9.342 2.569-12.417 1.715z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=128 height=32 viewBox="0 0 128 32"> <path d="M0 0h128v6.4h-128zM0 12.8h128v6.4h-128zM0 25.6h128v6.4h-128z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 32 32" width=32px height=32px> <path d="M14 18v13l-5-5-6 6-3-3 6-6-5-5zM32 3l-6 6 5 5h-13v-13l5 5 6-6z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M7.521 0c-0.747 0-1.362 0.614-1.362 1.362s0.614 1.362 1.362 1.362h15.593v5.53h-15.56c-0.747 0-1.362 0.614-1.362 1.362s0.614 1.362 1.362 1.362h15.56v5.729h-15.56c-0.747 0-1.362 0.614-1.362 1.362s0.614 1.362 1.362 1.362h15.56v1.876c0 0.747 0.614 1.362 1.362 1.362s1.362-0.614 1.362-1.362v-20.027c0-0.697-0.565-1.262-1.262-1.262h-17.054zM15.458 24.743h13.085c0.482 0 0.88 0.399 0.88 0.88v3.919c0 0.482-0.399 0.88-0.88 0.88h-13.085c-0.482 0-0.88-0.399-0.88-0.88v-3.919c0-0.482 0.399-0.88 0.88-0.88zM7.438 23.331l-4.65 3.819c-0.282 0.232-0.282 0.664 0 0.897l4.65 3.819c0.382 0.316 0.947 0.033 0.947-0.448v-2.74h3.354c0.316 0 0.581-0.266 0.581-0.581v-1.013c0-0.316-0.266-0.581-0.581-0.581h-3.354v-2.723c0-0.498-0.565-0.764-0.947-0.448z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M7.521 32c-0.747 0-1.362-0.614-1.362-1.362s0.614-1.362 1.362-1.362h15.593v-5.53h-15.56c-0.747 0-1.362-0.614-1.362-1.362s0.614-1.362 1.362-1.362h15.56v-5.729h-15.56c-0.747 0-1.362-0.614-1.362-1.362s0.614-1.362 1.362-1.362h15.56v-1.876c0-0.747 0.614-1.362 1.362-1.362s1.362 0.614 1.362 1.362v20.027c0 0.697-0.565 1.262-1.262 1.262h-17.054zM15.458 7.257h13.085c0.482 0 0.88-0.399 0.88-0.88v-3.919c0-0.482-0.399-0.88-0.88-0.88h-13.085c-0.482 0-0.88 0.399-0.88 0.88v3.919c0 0.482 0.399 0.88 0.88 0.88zM7.438 8.669l-4.65-3.819c-0.282-0.232-0.282-0.664 0-0.897l4.65-3.819c0.382-0.316 0.947-0.033 0.947 0.448v2.74h3.354c0.316 0 0.581 0.266 0.581 0.581v1.013c0 0.316-0.266 0.581-0.581 0.581h-3.354v2.723c0 0.498-0.565 0.764-0.947 0.448z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 32 32" width=32px height=32px> <path d="M22.996 15.023c1.339-1.591 2.147-3.643 2.147-5.88 0-5.041-4.102-9.143-9.143-9.143h-11.429v32h13.714c5.041 0 9.143-4.102 9.143-9.143 0-3.32-1.779-6.232-4.433-7.834zM11.429 4.571h3.625c1.999 0 3.625 2.051 3.625 4.571s-1.626 4.571-3.625 4.571h-3.625v-9.143zM17.107 27.429h-5.679v-9.143h5.679c2.087 0 3.786 2.051 3.786 4.571s-1.698 4.571-3.786 4.571z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M11.429 6.095h-9.905c-0.842 0-1.524 0.682-1.524 1.524v1.524c0 0.841 0.682 1.524 1.524 1.524h9.905c0.841 0 1.524-0.682 1.524-1.524v-1.524c0-0.842-0.682-1.524-1.524-1.524zM11.429 13.714h-9.905c-0.842 0-1.524 0.682-1.524 1.524v1.524c0 0.841 0.682 1.524 1.524 1.524h9.905c0.841 0 1.524-0.682 1.524-1.524v-1.524c0-0.841-0.682-1.524-1.524-1.524zM11.429 21.333h-9.905c-0.842 0-1.524 0.682-1.524 1.524v1.524c0 0.841 0.682 1.524 1.524 1.524h9.905c0.841 0 1.524-0.682 1.524-1.524v-1.524c0-0.841-0.682-1.524-1.524-1.524zM30.476 6.095h-12.952c-0.841 0-1.524 0.682-1.524 1.524v16.762c0 0.841 0.682 1.524 1.524 1.524h12.952c0.841 0 1.524-0.682 1.524-1.524v-16.762c0-0.841-0.682-1.524-1.524-1.524z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M32 18.24c0 0.7-0.58 1.28-1.28 1.28h-4.48c0 2.5-0.54 4.38-1.34 5.8l4.16 4.18c0.5 0.5 0.5 1.3 0 1.8-0.24 0.26-0.58 0.38-0.9 0.38s-0.66-0.12-0.9-0.38l-3.96-3.94s-2.62 2.4-6.020 2.4v-17.92h-2.56v17.92c-3.62 0-6.26-2.64-6.26-2.64l-3.66 4.14c-0.26 0.28-0.6 0.42-0.96 0.42-0.3 0-0.6-0.1-0.86-0.32-0.52-0.48-0.56-1.28-0.1-1.82l4.040-4.54c-0.7-1.38-1.16-3.16-1.16-5.48h-4.48c-0.7 0-1.28-0.58-1.28-1.28s0.58-1.28 1.28-1.28h4.48v-5.88l-3.46-3.46c-0.5-0.5-0.5-1.3 0-1.8s1.3-0.5 1.8 0l3.46 3.46h16.88l3.46-3.46c0.5-0.5 1.3-0.5 1.8 0s0.5 1.3 0 1.8l-3.46 3.46v5.88h4.48c0.7 0 1.28 0.58 1.28 1.28zM22.4 6.72h-12.8c0-3.54 2.86-6.4 6.4-6.4s6.4 2.86 6.4 6.4z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M27.094 19.485v6.12c0 3.059-2.483 5.542-5.542 5.542h-16.010c-3.059 0-5.542-2.483-5.542-5.542v-16.010c0-3.059 2.483-5.542 5.542-5.542h16.010c0.769 0 1.54 0.154 2.251 0.481 0.174 0.077 0.308 0.25 0.346 0.443 0.039 0.211-0.019 0.404-0.174 0.558l-0.943 0.943c-0.115 0.115-0.289 0.193-0.443 0.193-0.058 0-0.115-0.019-0.174-0.039-0.289-0.077-0.578-0.115-0.866-0.115h-16.010c-1.693 0-3.079 1.386-3.079 3.079v16.010c0 1.693 1.386 3.079 3.079 3.079h16.010c1.693 0 3.079-1.386 3.079-3.079v-4.888c0-0.154 0.058-0.308 0.174-0.424l1.232-1.232c0.135-0.135 0.289-0.193 0.443-0.193 0.077 0 0.154 0.019 0.231 0.058 0.231 0.096 0.385 0.308 0.385 0.558zM31.54 10.076l-15.664 15.664c-0.615 0.615-1.578 0.615-2.194 0l-8.275-8.275c-0.615-0.615-0.615-1.578 0-2.194l2.116-2.116c0.615-0.615 1.578-0.615 2.194 0l5.060 5.060 12.451-12.451c0.615-0.615 1.578-0.615 2.194 0l2.116 2.116c0.615 0.615 0.615 1.578 0 2.194z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M2.783 5.565v20.87h26.435v-20.87h-26.435zM2.783 2.783h26.435c1.535 0 2.783 1.248 2.783 2.783v20.87c0 1.535-1.248 2.783-2.783 2.783h-26.435c-1.535 0-2.783-1.248-2.783-2.783v-20.87c0-1.535 1.248-2.783 2.783-2.783zM0 9.739h31.304v1.391h-31.304v-1.391zM4.87 6.957h1.391c0.383 0 0.696 0.313 0.696 0.696s-0.313 0.696-0.696 0.696h-1.391c-0.383 0-0.696-0.313-0.696-0.696s0.313-0.696 0.696-0.696zM9.043 6.957h1.391c0.383 0 0.696 0.313 0.696 0.696s-0.313 0.696-0.696 0.696h-1.391c-0.383 0-0.696-0.313-0.696-0.696s0.313-0.696 0.696-0.696z"></path> <path d="M11.817 16.47v1.291l-4.635 1.965 4.635 1.965v1.291l-6.322-2.73v-1.052l6.322-2.73zM16.443 15.47h1.226l-3.648 8.739h-1.239l3.661-8.739zM19.33 16.47l6.322 2.73v1.052l-6.322 2.73v-1.291l4.635-1.965-4.635-1.965v-1.291z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"><path d="M21.053 21.895l2.526 2.526 8.421-8.421-8.421-8.421-2.526 2.526 5.895 5.895z"></path><path d="M10.947 10.105l-2.526-2.526-8.421 8.421 8.421 8.421 2.526-2.526-5.895-5.895z"></path><path d="M17.613 6.487l1.828 0.499-5.052 18.527-1.828-0.499 5.052-18.527z"></path></svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M15.931 22.315l2.081-2.081-2.727-2.727-2.081 2.081v1.005h1.722v1.722h1.005zM23.824 9.398c-0.161-0.161-0.431-0.143-0.592 0.018l-6.279 6.279c-0.161 0.161-0.179 0.431-0.018 0.592s0.431 0.143 0.592-0.018l6.279-6.279c0.161-0.161 0.179-0.431 0.018-0.592zM25.259 20.054v3.409c0 2.852-2.314 5.167-5.167 5.167h-14.926c-2.852 0-5.167-2.314-5.167-5.167v-14.926c0-2.852 2.314-5.167 5.167-5.167h14.926c0.718 0 1.435 0.143 2.099 0.449 0.161 0.072 0.287 0.233 0.323 0.413 0.036 0.197-0.018 0.377-0.161 0.52l-0.879 0.879c-0.161 0.161-0.377 0.215-0.574 0.144-0.269-0.072-0.538-0.108-0.807-0.108h-14.926c-1.579 0-2.87 1.292-2.87 2.87v14.926c0 1.579 1.292 2.87 2.87 2.87h14.926c1.579 0 2.87-1.292 2.87-2.87v-2.26c0-0.143 0.054-0.287 0.161-0.395l1.148-1.148c0.179-0.179 0.413-0.215 0.628-0.126s0.359 0.287 0.359 0.52zM23.537 6.815l5.167 5.167-12.055 12.055h-5.167v-5.167zM31.502 9.183l-1.65 1.65-5.167-5.167 1.65-1.65c0.664-0.664 1.776-0.664 2.44 0l2.727 2.727c0.664 0.664 0.664 1.776 0 2.44z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 32 32" width=32px height=32px> <path d="M16 24.789c-3.756 0-6.911-2.254-8.188-5.559h16.376c-1.277 3.305-4.432 5.559-8.188 5.559zM10.366 14.423c-1.352 0-2.404-1.052-2.404-2.404s1.052-2.404 2.404-2.404 2.404 1.052 2.404 2.404-1.052 2.404-2.404 2.404zM21.634 14.423c-1.352 0-2.404-1.052-2.404-2.404s1.052-2.404 2.404-2.404 2.404 1.052 2.404 2.404-1.052 2.404-2.404 2.404zM16 28.845c7.061 0 12.845-5.784 12.845-12.845s-5.784-12.845-12.845-12.845-12.845 5.784-12.845 12.845 5.784 12.845 12.845 12.845zM16 0c8.864 0 16 7.136 16 16s-7.136 16-16 16-16-7.136-16-16 7.136-16 16-16z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M24 25.279h-20.8v-14.4h4.152c0 0 1.102-1.434 3.472-3.2h-9.224c-0.885 0-1.6 0.718-1.6 1.6v17.6c0 0.885 0.715 1.6 1.6 1.6h24c0.885 0 1.6-0.715 1.6-1.6v-5.994l-3.2 2.632v1.762zM21.378 14.159v5.682l10.622-8.322-10.622-7.998v5.010c-12.898 0-12.898 12.749-12.898 12.749 3.651-5.997 5.898-7.12 12.898-7.12z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg viewBox="0 0 32 32" width=32px height=32px> <path d="M32 0v13l-5-5-6 6-3-3 6-6-5-5zM14 21l-6 6 5 5h-13v-13l5 5 6-6z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M4.45 0h3.594c0.595 0 1.078 0.384 1.078 0.858v30.284c0 0.474-0.482 0.858-1.078 0.858h-3.594c-0.595 0-1.078-0.384-1.078-0.858v-30.284c-0-0.474 0.482-0.858 1.078-0.858zM23.888 0h3.673c0.59 0 1.068 0.384 1.068 0.858v30.284c0 0.474-0.478 0.858-1.068 0.858h-3.673c-0.59 0-1.068-0.384-1.068-0.858v-30.284c0-0.474 0.478-0.858 1.068-0.858z"></path> <path d="M25.069 14.167v3.667c0 0.589-0.384 1.065-0.858 1.065h-15.655c-0.474 0-0.858-0.477-0.858-1.065v-3.667c0-0.589 0.384-1.065 0.858-1.065h15.655c0.474 0 0.858 0.477 0.858 1.065z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M19.652 25v6c0 0.55-0.45 1-1 1h-6c-0.55 0-1-0.45-1-1v-6c0-0.55 0.45-1 1-1h6c0.55 0 1 0.45 1 1zM27.552 10c0 4.75-3.225 6.575-5.6 7.9-1.475 0.85-2.4 2.575-2.4 3.3v0c0 0.55-0.425 1.2-1 1.2h-6c-0.55 0-0.9-0.85-0.9-1.4v-1.125c0-3.025 3-5.625 5.2-6.625 1.925-0.875 2.725-1.7 2.725-3.3 0-1.4-1.825-2.65-3.85-2.65-1.125 0-2.15 0.35-2.7 0.725-0.6 0.425-1.2 1.025-2.675 2.875-0.2 0.25-0.5 0.4-0.775 0.4-0.225 0-0.425-0.075-0.625-0.2l-4.1-3.125c-0.425-0.325-0.525-0.875-0.25-1.325 2.7-4.475 6.5-6.65 11.6-6.65 5.35 0 11.35 4.275 11.35 10z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M6.286 16c0 0.143-0.054 0.304-0.161 0.411l-5.143 5.143c-0.107 0.107-0.268 0.161-0.411 0.161-0.304 0-0.571-0.268-0.571-0.571v-10.286c0-0.304 0.268-0.571 0.571-0.571 0.143 0 0.304 0.054 0.411 0.161l5.143 5.143c0.107 0.107 0.161 0.268 0.161 0.411zM32 24.571v3.429c0 0.304-0.268 0.571-0.571 0.571h-30.857c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h30.857c0.304 0 0.571 0.268 0.571 0.571zM32 17.714v3.429c0 0.304-0.268 0.571-0.571 0.571h-19.429c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h19.429c0.304 0 0.571 0.268 0.571 0.571zM32 10.857v3.429c0 0.304-0.268 0.571-0.571 0.571h-19.429c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h19.429c0.304 0 0.571 0.268 0.571 0.571zM32 4v3.429c0 0.304-0.268 0.571-0.571 0.571h-30.857c-0.304 0-0.571-0.268-0.571-0.571v-3.429c0-0.304 0.268-0.571 0.571-0.571h30.857c0.304 0 0.571 0.268 0.571 0.571z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M23.273 27.636v2.909c0 0.795-0.659 1.455-1.455 1.455h-11.636c-0.795 0-1.455-0.659-1.455-1.455v-2.909c0-0.795 0.659-1.455 1.455-1.455h1.455v-8.727h-1.455c-0.795 0-1.455-0.659-1.455-1.455v-2.909c0-0.795 0.659-1.455 1.455-1.455h8.727c0.795 0 1.455 0.659 1.455 1.455v13.091h1.455c0.795 0 1.455 0.659 1.455 1.455zM20.364 1.455v4.364c0 0.795-0.659 1.455-1.455 1.455h-5.818c-0.795 0-1.455-0.659-1.455-1.455v-4.364c0-0.795 0.659-1.455 1.455-1.455h5.818c0.795 0 1.455 0.659 1.455 1.455z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M18.133 23.467l3.2 3.2 10.667-10.667-10.667-10.667-3.2 3.2 7.467 7.467z"></path> <path d="M13.867 8.533l-3.2-3.2-10.667 10.667 10.667 10.667 3.2-3.2-7.467-7.467z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M29.714 0v2.286h-4.571l-11.429 27.429h4.571v2.286h-16v-2.286h4.571l11.429-27.429h-4.571v-2.286z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M31.68 14.56h-31.36c-0.176 0-0.32 0.144-0.32 0.32v2.24c0 0.176 0.144 0.32 0.32 0.32h31.36c0.176 0 0.32-0.144 0.32-0.32v-2.24c0-0.176-0.144-0.32-0.32-0.32z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M29.187 2.933l-0.12-0.121c-2.813-2.812-7.415-2.812-10.228 0l-6.516 6.517c-2.812 2.812-2.812 7.415 0 10.227l0.12 0.12c0.234 0.234 0.482 0.446 0.739 0.641l2.386-2.386c-0.278-0.164-0.542-0.361-0.78-0.599l-0.121-0.121c-1.527-1.527-1.527-4.012 0-5.539l6.517-6.516c1.527-1.527 4.012-1.527 5.539 0l0.121 0.12c1.527 1.527 1.527 4.012 0 5.539l-2.948 2.948c0.512 1.264 0.754 2.611 0.733 3.955l4.559-4.559c2.812-2.812 2.812-7.415-0-10.227zM19.557 12.323c-0.234-0.234-0.482-0.446-0.739-0.641l-2.386 2.385c0.278 0.164 0.542 0.361 0.78 0.599l0.121 0.121c1.527 1.527 1.527 4.012 0 5.539l-6.517 6.517c-1.527 1.527-4.012 1.527-5.539 0l-0.121-0.121c-1.527-1.527-1.527-4.012 0-5.539l2.948-2.948c-0.512-1.264-0.754-2.611-0.733-3.955l-4.559 4.559c-2.812 2.812-2.812 7.415 0 10.228l0.12 0.12c2.813 2.812 7.415 2.812 10.228 0l6.516-6.517c2.812-2.812 2.812-7.415 0-10.228l-0.12-0.12z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M12 2h20v4h-20v-4zM12 14h20v4h-20v-4zM12 26h20v4h-20v-4zM0 4c0 2.209 1.791 4 4 4s4-1.791 4-4c0-2.209-1.791-4-4-4s-4 1.791-4 4zM0 16c0 2.209 1.791 4 4 4s4-1.791 4-4c0-2.209-1.791-4-4-4s-4 1.791-4 4zM0 28c0 2.209 1.791 4 4 4s4-1.791 4-4c0-2.209-1.791-4-4-4s-4 1.791-4 4z"></path> </svg>'},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M8.727 13.818v4.364c0 1.205-0.977 2.182-2.182 2.182h-4.364c-1.205 0-2.182-0.977-2.182-2.182v-4.364c0-1.205 0.977-2.182 2.182-2.182h4.364c1.205 0 2.182 0.977 2.182 2.182zM20.364 13.818v4.364c0 1.205-0.977 2.182-2.182 2.182h-4.364c-1.205 0-2.182-0.977-2.182-2.182v-4.364c0-1.205 0.977-2.182 2.182-2.182h4.364c1.205 0 2.182 0.977 2.182 2.182zM32 13.818v4.364c0 1.205-0.977 2.182-2.182 2.182h-4.364c-1.205 0-2.182-0.977-2.182-2.182v-4.364c0-1.205 0.977-2.182 2.182-2.182h4.364c1.205 0 2.182 0.977 2.182 2.182z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M11 26h20v4h-20zM11 14h20v4h-20zM11 2h20v4h-20zM5 0v8h-2v-6h-2v-2zM3 16.438v1.563h4v2h-6v-4.563l4-1.875v-1.563h-4v-2h6v4.563zM7 22v10h-6v-2h4v-2h-4v-2h4v-2h-4v-2z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M0 16c3.037-5.864 9.058-9.802 16-9.802s12.963 3.938 15.953 9.703l0.047 0.1c-3.037 5.864-9.058 9.802-16 9.802s-12.963-3.938-15.953-9.703l-0.047-0.1zM16 22.531c3.607 0 6.531-2.924 6.531-6.531s-2.924-6.531-6.531-6.531v0c-3.607 0-6.531 2.924-6.531 6.531s2.924 6.531 6.531 6.531v0zM16 19.265c-1.804 0-3.265-1.461-3.265-3.265s1.461-3.265 3.265-3.265v0c1.804 0 3.265 1.461 3.265 3.265s-1.461 3.265-3.265 3.265v0z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M7.024 13.003c3.862 0 6.993 3.131 6.993 6.993s-3.131 6.993-6.993 6.993-6.993-3.131-6.993-6.993l-0.031-0.999c0-7.724 6.262-13.986 13.986-13.986v3.996c-2.668 0-5.177 1.039-7.064 2.926-0.363 0.363-0.695 0.75-0.994 1.156 0.357-0.056 0.723-0.086 1.096-0.086zM25.007 13.003c3.862 0 6.993 3.131 6.993 6.993s-3.131 6.993-6.993 6.993-6.993-3.131-6.993-6.993l-0.031-0.999c0-7.724 6.262-13.986 13.986-13.986v3.996c-2.668 0-5.177 1.039-7.064 2.926-0.363 0.363-0.695 0.75-0.994 1.156 0.357-0.056 0.723-0.086 1.096-0.086z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M4.571 16c0 6.312 5.117 11.429 11.429 11.429s11.429-5.117 11.429-11.429v0c0-6.312-5.117-11.429-11.429-11.429s-11.429 5.117-11.429 11.429v0z"></path> <path d="M16 30.857c-8.229 0-14.933-6.705-14.933-14.933s6.705-14.933 14.933-14.933 15.010 6.705 15.010 15.010c0 8.152-6.705 14.857-15.010 14.857zM16 0c-8.838 0-16 7.162-16 16s7.162 16 16 16 16-7.162 16-16-7.162-16-16-16z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M19.583 9.75q-8.667 1.25-13.375 6.625t-6.208 12.958q6.417-9.083 19.583-9.083v7.25l12.417-12.417-12.417-12.417v7.083z"></path> </svg>'},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M32 16v2h-7.328c0.86 1.203 1.328 2.584 1.328 4 0 2.215-1.146 4.345-3.143 5.843-1.855 1.391-4.29 2.157-6.857 2.157s-5.002-0.766-6.857-2.157c-1.998-1.498-3.143-3.628-3.143-5.843h4c0 2.168 2.748 4 6 4s6-1.832 6-4c0-2.168-2.748-4-6-4h-16v-2h9.36c-0.073-0.052-0.146-0.104-0.217-0.157-1.998-1.498-3.143-3.628-3.143-5.843s1.146-4.345 3.143-5.843c1.855-1.391 4.29-2.157 6.857-2.157s5.002 0.766 6.857 2.157c1.997 1.498 3.143 3.628 3.143 5.843h-4c0-2.168-2.748-4-6-4s-6 1.832-6 4c0 2.168 2.748 4 6 4 2.468 0 4.814 0.709 6.64 2h9.36z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M9.846 26.462v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM9.846 19.077v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM19.692 26.462v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM9.846 11.692v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM19.692 19.077v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM29.538 26.462v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM19.692 11.692v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM29.538 19.077v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM29.538 11.692v-3.692c0-0.346-0.269-0.615-0.615-0.615h-6.154c-0.346 0-0.615 0.269-0.615 0.615v3.692c0 0.346 0.269 0.615 0.615 0.615h6.154c0.346 0 0.615-0.269 0.615-0.615zM32 5.538v20.923c0 1.692-1.385 3.077-3.077 3.077h-25.846c-1.692 0-3.077-1.385-3.077-3.077v-20.923c0-1.692 1.385-3.077 3.077-3.077h25.846c1.692 0 3.077 1.385 3.077 3.077z"></path> </svg> '},function(e,t){e.exports='<svg version=1.1 xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M25.75 16q1.083 0 1.875-0.75t0.792-1.917-0.792-1.917-1.875-0.75-1.875 0.75-0.792 1.917 0.792 1.917 1.875 0.75zM20.417 8.917q1.083 0 1.875-0.792t0.792-1.875-0.792-1.875-1.875-0.792-1.875 0.792-0.792 1.875 0.792 1.875 1.875 0.792zM11.583 8.917q1.083 0 1.875-0.792t0.792-1.875-0.792-1.875-1.875-0.792-1.875 0.792-0.792 1.875 0.792 1.875 1.875 0.792zM6.25 16q1.083 0 1.875-0.75t0.792-1.917-0.792-1.917-1.875-0.75-1.875 0.75-0.792 1.917 0.792 1.917 1.875 0.75zM16 0q6.583 0 11.292 4.167t4.708 10.083q0 3.667-2.625 6.25t-6.292 2.583h-3.083q-1.167 0-1.917 0.792t-0.75 1.875q0 0.917 0.667 1.75t0.667 1.833q0 1.167-0.75 1.917t-1.917 0.75q-6.667 0-11.333-4.667t-4.667-11.333 4.667-11.333 11.333-4.667z"></path> </svg> '},function(e,t){e.exports='<svg xmlns=http://www.w3.org/2000/svg width=32 height=32 viewBox="0 0 32 32"> <path d="M12.417 9.75q8.667 1.25 13.375 6.625t6.208 12.958q-6.417-9.083-19.583-9.083v7.25l-12.417-12.417 12.417-12.417v7.083z"></path> </svg> '},function(e,t,n){"use strict";n.r(t);var r,i,o=n(26),a=n(2),s=n(8),l=function(e){return"sv"===e.currentMode?Object(s.a)((e.sv.element.textContent+"\n").replace(/\n\n$/,"\n")):"wysiwyg"===e.currentMode?e.lute.VditorDOM2Md(e.wysiwyg.element.innerHTML):"ir"===e.currentMode?e.lute.VditorIRDOM2Md(e.ir.element.innerHTML):""},c=n(5),d=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-devtools",this.element.innerHTML='<div class="vditor-reset--error"></div><div style="height: 100%;"></div>'}return e.prototype.renderEchart=function(e){var t=this;"block"===e.devtools.element.style.display&&Object(c.a)(e.options.cdn+"/dist/js/echarts/echarts.min.js","vditorEchartsScript").then((function(){t.ASTChart||(t.ASTChart=echarts.init(e.devtools.element.lastElementChild));try{t.element.lastElementChild.style.display="block",t.element.firstElementChild.innerHTML="",t.ASTChart.setOption({series:[{data:JSON.parse(e.lute.RenderEChartsJSON(l(e))),initialTreeDepth:-1,label:{align:"left",fontSize:12,offset:[9,12],position:"top",verticalAlign:"middle"},lineStyle:{color:"#4285f4",type:"curve"},orient:"vertical",roam:!0,type:"tree"}],toolbox:{bottom:25,emphasis:{iconStyle:{color:"#4285f4"}},feature:{restore:{show:!0},saveAsImage:{show:!0}},right:15,show:!0}}),t.ASTChart.resize()}catch(e){t.element.lastElementChild.style.display="none",t.element.firstElementChild.innerHTML=e}}))},e}(),u=n(1),p=function(e,t){t.forEach((function(t){if(e[t]){var n=e[t].children[0];n&&n.classList.contains("vditor-menu--current")&&n.classList.remove("vditor-menu--current")}}))},h=function(e,t){t.forEach((function(t){if(e[t]){var n=e[t].children[0];n&&!n.classList.contains("vditor-menu--current")&&n.classList.add("vditor-menu--current")}}))},f=function(e,t){t.forEach((function(t){if(e[t]){var n=e[t].children[0];n&&n.classList.contains(a.a.CLASS_MENU_DISABLED)&&n.classList.remove(a.a.CLASS_MENU_DISABLED)}}))},m=function(e,t){t.forEach((function(t){if(e[t]){var n=e[t].children[0];n&&!n.classList.contains(a.a.CLASS_MENU_DISABLED)&&n.classList.add(a.a.CLASS_MENU_DISABLED)}}))},v=function(e,t){t.forEach((function(t){e[t]&&e[t]&&(e[t].style.display="none")}))},g=function(e,t){t.forEach((function(t){e[t]&&e[t]&&(e[t].style.display="block")}))},b=function(e,t,n){t.includes("subToolbar")&&(e.toolbar.element.querySelectorAll(".vditor-hint").forEach((function(e){n&&e.isEqualNode(n)||(e.style.display="none")})),e.toolbar.elements.emoji&&(e.toolbar.elements.emoji.lastElementChild.style.display="none")),t.includes("hint")&&(e.hint.element.style.display="none"),e.wysiwyg.popover&&t.includes("popover")&&(e.wysiwyg.popover.style.display="none")},y=function(e,t,n,r){n.addEventListener(Object(u.b)(),(function(r){r.preventDefault(),r.stopPropagation(),n.classList.contains(a.a.CLASS_MENU_DISABLED)||(e.toolbar.element.querySelectorAll(".vditor-hint--current").forEach((function(e){e.classList.remove("vditor-hint--current")})),"block"===t.style.display?t.style.display="none":(b(e,["subToolbar","hint","popover"],n.parentElement.parentElement),n.classList.contains("vditor-tooltipped")||n.classList.add("vditor-hint--current"),t.style.display="block",e.toolbar.element.getBoundingClientRect().right-n.getBoundingClientRect().right<250?t.classList.add("vditor-panel--left"):t.classList.remove("vditor-panel--left")))}))},w=n(3),E=n(6),M=n(0),S=function(e){clearTimeout(e.ir.hlToolbarTimeoutId),e.ir.hlToolbarTimeoutId=window.setTimeout((function(){if("false"!==e.ir.element.getAttribute("contenteditable")&&Object(M.e)(e.ir.element)){p(e.toolbar.elements,a.a.EDIT_TOOLBARS),f(e.toolbar.elements,a.a.EDIT_TOOLBARS);var t=Object(M.b)(e.ir.element),n=t.startContainer;3===t.startContainer.nodeType&&(n=t.startContainer.parentElement),n.classList.contains("vditor-reset")&&(n=n.childNodes[t.startOffset]),Object(E.a)(n)&&h(e.toolbar.elements,["headings"]),Object(w.f)(n,"BLOCKQUOTE")&&h(e.toolbar.elements,["quote"]),Object(w.d)(n,"data-type","a")&&h(e.toolbar.elements,["link"]),Object(w.d)(n,"data-type","em")&&h(e.toolbar.elements,["italic"]),Object(w.d)(n,"data-type","strong")&&h(e.toolbar.elements,["bold"]),Object(w.d)(n,"data-type","s")&&h(e.toolbar.elements,["strike"]),Object(w.d)(n,"data-type","code")&&(m(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),h(e.toolbar.elements,["inline-code"])),Object(w.d)(n,"data-type","code-block")&&(m(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),h(e.toolbar.elements,["code"])),Object(w.f)(n,"TABLE")&&m(e.toolbar.elements,["table"]);var r=Object(w.f)(n,"LI");r?(r.classList.contains("vditor-task")?h(e.toolbar.elements,["check"]):"OL"===r.parentElement.tagName?h(e.toolbar.elements,["ordered-list"]):"UL"===r.parentElement.tagName&&h(e.toolbar.elements,["list"]),f(e.toolbar.elements,["outdent","indent"])):m(e.toolbar.elements,["outdent","indent"])}}),200)},O=function(e,t,n,r){r&&console.log(e+" - "+n+": "+t)},k=n(10),x=n(11),T=n(12),L=n(13),j=n(14),_=n(15),C=n(16),A=n(17),N=function(e,t,n){void 0===n&&(n="sv");var r=document.createElement("div");r.innerHTML=e;var i=!1;1===r.childElementCount&&r.lastElementChild.style.fontFamily.indexOf("monospace")>-1&&(i=!0);var o=r.querySelectorAll("pre");if(1===r.childElementCount&&1===o.length&&"vditor-wysiwyg"!==o[0].className&&"vditor-textarea"!==o[0].className&&(i=!0),0===e.indexOf('\n<p class="p1">')&&(i=!0),i){var a=t||e;return/\n/.test(a)||1===o.length?"wysiwyg"===n?'<div class="vditor-wysiwyg__block" data-block="0" data-type="code-block"><pre><code>'+a.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"<wbr></code></pre></div>":"ir"===n?"```\n"+a.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"\n```":"```\n"+a+"\n```":"wysiwyg"===n?"<code>"+a.replace(/&/g,"&amp;").replace(/</g,"&lt;")+"</code><wbr>":"`"+a+"`"}return!1},H=function(e,t){if(e){var n=e.querySelector("code");if(n){var r=n.className.replace("language-","");if("abc"===r)Object(k.a)(e,t.options.cdn);else if("mermaid"===r)Object(C.a)(e,".vditor-"+t.currentMode+"__preview .language-mermaid",t.options.cdn);else if("echarts"===r)Object(x.a)(e,t.options.cdn);else if("mindmap"===r)Object(A.a)(e,t.options.cdn);else if("graphviz"===r)Object(L.a)(e,t.options.cdn);else if("math"===r){var i="div";"SPAN"===e.tagName&&(i="span"),e.innerHTML='<code class="language-math"><'+i+' class="vditor-math">'+e.innerHTML+"</"+i+"></code>",Object(_.a)(e.parentElement,{cdn:t.options.cdn,math:t.options.preview.math})}else Object(j.a)(Object.assign({},t.options.preview.hljs),e,t.options.cdn),Object(T.a)(e,t.options.lang);e.setAttribute("data-render","1")}else"html-block"===e.parentElement.getAttribute("data-type")&&(e.style.backgroundColor="var(--preview-background-color)",e.style.padding="0.2em 0.4em",e.setAttribute("data-render","1"))}},D=function(e,t,n){void 0===n&&(n=!1);var r=Object(w.c)(t.startContainer);if(r&&!n){if(at(r.innerHTML)||st(r.innerHTML,e.options.preview.markdown.setext))return;for(var i=Object(M.c)(r,t).start,o=!0,a=i-1;a>r.textContent.substr(0,i).lastIndexOf("\n");a--)if(" "!==r.textContent.charAt(a)&&"\t"!==r.textContent.charAt(a)){o=!1;break}0===i&&(o=!1);var s=!0;for(a=i-1;a<r.textContent.length;a++)if(" "!==r.textContent.charAt(a)&&"\n"!==r.textContent.charAt(a)){s=!1;break}if(o&&!r.querySelector(".language-mindmap")||s){if(!s)return;if(!Object(w.e)(t.startContainer,"vditor-ir__marker")){var l=t.startContainer.previousSibling;return void(l&&3!==l.nodeType&&l.classList.contains("vditor-ir__node--expand")&&l.classList.remove("vditor-ir__node--expand"))}}}if(e.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach((function(e){e.classList.remove("vditor-ir__node--expand")})),r||(r=e.ir.element),!r.querySelector("wbr")){var c=Object(w.e)(t.startContainer,"vditor-ir__preview");c&&(c.previousElementSibling.firstElementChild?t.selectNodeContents(c.previousElementSibling.firstElementChild):t.selectNodeContents(c.previousElementSibling),t.collapse(!1)),t.insertNode(document.createElement("wbr"))}r.querySelectorAll("[style]").forEach((function(e){e.removeAttribute("style")})),"link-ref-defs-block"===r.getAttribute("data-type")&&(r=e.ir.element);var d=r.isEqualNode(e.ir.element),u=Object(w.d)(r,"data-type","footnotes-block"),p="";if(d)p=r.innerHTML;else{var h,f,m=Object(w.b)(t.startContainer);if(m)r=Object(E.b)(t.startContainer,"BLOCKQUOTE")?Object(w.c)(t.startContainer)||r:m;if(u&&(r=u),p=r.outerHTML,"UL"===r.tagName||"OL"===r.tagName){var v=r.previousElementSibling,g=r.nextElementSibling;!v||"UL"!==v.tagName&&"OL"!==v.tagName||(p=v.outerHTML+p,v.remove()),!g||"UL"!==g.tagName&&"OL"!==g.tagName||(p+=g.outerHTML,g.remove()),p=p.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}else r.previousElementSibling&&""!==r.previousElementSibling.textContent&&(p=r.previousElementSibling.outerHTML+p,r.previousElementSibling.remove());(h=e.ir.element.querySelector("[data-type='link-ref-defs-block']"))&&!r.isEqualNode(h)&&(p+=h.outerHTML,h.remove()),(f=e.ir.element.querySelector("[data-type='footnotes-block']"))&&!r.isEqualNode(f)&&(p+=f.outerHTML,f.remove())}if(O("SpinVditorIRDOM",p,"argument",e.options.debugger),p=e.lute.SpinVditorIRDOM(p),O("SpinVditorIRDOM",p,"result",e.options.debugger),d)r.innerHTML=p;else if(r.outerHTML=p,(h=e.ir.element.querySelector("[data-type='link-ref-defs-block']"))&&e.ir.element.insertAdjacentElement("beforeend",h),(f=e.ir.element.querySelector("[data-type='footnotes-block']"))&&e.ir.element.insertAdjacentElement("beforeend",f),u){var b=Object(w.d)(e.ir.element.querySelector("wbr"),"data-type","footnotes-def");if(b){var y=b.textContent,S=y.substring(1,y.indexOf("]:")),k=e.ir.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'+S+'"]');k&&k.setAttribute("aria-label",y.substr(S.length+3).trim())}}Object(M.f)(e.ir.element,t),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach((function(t){H(t,e)})),ct(e),Tt(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},R=n(4),I=function(e,t){void 0===t&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var n=l(e);e.options.counter.enable&&e.counter.render(e,n),"function"==typeof e.options.input&&t.enableInput&&e.options.input(n,e.preview.element),t.enableHint&&e.hint.render(e),e.options.cache.enable&&Object(u.a)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.preview.render(e),t.enableAddUndoStack&&e.undo.addToUndoStack(e),e.devtools&&e.devtools.renderEchart(e)},z=function(e,t,n,r){void 0===r&&(r={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var i=t.replace(/\r\n/g,"\n").replace(/\r/g,"\n").split("\n"),o="",a='<span><br><span style="display: none">\n</span></span>',l=!0;i.forEach((function(e,t){""!==e&&(l=!1),t===i.length-1&&""===e||(o+=e?"<span>"+Object(s.a)(e.replace(/&/g,"&amp;").replace(/</g,"&lt;"))+"</span>"+a:a)})),i.length<=2&&l?e.sv.element.innerHTML="":e.sv.element.innerHTML=o||a,n&&Object(M.g)(n.start,n.end,e.sv.element),I(e,{enableAddUndoStack:r.enableAddUndoStack,enableHint:r.enableHint,enableInput:r.enableInput})},P=function(e,t,n,r,i){void 0===r&&(r=!1),void 0===i&&(i=!1);var o=Object(M.b)(e.sv.element),a=Object(M.c)(e.sv.element,o),s=l(e);if(o.collapsed||!o.collapsed&&r){var c=t+n;z(e,s.substring(0,a.start)+c+s.substring(a.end),{end:a.start+t.length,start:a.start+t.length})}else{var d=s.substring(a.start,a.end);if(i&&s.substring(a.start-t.length,a.start)===t&&s.substring(a.end,a.end+n.length)===n)z(e,s.substring(0,a.start-t.length)+d+s.substring(a.end+n.length),{end:a.start-t.length+d.length,start:a.start-t.length});else{c=t+d+n;z(e,s.substring(0,a.start)+c+s.substring(a.end),{end:a.start+t.length+d.length,start:a.start+t.length})}}},B=function(e){switch(e.currentMode){case"ir":return e.ir.element;case"wysiwyg":return e.wysiwyg.element;case"sv":return e.sv.element}},q=function(e,t){e.options.upload.setHeaders&&(e.options.upload.headers=e.options.upload.setHeaders()),e.options.upload.headers&&Object.keys(e.options.upload.headers).forEach((function(n){t.setRequestHeader(n,e.options.upload.headers[n])}))},U=function(){this.isUploading=!1,this.element=document.createElement("div"),this.element.className="vditor-upload"},F=function(e,t,n){for(var r,i=[],o=t.length,a=0;a<o;a++){var s=t[a];s instanceof DataTransferItem&&(s=s.getAsFile()),i.push(s)}if(e.options.upload.handler)return"string"==typeof(r=e.options.upload.handler(i))?void e.tip.show(r):void 0;if(!e.options.upload.url||!e.upload)return n&&(n.value=""),void e.tip.show("please config: options.upload.url");if((e.options.upload.file&&(i=e.options.upload.file(i)),e.options.upload.validate)&&"string"==typeof(r=e.options.upload.validate(i)))return void e.tip.show(r);var l=B(e);e.upload.range=Object(M.b)(l);var c=function(e,t){e.tip.hide();for(var n=[],r="",i="",o=e.options.lang,a=function(a,s){var l=t[s],c=!0;l.name||(r+="<li>"+R.a[o].nameEmpty+"</li>",c=!1),l.size>e.options.upload.max&&(r+="<li>"+l.name+" "+R.a[o].over+" "+e.options.upload.max/1024/1024+"M</li>",c=!1);var d=l.name.lastIndexOf("."),u=l.name.substr(d),p=e.options.upload.filename(l.name.substr(0,d))+u;if(e.options.upload.accept){var h=!1;e.options.upload.accept.split(",").forEach((function(e){var t=e.trim();0===t.indexOf(".")?u===t&&(h=!0):l.type.split("/")[0]===t.split("/")[0]&&(h=!0)})),h||(r+="<li>"+l.name+" "+R.a[o].fileTypeError+"</li>",c=!1)}c&&(n.push(l),i+="<li>"+p+" "+R.a[o].uploading+"</li>")},s=t.length,l=0;l<s;l++)a(0,l);return e.tip.show("<ul>"+r+i+"</ul>"),n}(e,i);if(0!==c.length){var d=new FormData;for(a=0,o=c.length;a<o;a++)d.append("file[]",c[a]);for(var u=e.options.upload.extraData,p=0,h=Object.keys(u);p<h.length;p++){var f=h[p];d.append(f,u[f])}var m=new XMLHttpRequest;m.open("POST",e.options.upload.url),e.options.upload.token&&m.setRequestHeader("X-Upload-Token",e.options.upload.token),e.options.upload.withCredentials&&(m.withCredentials=!0),q(e,m),e.upload.isUploading=!0,l.setAttribute("contenteditable","false"),m.onreadystatechange=function(){if(m.readyState===XMLHttpRequest.DONE){if(e.upload.isUploading=!1,n&&(n.value=""),l.setAttribute("contenteditable","true"),200===m.status)if(e.options.upload.success)e.options.upload.success(l,m.responseText);else{var r=m.responseText;e.options.upload.format&&(r=e.options.upload.format(t,m.responseText)),function(e,t){B(t).focus();var n=JSON.parse(e),r="";1===n.code&&(r=""+n.msg),n.data.errFiles&&n.data.errFiles.length>0&&(r="<ul><li>"+r+"</li>",n.data.errFiles.forEach((function(e){var n=e.lastIndexOf("."),i=t.options.upload.filename(e.substr(0,n))+e.substr(n);r+="<li>"+i+" "+R.a[t.options.lang].uploadError+"</li>"})),r+="</ul>"),r?t.tip.show(r):t.tip.hide();var i="";Object.keys(n.data.succMap).forEach((function(e){var r=n.data.succMap[e],o=e.lastIndexOf("."),a=e.substr(o),s=t.options.upload.filename(e.substr(0,o))+a;".wav"===(a=a.toLowerCase())||".mp3"===a||".ogg"===a?"wysiwyg"===t.currentMode?i+='<div class="vditor-wysiwyg__block" data-type="html-block"\n data-block="0"><pre><code>&lt;audio controls="controls" src="'+r+'"&gt;&lt;/audio&gt;</code></pre>':i+='<audio controls="controls" src="'+r+'"></audio>\n':".apng"===a||".bmp"===a||".gif"===a||".ico"===a||".cur"===a||".jpg"===a||".jpeg"===a||".jfif"===a||".pjp"===a||".pjpeg"===a||".png"===a||".svg"===a||".webp"===a?"wysiwyg"===t.currentMode?i+='<img alt="'+s+'" src="'+r+'">':i+="!["+s+"]("+r+")\n":"wysiwyg"===t.currentMode?i+='<a href="'+r+'">'+s+"</a>":i+="["+s+"]("+r+")\n"})),Object(M.h)(t.upload.range),"sv"!==t.currentMode?document.execCommand("insertHTML",!1,i):P(t,i,"",!0),t.upload.range=getSelection().getRangeAt(0).cloneRange()}(r,e)}else e.options.upload.error?e.options.upload.error(m.responseText):e.tip.show(m.responseText);e.upload.element.style.display="none"}},m.upload.onprogress=function(t){if(t.lengthComputable){var n=t.loaded/t.total*100;e.upload.element.style.display="block",e.upload.element.style.width=n+"%"}},m.send(d)}else n&&(n.value="")},K=function(e,t){void 0===t&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&e.hint.render(e),clearTimeout(e.wysiwyg.afterRenderTimeoutId),e.wysiwyg.afterRenderTimeoutId=window.setTimeout((function(){if(!e.wysiwyg.composingLock||!Object(u.f)()){var n=l(e);"function"==typeof e.options.input&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&Object(u.a)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.wysiwygUndo.addToUndoStack(e)}}),800)},W=n(27),V=n.n(W),Z=n(30),J=n.n(Z),G=n(31),X=n.n(G),Q=n(32),$=n.n(Q),Y=n(33),ee=n.n(Y),te=n(34),ne=n.n(te),re=n(35),ie=n.n(re),oe=n(36),ae=n.n(oe),se=n(37),le=n.n(se),ce=n(38),de=n.n(ce),ue=function(e,t){return Object(M.e)(e,t)?getSelection().toString():""},pe=function(e,t){var n=Object(u.g)(e).split("-"),r=n.length>2&&("shift"===n[1]||"⇧"===n[1]),i=(r?n[2]:n[1])||"-";return!r||"-"!==i||!Object(u.e)()&&/Mac/.test(navigator.platform)||(i="_"),!(!Object(u.d)(t)||t.key.toLowerCase()!==i.toLowerCase()||t.altKey||!(!r&&!t.shiftKey||r&&t.shiftKey))},he=function(e,t){for(var n=e.start-1,r=!1;!r&&n>-1;)"\n"===t.charAt(n)&&t.length!==n+1?(n++,r=!0):0===n?r=!0:n--;for(var i=e.end,o=!1;!o&&i<=t.length;)"\n"===t.charAt(i)?(i++,o=!0):i===t.length?o=!0:i++;return{end:Math.min(i,t.length),start:Math.max(0,n)}},fe=function(e,t,n){var r=(new DOMParser).parseFromString(t,"text/html");r.body&&(t=r.body.innerHTML);var i=N(t,n);return i||(e.lute.SetJSRenderers({renderers:{HTML2Md:{renderLinkDest:function(t){var r=t.TokensStr();if(34===t.__internal_object__.Parent.Type&&r&&-1===r.indexOf("file://")&&e.options.upload.linkToImgUrl&&void 0!==n){var i=new XMLHttpRequest;i.open("POST",e.options.upload.linkToImgUrl),q(e,i),i.onreadystatechange=function(){if(i.readyState===XMLHttpRequest.DONE)if(200===i.status){var t=JSON.parse(i.responseText);if(0!==t.code)return void e.tip.show(t.msg);!function(e,t){var n=0,r=0;if(Array.from(t).some((function(i,o){if((r=i.textContent.indexOf(e))>-1&&3===t[o].childNodes[0].nodeType)return n=o,!0})),!(r<0)){var i=document.createRange();i.setStart(t[n].childNodes[0],r),i.setEnd(t[n].childNodes[0],r+e.length),Object(M.h)(i)}}(t.data.originalURL,e.sv.element.childNodes),P(e,t.data.url,"",!0)}else e.tip.show(i.responseText)},i.send(JSON.stringify({url:r}))}return[t.TokensStr(),Lute.WalkStop]}}}}),e.lute.HTML2Md(t))},me=n(9),ve=function(e){"dark"===e.options.theme?e.element.classList.add("vditor--dark"):e.element.classList.remove("vditor--dark")},ge=function(e){var t=window.innerWidth<=a.a.MOBILE_WIDTH?10:35;if("none"!==e.wysiwyg.element.parentElement.style.display){var n=(e.wysiwyg.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.wysiwyg.element.style.padding="10px "+Math.max(t,n)+"px"}if("none"!==e.ir.element.parentElement.style.display){n=(e.ir.element.parentElement.clientWidth-e.options.preview.maxWidth)/2;e.ir.element.style.padding="10px "+Math.max(t,n)+"px"}"block"===e.preview.element.style.display&&"sv"!==e.currentMode||(e.toolbar.element.style.paddingLeft=Math.max(5,parseInt(e[e.currentMode].element.style.paddingLeft||"0",10)+e.outline.element.offsetWidth)+"px")},be=function(e){if(e.options.typewriterMode){var t=window.innerHeight;"number"==typeof e.options.height&&(t=e.options.height,"number"==typeof e.options.minHeight&&(t=Math.max(t,e.options.minHeight)),t=Math.min(window.innerHeight,t)),e.element.classList.contains("vditor--fullscreen")&&(t=window.innerHeight),e[e.currentMode].element.style.setProperty("--editor-bottom",(t-e.toolbar.element.offsetHeight)/2+"px")}},ye=function(e,t){be(e),window.addEventListener("resize",(function(){ge(e),be(e)}));var n=Object(u.a)()&&localStorage.getItem(e.options.cache.id);return e.options.cache.enable&&n||(e.options.value?n=e.options.value:e.originalInnerHTML?n=fe(e,e.originalInnerHTML):e.options.cache.enable||(n="")),n||""},we=function(e,t,n){void 0===n&&(n={enableAddUndoStack:!0,enableHint:!1,enableInput:!0});var r=e.wysiwyg.element;r.innerHTML=e.lute.Md2VditorDOM(t),r.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach((function(t){H(t,e),t.previousElementSibling.setAttribute("style","display:none")})),K(e,n)},Ee=function(e){for(var t="",n=e.nextSibling;n;)3===n.nodeType?t+=n.textContent:t+=n.outerHTML,n=n.nextSibling;return t},Me=function(e){for(var t="",n=e.previousSibling;n;)t=3===n.nodeType?n.textContent+t:n.outerHTML+t,n=n.previousSibling;return t},Se=function(e,t){Array.from(e.wysiwyg.element.childNodes).find((function(n){if(3===n.nodeType){var r=document.createElement("p");r.setAttribute("data-block","0"),r.textContent=n.textContent;var i=3===t.startContainer.nodeType?t.startOffset:n.textContent.length;return n.parentNode.insertBefore(r,n),n.remove(),t.setStart(r.firstChild,Math.min(r.firstChild.textContent.length,i)),t.collapse(!0),Object(M.h)(t),!0}if(!n.getAttribute("data-block"))return"P"===n.tagName?n.remove():("DIV"===n.tagName?(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'+n.innerHTML+"</p>"):"BR"===n.tagName?n.outerHTML='<p data-block="0">'+n.outerHTML+"<wbr></p>":(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'+n.outerHTML+"</p>"),Object(M.f)(e.wysiwyg.element,t),t=getSelection().getRangeAt(0)),!0}))},Oe=function(e,t,n){for(var r=e.startContainer.parentElement,i=!1,o="",s="",l=function(e){var t=Me(e.startContainer),n=Ee(e.startContainer),r=e.startContainer.textContent,i=e.startOffset,o="",s="";return(""!==r.substr(0,i)&&r.substr(0,i)!==a.a.ZWSP||t)&&(o=""+t+r.substr(0,i)),(""!==r.substr(i)&&r.substr(i)!==a.a.ZWSP||n)&&(s=""+r.substr(i)+n),{afterHTML:s,beforeHTML:o}}(e),c=l.beforeHTML,d=l.afterHTML;r&&!i;){var u=r.tagName;if("STRIKE"===u&&(u="S"),"I"===u&&(u="EM"),"B"===u&&(u="STRONG"),"S"===u||"STRONG"===u||"EM"===u){var p="",h="",f="";"0"!==r.parentElement.getAttribute("data-block")&&(h=Me(r),f=Ee(r)),(c||h)&&(c=p=h+"<"+u+">"+c+"</"+u+">"),("bold"===n&&"STRONG"===u||"italic"===n&&"EM"===u||"strikeThrough"===n&&"S"===u)&&(p+=""+o+a.a.ZWSP+"<wbr>"+s,i=!0),(d||f)&&(p+=d="<"+u+">"+d+"</"+u+">"+f),"0"!==r.parentElement.getAttribute("data-block")?(r=r.parentElement).innerHTML=p:(r.outerHTML=p,r=r.parentElement),o="<"+u+">"+o,s="</"+u+">"+s}else i=!0}Object(M.f)(t.wysiwyg.element,e)},ke=function(e,t){var n,r=this;this.element=document.createElement("div"),t.className&&(n=this.element.classList).add.apply(n,t.className.split(" "));var i=t.hotkey?" <"+Object(u.g)(t.hotkey)+">":"";2===t.level&&(i=t.hotkey?" &lt;"+Object(u.g)(t.hotkey)+"&gt;":"");var o=t.tip?t.tip+i:R.a[e.options.lang][t.name]+i,s="upload"===t.name?"div":"button";if(2===t.level)this.element.innerHTML="<"+s+' data-type="'+t.name+'">'+o+"</"+s+">";else{this.element.classList.add("vditor-toolbar__item");var l=document.createElement(s);l.setAttribute("data-type",t.name),l.className="vditor-tooltipped vditor-tooltipped__"+t.tipPosition,l.setAttribute("aria-label",o),l.innerHTML=t.icon,this.element.appendChild(l)}t.prefix&&this.element.children[0].addEventListener(Object(u.b)(),(function(n){n.preventDefault(),r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||("wysiwyg"===e.currentMode?function(e,t,n){if(!(e.wysiwyg.composingLock&&n instanceof CustomEvent)){var r=!0,i=!0;e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove();var o=Object(M.b)(e.wysiwyg.element),s=t.getAttribute("data-type");if(t.classList.contains("vditor-menu--current"))if("strike"===s&&(s="strikeThrough"),"quote"===s){var l=Object(w.f)(o.startContainer,"BLOCKQUOTE");l||(l=o.startContainer.childNodes[o.startOffset]),l&&(r=!1,t.classList.remove("vditor-menu--current"),o.insertNode(document.createElement("wbr")),l.outerHTML=""===l.innerHTML.trim()?'<p data-block="0">'+l.innerHTML+"</p>":l.innerHTML,Object(M.f)(e.wysiwyg.element,o))}else"inline-code"===s?o.collapsed?(o.selectNode(o.startContainer.parentElement),document.execCommand("removeFormat",!1,"")):document.execCommand("removeFormat",!1,""):"link"===s?o.collapsed?(o.selectNode(o.startContainer.parentElement),document.execCommand("unlink",!1,"")):document.execCommand("unlink",!1,""):"check"===s||"list"===s||"ordered-list"===s?(nt(e,o,s),Object(M.f)(e.wysiwyg.element,o),r=!1,t.classList.remove("vditor-menu--current")):(r=!1,t.classList.remove("vditor-menu--current"),""===o.toString()?Oe(o,e,s):document.execCommand(s,!1,""));else{0===e.wysiwyg.element.childNodes.length&&(e.wysiwyg.element.innerHTML='<p data-block="0"><wbr></p>',Object(M.f)(e.wysiwyg.element,o));var c=Object(w.c)(o.startContainer);if("quote"===s){if(c||(c=o.startContainer.childNodes[o.startOffset]),c){r=!1,t.classList.add("vditor-menu--current"),o.insertNode(document.createElement("wbr"));var d=Object(w.f)(o.startContainer,"LI");d&&c.contains(d)?d.innerHTML='<blockquote data-block="0">'+d.innerHTML+"</blockquote>":c.outerHTML='<blockquote data-block="0">'+c.outerHTML+"</blockquote>",Object(M.f)(e.wysiwyg.element,o)}}else if("check"===s||"list"===s||"ordered-list"===s)nt(e,o,s,!1),Object(M.f)(e.wysiwyg.element,o),r=!1,p(e.toolbar.elements,["check","list","ordered-list"]),t.classList.add("vditor-menu--current");else if("inline-code"===s){if(""===o.toString())(u=document.createElement("code")).textContent=a.a.ZWSP,o.insertNode(u),o.setStart(u.firstChild,1),o.collapse(!0),Object(M.h)(o);else if(3===o.startContainer.nodeType){var u=document.createElement("code");o.surroundContents(u),o.insertNode(u),Object(M.h)(o)}r=!1,h(e.toolbar.elements,["inline-code"])}else if("code"===s)(u=document.createElement("div")).className="vditor-wysiwyg__block",u.setAttribute("data-type","code-block"),u.setAttribute("data-block","0"),u.setAttribute("data-marker","```"),""===o.toString()?u.innerHTML="<pre><code><wbr>\n</code></pre>":(u.innerHTML="<pre><code>"+o.toString()+"<wbr></code></pre>",o.deleteContents()),o.insertNode(u),c&&(c.outerHTML=e.lute.SpinVditorDOM(c.outerHTML)),Object(M.f)(e.wysiwyg.element,o),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach((function(t){H(t,e)}));else if("link"===s)if(""===o.toString()){var f=document.createElement("a");f.innerText=a.a.ZWSP,o.insertNode(f),o.setStart(f.firstChild,1),o.collapse(!0),Ke(e,f);var m=e.wysiwyg.popover.querySelector("input");m.value="",m.focus(),r=!1,i=!1}else(u=document.createElement("a")).setAttribute("href",""),u.innerHTML=o.toString(),o.surroundContents(u),o.insertNode(u),Object(M.h)(o);else if("table"===s){var v='<table data-block="0"><thead><tr><th>col1<wbr></th><th>col2</th><th>col3</th></tr></thead><tbody><tr><td> </td><td> </td><td> </td></tr><tr><td> </td><td> </td><td> </td></tr></tbody></table>';if(""===o.toString().trim())c&&""===c.innerHTML.trim().replace(a.a.ZWSP,"")?c.outerHTML=v:document.execCommand("insertHTML",!1,v),o.selectNode(e.wysiwyg.element.querySelector("wbr").previousSibling),e.wysiwyg.element.querySelector("wbr").remove(),Object(M.h)(o);else{v='<table data-block="0"><thead><tr>';var g=o.toString().split("\n"),b=g[0].split(",").length>g[0].split("\t").length?",":"\t";g.forEach((function(e,t){0===t?(e.split(b).forEach((function(e,t){v+=0===t?"<th>"+e+"<wbr></th>":"<th>"+e+"</th>"})),v+="</tr></thead>"):(v+=1===t?"<tbody><tr>":"<tr>",e.split(b).forEach((function(e){v+="<td>"+e+"</td>"})),v+="</tr>")})),v+="</tbody></table>",document.execCommand("insertHTML",!1,v),Object(M.f)(e.wysiwyg.element,o)}}else if("line"===s){if(c){var y='<hr data-block="0"><p data-block="0"><wbr>\n</p>';""===c.innerHTML.trim()?c.outerHTML=y:c.insertAdjacentHTML("afterend",y),Object(M.f)(e.wysiwyg.element,o)}}else if(r=!1,t.classList.add("vditor-menu--current"),"strike"===s&&(s="strikeThrough"),""!==o.toString()||"bold"!==s&&"italic"!==s&&"strikeThrough"!==s)document.execCommand(s,!1,"");else{var E="strong";"italic"===s?E="em":"strikeThrough"===s&&(E="s"),(u=document.createElement(E)).textContent=a.a.ZWSP,o.insertNode(u),u.previousSibling&&u.previousSibling.textContent===a.a.ZWSP&&(u.previousSibling.textContent=""),o.setStart(u.firstChild,1),o.collapse(!0),Object(M.h)(o)}}r&&ze(e),i&&K(e)}}(e,r.element.children[0],n):"ir"===e.currentMode?_t(e,r.element.children[0],t.prefix||"",t.suffix||""):P(e,t.prefix||"",t.suffix||"",!1,!0))}))},xe=(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),Te=function(e,t,n){var r;"string"!=typeof n?(b(e,["subToolbar","hint"]),n.preventDefault(),r=l(e)):r=n,e.currentMode===t&&"string"!=typeof n||(e.devtools&&e.devtools.renderEchart(e),"both"===e.options.preview.mode&&"sv"===t?e.preview.element.style.display="block":e.preview.element.style.display="none",f(e.toolbar.elements,a.a.EDIT_TOOLBARS),p(e.toolbar.elements,a.a.EDIT_TOOLBARS),m(e.toolbar.elements,["outdent","indent"]),"ir"===t?(v(e.toolbar.elements,["format","both"]),g(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.irUndo.resetIcon(e),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="block",e.currentMode="ir",e.ir.element.innerHTML=e.lute.Md2VditorIRDOM(r),Tt(e,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),ge(e),e.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach((function(t){H(t,e)})),"string"!=typeof n&&(e.ir.element.focus(),S(e))):"wysiwyg"===t?(v(e.toolbar.elements,["format","both"]),g(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.wysiwygUndo.resetIcon(e),e.sv.element.style.display="none",e.wysiwyg.element.parentElement.style.display="block",e.ir.element.parentElement.style.display="none",e.currentMode="wysiwyg",ge(e),we(e,r,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),"string"!=typeof n&&(e.wysiwyg.element.focus(),ze(e)),e.wysiwyg.popover.style.display="none"):"sv"===t&&(g(e.toolbar.elements,["format","both"]),v(e.toolbar.elements,["outdent","indent","outline","insert-before","insert-after"]),e.undo.resetIcon(e),e.wysiwyg.element.parentElement.style.display="none",e.ir.element.parentElement.style.display="none",("both"===e.options.preview.mode||"editor"===e.options.preview.mode)&&(e.sv.element.style.display="block"),e.currentMode="sv",z(e,r,void 0,{enableAddUndoStack:!0,enableHint:!1,enableInput:!1}),"string"!=typeof n&&e.sv.element.focus(),ge(e)),"string"==typeof n&&e.outline.render(e),be(e),e.toolbar.elements["edit-mode"]&&(e.toolbar.elements["edit-mode"].querySelectorAll("button").forEach((function(e){e.classList.remove("vditor-menu--current")})),e.toolbar.elements["edit-mode"].querySelector('button[data-mode="'+e.currentMode+'"]').classList.add("vditor-menu--current")),e.outline.toggle(e,"sv"!==e.currentMode&&e.options.outline))},Le=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=document.createElement("div");return i.className="vditor-hint"+(2===n.level?"":" vditor-panel--arrow"),i.innerHTML='<button data-mode="wysiwyg">'+R.a[t.options.lang].wysiwyg+" &lt;"+Object(u.g)("⌘-⌥-7")+'></button>\n<button data-mode="ir">'+R.a[t.options.lang].instantRendering+" &lt;"+Object(u.g)("⌘-⌥-8")+'></button>\n<button data-mode="sv">'+R.a[t.options.lang].splitView+" &lt;"+Object(u.g)("⌘-⌥-9")+"></button>",r.element.appendChild(i),r._bindEvent(t,i,n),r}return xe(t,e),t.prototype._bindEvent=function(e,t,n){var r=this.element.children[0];y(e,t,r,n.level),t.children.item(0).addEventListener(Object(u.b)(),(function(t){Te(e,"wysiwyg",t),t.preventDefault(),t.stopPropagation()})),t.children.item(1).addEventListener(Object(u.b)(),(function(t){Te(e,"ir",t),t.preventDefault(),t.stopPropagation()})),t.children.item(2).addEventListener(Object(u.b)(),(function(t){Te(e,"sv",t),t.preventDefault(),t.stopPropagation()}))},t}(ke),je=function(e,t){var n=Object(M.b)(e.wysiwyg.element),r=Object(w.c)(n.startContainer);r||(r=n.startContainer.childNodes[n.startOffset]),r||0!==e.wysiwyg.element.children.length||(r=e.wysiwyg.element),r&&!r.classList.contains("vditor-wysiwyg__block")&&(n.insertNode(document.createElement("wbr")),"<wbr>"===r.innerHTML.trim()&&(r.innerHTML="<wbr><br>"),"BLOCKQUOTE"===r.tagName||r.classList.contains("vditor-reset")?r.innerHTML="<"+t+' data-block="0">'+r.innerHTML.trim()+"</"+t+">":r.outerHTML="<"+t+' data-block="0">'+r.innerHTML.trim()+"</"+t+">",Object(M.f)(e.wysiwyg.element,n),ct(e))},_e=function(e){var t=getSelection().getRangeAt(0),n=Object(w.c)(t.startContainer);n||(n=t.startContainer.childNodes[t.startOffset]),n&&(t.insertNode(document.createElement("wbr")),n.outerHTML='<p data-block="0">'+n.innerHTML+"</p>",Object(M.f)(e.wysiwyg.element,t)),e.wysiwyg.popover.style.display="none"},Ce=function(e,t,n){void 0===n&&(n=!0);var r=e.previousElementSibling,i=r.ownerDocument.createRange();"CODE"===r.tagName?(r.style.display="inline-block",n?i.setStart(r.firstChild,1):i.selectNodeContents(r)):(r.style.display="block",r.firstChild.firstChild||r.firstChild.appendChild(document.createTextNode("")),i.selectNodeContents(r.firstChild)),n?i.collapse(!0):i.collapse(!1),Object(M.h)(i),e.firstElementChild.classList.contains("language-mindmap")||De(t)},Ae=function(e,t){if(pe("⌘-⇧-X",t)){var n=e.wysiwyg.popover.querySelector('[data-type="remove"]');if(n)return n.click(),t.preventDefault(),!0}},Ne=function(e,t){t.addEventListener("focus",(function(){e.options.focus&&e.options.focus(l(e)),b(e,["subToolbar"])}))},He=function(e,t){t.addEventListener("blur",(function(){e.options.blur&&e.options.blur(l(e))}))},De=function(e){if(e.options.typewriterMode){var t=e[e.currentMode].element,n=Object(M.a)(t).top;"string"!=typeof e.options.height||e.element.classList.contains("vditor--fullscreen")||window.scrollTo(window.scrollX,n+e.element.offsetTop+e.toolbar.element.offsetHeight-window.innerHeight/2+10),("number"==typeof e.options.height||e.element.classList.contains("vditor--fullscreen"))&&(t.scrollTop=n+t.scrollTop-t.clientHeight/2+10)}},Re=function(e,t){t.addEventListener("keydown",(function(t){if(!e.options.hint.at&&!e.toolbar.elements.emoji||!e.hint.select(t,e)){if("sv"===e.currentMode){if(function(e,t){e.undo.recordFirstPosition(e);var n=e.sv.element,r=Object(M.c)(n),i=l(e);if(e.options.tab&&"Tab"===t.key){t.preventDefault(),t.stopPropagation();var o=he(r,i),a=i.substring(o.start,o.end-1).split("\n");if(t.shiftKey){var s=0,c=!1,d=a.map((function(t,n){var r=t;return 0===t.indexOf(e.options.tab)&&(0===n&&(c=!0),s++,r=t.replace(e.options.tab,"")),r})).join("\n");return z(e,i.substring(0,o.start)+d+i.substring(o.end-1),{end:r.end-s*e.options.tab.length,start:r.start-(c?e.options.tab.length:0)}),!0}if(r.start===r.end)return P(e,e.options.tab,""),!0;var p=a.map((function(t){return e.options.tab+t})).join("\n");return z(e,i.substring(0,o.start)+p+i.substring(o.end-1),{end:r.end+a.length*e.options.tab.length,start:r.start+e.options.tab.length}),!0}if(!Object(u.d)(t)&&!t.shiftKey&&8===t.keyCode){if(r.start!==r.end)P(e,"","",!0);else{var h=i.substring(0,r.start).match(/([\u{1F300}-\u{1F5FF}][\u{2000}-\u{206F}][\u{2700}-\u{27BF}]|([\u{1F900}-\u{1F9FF}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F600}-\u{1F64F}])[\u{2000}-\u{206F}][\u{2600}-\u{26FF}]|[\u{1F300}-\u{1F5FF}]|[\u{1F100}-\u{1F1FF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F200}-\u{1F2FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F000}-\u{1F02F}]|[\u{FE00}-\u{FE0F}]|[\u{1F0A0}-\u{1F0FF}]|[\u{0000}-\u{007F}][\u{20D0}-\u{20FF}]|[\u{0000}-\u{007F}][\u{FE00}-\u{FE0F}][\u{20D0}-\u{20FF}])$/u),f=h?h[0].length:1;z(e,i.substring(0,r.start-f)+i.substring(r.start),{end:r.start-f,start:r.start-f},{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})}return t.preventDefault(),t.stopPropagation(),!0}if(e.options.keymap.deleteLine&&pe(e.options.keymap.deleteLine,t)){var m=he(r,i),v=i.substring(0,m.start)+i.substring(m.end),g=Math.min(v.length,r.start);return z(e,v,{end:g,start:g}),t.preventDefault(),!0}if(e.options.keymap.duplicate&&pe(e.options.keymap.duplicate,t)){var b=i.substring(r.start,r.end);if(r.start===r.end){m=he(r,i);b=i.substring(m.start,m.end),z(e,i.substring(0,m.end)+b+i.substring(m.end),{end:r.end+b.length,start:r.start+b.length})}else z(e,i.substring(0,r.end)+b+i.substring(r.end),{end:r.end+b.length,start:r.start+b.length});return t.preventDefault(),!0}return!1}(e,t))return}else if("wysiwyg"===e.currentMode){if(function(e,t){if(e.wysiwyg.composingLock=t.isComposing,t.isComposing)return!1;if(-1===t.key.indexOf("Arrow")&&e.wysiwygUndo.recordFirstWbr(e,t),!Ze(t,e))return!1;var n=Object(M.b)(e.wysiwyg.element),r=n.startContainer;if(Je(n,t),St(n),"Enter"!==t.key&&"Tab"!==t.key&&"Backspace"!==t.key&&-1===t.key.indexOf("Arrow")&&!Object(u.d)(t)&&"Escape"!==t.key)return!1;var i=Object(w.c)(r),o=Object(w.f)(r,"P");if(ht(t,e,o,n))return!0;if(ut(n,e,o,t))return!0;if(bt(e,t,n))return!0;var s=Object(w.e)(r,"vditor-wysiwyg__block");if(s){if("Escape"===t.key&&2===s.children.length)return e.wysiwyg.popover.style.display="none",s.firstElementChild.style.display="none",e.wysiwyg.element.blur(),t.preventDefault(),!0;if(!Object(u.d)(t)&&!t.shiftKey&&t.altKey&&"Enter"===t.key&&"code-block"===s.getAttribute("data-type")){var l=e.wysiwyg.popover.querySelector(".vditor-input");return l.focus(),l.select(),t.preventDefault(),!0}if("0"===s.getAttribute("data-block")){if(yt(e,t,s.firstElementChild,n))return!0;if(et(e,t,n,s.firstElementChild,s))return!0}}if(wt(e,n,t,o))return!0;var c=Object(w.h)(r,"BLOCKQUOTE");if(c&&!t.shiftKey&&t.altKey&&"Enter"===t.key){Object(u.d)(t)?n.setStartBefore(c):n.setStartAfter(c),Object(M.h)(n);var d=document.createElement("p");return d.setAttribute("data-block","0"),d.innerHTML="\n",n.insertNode(d),n.collapse(!0),Object(M.h)(n),K(e),De(e),t.preventDefault(),!0}var p,h=Object(E.a)(r);if(h){if("H6"===h.tagName&&r.textContent.length===n.startOffset&&!Object(u.d)(t)&&!t.shiftKey&&!t.altKey&&"Enter"===t.key){var f=document.createElement("p");return f.textContent="\n",f.setAttribute("data-block","0"),r.parentElement.insertAdjacentElement("afterend",f),n.setStart(f,0),Object(M.h)(n),K(e),De(e),t.preventDefault(),!0}var m;if(pe("⌘-=",t))return(m=parseInt(h.tagName.substr(1),10)-1)>0&&(je(e,"h"+m),K(e)),t.preventDefault(),!0;if(pe("⌘--",t))return(m=parseInt(h.tagName.substr(1),10)+1)<7&&(je(e,"h"+m),K(e)),t.preventDefault(),!0;"Backspace"!==t.key||Object(u.d)(t)||t.shiftKey||t.altKey||""!==h.textContent||_e(e)}if(Et(e,n,t))return!0;if(t.altKey&&"Enter"===t.key&&!Object(u.d)(t)&&!t.shiftKey){var v=Object(w.f)(r,"A"),g=Object(w.d)(r,"data-type","link-ref"),b=Object(w.d)(r,"data-type","footnotes-ref");if(v||g||b||h&&2===h.tagName.length){var y=e.wysiwyg.popover.querySelector("input");y.focus(),y.select()}}if(Ae(e,t))return!0;if(pe("⌘-⇧-U",t)&&(p=e.wysiwyg.popover.querySelector('[data-type="up"]')))return p.click(),t.preventDefault(),!0;if(pe("⌘-⇧-D",t)&&(p=e.wysiwyg.popover.querySelector('[data-type="down"]')))return p.click(),t.preventDefault(),!0;if(pt(e,n,t))return!0;if(!Object(u.d)(t)&&t.shiftKey&&!t.altKey&&"Enter"===t.key&&"LI"!==r.parentElement.tagName&&"P"!==r.parentElement.tagName)return["STRONG","S","STRONG","I","EM","B"].includes(r.parentElement.tagName)?n.insertNode(document.createTextNode("\n"+a.a.ZWSP)):n.insertNode(document.createTextNode("\n")),n.collapse(!1),Object(M.h)(n),K(e),De(e),t.preventDefault(),!0;if("Backspace"===t.key&&!Object(u.d)(t)&&!t.shiftKey&&!t.altKey&&""===n.toString()){if(Mt(e,n,t,o))return!0;if(i){if(i.previousElementSibling&&i.previousElementSibling.classList.contains("vditor-wysiwyg__block")&&"0"===i.previousElementSibling.getAttribute("data-block")){var S=Object(M.c)(i,n).start;if(0===S||1===S&&i.innerText.startsWith(a.a.ZWSP))return Ce(i.previousElementSibling.lastElementChild,e,!1),""===i.innerHTML.trim()&&(i.remove(),K(e)),t.preventDefault(),!0}var O=n.startOffset;if(""===n.toString()&&3===r.nodeType&&"\n"===r.textContent.charAt(O-2)&&r.textContent.charAt(O-1)!==a.a.ZWSP&&["STRONG","S","STRONG","I","EM","B"].includes(r.parentElement.tagName))return r.textContent=r.textContent.substring(0,O-1)+a.a.ZWSP,n.setStart(r,O),n.collapse(!0),K(e),t.preventDefault(),!0;r.textContent===a.a.ZWSP&&1===n.startOffset&&!r.previousSibling&&function(e){for(var t=e.startContainer.nextSibling;t&&""===t.textContent;)t=t.nextSibling;return!(!t||3===t.nodeType||"CODE"!==t.tagName&&"math-inline"!==t.getAttribute("data-type")&&"html-inline"!==t.getAttribute("data-type"))}(n)&&(r.textContent=""),i.querySelectorAll("span.vditor-wysiwyg__block[data-type='math-inline']").forEach((function(e){e.firstElementChild.style.display="inline",e.lastElementChild.style.display="none"}))}}if(Object(u.e)()&&1===n.startOffset&&r.textContent.indexOf(a.a.ZWSP)>-1&&r.previousSibling&&3!==r.previousSibling.nodeType&&"CODE"===r.previousSibling.tagName&&("Backspace"===t.key||"ArrowLeft"===t.key))return n.selectNodeContents(r.previousSibling),n.collapse(!1),t.preventDefault(),!0;if(Ot(t,i,n))return t.preventDefault(),!0;if(Ge(n,t.key),"ArrowDown"===t.key){var k=r.nextSibling;k&&3!==k.nodeType&&"math-inline"===k.getAttribute("data-type")&&n.setStartAfter(k)}return!1}(e,t))return}else if("ir"===e.currentMode&&function(e,t){if(e.ir.composingLock=t.isComposing,t.isComposing)return!1;if(-1===t.key.indexOf("Arrow")&&e.irUndo.recordFirstWbr(e,t),!Ze(t,e))return!1;var n=Object(M.b)(e.ir.element),r=n.startContainer;if(Je(n,t),St(n),"Enter"!==t.key&&"Tab"!==t.key&&"Backspace"!==t.key&&-1===t.key.indexOf("Arrow")&&!Object(u.d)(t)&&"Escape"!==t.key)return!1;var i=Object(w.d)(r,"data-newline","1");if(!Object(u.d)(t)&&!t.altKey&&!t.shiftKey&&"Enter"===t.key&&i&&n.startOffset<i.textContent.length){var o=i.previousElementSibling;o&&(n.insertNode(document.createTextNode(o.textContent)),n.collapse(!1));var a=i.nextSibling;a&&(n.insertNode(document.createTextNode(a.textContent)),n.collapse(!0))}var s=Object(w.f)(r,"P");if(ht(t,e,s,n))return!0;if(ut(n,e,s,t))return!0;if(wt(e,n,t,s))return!0;if(s&&s.previousElementSibling&&s.previousElementSibling.classList.contains("vditor-toc")&&tt(e,t,n,s,s.previousElementSibling))return!0;var l=Object(w.e)(r,"vditor-ir__marker--pre");if(l&&"PRE"===l.tagName){var c=l.firstChild;if(yt(e,t,l,n))return!0;if(("math-block"===c.getAttribute("data-type")||"html-block"===c.getAttribute("data-type"))&&tt(e,t,n,c,l.parentElement))return!0;if(et(e,t,n,c,l.parentElement))return!0}var d=Object(w.d)(r,"data-type","code-block-info");if(d){if("Enter"===t.key||"Tab"===t.key)return n.selectNodeContents(d.nextElementSibling.firstChild),n.collapse(!0),t.preventDefault(),!0;if("Backspace"===t.key){var p=Object(M.c)(d).start;1===p&&n.setStart(r,0),2===p&&(e.hint.recentLanguage="")}if(tt(e,t,n,d,d.parentElement))return b(e,["hint"]),!0}var h=Object(w.f)(r,"TD")||Object(w.f)(r,"TH");if(t.key.indexOf("Arrow")>-1&&h){var f=Qe(h);if(f&&tt(e,t,n,h,f))return!0;var m=$e(h);if(m&&et(e,t,n,h,m))return!0}if(bt(e,t,n))return!0;if(Et(e,n,t))return!0;if(pt(e,n,t))return!0;if("Backspace"===t.key&&!Object(u.d)(t)&&!t.shiftKey&&!t.altKey&&""===n.toString()){if(Mt(e,n,t,s))return!0;var v=Object(E.a)(r);if(v){var g=v.firstElementChild.textContent.length;Object(M.c)(v).start===g&&(n.setStart(v.firstElementChild.firstChild,g-1),n.collapse(!0))}}var y=Object(w.c)(r);return!("ArrowUp"!==t.key&&"ArrowDown"!==t.key||!y||(y.querySelectorAll(".vditor-ir__node").forEach((function(e){e.contains(r)||e.classList.add("vditor-ir__node--hidden")})),!Ot(t,y,n)))||(Ge(n,t.key),!1)}(e,t))return;if(e.options.ctrlEnter&&pe("⌘-Enter",t))return e.options.ctrlEnter(l(e)),void t.preventDefault();if(pe("⌘-Z",t)){if("sv"===e.currentMode&&!e.toolbar.elements.undo)return e.undo.undo(e),void t.preventDefault();if("wysiwyg"===e.currentMode&&!e.toolbar.elements.undo)return e.wysiwygUndo.undo(e),void t.preventDefault();if("ir"===e.currentMode)return e.irUndo.undo(e),void t.preventDefault()}if(pe("⌘-Y",t)){if("sv"===e.currentMode&&!e.toolbar.elements.redo)return e.undo.redo(e),void t.preventDefault();if("wysiwyg"===e.currentMode&&!e.toolbar.elements.redo)return e.wysiwygUndo.redo(e),void t.preventDefault();if("ir"===e.currentMode)return e.irUndo.redo(e),void t.preventDefault()}if("Escape"===t.key)return"block"===e.hint.element.style.display?e.hint.element.style.display="none":e.options.esc&&e.options.esc(l(e)),void t.preventDefault();if(Object(u.d)(t)&&t.altKey&&!t.shiftKey&&/^Digit[1-6]$/.test(t.code)){if("wysiwyg"===e.currentMode){var n=t.code.replace("Digit","H");Object(w.f)(getSelection().getRangeAt(0).startContainer,n)?_e(e):je(e,n),K(e)}else"sv"===e.currentMode?P(e,"#".repeat(parseInt(t.code.replace("Digit",""),10))+" ","",!1,!0):"ir"===e.currentMode&&Lt(e,"#".repeat(parseInt(t.code.replace("Digit",""),10))+" ");return t.preventDefault(),!0}if(Object(u.d)(t)&&t.altKey&&!t.shiftKey&&/^Digit[7-9]$/.test(t.code))return"Digit7"===t.code?Te(e,"wysiwyg",t):"Digit8"===t.code?Te(e,"ir",t):"Digit9"===t.code&&Te(e,"sv",t),!0;e.options.toolbar.find((function(n){return!n.hotkey||n.toolbar?!!n.toolbar&&!!n.toolbar.find((function(n){return!!n.hotkey&&(pe(n.hotkey,t)?(e.toolbar.elements[n.name].children[0].dispatchEvent(new CustomEvent(Object(u.b)())),t.preventDefault(),!0):void 0)})):pe(n.hotkey,t)?(e.toolbar.elements[n.name].children[0].dispatchEvent(new CustomEvent(Object(u.b)())),t.preventDefault(),!0):void 0}))}}))},Ie=function(e,t){e.options.select&&t.addEventListener("selectstart",(function(n){t.onmouseup=function(){var t=ue(e[e.currentMode].element);t&&e.options.select(t)}}))},ze=function(e){clearTimeout(e.wysiwyg.hlToolbarTimeoutId),e.wysiwyg.hlToolbarTimeoutId=window.setTimeout((function(){var t;if("false"!==e.wysiwyg.element.getAttribute("contenteditable")&&Object(M.e)(e.wysiwyg.element)){p(e.toolbar.elements,a.a.EDIT_TOOLBARS),f(e.toolbar.elements,a.a.EDIT_TOOLBARS);var n=getSelection().getRangeAt(0),r=n.startContainer;3===n.startContainer.nodeType&&(r=n.startContainer.parentElement),r.classList.contains("vditor-reset")&&(r=r.childNodes[n.startOffset]);var i=Object(w.d)(r,"data-type","footnotes-block");if(i)return e.wysiwyg.popover.innerHTML="",Ue(i,e),void Pe(e,i);var o=Object(w.f)(r,"LI");o?(o.classList.contains("vditor-task")?h(e.toolbar.elements,["check"]):"OL"===o.parentElement.tagName?h(e.toolbar.elements,["ordered-list"]):"UL"===o.parentElement.tagName&&h(e.toolbar.elements,["list"]),f(e.toolbar.elements,["outdent","indent"])):m(e.toolbar.elements,["outdent","indent"]),Object(w.f)(r,"BLOCKQUOTE")&&h(e.toolbar.elements,["quote"]),(Object(w.f)(r,"B")||Object(w.f)(r,"STRONG"))&&h(e.toolbar.elements,["bold"]),(Object(w.f)(r,"I")||Object(w.f)(r,"EM"))&&h(e.toolbar.elements,["italic"]),(Object(w.f)(r,"STRIKE")||Object(w.f)(r,"S"))&&h(e.toolbar.elements,["strike"]);var s=Object(w.f)(r,"A");s&&h(e.toolbar.elements,["link"]);var l=Object(w.f)(r,"TABLE"),c=Object(E.a)(r);Object(w.f)(r,"CODE")?Object(w.f)(r,"PRE")?(m(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","inline-code","upload","link","table","record"]),h(e.toolbar.elements,["code"])):(m(e.toolbar.elements,["headings","bold","italic","strike","line","quote","list","ordered-list","check","code","upload","link","table","record"]),h(e.toolbar.elements,["inline-code"])):c?(m(e.toolbar.elements,["bold"]),h(e.toolbar.elements,["headings"])):l&&m(e.toolbar.elements,["table"]);var d=Object(w.e)(r,"vditor-toc");if(!d)(W=Object(w.d)(r,"data-block","0"))&&(null===(t=W.previousElementSibling)||void 0===t?void 0:t.classList.contains("vditor-toc"))&&(d=W.previousElementSibling);d&&(e.wysiwyg.popover.innerHTML="",Ue(d,e),Pe(e,d));var v=Object(E.b)(r,"BLOCKQUOTE");if(v&&(e.wysiwyg.popover.innerHTML="",Be(n,v,e),qe(n,v,e),Ue(v,e),Pe(e,v)),o&&(e.wysiwyg.popover.innerHTML="",Be(n,o,e),qe(n,o,e),Ue(o,e),Pe(e,o)),l){e.wysiwyg.popover.innerHTML="";var g=function(){var e=l.rows.length,t=l.rows[0].cells.length,n=parseInt(A.value,10)||e,r=parseInt(D.value,10)||t;if(n!==e||t!==r){if(t!==r)for(var i=r-t,o=0;o<l.rows.length;o++)if(i>0)for(var a=0;a<i;a++)0===o?l.rows[o].lastElementChild.insertAdjacentHTML("afterend","<th> </th>"):l.rows[o].lastElementChild.insertAdjacentHTML("afterend","<td> </td>");else for(var s=t-1;s>=r;s--)l.rows[o].cells[s].remove();if(e!==n){var c=n-e;if(c>0){for(var d="<tr>",u=0;u<r;u++)d+="<td> </td>";for(var p=0;p<c;p++)l.querySelector("tbody")?l.querySelector("tbody").insertAdjacentHTML("beforeend",d):l.querySelector("thead").insertAdjacentHTML("afterend",d+"</tr>")}else for(u=e-1;u>=n;u--)l.rows[u].remove(),1===l.rows.length&&l.querySelector("tbody").remove()}}},b=function(t){ot(l,t),"right"===t?(k.classList.remove("vditor-icon--current"),x.classList.remove("vditor-icon--current"),T.classList.add("vditor-icon--current")):"center"===t?(k.classList.remove("vditor-icon--current"),T.classList.remove("vditor-icon--current"),x.classList.add("vditor-icon--current")):(x.classList.remove("vditor-icon--current"),T.classList.remove("vditor-icon--current"),k.classList.add("vditor-icon--current")),Object(M.h)(n),K(e)},y=Object(w.f)(r,"TD"),S=Object(w.f)(r,"TH"),O="left";y?O=y.getAttribute("align")||"left":S&&(O=S.getAttribute("align")||"center");var k=document.createElement("button");k.setAttribute("aria-label",R.a[e.options.lang].alignLeft+"<"+Object(u.g)("⌘-⇧-L")+">"),k.setAttribute("data-type","left"),k.innerHTML=J.a,k.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+("left"===O?" vditor-icon--current":""),k.onclick=function(){b("left")};var x=document.createElement("button");x.setAttribute("aria-label",R.a[e.options.lang].alignCenter+"<"+Object(u.g)("⌘-⇧-C")+">"),x.setAttribute("data-type","center"),x.innerHTML=V.a,x.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+("center"===O?" vditor-icon--current":""),x.onclick=function(){b("center")};var T=document.createElement("button");T.setAttribute("aria-label",R.a[e.options.lang].alignRight+"<"+Object(u.g)("⌘-⇧-R")+">"),T.setAttribute("data-type","right"),T.innerHTML=X.a,T.className="vditor-icon vditor-tooltipped vditor-tooltipped__n"+("right"===O?" vditor-icon--current":""),T.onclick=function(){b("right")};var L=document.createElement("button");L.setAttribute("aria-label",R.a[e.options.lang]["insert-row"]+"<"+Object(u.g)("⌘-=")+">"),L.setAttribute("data-type","insertRow"),L.innerHTML=ae.a,L.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",L.onclick=function(){var t=getSelection().getRangeAt(0).startContainer,r=Object(w.f)(t,"TD")||Object(w.f)(t,"TH");r&&ft(e,n,r)};var j=document.createElement("button");j.setAttribute("aria-label",R.a[e.options.lang]["insert-column"]+"<"+Object(u.g)("⌘-⇧-=")+">"),j.setAttribute("data-type","insertColumn"),j.innerHTML=ie.a,j.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",j.onclick=function(){var t=getSelection().getRangeAt(0).startContainer,n=Object(w.f)(t,"TD")||Object(w.f)(t,"TH");n&&mt(e,l,n)};var _=document.createElement("button");_.setAttribute("aria-label",R.a[e.options.lang]["delete-row"]+"<"+Object(u.g)("⌘--")+">"),_.setAttribute("data-type","deleteRow"),_.innerHTML=ee.a,_.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",_.onclick=function(){var t=getSelection().getRangeAt(0).startContainer,r=Object(w.f)(t,"TD")||Object(w.f)(t,"TH");r&&vt(e,n,r)};var C=document.createElement("button");C.setAttribute("aria-label",R.a[e.options.lang]["delete-column"]+"<"+Object(u.g)("⌘-⇧--")+">"),C.setAttribute("data-type","deleteColumn"),C.innerHTML=$.a,C.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",C.onclick=function(){var t=getSelection().getRangeAt(0).startContainer,r=Object(w.f)(t,"TD")||Object(w.f)(t,"TH");r&&gt(e,n,l,r)},(te=document.createElement("span")).setAttribute("aria-label",R.a[e.options.lang].row),te.className="vditor-tooltipped vditor-tooltipped__n";var A=document.createElement("input");te.appendChild(A),A.type="number",A.min="1",A.className="vditor-input",A.style.width="42px",A.style.textAlign="center",A.setAttribute("placeholder",R.a[e.options.lang].row),A.value=l.rows.length.toString(),A.oninput=function(){g()},A.onkeydown=function(t){if(!t.isComposing)return"Tab"===t.key?(D.focus(),D.select(),void t.preventDefault()):void Ae(e,t)};var N=document.createElement("span");N.setAttribute("aria-label",R.a[e.options.lang].column),N.className="vditor-tooltipped vditor-tooltipped__n";var D=document.createElement("input");N.appendChild(D),D.type="number",D.min="1",D.className="vditor-input",D.style.width="42px",D.style.textAlign="center",D.setAttribute("placeholder",R.a[e.options.lang].column),D.value=l.rows[0].cells.length.toString(),D.oninput=function(){g()},D.onkeydown=function(t){if(!t.isComposing)return"Tab"===t.key?(A.focus(),A.select(),void t.preventDefault()):void Ae(e,t)},Be(n,l,e),qe(n,l,e),Ue(l,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",k),e.wysiwyg.popover.insertAdjacentElement("beforeend",x),e.wysiwyg.popover.insertAdjacentElement("beforeend",T),e.wysiwyg.popover.insertAdjacentElement("beforeend",L),e.wysiwyg.popover.insertAdjacentElement("beforeend",j),e.wysiwyg.popover.insertAdjacentElement("beforeend",_),e.wysiwyg.popover.insertAdjacentElement("beforeend",C),e.wysiwyg.popover.insertAdjacentElement("beforeend",te),e.wysiwyg.popover.insertAdjacentHTML("beforeend"," x "),e.wysiwyg.popover.insertAdjacentElement("beforeend",N),Pe(e,l)}var I=Object(w.d)(r,"data-type","link-ref");if(I){e.wysiwyg.popover.innerHTML="";var z=function(){""!==P.value.trim()&&(I.textContent=P.value),""!==q.value.trim()&&I.setAttribute("data-link-label",q.value)};(te=document.createElement("span")).setAttribute("aria-label",R.a[e.options.lang].textIsNotEmpty),te.className="vditor-tooltipped vditor-tooltipped__n";var P=document.createElement("input");te.appendChild(P),P.className="vditor-input",P.setAttribute("placeholder",R.a[e.options.lang].textIsNotEmpty),P.style.width="120px",P.value=I.textContent,P.oninput=function(){z()},P.onkeydown=function(t){Ae(e,t)||Fe(e.wysiwyg.element,I,t,q)};var B=document.createElement("span");B.setAttribute("aria-label",R.a[e.options.lang].linkRef),B.className="vditor-tooltipped vditor-tooltipped__n";var q=document.createElement("input");B.appendChild(q),q.className="vditor-input",q.setAttribute("placeholder",R.a[e.options.lang].linkRef),q.value=I.getAttribute("data-link-label"),q.oninput=function(){z()},q.onkeydown=function(t){Ae(e,t)||Fe(e.wysiwyg.element,I,t,P)},Ue(I,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",te),e.wysiwyg.popover.insertAdjacentElement("beforeend",B),Pe(e,I)}var U=Object(w.d)(r,"data-type","footnotes-ref");if(U){e.wysiwyg.popover.innerHTML="",(te=document.createElement("span")).setAttribute("aria-label",R.a[e.options.lang].footnoteRef+"<"+Object(u.g)("⌥-Enter")+">"),te.className="vditor-tooltipped vditor-tooltipped__n";var F=document.createElement("input");te.appendChild(F),F.className="vditor-input",F.setAttribute("placeholder",R.a[e.options.lang].footnoteRef+"<"+Object(u.g)("⌥-Enter")+">"),F.style.width="120px",F.value=U.getAttribute("data-footnotes-label"),F.oninput=function(){""!==F.value.trim()&&U.setAttribute("data-footnotes-label",F.value)},F.onkeydown=function(t){if(!t.isComposing)return Object(u.d)(t)||t.shiftKey||!t.altKey||"Enter"!==t.key?void Ae(e,t):(n.selectNodeContents(U),n.collapse(!1),Object(M.h)(n),void t.preventDefault())},Ue(U,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",te),Pe(e,U)}var W,Z=Object(w.e)(r,"vditor-wysiwyg__block");if(Z&&Z.getAttribute("data-type").indexOf("block")>-1){if(e.wysiwyg.popover.innerHTML="",Be(n,Z,e),qe(n,Z,e),Ue(Z,e),"code-block"===Z.getAttribute("data-type")){var G=document.createElement("span");G.setAttribute("aria-label",R.a[e.options.lang].language+"<"+Object(u.g)("⌥-Enter")+">"),G.className="vditor-tooltipped vditor-tooltipped__n";var Q=document.createElement("input");G.appendChild(Q);var Y=Z.firstElementChild.firstElementChild;Q.className="vditor-input",Q.setAttribute("placeholder",R.a[e.options.lang].language+"<"+Object(u.g)("⌥-Enter")+">"),Q.value=Y.className.indexOf("language-")>-1?Y.className.split("-")[1].split(" ")[0]:e.hint.recentLanguage,Q.oninput=function(){Y.className="language-"+Q.value,Z.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(Z.lastElementChild.innerHTML=Z.firstElementChild.innerHTML,H(Z.lastElementChild,e)),K(e)},Q.onkeydown=function(t){if(!t.isComposing&&!Ae(e,t)){if("Escape"===t.key&&"block"===e.hint.element.style.display)return e.hint.element.style.display="none",void t.preventDefault();Object(u.d)(t)||t.shiftKey||!t.altKey||"Enter"!==t.key||(n.setStart(Y.firstChild,0),n.collapse(!0),Object(M.h)(n)),e.hint.select(t,e)}},Q.onkeyup=function(t){if(!t.isComposing&&"Enter"!==t.key&&"ArrowUp"!==t.key&&"Escape"!==t.key&&"ArrowDown"!==t.key){var n=[],r=Q.value.substring(0,Q.selectionStart);a.a.CODE_LANGUAGES.forEach((function(e){e.indexOf(r.toLowerCase())>-1&&n.push({html:e,value:e})})),e.hint.genHTML(n,r,e),t.preventDefault()}},e.wysiwyg.popover.insertAdjacentElement("beforeend",G)}Pe(e,Z)}else Z||e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview").forEach((function(e){e.previousElementSibling.style.display="none"})),Z=void 0;if(c){var te;e.wysiwyg.popover.innerHTML="",(te=document.createElement("span")).setAttribute("aria-label","ID<"+Object(u.g)("⌥-Enter")+">"),te.className="vditor-tooltipped vditor-tooltipped__n";var ne=document.createElement("input");te.appendChild(ne),ne.className="vditor-input",ne.setAttribute("placeholder","ID<"+Object(u.g)("⌥-Enter")+">"),ne.style.width="120px",ne.value=c.getAttribute("data-id")||"",ne.oninput=function(){c.setAttribute("data-id",ne.value)},ne.onkeydown=function(t){if(!t.isComposing)return Object(u.d)(t)||t.shiftKey||!t.altKey||"Enter"!==t.key?void Ae(e,t):(n.selectNodeContents(c),n.collapse(!1),Object(M.h)(n),void t.preventDefault())},Be(n,c,e),qe(n,c,e),Ue(c,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",te),Pe(e,c)}if(s&&Ke(e,s),!(v||o||l||Z||s||I||U||c||d))(W=Object(w.d)(r,"data-block","0"))&&W.parentElement.isEqualNode(e.wysiwyg.element)?(e.wysiwyg.popover.innerHTML="",Be(n,W,e),qe(n,W,e),Ue(W,e),Pe(e,W)):e.wysiwyg.popover.style.display="none";e.wysiwyg.element.querySelectorAll('span[data-type="backslash"] > span').forEach((function(e){e.style.display="none"}));var re=Object(w.d)(n.startContainer,"data-type","backslash");re&&(re.querySelector("span").style.display="inline")}}),200)},Pe=function(e,t){var n=t,r=Object(w.f)(t,"TABLE");r&&(n=r),e.wysiwyg.popover.style.left="0",e.wysiwyg.popover.style.display="block",e.wysiwyg.popover.style.top=Math.max(-8,n.offsetTop-21-e.wysiwyg.element.scrollTop)+"px",e.wysiwyg.popover.style.left=Math.min(n.offsetLeft,e.wysiwyg.element.clientWidth-e.wysiwyg.popover.clientWidth)+"px",e.wysiwyg.popover.setAttribute("data-top",(n.offsetTop-21).toString())},Be=function(e,t,n){var r=t.previousElementSibling;if(r&&(t.parentElement.isEqualNode(n.wysiwyg.element)||"LI"===t.tagName)){var i=document.createElement("button");i.setAttribute("data-type","up"),i.setAttribute("aria-label",R.a[n.options.lang].up+"<"+Object(u.g)("⌘-⇧-U")+">"),i.innerHTML=de.a,i.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",i.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("beforebegin",t),Object(M.f)(n.wysiwyg.element,e),K(n),ze(n),De(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",i)}},qe=function(e,t,n){var r=t.nextElementSibling;if(r&&(t.parentElement.isEqualNode(n.wysiwyg.element)||"LI"===t.tagName)){var i=document.createElement("button");i.setAttribute("data-type","down"),i.setAttribute("aria-label",R.a[n.options.lang].down+"<"+Object(u.g)("⌘-⇧-D")+">"),i.innerHTML=ne.a,i.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",i.onclick=function(){e.insertNode(document.createElement("wbr")),r.insertAdjacentElement("afterend",t),Object(M.f)(n.wysiwyg.element,e),K(n),ze(n),De(n)},n.wysiwyg.popover.insertAdjacentElement("beforeend",i)}},Ue=function(e,t){var n=document.createElement("button");n.setAttribute("data-type","remove"),n.setAttribute("aria-label",R.a[t.options.lang].remove+"<"+Object(u.g)("⌘-⇧-X")+">"),n.innerHTML=le.a,n.className="vditor-icon vditor-tooltipped vditor-tooltipped__n",n.onclick=function(){var n=Object(M.b)(t.wysiwyg.element);n.setStartAfter(e),Object(M.h)(n),e.remove(),K(t),ze(t)},t.wysiwyg.popover.insertAdjacentElement("beforeend",n)},Fe=function(e,t,n,r){if(!n.isComposing){if("Tab"===n.key)return r.focus(),r.select(),void n.preventDefault();if(!Object(u.d)(n)&&!n.shiftKey&&n.altKey&&"Enter"===n.key){var i=Object(M.b)(e);t.insertAdjacentHTML("afterend",a.a.ZWSP),i.setStartAfter(t.nextSibling),i.collapse(!0),Object(M.h)(i),n.preventDefault()}}},Ke=function(e,t){e.wysiwyg.popover.innerHTML="";var n=function(){""!==i.value.trim()&&(t.innerHTML=i.value),t.setAttribute("href",a.value),t.setAttribute("title",l.value)};t.querySelectorAll("[data-marker]").forEach((function(e){e.removeAttribute("data-marker")}));var r=document.createElement("span");r.setAttribute("aria-label",R.a[e.options.lang].textIsNotEmpty),r.className="vditor-tooltipped vditor-tooltipped__n";var i=document.createElement("input");r.appendChild(i),i.className="vditor-input",i.setAttribute("placeholder",R.a[e.options.lang].textIsNotEmpty),i.style.width="120px",i.value=t.innerHTML||"",i.oninput=function(){n()},i.onkeydown=function(n){Ae(e,n)||Fe(e.wysiwyg.element,t,n,a)};var o=document.createElement("span");o.setAttribute("aria-label",R.a[e.options.lang].link),o.className="vditor-tooltipped vditor-tooltipped__n";var a=document.createElement("input");o.appendChild(a),a.className="vditor-input",a.setAttribute("placeholder",R.a[e.options.lang].link),a.value=t.getAttribute("href")||"",a.oninput=function(){n()},a.onkeydown=function(n){Ae(e,n)||Fe(e.wysiwyg.element,t,n,l)};var s=document.createElement("span");s.setAttribute("aria-label",R.a[e.options.lang].tooltipText),s.className="vditor-tooltipped vditor-tooltipped__n";var l=document.createElement("input");s.appendChild(l),l.className="vditor-input",l.setAttribute("placeholder",R.a[e.options.lang].tooltipText),l.style.width="60px",l.value=t.getAttribute("title")||"",l.oninput=function(){n()},l.onkeydown=function(n){Ae(e,n)||Fe(e.wysiwyg.element,t,n,i)},Ue(t,e),e.wysiwyg.popover.insertAdjacentElement("beforeend",r),e.wysiwyg.popover.insertAdjacentElement("beforeend",o),e.wysiwyg.popover.insertAdjacentElement("beforeend",s),Pe(e,t)},We=function(e,t,n){var r=Object(w.c)(t.startContainer);if(r||(r=e.wysiwyg.element),n&&"formatItalic"!==n.inputType&&"deleteByDrag"!==n.inputType&&"insertFromDrop"!==n.inputType&&"formatBold"!==n.inputType&&"formatRemove"!==n.inputType&&"formatStrikeThrough"!==n.inputType&&"insertUnorderedList"!==n.inputType&&"insertOrderedList"!==n.inputType&&"formatOutdent"!==n.inputType&&"formatIndent"!==n.inputType&&""!==n.inputType||!n){var i=function(e){for(var t=e.previousSibling;t;){if(3!==t.nodeType&&"A"===t.tagName&&!t.previousSibling&&""===t.innerHTML.replace(a.a.ZWSP,"")&&t.nextSibling)return t;t=t.previousSibling}return!1}(t.startContainer);i&&i.remove(),e.wysiwyg.element.querySelectorAll("wbr").forEach((function(e){e.remove()})),t.insertNode(document.createElement("wbr")),r.querySelectorAll("[style]").forEach((function(e){e.removeAttribute("style")}));var o="";("link-ref-defs-block"===r.getAttribute("data-type")||lt(r.innerText))&&(r=e.wysiwyg.element);var s=r.isEqualNode(e.wysiwyg.element),l=Object(w.d)(r,"data-type","footnotes-block");if(s)o=r.innerHTML;else{var c,d,u=Object(w.b)(t.startContainer);if(u)r=Object(E.b)(t.startContainer,"BLOCKQUOTE")?Object(w.c)(t.startContainer)||r:u;if(l&&(r=l),o=r.outerHTML,"UL"===r.tagName||"OL"===r.tagName){var p=r.previousElementSibling,h=r.nextElementSibling;!p||"UL"!==p.tagName&&"OL"!==p.tagName||(o=p.outerHTML+o,p.remove()),!h||"UL"!==h.tagName&&"OL"!==h.tagName||(o+=h.outerHTML,h.remove()),o=o.replace("<div><wbr><br></div>","<li><p><wbr><br></p></li>")}(c=e.wysiwyg.element.querySelector("[data-type='link-ref-defs-block']"))&&!r.isEqualNode(c)&&(o+=c.outerHTML,c.remove()),(d=e.wysiwyg.element.querySelector("[data-type='footnotes-block']"))&&!r.isEqualNode(d)&&(o+=d.outerHTML,d.remove())}if(o=o.replace(/<\/(strong|b)><strong data-marker="\W{2}">/g,"").replace(/<\/(em|i)><em data-marker="\W{1}">/g,"").replace(/<\/(s|strike)><s data-marker="~{1,2}">/g,""),O("SpinVditorDOM",o,"argument",e.options.debugger),o=e.lute.SpinVditorDOM(o),O("SpinVditorDOM",o,"result",e.options.debugger),s)r.innerHTML=o;else if(r.outerHTML=o,(c=e.wysiwyg.element.querySelector("[data-type='link-ref-defs-block']"))&&e.wysiwyg.element.insertAdjacentElement("beforeend",c),(d=e.wysiwyg.element.querySelector("[data-type='footnotes-block']"))&&e.wysiwyg.element.insertAdjacentElement("beforeend",d),l){var f=Object(w.h)(e.wysiwyg.element.querySelector("wbr"),"LI");if(f){var m=e.wysiwyg.element.querySelector('sup[data-type="footnotes-ref"][data-footnotes-label="'+f.getAttribute("data-marker")+'"]');m&&m.setAttribute("aria-label",f.textContent.trim())}}(Object(E.a)(r)||o.startsWith("<h")||"deleteContentBackward"===(null==n?void 0:n.inputType)||"deleteContentForward"===(null==n?void 0:n.inputType))&&ct(e),Object(M.f)(e.wysiwyg.element,t),e.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach((function(t){H(t,e)}))}K(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0})},Ve=function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},Ze=function(e,t){return 229!==e.keyCode||""!==e.code||"Unidentified"!==e.key||"sv"===t.currentMode||(t[t.currentMode].composingLock=!0,!1)},Je=function(e,t){if(!("Enter"===t.key||"Tab"===t.key||"Backspace"===t.key||t.key.indexOf("Arrow")>-1||Object(u.d)(t)||"Escape"===t.key||t.shiftKey||t.altKey)){var n=Object(w.f)(e.startContainer,"P");if(n&&0===Object(M.c)(n,e).start){var r=document.createTextNode(a.a.ZWSP);e.insertNode(r),e.setStartAfter(r)}}},Ge=function(e,t){if("ArrowDown"===t||"ArrowUp"===t){var n=Object(w.d)(e.startContainer,"data-type","math-inline");n&&("ArrowDown"===t&&e.setStartAfter(n.parentElement),"ArrowUp"===t&&e.setStartBefore(n.parentElement))}},Xe=function(e,t){var n=Object(M.b)(e[e.currentMode].element),r=Object(w.c)(n.startContainer);r&&(r.insertAdjacentHTML(t,'<p data-block="0">'+a.a.ZWSP+"<wbr>\n</p>"),Object(M.f)(e[e.currentMode].element,n),"ir"===e.currentMode?S(e):ze(e),dt(e))},Qe=function(e){var t=Object(w.f)(e,"TABLE");return!(!t||!t.rows[0].cells[0].isSameNode(e))&&t},$e=function(e){var t=Object(w.f)(e,"TABLE");return!(!t||!t.lastElementChild.lastElementChild.lastElementChild.isSameNode(e))&&t},Ye=function(e,t,n){void 0===n&&(n=!0);var r=e.previousElementSibling;return r||(r=e.parentElement.previousElementSibling?e.parentElement.previousElementSibling.lastElementChild:"TBODY"===e.parentElement.parentElement.tagName&&e.parentElement.parentElement.previousElementSibling?e.parentElement.parentElement.previousElementSibling.lastElementChild.lastElementChild:null),r&&(t.selectNodeContents(r),n||t.collapse(!1),Object(M.h)(t)),r},et=function(e,t,n,r,i){var o=Object(M.c)(r,n);if("ArrowDown"===t.key&&-1===r.textContent.trimRight().substr(o.start).indexOf("\n")||"ArrowRight"===t.key&&o.start>=r.textContent.trimRight().length){var s=i.nextElementSibling;return!s||s&&("TABLE"===s.tagName||s.getAttribute("data-type"))?(i.insertAdjacentHTML("afterend",'<p data-block="0">'+a.a.ZWSP+"<wbr></p>"),Object(M.f)(e[e.currentMode].element,n)):(n.selectNodeContents(s),n.collapse(!0),Object(M.h)(n)),t.preventDefault(),!0}return!1},tt=function(e,t,n,r,i){var o=Object(M.c)(r,n);if("ArrowUp"===t.key&&-1===r.textContent.substr(o.start).indexOf("\n")||("ArrowLeft"===t.key||"Backspace"===t.key)&&0===o.start){var s=i.previousElementSibling;return!s||s&&("TABLE"===s.tagName||s.getAttribute("data-type"))?(i.insertAdjacentHTML("beforebegin",'<p data-block="0">'+a.a.ZWSP+"<wbr></p>"),Object(M.f)(e.ir.element,n)):(n.selectNodeContents(s),n.collapse(!1),Object(M.h)(n)),t.preventDefault(),!0}return!1},nt=function(e,t,n,r){void 0===r&&(r=!0);var i=Object(w.f)(t.startContainer,"LI");if(e[e.currentMode].element.querySelectorAll("wbr").forEach((function(e){e.remove()})),t.insertNode(document.createElement("wbr")),r&&i){for(var o="",a=0;a<i.parentElement.childElementCount;a++){var s=i.parentElement.children[a].querySelector("input");s&&s.remove(),o+='<p data-block="0">'+i.parentElement.children[a].innerHTML.trimLeft()+"</p>"}i.parentElement.insertAdjacentHTML("beforebegin",o),i.parentElement.remove()}else if(i)if("check"===n)i.parentElement.querySelectorAll("li").forEach((function(e){e.insertAdjacentHTML("afterbegin",'<input type="checkbox" />'+(0===e.textContent.indexOf(" ")?"":" ")),e.classList.add("vditor-task")}));else{i.querySelector("input")&&i.parentElement.querySelectorAll("li").forEach((function(e){e.querySelector("input").remove(),e.classList.remove("vditor-task")}));var l=void 0;(l="list"===n?document.createElement("ul"):document.createElement("ol")).innerHTML=i.parentElement.innerHTML,i.parentElement.parentNode.replaceChild(l,i.parentElement)}else{var c=Object(w.d)(t.startContainer,"data-block","0");c||(e[e.currentMode].element.querySelector("wbr").remove(),(c=e[e.currentMode].element.querySelector("p")).innerHTML="<wbr>"),"check"===n?(c.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li class="vditor-task"><input type="checkbox" /> '+c.innerHTML+"</li></ul>"),c.remove()):"list"===n?(c.insertAdjacentHTML("beforebegin",'<ul data-block="0"><li>'+c.innerHTML+"</li></ul>"),c.remove()):"ordered-list"===n&&(c.insertAdjacentHTML("beforebegin",'<ol data-block="0"><li>'+c.innerHTML+"</li></ol>"),c.remove())}},rt=function(e,t,n){if(t&&t.previousElementSibling){e[e.currentMode].element.querySelectorAll("wbr").forEach((function(e){e.remove()})),n.insertNode(document.createElement("wbr"));var r=t.parentElement,i=t.getAttribute("data-marker");1!==i.length&&(i="1"+i.slice(-1)),t.previousElementSibling.insertAdjacentHTML("beforeend","<"+r.tagName+' data-block="0"><li data-marker="'+i+'">'+t.innerHTML+"</li></"+r.tagName+">"),t.remove(),"wysiwyg"===e.currentMode?r.outerHTML=e.lute.SpinVditorDOM(r.outerHTML):r.outerHTML=e.lute.SpinVditorIRDOM(r.outerHTML),Object(M.f)(e[e.currentMode].element,n);var o=Object(w.b)(n.startContainer);o&&o.querySelectorAll(".vditor-"+e.currentMode+"__preview[data-render='2']").forEach((function(t){H(t,e),"wysiwyg"===e.currentMode&&t.previousElementSibling.setAttribute("style","display:none")})),dt(e),"wysiwyg"===e.currentMode&&ze(e)}else e[e.currentMode].element.focus()},it=function(e,t,n,r){var i=Object(w.f)(t.parentElement,"LI");if(i){e[e.currentMode].element.querySelectorAll("wbr").forEach((function(e){e.remove()})),n.insertNode(document.createElement("wbr"));var o=t.parentElement,a=o.cloneNode(),s=!1,l="";o.querySelectorAll("li").forEach((function(e){s&&(l+=e.outerHTML,e.nextElementSibling||e.previousElementSibling?e.remove():e.parentElement.remove()),e.isSameNode(t)&&(s=!0)})),i.insertAdjacentElement("afterend",t),l&&(a.innerHTML=l,t.insertAdjacentElement("beforeend",a)),"wysiwyg"===e.currentMode?r.outerHTML=e.lute.SpinVditorDOM(r.outerHTML):r.outerHTML=e.lute.SpinVditorIRDOM(r.outerHTML),Object(M.f)(e[e.currentMode].element,n);var c=Object(w.b)(n.startContainer);c&&c.querySelectorAll(".vditor-"+e.currentMode+"__preview[data-render='2']").forEach((function(t){H(t,e),"wysiwyg"===e.currentMode&&t.previousElementSibling.setAttribute("style","display:none")})),dt(e),"wysiwyg"===e.currentMode&&ze(e)}else e[e.currentMode].element.focus()},ot=function(e,t){for(var n=getSelection().getRangeAt(0).startContainer.parentElement,r=e.rows[0].cells.length,i=e.rows.length,o=0,a=0;a<i;a++)for(var s=0;s<r;s++)if(e.rows[a].cells[s].isSameNode(n)){o=s;break}for(var l=0;l<i;l++)e.rows[l].cells[o].setAttribute("align",t)},at=function(e){var t=e.trimRight().split("\n").pop();return""!==t&&((""===t.replace(/ |-/g,"")||""===t.replace(/ |_/g,"")||""===t.replace(/ |\*/g,""))&&(t.replace(/ /g,"").length>2&&(!(t.indexOf("-")>-1&&-1===t.trimLeft().indexOf(" ")&&e.trimRight().split("\n").length>1)&&(0!==t.indexOf("    ")&&0!==t.indexOf("\t")))))},st=function(e,t){if(!t)return!1;var n=e.trimRight().split("\n");return 0!==(e=n.pop()).indexOf("    ")&&0!==e.indexOf("\t")&&(""!==(e=e.trimLeft())&&0!==n.length&&(""===e.replace(/-/g,"")||""===e.replace(/=/g,"")))},lt=function(e){return"[toc]"===e.trim().toLowerCase()},ct=function(e){var t=e[e.currentMode].element;e.outline.render(e);var n=t.querySelector('[data-type="toc-block"]');if(n){var r="";Array.from(t.children).forEach((function(t){if(Object(E.a)(t)){var n=parseInt(t.tagName.substring(1),10),i=new Array(2*(n-1)).fill("&emsp;").join("");"ir"===e.currentMode?r+=i+'<span data-type="toc-h">'+t.textContent.substring(n+1).trim()+"</span><br>":r+=i+'<span data-type="toc-h">'+t.textContent.trim()+"</span><br>"}})),n.innerHTML=r||"[ToC]"}},dt=function(e){"wysiwyg"===e.currentMode?K(e):"ir"===e.currentMode&&Tt(e)},ut=function(e,t,n,r){var i=e.startContainer,o=Object(w.f)(i,"LI");if(o){if(!Object(u.d)(r)&&!r.altKey&&"Enter"===r.key&&!r.shiftKey&&n&&o.contains(n)&&n.nextElementSibling)return o&&!o.textContent.endsWith("\n")&&o.insertAdjacentText("beforeend","\n"),e.insertNode(document.createTextNode("\n")),e.collapse(!1),dt(t),r.preventDefault(),!0;if(!(Object(u.d)(r)||r.shiftKey||r.altKey||"Backspace"!==r.key||o.previousElementSibling||""!==e.toString()||0!==Object(M.c)(o,e).start))return o.nextElementSibling?(o.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'+o.innerHTML+"</p>"),o.remove()):o.parentElement.outerHTML='<p data-block="0"><wbr>'+o.innerHTML+"</p>",Object(M.f)(t[t.currentMode].element,e),dt(t),r.preventDefault(),!0;if(!Object(u.d)(r)&&!r.altKey&&"Tab"===r.key){var a=!1;if((0===e.startOffset&&(3===i.nodeType&&!i.previousSibling||3!==i.nodeType&&"LI"===i.nodeName)||o.classList.contains("vditor-task")&&1===e.startOffset&&3!==i.previousSibling.nodeType&&"INPUT"===i.previousSibling.tagName)&&(a=!0),a)return r.shiftKey?it(t,o,e,o.parentElement):rt(t,o,e),r.preventDefault(),!0}}return!1},pt=function(e,t,n){if(e.options.tab&&"Tab"===n.key)return n.shiftKey||(""===t.toString()?(t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1)):(t.extractContents(),t.insertNode(document.createTextNode(e.options.tab)),t.collapse(!1))),Object(M.h)(t),dt(e),n.preventDefault(),!0},ht=function(e,t,n,r){if(n){if(!Object(u.d)(e)&&!e.altKey&&"Enter"===e.key){var o=String.raw(i||(i=Ve(["",""],["",""])),n.textContent).replace(/\\\|/g,"").trim(),a=o.split("|");if(o.startsWith("|")&&o.endsWith("|")&&a.length>3){var s=a.map((function(){return"---"})).join("|");return s=n.textContent+"\n"+s.substring(3,s.length-3)+"\n|<wbr>",n.outerHTML=t.lute.SpinVditorDOM(s),Object(M.f)(t[t.currentMode].element,r),dt(t),De(t),e.preventDefault(),!0}if(at(n.innerHTML)){var l="",c=n.innerHTML.trimRight().split("\n");return c.length>1&&(c.pop(),l='<p data-block="0">'+c.join("\n")+"</p>"),n.insertAdjacentHTML("afterend",l+'<hr data-block="0"><p data-block="0"><wbr>\n</p>'),n.remove(),Object(M.f)(t[t.currentMode].element,r),dt(t),De(t),e.preventDefault(),!0}if(st(n.innerHTML,t.options.preview.markdown.setext))return n.outerHTML=t.lute.SpinVditorDOM(n.innerHTML+'<p data-block="0"><wbr>\n</p>'),Object(M.f)(t[t.currentMode].element,r),dt(t),De(t),e.preventDefault(),!0}if(n.previousElementSibling&&"Backspace"===e.key&&!Object(u.d)(e)&&!e.altKey&&!e.shiftKey&&n.textContent.trimRight().split("\n").length>1&&0===Object(M.c)(n,r).start){var d=Object(w.a)(n.previousElementSibling);return d.textContent.endsWith("\n")||(d.textContent=d.textContent+"\n"),d.parentElement.insertAdjacentHTML("beforeend","<wbr>"+n.innerHTML),n.remove(),Object(M.f)(t[t.currentMode].element,r),!1}return!1}},ft=function(e,t,n){for(var r="",i=0;i<n.parentElement.childElementCount;i++)r+="<td>"+(0===i?" <wbr>":" ")+"</td>";"TH"===n.tagName?n.parentElement.parentElement.insertAdjacentHTML("afterend","<tbody><tr>"+r+"</tr></tbody>"):n.parentElement.insertAdjacentHTML("afterend","<tr>"+r+"</tr>"),Object(M.f)(e[e.currentMode].element,t),dt(e),De(e)},mt=function(e,t,n){for(var r=0,i=n.previousElementSibling;i;)r++,i=i.previousElementSibling;for(var o=0;o<t.rows.length;o++)0===o?t.rows[o].cells[r].insertAdjacentHTML("afterend","<th> </th>"):t.rows[o].cells[r].insertAdjacentHTML("afterend","<td> </td>");dt(e)},vt=function(e,t,n){if("TD"===n.tagName){var r=n.parentElement.parentElement;n.parentElement.previousElementSibling?t.selectNodeContents(n.parentElement.previousElementSibling.lastElementChild):t.selectNodeContents(r.previousElementSibling.lastElementChild.lastElementChild),1===r.childElementCount?r.remove():n.parentElement.remove(),t.collapse(!1),Object(M.h)(t),dt(e)}},gt=function(e,t,n,r){for(var i=0,o=r.previousElementSibling;o;)i++,o=o.previousElementSibling;(r.previousElementSibling||r.nextElementSibling)&&(t.selectNodeContents(r.previousElementSibling||r.nextElementSibling),t.collapse(!0));for(var a=0;a<n.rows.length;a++){var s=n.rows[a].cells;if(1===s.length){n.remove(),"wysiwyg"===e.currentMode&&ze(e);break}s[i].remove()}Object(M.h)(t),dt(e)},bt=function(e,t,n){var r=n.startContainer,i=Object(w.f)(r,"TD")||Object(w.f)(r,"TH");if(i){if(!Object(u.d)(t)&&!t.altKey&&"Enter"===t.key){i.lastElementChild&&(!i.lastElementChild||i.lastElementChild.isSameNode(i.lastChild)&&"BR"===i.lastElementChild.tagName)||i.insertAdjacentHTML("beforeend","<br>");var o=document.createElement("br");return n.insertNode(o),n.setStartAfter(o),dt(e),De(e),t.preventDefault(),!0}if("Tab"===t.key)return t.shiftKey?(Ye(i,n),t.preventDefault(),!0):((d=i.nextElementSibling)||(d=i.parentElement.nextElementSibling?i.parentElement.nextElementSibling.firstElementChild:"THEAD"===i.parentElement.parentElement.tagName&&i.parentElement.parentElement.nextElementSibling?i.parentElement.parentElement.nextElementSibling.firstElementChild.firstElementChild:null),d&&(n.selectNodeContents(d),Object(M.h)(n)),t.preventDefault(),!0);var a=i.parentElement.parentElement.parentElement;if("ArrowUp"===t.key){if(t.preventDefault(),"TH"===i.tagName)return a.previousElementSibling?(n.selectNodeContents(a.previousElementSibling),n.collapse(!1),Object(M.h)(n)):Xe(e,"beforebegin"),!0;for(var s=0,l=i.parentElement;s<l.cells.length&&!l.cells[s].isSameNode(i);s++);var c=l.previousElementSibling;return c||(c=l.parentElement.previousElementSibling.firstChild),n.selectNodeContents(c.cells[s]),n.collapse(!1),Object(M.h)(n),!0}if("ArrowDown"===t.key){var d;if(t.preventDefault(),!(l=i.parentElement).nextElementSibling&&"TD"===i.tagName)return a.nextElementSibling?(n.selectNodeContents(a.nextElementSibling),n.collapse(!0),Object(M.h)(n)):Xe(e,"afterend"),!0;for(s=0;s<l.cells.length&&!l.cells[s].isSameNode(i);s++);return(d=l.nextElementSibling)||(d=l.parentElement.nextElementSibling.firstChild),n.selectNodeContents(d.cells[s]),n.collapse(!0),Object(M.h)(n),!0}if("wysiwyg"===e.currentMode&&!Object(u.d)(t)&&"Enter"===t.key&&!t.shiftKey&&t.altKey){var p=e.wysiwyg.popover.querySelector(".vditor-input");return p.focus(),p.select(),t.preventDefault(),!0}if(!Object(u.d)(t)&&!t.shiftKey&&!t.altKey&&"Backspace"===t.key&&0===n.startOffset&&""===n.toString())return!Ye(i,n,!1)&&a&&(""===a.textContent.trim()?(a.outerHTML='<p data-block="0"><wbr>\n</p>',Object(M.f)(e[e.currentMode].element,n)):(n.setStartBefore(a),n.collapse(!0)),dt(e)),t.preventDefault(),!0;if(pe("⌘-=",t))return ft(e,n,i),t.preventDefault(),!0;if(pe("⌘-⇧-=",t))return mt(e,a,i),t.preventDefault(),!0;if(pe("⌘--",t))return vt(e,n,i),t.preventDefault(),!0;if(pe("⌘-⇧--",t))return gt(e,n,a,i),t.preventDefault(),!0;if(pe("⌘-⇧-L",t)){if("ir"===e.currentMode)return ot(a,"left"),dt(e),t.preventDefault(),!0;if(h=e.wysiwyg.popover.querySelector('[data-type="left"]'))return h.click(),t.preventDefault(),!0}if(pe("⌘-⇧-C",t)){if("ir"===e.currentMode)return ot(a,"center"),dt(e),t.preventDefault(),!0;if(h=e.wysiwyg.popover.querySelector('[data-type="center"]'))return h.click(),t.preventDefault(),!0}if(pe("⌘-⇧-R",t)){if("ir"===e.currentMode)return ot(a,"right"),dt(e),t.preventDefault(),!0;var h;if(h=e.wysiwyg.popover.querySelector('[data-type="right"]'))return h.click(),t.preventDefault(),!0}}return!1},yt=function(e,t,n,r){if("PRE"===n.tagName&&pe("⌘-A",t))return r.selectNodeContents(n.firstElementChild),t.preventDefault(),!0;if(e.options.tab&&"Tab"===t.key&&!t.shiftKey&&""===r.toString())return r.insertNode(document.createTextNode(e.options.tab)),r.collapse(!1),dt(e),t.preventDefault(),!0;if("Backspace"===t.key&&!Object(u.d)(t)&&!t.shiftKey&&!t.altKey){var i=Object(M.c)(n,r);if((0===i.start||1===i.start&&"\n"===n.innerText)&&""===r.toString())return n.parentElement.outerHTML='<p data-block="0"><wbr>'+n.firstElementChild.innerHTML+"</p>",Object(M.f)(e[e.currentMode].element,r),dt(e),t.preventDefault(),!0}return!Object(u.d)(t)&&!t.altKey&&"Enter"===t.key&&(n.firstElementChild.textContent.endsWith("\n")||n.firstElementChild.insertAdjacentText("beforeend","\n"),r.insertNode(document.createTextNode("\n")),r.collapse(!1),Object(M.h)(r),n.firstElementChild.classList.contains("language-mindmap")?"wysiwyg"===e.currentMode?We(e,r):D(e,r):(dt(e),De(e)),t.preventDefault(),!0)},wt=function(e,t,n,r){var i=t.startContainer,o=Object(w.f)(i,"BLOCKQUOTE");if(o&&""===t.toString()){if("Backspace"===n.key&&!Object(u.d)(n)&&!n.shiftKey&&!n.altKey&&0===Object(M.c)(o,t).start)return t.insertNode(document.createElement("wbr")),o.outerHTML=o.innerHTML,Object(M.f)(e[e.currentMode].element,t),dt(e),n.preventDefault(),!0;if(r&&"Enter"===n.key&&!Object(u.d)(n)&&!n.shiftKey&&!n.altKey&&"BLOCKQUOTE"===r.parentElement.tagName){var s=!1;if("\n"===r.innerHTML.replace(a.a.ZWSP,"")?(s=!0,r.remove()):r.innerHTML.endsWith("\n\n")&&Object(M.c)(r,t).start===r.textContent.length-1&&(r.innerHTML=r.innerHTML.substr(0,r.innerHTML.length-2),s=!0),s)return o.insertAdjacentHTML("afterend",'<p data-block="0">'+a.a.ZWSP+"<wbr>\n</p>"),Object(M.f)(e[e.currentMode].element,t),dt(e),n.preventDefault(),!0}var l=Object(w.c)(i);if("wysiwyg"===e.currentMode&&l&&pe("⌘-⇧-:",n))return t.insertNode(document.createElement("wbr")),l.outerHTML='<blockquote data-block="0">'+l.outerHTML+"</blockquote>",Object(M.f)(e.wysiwyg.element,t),K(e),n.preventDefault(),!0}return!1},Et=function(e,t,n){var r=t.startContainer,i=Object(w.e)(r,"vditor-task");if(i){if(pe("⌘-⇧-J",n)){var o=i.firstElementChild;return o.checked?o.removeAttribute("checked"):o.setAttribute("checked","checked"),dt(e),n.preventDefault(),!0}if("Backspace"===n.key&&!Object(u.d)(n)&&!n.shiftKey&&!n.altKey&&""===t.toString()&&1===t.startOffset&&(3===r.nodeType&&r.previousSibling&&"INPUT"===r.previousSibling.tagName||3!==r.nodeType)){var a=i.previousElementSibling;if(i.querySelector("input").remove(),a)Object(w.a)(a).parentElement.insertAdjacentHTML("beforeend","<wbr>"+i.innerHTML.trim()),i.remove();else i.parentElement.insertAdjacentHTML("beforebegin",'<p data-block="0"><wbr>'+(i.innerHTML.trim()||"\n")+"</p>"),i.nextElementSibling?i.remove():i.parentElement.remove();return Object(M.f)(e[e.currentMode].element,t),dt(e),n.preventDefault(),!0}if("Enter"===n.key&&!Object(u.d)(n)&&!n.shiftKey&&!n.altKey){if(""===i.textContent.trim())if(Object(w.e)(i.parentElement,"vditor-task")){var s=Object(w.b)(r);s&&it(e,i,t,s)}else if(i.nextElementSibling){var l="",c="",d=!1;Array.from(i.parentElement.children).forEach((function(e){i.isSameNode(e)?d=!0:d?l+=e.outerHTML:c+=e.outerHTML}));var p=i.parentElement.tagName,h="OL"===i.parentElement.tagName?"":' data-marker="'+i.parentElement.getAttribute("data-marker")+'"',f="";c&&(f="UL"===i.parentElement.tagName?"":' start="1"',c="<"+p+' data-tight="true"'+h+' data-block="0">'+c+"</"+p+">"),i.parentElement.outerHTML=c+'<p data-block="0"><wbr>\n</p><'+p+'\n data-tight="true"'+h+' data-block="0"'+f+">"+l+"</"+p+">"}else i.parentElement.insertAdjacentHTML("afterend",'<p data-block="0"><wbr>\n</p>'),1===i.parentElement.querySelectorAll("li").length?i.parentElement.remove():i.remove();else 3!==r.nodeType&&0===t.startOffset&&"INPUT"===r.firstChild.tagName?t.setStart(r.childNodes[1],1):(t.setEndAfter(i.lastChild),i.insertAdjacentHTML("afterend",'<li class="vditor-task" data-marker="'+i.getAttribute("data-marker")+'"><input type="checkbox"> <wbr></li>'),document.querySelector("wbr").after(t.extractContents()));return Object(M.f)(e[e.currentMode].element,t),dt(e),De(e),n.preventDefault(),!0}}return!1},Mt=function(e,t,n,r){if(3!==t.startContainer.nodeType){var i=t.startContainer.children[t.startOffset];if(i&&"HR"===i.tagName)return t.selectNodeContents(i.previousElementSibling),t.collapse(!1),n.preventDefault(),!0}if(r){var o=r.previousElementSibling;if(o&&0===Object(M.c)(r,t).start&&(Object(u.e)()&&"HR"===o.tagName||"TABLE"===o.tagName)){if("TABLE"===o.tagName){var a=o.lastElementChild.lastElementChild.lastElementChild;a.innerHTML=a.innerHTML.trimLeft()+"<wbr>"+r.textContent.trim(),r.remove()}else o.remove();return Object(M.f)(e[e.currentMode].element,t),dt(e),n.preventDefault(),!0}}return!1},St=function(e){Object(u.e)()&&3!==e.startContainer.nodeType&&"HR"===e.startContainer.tagName&&e.setStartBefore(e.startContainer)},Ot=function(e,t,n){var r,i;if(!Object(u.e)())return!1;if("ArrowUp"===e.key&&t&&"TABLE"===(null===(r=t.previousElementSibling)||void 0===r?void 0:r.tagName)){var o=t.previousElementSibling;return n.selectNodeContents(o.rows[o.rows.length-1].lastElementChild),n.collapse(!1),e.preventDefault(),!0}return!("ArrowDown"!==e.key||!t||"TABLE"!==(null===(i=t.nextElementSibling)||void 0===i?void 0:i.tagName))&&(n.selectNodeContents(t.nextElementSibling.rows[0].cells[0]),n.collapse(!0),e.preventDefault(),!0)},kt=function(e,t,n){var r;t.stopPropagation(),t.preventDefault();var i=t.clipboardData.getData("text/html"),o=t.clipboardData.getData("text/plain"),s={},l=function(t){var n=t.TokensStr();if(34===t.__internal_object__.Parent.Type&&n&&-1===n.indexOf("file://")&&e.options.upload.linkToImgUrl){var r=new XMLHttpRequest;r.open("POST",e.options.upload.linkToImgUrl),q(e,r),r.onreadystatechange=function(){if(r.readyState===XMLHttpRequest.DONE)if(200===r.status){var t=JSON.parse(r.responseText);if(0!==t.code)return void e.tip.show(t.msg);var n=t.data.originalURL,i=e[e.currentMode].element.querySelector('img[src="'+n+'"]');i.src=t.data.url,"ir"===e.currentMode&&(i.previousElementSibling.previousElementSibling.innerHTML=t.data.url),dt(e)}else e.tip.show(r.responseText)},r.send(JSON.stringify({url:n}))}return"ir"===e.currentMode?['<span class="vditor-ir__marker vditor-ir__marker--link">'+n+"</span>",Lute.WalkStop]:["",Lute.WalkStop]};i.replace(/<(|\/)(html|body|meta)[^>]*?>/gi,"").trim()!=='<a href="'+o+'">'+o+"</a>"&&i.replace(/<(|\/)(html|body|meta)[^>]*?>/gi,"").trim()!=='\x3c!--StartFragment--\x3e<a href="'+o+'">'+o+"</a>\x3c!--EndFragment--\x3e"||(i="");var c=(new DOMParser).parseFromString(i,"text/html");c.body&&(i=c.body.innerHTML);var d=N(i,o,e.currentMode),u=Object(w.f)(t.target,"CODE");if(u){var p=Object(M.c)(t.target);"PRE"!==u.parentElement.tagName&&(o+=a.a.ZWSP),u.textContent=u.textContent.substring(0,p.start)+o+u.textContent.substring(p.end),Object(M.g)(p.start+o.length,p.start+o.length,u.parentElement),(null===(r=u.parentElement)||void 0===r?void 0:r.nextElementSibling.classList.contains("vditor-"+e.currentMode+"__preview"))&&(u.parentElement.nextElementSibling.innerHTML=u.outerHTML,H(u.parentElement.nextElementSibling,e))}else if(d)n.pasteCode(d);else if(""!==i.trim()){var h=document.createElement("div");h.innerHTML=i,h.querySelectorAll("[style]").forEach((function(e){e.removeAttribute("style")})),h.querySelectorAll(".vditor-copy").forEach((function(e){e.remove()})),"ir"===e.currentMode?(s.HTML2VditorIRDOM={renderLinkDest:l},e.lute.SetJSRenderers({renderers:s}),Object(M.d)(e.lute.HTML2VditorIRDOM(h.innerHTML),e)):(s.HTML2VditorDOM={renderLinkDest:l},e.lute.SetJSRenderers({renderers:s}),Object(M.d)(e.lute.HTML2VditorDOM(h.innerHTML),e)),e.outline.render(e)}else t.clipboardData.files.length>0&&e.options.upload.url?F(e,t.clipboardData.files):""!==o.trim()&&0===t.clipboardData.files.length&&("ir"===e.currentMode?(s.Md2VditorIRDOM={renderLinkDest:l},e.lute.SetJSRenderers({renderers:s}),Object(M.d)(e.lute.Md2VditorIRDOM(o),e)):(s.Md2VditorDOM={renderLinkDest:l},e.lute.SetJSRenderers({renderers:s}),Object(M.d)(e.lute.Md2VditorDOM(o),e)),e.outline.render(e));e[e.currentMode].element.querySelectorAll(".vditor-"+e.currentMode+"__preview[data-render='2']").forEach((function(t){H(t,e)})),dt(e)},xt=function(e){e.hint.render(e);var t=Object(M.b)(e.ir.element).startContainer,n=Object(w.d)(t,"data-type","code-block-info");if(n)if(""===n.textContent.replace(a.a.ZWSP,"")&&e.hint.recentLanguage){n.textContent=a.a.ZWSP+e.hint.recentLanguage,Object(M.b)(e.ir.element).selectNodeContents(n)}else{var r=[],i=n.textContent.substring(0,Object(M.c)(n).start).replace(a.a.ZWSP,"");a.a.CODE_LANGUAGES.forEach((function(e){e.indexOf(i.toLowerCase())>-1&&r.push({html:e,value:e})})),e.hint.genHTML(r,i,e)}},Tt=function(e,t){void 0===t&&(t={enableAddUndoStack:!0,enableHint:!1,enableInput:!0}),t.enableHint&&xt(e),clearTimeout(e.ir.processTimeoutId),e.ir.processTimeoutId=window.setTimeout((function(){if(!e.ir.composingLock||!Object(u.f)()){var n=l(e);"function"==typeof e.options.input&&t.enableInput&&e.options.input(n),e.options.counter.enable&&e.counter.render(e,n),e.options.cache.enable&&Object(u.a)()&&(localStorage.setItem(e.options.cache.id,n),e.options.cache.after&&e.options.cache.after(n)),e.devtools&&e.devtools.renderEchart(e),t.enableAddUndoStack&&e.irUndo.addToUndoStack(e)}}),800)},Lt=function(e,t){var n=getSelection().getRangeAt(0),r=Object(w.c)(n.startContainer)||n.startContainer;if(r){if(""===t){var i=r.querySelector(".vditor-ir__marker--heading");n.selectNodeContents(i),document.execCommand("delete")}else n.selectNodeContents(r),n.collapse(!0),document.execCommand("insertHTML",!1,t);S(e),ct(e)}},jt=function(e,t,n){var r=Object(w.d)(e.startContainer,"data-type",n);if(r){r.firstElementChild.remove(),r.lastElementChild.remove(),e.insertNode(document.createElement("wbr"));var i=document.createElement("div");i.innerHTML=t.lute.SpinVditorIRDOM(r.outerHTML),r.outerHTML=i.firstElementChild.innerHTML.trim()}},_t=function(e,t,n,r){var i=Object(M.b)(e.ir.element),o=t.getAttribute("data-type"),a=i.startContainer;if(3===a.nodeType&&(a=a.parentElement),t.classList.contains("vditor-menu--current"))if("quote"===o){var s=Object(w.f)(a,"BLOCKQUOTE");s&&(i.insertNode(document.createElement("wbr")),s.outerHTML=""===s.innerHTML.trim()?'<p data-block="0">'+s.innerHTML+"</p>":s.innerHTML)}else if("link"===o){var l=Object(w.d)(i.startContainer,"data-type","a");if(l){var c=Object(w.e)(i.startContainer,"vditor-ir__link");c?(i.insertNode(document.createElement("wbr")),l.outerHTML=c.innerHTML):l.outerHTML=l.querySelector(".vditor-ir__link").innerHTML+"<wbr>"}}else"italic"===o?jt(i,e,"em"):"bold"===o?jt(i,e,"strong"):"strike"===o?jt(i,e,"s"):"inline-code"===o?jt(i,e,"code"):"check"!==o&&"list"!==o&&"ordered-list"!==o||nt(e,i,o);else{0===e.ir.element.childNodes.length&&(e.ir.element.innerHTML='<p data-block="0"><wbr></p>',Object(M.f)(e.ir.element,i));var d=Object(w.c)(i.startContainer);if("line"===o){if(d){var u='<hr data-block="0"><p data-block="0"><wbr>\n</p>';""===d.innerHTML.trim()?d.outerHTML=u:d.insertAdjacentHTML("afterend",u)}}else if("quote"===o)d&&(i.insertNode(document.createElement("wbr")),d.outerHTML='<blockquote data-block="0">'+d.outerHTML+"</blockquote>");else if("link"===o){var p=void 0;p=""===i.toString()?n+"<wbr>"+r:""+n+i.toString()+r.replace(")","<wbr>)"),document.execCommand("insertHTML",!1,p)}else if("italic"===o||"bold"===o||"strike"===o||"inline-code"===o||"code"===o||"table"===o){p=void 0;p=""===i.toString()?n+"<wbr>"+r:""+n+i.toString()+"<wbr>"+r,"table"!==o&&"code"!==o||(p="\n"+p),document.execCommand("insertHTML",!1,p),"table"===o&&(i.selectNodeContents(getSelection().getRangeAt(0).startContainer.parentElement),Object(M.h)(i))}else"check"!==o&&"list"!==o&&"ordered-list"!==o||nt(e,i,o,!1)}Object(M.f)(e.ir.element,i),Tt(e),S(e)},Ct=function(){function e(){var e=this;this.fillEmoji=function(t,n){e.element.style.display="none";var r=t.getAttribute("data-value"),i=0===r.indexOf("@")?"@":":",o=window.getSelection().getRangeAt(0);if("sv"!==n.currentMode){if("ir"===n.currentMode){var s=Object(w.d)(o.startContainer,"data-type","code-block-info");if(s)return s.textContent=a.a.ZWSP+r.trimRight(),o.selectNodeContents(s),o.collapse(!1),Tt(n),s.parentElement.querySelectorAll("code").forEach((function(e){e.className="language-"+r.trimRight()})),H(s.parentElement.querySelector(".vditor-ir__preview"),n),void(e.recentLanguage=r.trimRight())}if("wysiwyg"===n.currentMode&&3!==o.startContainer.nodeType&&o.startContainer.firstElementChild.classList.contains("vditor-input")){var c=o.startContainer.firstElementChild;return c.value=r.trimRight(),o.selectNodeContents(c),o.collapse(!1),c.dispatchEvent(new CustomEvent("input")),void(e.recentLanguage=r.trimRight())}var d;if(o.setStart(o.startContainer,o.startContainer.textContent.lastIndexOf(i)),o.deleteContents(),r.indexOf(":")>-1?(Object(M.d)(n.lute.SpinVditorDOM(r),n),o.insertNode(document.createTextNode(" "))):o.insertNode(document.createTextNode(r)),o.collapse(!1),Object(M.h)(o),"wysiwyg"===n.currentMode)(d=Object(w.e)(o.startContainer,"vditor-wysiwyg__block"))&&d.lastElementChild.classList.contains("vditor-wysiwyg__preview")&&(d.lastElementChild.innerHTML=d.firstElementChild.innerHTML,H(d.lastElementChild,n));else(d=Object(w.e)(o.startContainer,"vditor-ir__marker--pre"))&&d.nextElementSibling.classList.contains("vditor-ir__preview")&&(d.nextElementSibling.innerHTML=d.innerHTML,H(d.nextElementSibling,n));dt(n)}else{var u=Object(M.c)(n.sv.element,o),p=l(n),h=p.substring(0,p.substring(0,u.start).lastIndexOf(i));z(n,h+r+p.substring(u.start),{end:(h+r).length,start:(h+r).length})}},this.timeId=-1,this.element=document.createElement("div"),this.element.className="vditor-hint",this.recentLanguage=""}return e.prototype.render=function(e){var t=this;if(window.getSelection().focusNode){var n,r=Object(M.c)(e[e.currentMode].element);if("sv"!==e.currentMode){var i=getSelection().getRangeAt(0);n=i.startContainer.textContent.substring(0,i.startOffset)||""}else n=l(e).substring(0,r.end).split("\n").slice(-1).pop();var o=this.getKey(n,":"),a=!1;if(void 0===o&&(a=!0,o=this.getKey(n,"@")),void 0===o)this.element.style.display="none",clearTimeout(this.timeId);else if(a&&e.options.hint.at&&(clearTimeout(this.timeId),this.timeId=window.setTimeout((function(){t.genHTML(e.options.hint.at(o),o,e)}),e.options.hint.delay)),!a){var s=""===o?e.options.hint.emoji:e.lute.GetEmojis(),c=[];Object.keys(s).forEach((function(e){0===e.indexOf(o.toLowerCase())&&(s[e].indexOf(".")>-1?c.push({html:'<img src="'+s[e]+'" title=":'+e+':"/> :'+e+":",value:":"+e+":"}):c.push({html:'<span class="vditor-hint__emoji">'+s[e]+"</span>"+e,value:s[e]}))})),this.genHTML(c,o,e)}}},e.prototype.genHTML=function(e,t,n){var r=this;if(0!==e.length){var i=n[n.currentMode].element,o=Object(M.a)(i),a=o.left+n.outline.element.offsetWidth,s=o.top,l="";e.forEach((function(e,n){if(!(n>7)){var r=e.html;if(""!==t){var i=r.lastIndexOf(">")+1,o=r.substr(i),a=o.toLowerCase().indexOf(t.toLowerCase());a>-1&&(o=o.substring(0,a)+"<b>"+o.substring(a,a+t.length)+"</b>"+o.substring(a+t.length),r=r.substr(0,i)+o)}l+='<button data-value="'+e.value+' "\n'+(0===n?"class='vditor-hint--current'":"")+"> "+r+"</button>"}})),this.element.innerHTML=l;var c=parseInt(document.defaultView.getComputedStyle(i,null).getPropertyValue("line-height"),10);this.element.style.top=s+(c||22)+"px",this.element.style.left=a+"px",this.element.style.display="block",this.element.querySelectorAll("button").forEach((function(e){e.addEventListener("click",(function(t){r.fillEmoji(e,n),t.preventDefault()}))})),this.element.getBoundingClientRect().bottom>window.innerHeight&&(this.element.style.top=s-this.element.offsetHeight+"px")}else this.element.style.display="none"},e.prototype.select=function(e,t){if(0===this.element.querySelectorAll("button").length||"none"===this.element.style.display)return!1;var n=this.element.querySelector(".vditor-hint--current");if("ArrowDown"===e.key)return e.preventDefault(),e.stopPropagation(),n.removeAttribute("class"),n.nextElementSibling?n.nextElementSibling.className="vditor-hint--current":this.element.children[0].className="vditor-hint--current",!0;if("ArrowUp"===e.key){if(e.preventDefault(),e.stopPropagation(),n.removeAttribute("class"),n.previousElementSibling)n.previousElementSibling.className="vditor-hint--current";else{var r=this.element.children.length;this.element.children[r-1].className="vditor-hint--current"}return!0}return!(Object(u.d)(e)||e.shiftKey||e.altKey||"Enter"!==e.key)&&(e.preventDefault(),e.stopPropagation(),this.fillEmoji(n,t),!0)},e.prototype.getKey=function(e,t){var n,r=e.split(t),i=r[r.length-1];if(r.length>1&&i.trim()===i)if(2===r.length&&""===r[0]&&r[1].length<32)n=r[1];else{var o=r[r.length-2].slice(-1);" "===Object(s.a)(o)&&i.length<32&&(n=i)}return n},e}(),At=function(e,t){t.ir.element.querySelectorAll(".vditor-ir__node--expand").forEach((function(e){e.classList.remove("vditor-ir__node--expand")}));var n=Object(w.g)(e.startContainer,"vditor-ir__node");n&&(n.classList.add("vditor-ir__node--expand"),n.classList.remove("vditor-ir__node--hidden"));var r=function(e){var t=e.startContainer;if(3===t.nodeType&&t.nodeValue.length!==e.startOffset)return!1;for(var n=t.nextSibling;n&&""===n.textContent;)n=n.nextSibling;if(!n){var r=Object(w.e)(t,"vditor-ir__marker");if(r&&!r.nextSibling){var i=t.parentElement.parentElement.nextSibling;if(i&&3!==i.nodeType&&i.classList.contains("vditor-ir__node"))return i}return!1}return!(!n||3===n.nodeType||!n.classList.contains("vditor-ir__node")||n.getAttribute("data-block"))&&n}(e);if(r)return r.classList.add("vditor-ir__node--expand"),void r.classList.remove("vditor-ir__node--hidden");var i=function(e){var t=e.startContainer,n=t.previousSibling;return!(3!==t.nodeType||0!==e.startOffset||!n||3===n.nodeType||!n.classList.contains("vditor-ir__node")||n.getAttribute("data-block"))&&n}(e);return i?(i.classList.add("vditor-ir__node--expand"),void i.classList.remove("vditor-ir__node--hidden")):void 0},Nt=function(){function e(e){this.composingLock=!1;var t=document.createElement("div");t.className="vditor-ir",t.innerHTML='<pre class="vditor-reset" placeholder="'+e.options.placeholder+'"\n contenteditable="true" spellcheck="false"></pre>',this.element=t.firstElementChild,this.bindEvent(e),document.execCommand("DefaultParagraphSeparator",!1,"p"),Ne(e,this.element),He(e,this.element),Re(e,this.element),Ie(e,this.element)}return e.prototype.bindEvent=function(e){var t=this;this.element.addEventListener("copy",(function(t){var n=getSelection().getRangeAt(0);if(""!==n.toString()){t.stopPropagation(),t.preventDefault();var r=document.createElement("div");r.appendChild(n.cloneContents()),t.clipboardData.setData("text/plain",e.lute.VditorIRDOM2Md(r.innerHTML).trim()),t.clipboardData.setData("text/html","")}})),this.element.addEventListener("paste",(function(t){kt(e,t,{pasteCode:function(e){document.execCommand("insertHTML",!1,e)}})})),(e.options.upload.url||e.options.upload.handler)&&this.element.addEventListener("drop",(function(t){if("Files"===t.dataTransfer.types[0]){var n=t.dataTransfer.items;n.length>0&&F(e,n),t.preventDefault()}})),this.element.addEventListener("compositionstart",(function(e){t.composingLock=!0})),this.element.addEventListener("compositionend",(function(t){D(e,getSelection().getRangeAt(0).cloneRange())})),this.element.addEventListener("input",(function(n){t.preventInput?t.preventInput=!1:t.composingLock||D(e,getSelection().getRangeAt(0).cloneRange())})),this.element.addEventListener("click",(function(n){if("INPUT"===n.target.tagName)return n.target.checked?n.target.setAttribute("checked","checked"):n.target.removeAttribute("checked"),t.preventInput=!0,void Tt(e);var r=Object(M.b)(t.element);if(n.target.isEqualNode(t.element)&&t.element.lastElementChild&&r.collapsed){var i=t.element.lastElementChild.getBoundingClientRect();if(n.y>i.top+i.height)return void("P"===t.element.lastElementChild.tagName?(r.selectNodeContents(t.element.lastElementChild),r.collapse(!1)):(t.element.insertAdjacentHTML("beforeend",'<p data-block="0">'+a.a.ZWSP+"<wbr></p>"),Object(M.f)(t.element,r)))}var o=Object(w.e)(n.target,"vditor-ir__preview");o||(o=Object(w.e)(r.startContainer,"vditor-ir__preview")),o&&(o.previousElementSibling.firstElementChild?r.selectNodeContents(o.previousElementSibling.firstElementChild):r.selectNodeContents(o.previousElementSibling),r.collapse(!0),Object(M.h)(r),De(e)),At(r,e),S(e)})),this.element.addEventListener("keyup",(function(n){if(!n.isComposing&&!Object(u.d)(n))if("Enter"===n.key&&De(e),S(e),"Backspace"!==n.key&&"Delete"!==n.key||""===e.ir.element.innerHTML||1!==e.ir.element.childNodes.length||!e.ir.element.firstElementChild||"P"!==e.ir.element.firstElementChild.tagName||0!==e.ir.element.firstElementChild.childElementCount||""!==e.ir.element.textContent&&"\n"!==e.ir.element.textContent){var r=Object(M.b)(t.element);"Backspace"===n.key?(Object(u.e)()&&"\n"===r.startContainer.textContent&&1===r.startOffset&&(r.startContainer.textContent="",At(r,e)),t.element.querySelectorAll(".language-math").forEach((function(e){var t=e.querySelector("br");t&&t.remove()}))):n.key.indexOf("Arrow")>-1?("ArrowLeft"!==n.key&&"ArrowRight"!==n.key||xt(e),At(r,e)):229===n.keyCode&&""===n.code&&"Unidentified"===n.key&&At(r,e);var i=Object(w.e)(r.startContainer,"vditor-ir__preview");if(i){if("ArrowUp"===n.key||"ArrowLeft"===n.key)return i.previousElementSibling.firstElementChild?r.selectNodeContents(i.previousElementSibling.firstElementChild):r.selectNodeContents(i.previousElementSibling),r.collapse(!1),n.preventDefault(),!0;if("SPAN"===i.tagName&&("ArrowDown"===n.key||"ArrowRight"===n.key))return r.selectNodeContents(i.parentElement.lastElementChild),r.collapse(!1),n.preventDefault(),!0}}else e.ir.element.innerHTML=""}))},e}(),Ht=function(e){return"sv"===e.currentMode?e.lute.Md2HTML(l(e)):"wysiwyg"===e.currentMode?e.lute.VditorDOM2HTML(e.wysiwyg.element.innerHTML):"ir"===e.currentMode?e.lute.VditorIRDOM2HTML(e.ir.element.innerHTML):void 0},Dt=n(22),Rt=n(21),It=function(){function e(e){this.element=document.createElement("div"),this.element.className="vditor-outline",this.element.innerHTML='<div class="vditor-outline__title">'+e+'</div>\n<div class="vditor-outline__content"></div>'}return e.prototype.render=function(e){"block"===this.element.style.display&&("block"===e.preview.element.style.display?Object(Rt.a)(e.preview.element.lastElementChild,this.element.lastElementChild,e):Object(Rt.a)(e[e.currentMode].element,this.element.lastElementChild,e))},e.prototype.toggle=function(e,t){var n;void 0===t&&(t=!0);var r=null===(n=e.toolbar.elements.outline)||void 0===n?void 0:n.firstElementChild;t&&window.innerWidth>=a.a.MOBILE_WIDTH?(this.element.style.display="block",this.render(e),null==r||r.classList.add("vditor-menu--current")):(this.element.style.display="none",null==r||r.classList.remove("vditor-menu--current")),ge(e)},e}(),zt=n(39),Pt=n.n(zt),Bt=n(18),qt=function(){function e(e){var t=this;this.element=document.createElement("div"),this.element.className="vditor-preview";var n=document.createElement("div");n.className="vditor-reset",e.options.classes.preview&&n.classList.add(e.options.classes.preview),n.style.maxWidth=e.options.preview.maxWidth+"px",n.addEventListener("copy",(function(n){var r=document.createElement("div");r.className="vditor-reset",r.appendChild(getSelection().getRangeAt(0).cloneContents()),t.copyToWechat(e,r),n.preventDefault()}));var r=document.createElement("div");r.className="vditor-preview__action",r.innerHTML='<button class="vditor-preview__action--current" data-type="desktop">Desktop</button>\n<button data-type="tablet">Tablet</button>\n<button data-type="mobile">Mobile/Wechat</button>\n<button data-type="mp-wechat" class="vditor-tooltipped vditor-tooltipped__w" aria-label="复制到公众号">'+Pt.a+"</button>",this.element.appendChild(r),this.element.appendChild(n),r.addEventListener(Object(u.b)(),(function(i){var o=Object(E.b)(i.target,"BUTTON");if(o){var a=o.getAttribute("data-type");a!==r.querySelector(".vditor-preview__action--current").getAttribute("data-type")&&("mp-wechat"!==a?(n.style.width="desktop"===a?"auto":"tablet"===a?"780px":"360px",n.scrollWidth>n.parentElement.clientWidth&&(n.style.width="auto"),t.render(e),r.querySelectorAll("button").forEach((function(e){e.classList.remove("vditor-preview__action--current")})),o.classList.add("vditor-preview__action--current")):t.copyToWechat(e,t.element.lastElementChild.cloneNode(!0)))}}))}return e.prototype.render=function(e,t){var n=this;if(clearTimeout(this.mdTimeoutId),"none"!==this.element.style.display)if(t)this.element.lastElementChild.innerHTML=t;else if(""!==l(e).replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")){var r=(new Date).getTime(),i=l(e);this.mdTimeoutId=window.setTimeout((function(){if(e.options.preview.url){var t=new XMLHttpRequest;t.open("POST",e.options.preview.url),t.setRequestHeader("Content-Type","application/json;charset=UTF-8"),t.onreadystatechange=function(){if(t.readyState===XMLHttpRequest.DONE)if(200===t.status){var o=JSON.parse(t.responseText);if(0!==o.code)return void e.tip.show(o.msg);e.options.preview.transform&&(o.data=e.options.preview.transform(o.data)),n.element.lastElementChild.innerHTML=o.data,n.afterRender(e,r)}else{var a=e.lute.Md2HTML(i);e.options.preview.transform&&(a=e.options.preview.transform(a)),n.element.lastElementChild.innerHTML=a,n.afterRender(e,r)}},t.send(JSON.stringify({markdownText:i}))}else{var o=e.lute.Md2HTML(i);e.options.preview.transform&&(o=e.options.preview.transform(o)),n.element.lastElementChild.innerHTML=o,n.afterRender(e,r)}}),e.options.preview.delay)}else this.element.lastElementChild.innerHTML="";else"renderPerformance"===this.element.getAttribute("data-type")&&e.tip.hide()},e.prototype.afterRender=function(e,t){e.options.preview.parse&&e.options.preview.parse(this.element);var n=(new Date).getTime()-t;(new Date).getTime()-t>2600?(e.tip.show(R.a[e.options.lang].performanceTip.replace("${x}",n.toString())),e.preview.element.setAttribute("data-type","renderPerformance")):"renderPerformance"===e.preview.element.getAttribute("data-type")&&(e.tip.hide(),e.preview.element.removeAttribute("data-type")),Object(T.a)(e.preview.element.lastElementChild,e.options.lang),Object(j.a)(e.options.preview.hljs,e.preview.element.lastElementChild,e.options.cdn),Object(_.a)(e.preview.element.lastElementChild,{cdn:e.options.cdn,math:e.options.preview.math}),Object(C.a)(e.preview.element.lastElementChild,".language-mermaid",e.options.cdn),Object(L.a)(e.preview.element.lastElementChild,e.options.cdn),Object(x.a)(e.preview.element.lastElementChild,e.options.cdn),Object(A.a)(e.preview.element.lastElementChild,e.options.cdn),Object(k.a)(e.preview.element.lastElementChild,e.options.cdn),Object(Bt.a)(e.preview.element.lastElementChild)},e.prototype.copyToWechat=function(e,t){t.querySelectorAll(".katex-html .base").forEach((function(e){e.style.display="initial"})),t.style.backgroundColor="#fff",t.querySelectorAll("code").forEach((function(e){e.style.backgroundImage="none"})),this.element.append(t);var n=t.ownerDocument.createRange();n.selectNode(t),Object(M.h)(n),document.execCommand("copy"),this.element.lastElementChild.remove(),e.tip.show("已复制，可到微信公众号平台进行粘贴")},e}(),Ut=n(40),Ft=n.n(Ut),Kt=function(){function e(e){this.element=document.createElement("div"),this.element.className="vditor-resize vditor-resize--"+e.options.resize.position,this.element.innerHTML="<div>"+Ft.a+"</div>",this.bindEvent(e)}return e.prototype.bindEvent=function(e){var t=this;this.element.addEventListener("mousedown",(function(n){var r=document,i=n.clientY,o=e.element.offsetHeight,a=63+e.element.querySelector(".vditor-toolbar").clientHeight;r.ondragstart=function(){return!1},window.captureEvents&&window.captureEvents(),t.element.classList.add("vditor-resize--selected"),r.onmousemove=function(t){"top"===e.options.resize.position?e.element.style.height=Math.max(a,o+(i-t.clientY))+"px":e.element.style.height=Math.max(a,o+(t.clientY-i))+"px",e.options.typewriterMode&&(e.sv.element.style.paddingBottom=e.sv.element.parentElement.offsetHeight/2+"px")},r.onmouseup=function(){e.options.resize.after&&e.options.resize.after(e.element.offsetHeight-o),window.captureEvents&&window.captureEvents(),r.onmousemove=null,r.onmouseup=null,r.ondragstart=null,r.onselectstart=null,r.onselect=null,t.element.classList.remove("vditor-resize--selected")}}))},e}(),Wt=function(){function e(e){this.element=document.createElement("pre"),this.element.className="vditor-textarea",this.element.setAttribute("placeholder",e.options.placeholder),this.element.setAttribute("contenteditable","true"),this.element.setAttribute("spellcheck","false"),this.bindEvent(e),Ne(e,this.element),Re(e,this.element),Ie(e,this.element)}return e.prototype.bindEvent=function(e){var t=this;this.element.addEventListener("copy",(function(e){e.stopPropagation(),e.preventDefault(),e.clipboardData.setData("text/plain",ue(t.element))})),this.element.addEventListener("keypress",(function(t){Object(u.d)(t)||"Enter"!==t.key||(P(e,"\n","",!0),De(e),t.preventDefault())})),this.element.addEventListener("input",(function(){I(e,{enableAddUndoStack:!0,enableHint:!0,enableInput:!0}),t.element.querySelectorAll("br").forEach((function(e){e.nextElementSibling||e.insertAdjacentHTML("afterend",'<span style="display: none">\n</span>')}))})),this.element.addEventListener("blur",(function(){e.options.blur&&e.options.blur(l(e))})),this.element.addEventListener("scroll",(function(){if("block"===e.preview.element.style.display){var n=t.element.scrollTop,r=t.element.clientHeight,i=t.element.scrollHeight-parseFloat(t.element.style.paddingBottom||"0"),o=e.preview.element;o.scrollTop=n/r>.5?(n+r)*o.scrollHeight/i-r:n*o.scrollHeight/i}})),(e.options.upload.url||e.options.upload.handler)&&this.element.addEventListener("drop",(function(t){if("Files"!==t.dataTransfer.types[0])return P(e,getSelection().toString(),"",!1),void t.preventDefault();var n=t.dataTransfer.items;0!==n.length&&(F(e,n),t.preventDefault())})),this.element.addEventListener("paste",(function(t){var n=t.clipboardData.getData("text/html"),r=t.clipboardData.getData("text/plain");if(t.stopPropagation(),t.preventDefault(),""!==n.trim()){if(n.replace(/<(|\/)(html|body|meta)[^>]*?>/gi,"").trim()!=='<a href="'+r+'">'+r+"</a>"&&n.replace(/<(|\/)(html|body|meta)[^>]*?>/gi,"").trim()!=='\x3c!--StartFragment--\x3e<a href="'+r+'">'+r+"</a>\x3c!--EndFragment--\x3e"){var i=document.createElement("div");i.innerHTML=n,i.querySelectorAll("[style]").forEach((function(e){e.removeAttribute("style")})),i.querySelectorAll(".vditor-copy").forEach((function(e){e.remove()})),i.querySelectorAll(".vditor-anchor").forEach((function(e){e.remove()}));var o=fe(e,i.innerHTML,r).trimRight();return void P(e,o,"",!0)}}else if(""!==r.trim()&&0===t.clipboardData.files.length);else if(t.clipboardData.files.length>0){if(!e.options.upload.url&&!e.options.upload.handler)return;return void F(e,t.clipboardData.files)}P(e,r,"",!0)}))},e}(),Vt=function(){function e(){this.element=document.createElement("div"),this.element.className="vditor-tip"}return e.prototype.show=function(e,t){var n=this;if(void 0===t&&(t=6e3),this.element.className="vditor-tip vditor-tip--show",0===t)return this.element.innerHTML='<div class="vditor-tip__content">'+e+'\n<div class="vditor-tip__close">X</div></div>',void this.element.querySelector(".vditor-tip__close").addEventListener("click",(function(){n.hide()}));this.element.innerHTML='<div class="vditor-tip__content">'+e+"</div>",setTimeout((function(){n.hide()}),t)},e.prototype.hide=function(){this.element.className="vditor-messageElementtip",this.element.innerHTML=""},e}(),Zt=function(e,t){if(t.options.preview.mode!==e){switch(t.options.preview.mode=e,e){case"both":t.sv.element.style.display="block",t.preview.element.style.display="block",t.preview.render(t),h(t.toolbar.elements,["both"]);break;case"editor":t.sv.element.style.display="block",t.preview.element.style.display="none",p(t.toolbar.elements,["both"])}t.devtools&&t.devtools.renderEchart(t)}},Jt=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Gt=function(e){function t(t,n){var r=e.call(this,t,n)||this;return"both"===t.options.preview.mode&&r.element.children[0].classList.add("vditor-menu--current"),r.element.children[0].addEventListener(Object(u.b)(),(function(e){r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||(e.preventDefault(),"sv"===t.currentMode&&("both"===t.options.preview.mode?Zt("editor",t):Zt("both",t)))})),r}return Jt(t,e),t}(ke),Xt=function(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__br"},Qt=n(19),$t=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Yt=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=r.element.children[0],o=document.createElement("div");o.className="vditor-hint"+(2===n.level?"":" vditor-panel--arrow");var s="";return a.a.CODE_THEME.forEach((function(e){s+="<button>"+e+"</button>"})),o.innerHTML='<div style="overflow: auto;max-height:'+window.innerHeight/2+'px">'+s+"</div>",o.addEventListener(Object(u.b)(),(function(e){"BUTTON"===e.target.tagName&&(b(t,["subToolbar"]),t.options.preview.hljs.style=e.target.textContent,Object(Qt.a)(e.target.textContent,t.options.cdn),e.preventDefault(),e.stopPropagation())})),r.element.appendChild(o),y(t,o,i,n.level),r}return $t(t,e),t}(ke),en=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),tn=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=r.element.children[0],o=document.createElement("div");o.className="vditor-hint"+(2===n.level?"":" vditor-panel--arrow");var s="";return a.a.CONTENT_THEME.forEach((function(e){s+="<button>"+e+"</button>"})),o.innerHTML='<div style="overflow: auto;max-height:'+window.innerHeight/2+'px">'+s+"</div>",o.addEventListener(Object(u.b)(),(function(e){"BUTTON"===e.target.tagName&&(b(t,["subToolbar"]),t.options.preview.markdown.theme=e.target.textContent,Object(me.a)(e.target.textContent,t.options.cdn),e.preventDefault(),e.stopPropagation())})),r.element.appendChild(o),y(t,o,i,n.level),r}return en(t,e),t}(ke),nn=function(){function e(e){this.element=document.createElement("span"),this.element.className="vditor-counter vditor-tooltipped vditor-tooltipped__nw",this.render(e,"")}return e.prototype.render=function(e,t){var n=t.endsWith("\n")?t.length-1:t.length;if("text"===e.options.counter.type&&e[e.currentMode]){var r=e[e.currentMode].element.cloneNode(!0);r.querySelectorAll(".vditor-wysiwyg__preview").forEach((function(e){e.remove()})),n=r.textContent.length}"number"==typeof e.options.counter.max?(n>e.options.counter.max?this.element.className="vditor-counter vditor-counter--error":this.element.className="vditor-counter",this.element.innerHTML=n+"/"+e.options.counter.max):this.element.innerHTML=""+n,this.element.setAttribute("aria-label",e.options.counter.type)},e}(),rn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),on=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].innerHTML=n.icon,r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),n.click()})),r}return rn(t,e),t}(ke),an=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),sn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.firstElementChild.addEventListener(Object(u.b)(),(function(e){var n=r.element.firstElementChild;n.classList.contains(a.a.CLASS_MENU_DISABLED)||(e.preventDefault(),n.classList.contains("vditor-menu--current")?(n.classList.remove("vditor-menu--current"),t.devtools.element.style.display="none",ge(t)):(n.classList.add("vditor-menu--current"),t.devtools.element.style.display="block",ge(t),t.devtools.renderEchart(t)))})),r}return an(t,e),t}(ke),ln=function(){this.element=document.createElement("div"),this.element.className="vditor-toolbar__divider"},cn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),dn=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=document.createElement("div");i.className="vditor-panel vditor-panel--arrow";var o="";Object.keys(t.options.hint.emoji).forEach((function(e){var n=t.options.hint.emoji[e];n.indexOf(".")>-1?o+='<button data-value=":'+e+': " data-key=":'+e+':"><img\ndata-value=":'+e+': " data-key=":'+e+':" class="vditor-emojis__icon" src="'+n+'"/></button>':o+='<button data-value="'+n+' "\n data-key="'+e+'"><span class="vditor-emojis__icon">'+n+"</span></button>"}));var a='<div class="vditor-emojis__tail">\n    <span class="vditor-emojis__tip"></span><span>'+(t.options.hint.emojiTail||"")+"</span>\n</div>";return i.innerHTML='<div class="vditor-emojis" style="max-height: '+("auto"===t.options.height?"auto":t.options.height-80)+'px">'+o+"</div>"+a,r.element.appendChild(i),y(t,i,r.element.children[0],n.level),r._bindEvent(t,i),r}return cn(t,e),t.prototype._bindEvent=function(e,t){t.querySelectorAll(".vditor-emojis button").forEach((function(n){n.addEventListener(Object(u.b)(),(function(r){r.preventDefault();var i=n.getAttribute("data-value");if("sv"===e.currentMode)P(e,i,"",!0);else{var o=void 0,a="";if("wysiwyg"===e.currentMode?(o=Object(M.b)(e.wysiwyg.element),a=e.lute.SpinVditorDOM(i)):"ir"===e.currentMode&&(o=Object(M.b)(e.ir.element),a=e.lute.SpinVditorIRDOM(i)),i.indexOf(":")>-1){var s=document.createElement("div");s.innerHTML=a,a=s.firstElementChild.firstElementChild.outerHTML+" ",Object(M.d)(a,e)}else o.insertNode(document.createTextNode(i));o.collapse(!1),Object(M.h)(o)}t.style.display="none"})),n.addEventListener("mouseover",(function(e){"BUTTON"===e.target.tagName&&(t.querySelector(".vditor-emojis__tip").innerHTML=e.target.getAttribute("data-key"))}))}))},t}(ke),un=function(e,t,n){var r=document.createElement("a");"download"in r?(r.download=n,r.style.display="none",r.href=URL.createObjectURL(new Blob([t])),document.body.appendChild(r),r.click(),r.remove()):e.tip.show(R.a[e.options.lang].downloadTip,0)},pn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),hn=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=r.element.children[0],o=document.createElement("div");return o.className="vditor-hint"+(2===n.level?"":" vditor-panel--arrow"),o.innerHTML='<button data-type="markdown">Markdown</button>\n<button data-type="pdf">PDF</button>\n<button data-type="html">HTML</button>',o.addEventListener(Object(u.b)(),(function(e){var n=e.target;if("BUTTON"===n.tagName){switch(n.getAttribute("data-type")){case"markdown":!function(e){var t=l(e);un(e,t,t.substr(0,10)+".md")}(t);break;case"pdf":!function(e){e.tip.show(R.a[e.options.lang].generate,3800);var t=document.querySelector("iframe");t.contentDocument.open(),t.contentDocument.write('<link rel="stylesheet" href="'+e.options.cdn+'/dist/index.css"/>\n<script src="'+e.options.cdn+'/dist/method.min.js"><\/script>\n<div id="preview"></div>\n<script>\nwindow.addEventListener("message", (e) => {\n  if(!e.data) {\n    return;\n  }\n  Vditor.preview(document.getElementById(\'preview\'), e.data, {\n    markdown: {\n      theme: "'+e.options.preview.markdown.theme+'"\n    },\n    hljs: {\n      style: "'+e.options.preview.hljs.style+'"\n    }\n  });\n  setTimeout(() => {\n        window.print();\n    }, 3600);\n}, false);\n<\/script>'),t.contentDocument.close(),setTimeout((function(){t.contentWindow.postMessage(l(e),"*")}),200)}(t);break;case"html":!function(e){var t=Ht(e),n='<html><head><link rel="stylesheet" type="text/css" href="'+e.options.cdn+'/dist/index.css"/>\n<script src="'+e.options.cdn+'/dist/method.min.js"><\/script></head>\n<body><div class="vditor-reset" id="preview">'+t+"</div>\n<script>\n    const previewElement = document.getElementById('preview')\n    Vditor.setContentTheme('"+e.options.preview.markdown.theme+"', '"+e.options.cdn+"');\n    Vditor.codeRender(previewElement, '"+e.options.lang+"');\n    Vditor.highlightRender("+JSON.stringify(e.options.preview.hljs)+", previewElement, '"+e.options.cdn+"');\n    Vditor.mathRender(previewElement, {\n        cdn: '"+e.options.cdn+"',\n        math: "+JSON.stringify(e.options.preview.math)+',\n    });\n    Vditor.mermaidRender(previewElement, ".language-mermaid", \''+e.options.cdn+"');\n    Vditor.graphvizRender(previewElement, '"+e.options.cdn+"');\n    Vditor.chartRender(previewElement, '"+e.options.cdn+"');\n    Vditor.mindmapRender(previewElement, '"+e.options.cdn+"');\n    Vditor.abcRender(previewElement, '"+e.options.cdn+"');\n    Vditor.mediaRender(previewElement);\n<\/script></body></html>";un(e,n,t.substr(0,10)+".html")}(t)}b(t,["subToolbar"]),e.preventDefault(),e.stopPropagation()}})),r.element.appendChild(o),y(t,o,i,n.level),r}return pn(t,e),t}(ke),fn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),mn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),z(t,t.lute.FormatMd(l(t)),Object(M.c)(t.sv.element,getSelection().getRangeAt(0)))})),r}return fn(t,e),t}(ke),vn=n(41),gn=n.n(vn),bn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),yn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r._bindEvent(t,n),r}return bn(t,e),t.prototype._bindEvent=function(e,t){this.element.children[0].addEventListener(Object(u.b)(),(function(n){n.preventDefault(),e.element.className.includes("vditor--fullscreen")?(t.level||(this.innerHTML=t.icon),e.element.classList.remove("vditor--fullscreen"),Object.keys(e.toolbar.elements).forEach((function(t){var n=e.toolbar.elements[t].firstChild;n&&(n.className=n.className.replace("__s","__n"))})),e.counter&&(e.counter.element.className=e.counter.element.className.replace("__s","__n"))):(t.level||(this.innerHTML=gn.a),e.element.classList.add("vditor--fullscreen"),Object.keys(e.toolbar.elements).forEach((function(t){var n=e.toolbar.elements[t].firstChild;n&&(n.className=n.className.replace("__n","__s"))})),e.counter&&(e.counter.element.className=e.counter.element.className.replace("__n","__s"))),e.devtools&&e.devtools.renderEchart(e),t.click&&t.click(e.element.classList.contains("vditor--fullscreen")),ge(e),be(e)}))},t}(ke),wn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),En=function(e){function t(t,n){var r=e.call(this,t,n)||this,i=document.createElement("div");return i.className="vditor-hint vditor-panel--arrow",i.innerHTML='<button data-tag="h1" data-value="# ">Heading 1 '+Object(u.g)("&lt;⌘-⌥-1>")+'</button>\n<button data-tag="h2" data-value="## ">Heading 2 &lt;'+Object(u.g)("⌘-⌥-2")+'></button>\n<button data-tag="h3" data-value="### ">Heading 3 &lt;'+Object(u.g)("⌘-⌥-3")+'></button>\n<button data-tag="h4" data-value="#### ">Heading 4 &lt;'+Object(u.g)("⌘-⌥-4")+'></button>\n<button data-tag="h5" data-value="##### ">Heading 5 &lt;'+Object(u.g)("⌘-⌥-5")+'></button>\n<button data-tag="h6" data-value="###### ">Heading 6 &lt;'+Object(u.g)("⌘-⌥-6")+"></button>",r.element.appendChild(i),r._bindEvent(t,i),r}return wn(t,e),t.prototype._bindEvent=function(e,t){var n=this.element.children[0];n.addEventListener(Object(u.b)(),(function(r){r.preventDefault(),n.classList.contains(a.a.CLASS_MENU_DISABLED)||(n.blur(),"wysiwyg"===e.currentMode&&n.classList.contains("vditor-menu--current")?(_e(e),K(e),n.classList.remove("vditor-menu--current")):"ir"===e.currentMode&&n.classList.contains("vditor-menu--current")?(Lt(e,""),n.classList.remove("vditor-menu--current")):"block"===t.style.display?t.style.display="none":(b(e,["subToolbar"]),t.style.display="block"))}));for(var r=0;r<6;r++)t.children.item(r).addEventListener(Object(u.b)(),(function(r){r.preventDefault(),"wysiwyg"===e.currentMode?(je(e,r.target.getAttribute("data-tag")),K(e),n.classList.add("vditor-menu--current")):"ir"===e.currentMode?(Lt(e,r.target.getAttribute("data-value")),n.classList.add("vditor-menu--current")):P(e,r.target.getAttribute("data-value"),"",!1,!0),t.style.display="none"}))},t}(ke),Mn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Sn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),t.tip.show('<div style="margin-bottom:14px;font-size: 14px;line-height: 22px;min-width:300px;max-width: 360px;display: flex;">\n<div style="margin-top: 14px;flex: 1">\n    <div>Markdown 使用指南</div>\n    <ul style="list-style: none">\n        <li><a href="https://hacpai.com/article/1583308420519" target="_blank">语法速查手册</a></li>\n        <li><a href="https://hacpai.com/article/1583129520165" target="_blank">基础语法</a></li>\n        <li><a href="https://hacpai.com/article/1583305480675" target="_blank">扩展语法</a></li>\n        <li><a href="https://hacpai.com/article/1582778815353" target="_blank">键盘快捷键</a></li>\n    </ul>\n</div>\n<div style="margin-top: 14px;flex: 1">\n    <div>Vditor 支持</div>\n    <ul style="list-style: none">\n        <li><a href="https://github.com/Vanessa219/vditor/issues" target="_blank">Issues</a></li>\n        <li><a href="https://hacpai.com/tag/vditor" target="_blank">官方讨论区</a></li>\n        <li><a href="https://hacpai.com/article/1549638745630" target="_blank">开发手册</a></li>\n        <li><a href="https://hacpai.com/guide/markdown" target="_blank">演示地址</a></li>\n    </ul>\n</div></div>',0)})),r}return Mn(t,e),t}(ke),On=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),kn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){if(e.preventDefault(),!r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)&&"sv"!==t.currentMode){var n=Object(M.b)(t[t.currentMode].element),i=Object(w.f)(n.startContainer,"LI");i&&rt(t,i,n)}})),r}return On(t,e),t}(ke),xn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Tn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),t.tip.show('<div style="max-width: 520px; font-size: 14px;line-height: 22px;margin-bottom: 14px;">\n<p style="text-align: center;margin: 14px 0">\n    <em>下一代的 Markdown 编辑器，为未来而构建</em>\n</p>\n<div style="display: flex;margin-bottom: 14px;flex-wrap: wrap;align-items: center">\n    <img src="https://cdn.jsdelivr.net/npm/vditor/src/assets/images/logo.png" style="margin: 0 auto;height: 68px"/>\n    <div>&nbsp;&nbsp;</div>\n    <div style="flex: 1;min-width: 250px">\n        Vditor 是一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。\n        它使用 TypeScript 实现，支持原生 JavaScript、Vue、React、Angular，提供<a target="_blank" href="https://github.com/88250/liandi">桌面版</a>。\n    </div>\n</div>\n<div style="display: flex;flex-wrap: wrap;">\n    <ul style="list-style: none;flex: 1;min-width:148px">\n        <li>\n        项目地址：<a href="https://vditor.b3log.org" target="_blank">vditor.b3log.org</a>\n        </li>\n        <li>\n        开源协议：MIT\n        </li>\n    </ul>\n    <ul style="list-style: none;margin-right: 18px">\n        <li>\n        组件版本：Vditor v'+a.b+" / Lute v"+Lute.Version+'\n        </li>\n        <li>\n        赞助捐赠：<a href="https://hacpai.com/sponsor" target="_blank">https://hacpai.com/sponsor</a>\n        </li>\n    </ul>\n</div>\n</div>',0)})),r}return xn(t,e),t}(ke),Ln=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),jn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||"sv"===t.currentMode||Xe(t,"afterend")})),r}return Ln(t,e),t}(ke),_n=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Cn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||"sv"===t.currentMode||Xe(t,"beforebegin")})),r}return _n(t,e),t}(ke),An=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Nn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.element.children[0].addEventListener(Object(u.b)(),(function(e){if(e.preventDefault(),!r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)&&"sv"!==t.currentMode){var n=Object(M.b)(t[t.currentMode].element),i=Object(w.f)(n.startContainer,"LI");i&&it(t,i,n,i.parentElement)}})),r}return An(t,e),t}(ke),Hn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Dn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return t.options.outline&&r.element.firstElementChild.classList.add("vditor-menu--current"),r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),t.toolbar.elements.outline.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||(t.options.outline=!r.element.firstElementChild.classList.contains("vditor-menu--current"),t.outline.toggle(t,t.options.outline))})),r}return Hn(t,e),t}(ke),Rn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),In=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r._bindEvent(t),r}return Rn(t,e),t.prototype._bindEvent=function(e){var t=this;this.element.children[0].addEventListener(Object(u.b)(),(function(n){n.preventDefault();var r=t.element.firstElementChild;if(!r.classList.contains(a.a.CLASS_MENU_DISABLED)){var i=a.a.EDIT_TOOLBARS.concat(["both","format","edit-mode","devtools"]);r.classList.contains("vditor-menu--current")?(r.classList.remove("vditor-menu--current"),"sv"===e.currentMode?(e.sv.element.style.display="block","both"===e.options.preview.mode?e.preview.element.style.display="block":e.preview.element.style.display="none"):(e[e.currentMode].element.parentElement.style.display="block",e.preview.element.style.display="none"),f(e.toolbar.elements,i),e.outline.render(e)):(m(e.toolbar.elements,i),e.preview.element.style.display="block","sv"===e.currentMode?e.sv.element.style.display="none":e[e.currentMode].element.parentElement.style.display="none",e.preview.render(e),r.classList.add("vditor-menu--current"),b(e,["subToolbar","hint","popover"]),setTimeout((function(){e.outline.render(e)}),e.options.preview.delay+10)),ge(e)}}))},t}(ke),zn=function(){function e(e){var t;if(this.SAMPLE_RATE=5e3,this.isRecording=!1,this.readyFlag=!1,this.leftChannel=[],this.rightChannel=[],this.recordingLength=0,"undefined"!=typeof AudioContext)t=new AudioContext;else{if(!webkitAudioContext)return;t=new webkitAudioContext}this.DEFAULT_SAMPLE_RATE=t.sampleRate;var n=t.createGain();t.createMediaStreamSource(e).connect(n),this.recorder=t.createScriptProcessor(2048,2,1),this.recorder.onaudioprocess=null,n.connect(this.recorder),this.recorder.connect(t.destination),this.readyFlag=!0}return e.prototype.cloneChannelData=function(e,t){this.leftChannel.push(new Float32Array(e)),this.rightChannel.push(new Float32Array(t)),this.recordingLength+=2048},e.prototype.startRecordingNewWavFile=function(){this.readyFlag&&(this.isRecording=!0,this.leftChannel.length=this.rightChannel.length=0,this.recordingLength=0)},e.prototype.stopRecording=function(){this.isRecording=!1},e.prototype.buildWavFileBlob=function(){for(var e=this.mergeBuffers(this.leftChannel),t=this.mergeBuffers(this.rightChannel),n=new Float32Array(e.length),r=0;r<e.length;++r)n[r]=.5*(e[r]+t[r]);this.DEFAULT_SAMPLE_RATE>this.SAMPLE_RATE&&(n=this.downSampleBuffer(n,this.SAMPLE_RATE));var i=44+2*n.length,o=new ArrayBuffer(i),a=new DataView(o);this.writeUTFBytes(a,0,"RIFF"),a.setUint32(4,i,!0),this.writeUTFBytes(a,8,"WAVE"),this.writeUTFBytes(a,12,"fmt "),a.setUint32(16,16,!0),a.setUint16(20,1,!0),a.setUint16(22,1,!0),a.setUint32(24,this.SAMPLE_RATE,!0),a.setUint32(28,2*this.SAMPLE_RATE,!0),a.setUint16(32,2,!0),a.setUint16(34,16,!0);var s=2*n.length;this.writeUTFBytes(a,36,"data"),a.setUint32(40,s,!0);for(var l=n.length,c=44,d=0;d<l;d++)a.setInt16(c,32767*n[d],!0),c+=2;return new Blob([a],{type:"audio/wav"})},e.prototype.downSampleBuffer=function(e,t){if(t===this.DEFAULT_SAMPLE_RATE)return e;if(t>this.DEFAULT_SAMPLE_RATE)return e;for(var n=this.DEFAULT_SAMPLE_RATE/t,r=Math.round(e.length/n),i=new Float32Array(r),o=0,a=0;o<i.length;){for(var s=Math.round((o+1)*n),l=0,c=0,d=a;d<s&&d<e.length;d++)l+=e[d],c++;i[o]=l/c,o++,a=s}return i},e.prototype.mergeBuffers=function(e){for(var t=new Float32Array(this.recordingLength),n=0,r=e.length,i=0;i<r;++i){var o=e[i];t.set(o,n),n+=o.length}return t},e.prototype.writeUTFBytes=function(e,t,n){for(var r=n.length,i=0;i<r;i++)e.setUint8(t+i,n.charCodeAt(i))},e}(),Pn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Bn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r._bindEvent(t),r}return Pn(t,e),t.prototype._bindEvent=function(e){var t,n=this;this.element.children[0].addEventListener(Object(u.b)(),(function(r){if(r.preventDefault(),!n.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)){var i="wysiwyg"===e.currentMode?e.wysiwyg.element:e.sv.element;if(t)if(t.isRecording){t.stopRecording(),e.tip.hide();var o=new File([t.buildWavFileBlob()],"record"+(new Date).getTime()+".wav",{type:"video/webm"});F(e,[o]),n.element.children[0].classList.remove("vditor-menu--current")}else e.tip.show(R.a[e.options.lang].recording),i.setAttribute("contenteditable","false"),t.startRecordingNewWavFile(),n.element.children[0].classList.add("vditor-menu--current");else navigator.mediaDevices.getUserMedia({audio:!0}).then((function(r){(t=new zn(r)).recorder.onaudioprocess=function(e){if(t.isRecording){var n=e.inputBuffer.getChannelData(0),r=e.inputBuffer.getChannelData(1);t.cloneChannelData(n,r)}},t.startRecordingNewWavFile(),e.tip.show(R.a[e.options.lang].recording),i.setAttribute("contenteditable","false"),n.element.children[0].classList.add("vditor-menu--current")})).catch((function(){e.tip.show(R.a[e.options.lang]["record-tip"])}))}}))},t}(ke),qn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Un=function(e){function t(t,n){var r=e.call(this,t,n)||this;return m({redo:r.element},["redo"]),r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||("sv"===t.currentMode?t.undo.redo(t):"wysiwyg"===t.currentMode?t.wysiwygUndo.redo(t):"ir"===t.currentMode&&t.irUndo.redo(t))})),r}return qn(t,e),t}(ke),Fn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Kn=function(e){function t(t,n){var r=e.call(this,t,n)||this;return m({undo:r.element},["undo"]),r.element.children[0].addEventListener(Object(u.b)(),(function(e){e.preventDefault(),r.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||("sv"===t.currentMode?t.undo.undo(t):"wysiwyg"===t.currentMode?t.wysiwygUndo.undo(t):"ir"===t.currentMode&&t.irUndo.undo(t))})),r}return Fn(t,e),t}(ke),Wn=n(28),Vn=n.n(Wn),Zn=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Jn=function(e){function t(t,n){var r=e.call(this,t,n)||this,i='<input multiple="multiple" type="file">';return t.options.upload.accept&&(i='<input multiple="multiple" type="file" accept="'+t.options.upload.accept+'">'),r.element.children[0].innerHTML=""+(n.icon||Vn.a)+i,r._bindEvent(t),r}return Zn(t,e),t.prototype._bindEvent=function(e){var t=this;this.element.querySelector("input").addEventListener("change",(function(n){t.element.firstElementChild.classList.contains(a.a.CLASS_MENU_DISABLED)||0!==n.target.files.length&&F(e,n.target.files,n.target)}))},t}(ke),Gn=function(){function e(e){var t=this,n=e.options;this.elements={},this.element=document.createElement("div"),this.element.className="vditor-toolbar",n.toolbar.forEach((function(n,r){var i=t.genItem(e,n,r);if(t.element.appendChild(i),n.toolbar){var o=document.createElement("div");o.className="vditor-hint vditor-panel--arrow",o.addEventListener(Object(u.b)(),(function(e){o.style.display="none"})),n.toolbar.forEach((function(n,i){n.level=2,o.appendChild(t.genItem(e,n,r+i))})),i.appendChild(o),y(e,o,i.children[0])}})),e.options.toolbarConfig.hide&&this.element.classList.add("vditor-toolbar--hide"),e.options.toolbarConfig.pin&&this.element.classList.add("vditor-toolbar--pin"),e.options.counter.enable&&(e.counter=new nn(e),this.element.appendChild(e.counter.element))}return e.prototype.genItem=function(e,t,n){var r;switch(t.name){case"bold":case"italic":case"more":case"strike":case"line":case"quote":case"list":case"ordered-list":case"check":case"code":case"inline-code":case"link":case"table":r=new ke(e,t);break;case"emoji":r=new dn(e,t);break;case"headings":r=new En(e,t);break;case"|":r=new ln;break;case"br":r=new Xt;break;case"undo":r=new Kn(e,t);break;case"redo":r=new Un(e,t);break;case"help":r=new Sn(e,t);break;case"both":r=new Gt(e,t);break;case"preview":r=new In(e,t);break;case"fullscreen":r=new yn(e,t);break;case"upload":r=new Jn(e,t);break;case"record":r=new Bn(e,t);break;case"info":r=new Tn(e,t);break;case"format":r=new mn(e,t);break;case"edit-mode":r=new Le(e,t);break;case"devtools":r=new sn(e,t);break;case"outdent":r=new Nn(e,t);break;case"indent":r=new kn(e,t);break;case"outline":r=new Dn(e,t);break;case"insert-after":r=new jn(e,t);break;case"insert-before":r=new Cn(e,t);break;case"code-theme":r=new Yt(e,t);break;case"content-theme":r=new tn(e,t);break;case"export":r=new hn(e,t);break;default:r=new on(e,t)}if(r){var i=t.name;return"br"!==i&&"|"!==i||(i+=n),this.elements[i]=r.element,r.element}},e}(),Xn=n(24),Qn=n.n(Xn),$n=function(){function e(){this.stackSize=50,this.redoStack=[],this.undoStack=[],this.dmp=new Qn.a,this.lastText="",this.hasUndo=!1}return e.prototype.clearStack=function(e){this.redoStack=[],this.undoStack=[],this.lastText="",this.hasUndo=!1,this.resetIcon(e)},e.prototype.resetIcon=function(e){e.toolbar&&(this.undoStack.length>1?f(e.toolbar.elements,["undo"]):m(e.toolbar.elements,["undo"]),0!==this.redoStack.length?f(e.toolbar.elements,["redo"]):m(e.toolbar.elements,["redo"]))},e.prototype.recordFirstPosition=function(e){1===this.undoStack.length&&(this.undoStack[0].end=Object(M.c)(e.sv.element).end)},e.prototype.undo=function(e){if("false"!==e.sv.element.getAttribute("contenteditable")&&!(this.undoStack.length<2)){var t=this.undoStack.pop();t&&t.patchList&&(this.redoStack.push(t),this.renderDiff(t,e),this.hasUndo=!0)}},e.prototype.redo=function(e){if("false"!==e.sv.element.getAttribute("contenteditable")){var t=this.redoStack.pop();t&&t.patchList&&(this.undoStack.push(t),this.renderDiff(t,e,!0))}},e.prototype.addToUndoStack=function(e){var t=this;clearTimeout(this.timeout),this.timeout=window.setTimeout((function(){var n=l(e),r=t.dmp.diff_main(n,t.lastText,!0),i=t.dmp.patch_make(n,t.lastText,r);0!==i.length&&(t.lastText=n,t.undoStack.push({patchList:i,end:Object(M.c)(e.sv.element).end}),t.undoStack.length>t.stackSize&&t.undoStack.shift(),t.hasUndo&&(t.redoStack=[],t.hasUndo=!1,m(e.toolbar.elements,["redo"])),t.undoStack.length>1&&f(e.toolbar.elements,["undo"]))}),500)},e.prototype.renderDiff=function(e,t,n){var r,i;if(void 0===n&&(n=!1),n){var o=this.dmp.patch_deepCopy(e.patchList).reverse();o.forEach((function(e){e.diffs.forEach((function(e){e[0]=-e[0]}))})),r=this.dmp.patch_apply(o,this.lastText)[0],i={end:e.end,start:e.end}}else r=this.dmp.patch_apply(e.patchList,this.lastText)[0],this.undoStack[this.undoStack.length-1]&&(i={end:this.undoStack[this.undoStack.length-1].end,start:this.undoStack[this.undoStack.length-1].end});this.lastText=r,z(t,r,i,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),De(t),this.undoStack.length>1?f(t.toolbar.elements,["undo"]):m(t.toolbar.elements,["undo"]),0!==this.redoStack.length?f(t.toolbar.elements,["redo"]):m(t.toolbar.elements,["redo"])},e}(),Yn=function(){function e(){this.stackSize=50,this.redoStack=[],this.undoStack=[],this.dmp=new Qn.a,this.lastText="",this.hasUndo=!1}return e.prototype.clearStack=function(e){this.redoStack=[],this.undoStack=[],this.lastText="",this.hasUndo=!1,this.resetIcon(e)},e.prototype.resetIcon=function(e){e.toolbar&&(this.undoStack.length>1?f(e.toolbar.elements,["undo"]):m(e.toolbar.elements,["undo"]),0!==this.redoStack.length?f(e.toolbar.elements,["redo"]):m(e.toolbar.elements,["redo"]))},e.prototype.undo=function(e){if("false"!==e.ir.element.getAttribute("contenteditable")&&!(this.undoStack.length<2)){var t=this.undoStack.pop();t&&t&&(this.redoStack.push(t),this.renderDiff(t,e),this.hasUndo=!0)}},e.prototype.redo=function(e){if("false"!==e.ir.element.getAttribute("contenteditable")){var t=this.redoStack.pop();t&&(this.undoStack.push(t),this.renderDiff(t,e,!0))}},e.prototype.recordFirstWbr=function(e,t){0!==getSelection().rangeCount&&1===this.undoStack.length&&0!==this.undoStack[0].length&&(Object(u.e)()&&"Backspace"===t.key||Object(u.f)()||(getSelection().getRangeAt(0).insertNode(document.createElement("wbr")),this.undoStack[0][0].diffs[0][1]=e.lute.SpinVditorIRDOM(e.ir.element.innerHTML),this.lastText=this.undoStack[0][0].diffs[0][1],e.ir.element.querySelector("wbr")&&e.ir.element.querySelector("wbr").remove()))},e.prototype.addToUndoStack=function(e){var t;if(0!==getSelection().rangeCount&&!e.ir.element.querySelector("wbr")){var n=getSelection().getRangeAt(0);t=n.cloneRange(),e.ir.element.contains(n.startContainer)&&n.insertNode(document.createElement("wbr"))}var r=e.lute.SpinVditorIRDOM(e.ir.element.innerHTML);e.ir.element.querySelector("wbr")&&e.ir.element.querySelector("wbr").remove(),t&&Object(M.h)(t);var i=this.dmp.diff_main(r,this.lastText,!0),o=this.dmp.patch_make(r,this.lastText,i);0===o.length&&this.undoStack.length>0||(this.lastText=r,this.undoStack.push(o),this.undoStack.length>this.stackSize&&this.undoStack.shift(),this.hasUndo&&(this.redoStack=[],this.hasUndo=!1),this.undoStack.length>1&&f(e.toolbar.elements,["undo"]))},e.prototype.renderDiff=function(e,t,n){var r;if(void 0===n&&(n=!1),n){var i=this.dmp.patch_deepCopy(e).reverse();i.forEach((function(e){e.diffs.forEach((function(e){e[0]=-e[0]}))})),r=this.dmp.patch_apply(i,this.lastText)[0]}else r=this.dmp.patch_apply(e,this.lastText)[0];this.lastText=r,t.ir.element.innerHTML=r,t.ir.element.querySelectorAll(".vditor-ir__preview[data-render='2']").forEach((function(e){H(e,t)})),Object(M.f)(t.ir.element,t.ir.element.ownerDocument.createRange()),De(t),Tt(t,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),this.undoStack.length>1?f(t.toolbar.elements,["undo"]):m(t.toolbar.elements,["undo"]),0!==this.redoStack.length?f(t.toolbar.elements,["redo"]):m(t.toolbar.elements,["redo"])},e}(),er=function(){function e(){this.stackSize=50,this.redoStack=[],this.undoStack=[],this.dmp=new Qn.a,this.lastText="",this.hasUndo=!1}return e.prototype.clearStack=function(e){this.redoStack=[],this.undoStack=[],this.lastText="",this.hasUndo=!1,this.resetIcon(e)},e.prototype.resetIcon=function(e){e.toolbar&&(this.undoStack.length>1?f(e.toolbar.elements,["undo"]):m(e.toolbar.elements,["undo"]),0!==this.redoStack.length?f(e.toolbar.elements,["redo"]):m(e.toolbar.elements,["redo"]))},e.prototype.undo=function(e){if("false"!==e.wysiwyg.element.getAttribute("contenteditable")&&!(this.undoStack.length<2)){var t=this.undoStack.pop();t&&t&&(this.redoStack.push(t),this.renderDiff(t,e),this.hasUndo=!0)}},e.prototype.redo=function(e){if("false"!==e.wysiwyg.element.getAttribute("contenteditable")){var t=this.redoStack.pop();t&&(this.undoStack.push(t),this.renderDiff(t,e,!0))}},e.prototype.recordFirstWbr=function(e,t){0!==getSelection().rangeCount&&1===this.undoStack.length&&0!==this.undoStack[0].length&&(Object(u.e)()&&"Backspace"===t.key||Object(u.f)()||(getSelection().getRangeAt(0).insertNode(document.createElement("wbr")),this.undoStack[0][0].diffs[0][1]=e.lute.SpinVditorDOM(e.wysiwyg.element.innerHTML),this.lastText=this.undoStack[0][0].diffs[0][1],e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove()))},e.prototype.addToUndoStack=function(e){var t;if(0!==getSelection().rangeCount&&!e.wysiwyg.element.querySelector("wbr")){var n=getSelection().getRangeAt(0);e.wysiwyg.element.contains(n.startContainer)&&(t=n.cloneRange(),n.insertNode(document.createElement("wbr")))}var r=e.lute.SpinVditorDOM(e.wysiwyg.element.innerHTML);e.wysiwyg.element.querySelector("wbr")&&e.wysiwyg.element.querySelector("wbr").remove(),t&&Object(M.h)(t);var i=this.dmp.diff_main(r,this.lastText,!0),o=this.dmp.patch_make(r,this.lastText,i);0===o.length&&this.undoStack.length>0||(this.lastText=r,this.undoStack.push(o),this.undoStack.length>this.stackSize&&this.undoStack.shift(),this.hasUndo&&(this.redoStack=[],this.hasUndo=!1,m(e.toolbar.elements,["redo"])),this.undoStack.length>1&&f(e.toolbar.elements,["undo"]))},e.prototype.renderDiff=function(e,t,n){var r;if(void 0===n&&(n=!1),n){var i=this.dmp.patch_deepCopy(e).reverse();i.forEach((function(e){e.diffs.forEach((function(e){e[0]=-e[0]}))})),r=this.dmp.patch_apply(i,this.lastText)[0]}else r=this.dmp.patch_apply(e,this.lastText)[0];this.lastText=r,t.wysiwyg.element.innerHTML=r,t.wysiwyg.element.querySelectorAll(".vditor-wysiwyg__preview[data-render='2']").forEach((function(e){H(e,t)})),Object(M.f)(t.wysiwyg.element,t.wysiwyg.element.ownerDocument.createRange()),De(t),K(t,{enableAddUndoStack:!1,enableHint:!1,enableInput:!0}),ze(t),this.undoStack.length>1?f(t.toolbar.elements,["undo"]):m(t.toolbar.elements,["undo"]),0!==this.redoStack.length?f(t.toolbar.elements,["redo"]):m(t.toolbar.elements,["redo"])},e}(),tr=n(42),nr=n.n(tr),rr=n(43),ir=n.n(rr),or=n(44),ar=n.n(or),sr=n(45),lr=n.n(sr),cr=n(46),dr=n.n(cr),ur=n(47),pr=n.n(ur),hr=n(48),fr=n.n(hr),mr=n(49),vr=n.n(mr),gr=n(50),br=n.n(gr),yr=n(51),wr=n.n(yr),Er=n(52),Mr=n.n(Er),Sr=n(53),Or=n.n(Sr),kr=n(54),xr=n.n(kr),Tr=n(55),Lr=n.n(Tr),jr=n(56),_r=n.n(jr),Cr=n(57),Ar=n.n(Cr),Nr=n(58),Hr=n.n(Nr),Dr=n(59),Rr=n.n(Dr),Ir=n(60),zr=n.n(Ir),Pr=n(61),Br=n.n(Pr),qr=n(62),Ur=n.n(qr),Fr=n(63),Kr=n.n(Fr),Wr=n(64),Vr=n.n(Wr),Zr=n(29),Jr=n.n(Zr),Gr=n(65),Xr=n.n(Gr),Qr=n(66),$r=n.n(Qr),Yr=n(67),ei=n.n(Yr),ti=n(68),ni=n.n(ti),ri=n(69),ii=n.n(ri),oi=n(70),ai=n.n(oi),si=n(71),li=n.n(si),ci=n(72),di=n.n(ci),ui=function(){function e(e){this.defaultOptions={after:void 0,cache:{enable:!0},cdn:"https://cdn.jsdelivr.net/npm/vditor@"+a.b,classes:{preview:""},counter:{enable:!1,type:"markdown"},debugger:!1,height:"auto",hint:{delay:200,emoji:{"+1":"👍","-1":"👎",confused:"😕",eyes:"👀️",heart:"❤️",rocket:"🚀️",smile:"😄",tada:"🎉️"},emojiPath:"https://cdn.jsdelivr.net/npm/vditor@"+a.b+"/dist/images/emoji"},keymap:{deleteLine:"⌘-Backspace",duplicate:"⌘-D"},lang:"zh_CN",mode:"wysiwyg",outline:!1,placeholder:"",preview:{delay:1e3,hljs:{enable:!0,lineNumber:!1,style:"github"},markdown:{autoSpace:!1,chinesePunct:!1,codeBlockPreview:!0,fixTermTypo:!1,footnotes:!0,linkBase:"",listStyle:!1,sanitize:!0,setext:!1,theme:"light",toc:!1},math:{engine:"KaTeX",inlineDigit:!1,macros:{}},maxWidth:800,mode:"both"},resize:{enable:!1,position:"bottom"},theme:"classic",toolbar:["emoji","headings","bold","italic","strike","link","|","list","ordered-list","check","outdent","indent","|","quote","line","code","inline-code","insert-before","insert-after","|","upload","record","table","|","undo","redo","|","fullscreen","edit-mode",{name:"more",toolbar:["both","code-theme","content-theme","export","outline","preview","format","devtools","info","help"]}],toolbarConfig:{hide:!1,pin:!1},typewriterMode:!1,upload:{extraData:{},filename:function(e){return e.replace(/\W/g,"")},linkToImgUrl:"",max:10485760,url:"",withCredentials:!1},value:"",width:"auto"},this.toolbarItem=[{icon:Mr.a,name:"export",tipPosition:"ne"},{hotkey:"⌘-E",icon:wr.a,name:"emoji",tipPosition:"ne"},{hotkey:"⌘-H",icon:xr.a,name:"headings",tipPosition:"ne"},{hotkey:"⌘-B",icon:ar.a,name:"bold",prefix:"**",suffix:"**",tipPosition:"ne"},{hotkey:"⌘-I",icon:Rr.a,name:"italic",prefix:"*",suffix:"*",tipPosition:"ne"},{hotkey:"⌘-S",icon:ii.a,name:"strike",prefix:"~~",suffix:"~~",tipPosition:"ne"},{hotkey:"⌘-K",icon:Br.a,name:"link",prefix:"[",suffix:"](https://)",tipPosition:"n"},{name:"|"},{hotkey:"⌘-L",icon:Ur.a,name:"list",prefix:"* ",tipPosition:"n"},{hotkey:"⌘-O",icon:Vr.a,name:"ordered-list",prefix:"1. ",tipPosition:"n"},{hotkey:"⌘-J",icon:pr.a,name:"check",prefix:"* [ ] ",tipPosition:"n"},{hotkey:"⌘-⇧-I",icon:Jr.a,name:"outdent",tipPosition:"n"},{hotkey:"⌘-⇧-O",icon:_r.a,name:"indent",tipPosition:"n"},{name:"|"},{hotkey:"⌘-;",icon:$r.a,name:"quote",prefix:"> ",tipPosition:"n"},{hotkey:"⌘-⇧-H",icon:zr.a,name:"line",prefix:"---",tipPosition:"n"},{hotkey:"⌘-U",icon:vr.a,name:"code",prefix:"```\n",suffix:"\n```",tipPosition:"n"},{hotkey:"⌘-G",icon:Hr.a,name:"inline-code",prefix:"`",suffix:"`",tipPosition:"n"},{hotkey:"⌘-⇧-B",icon:ir.a,name:"insert-before",tipPosition:"n"},{hotkey:"⌘-⇧-E",icon:nr.a,name:"insert-after",tipPosition:"n"},{name:"|"},{icon:Vn.a,name:"upload",tipPosition:"n"},{icon:ei.a,name:"record",tipPosition:"n"},{hotkey:"⌘-M",icon:ai.a,name:"table",prefix:"| col1",suffix:" | col2 | col3 |\n| --- | --- | --- |\n|  |  |  |\n|  |  |  |",tipPosition:"n"},{name:"|"},{hotkey:"⌘-Z",icon:di.a,name:"undo",tipPosition:"nw"},{hotkey:"⌘-Y",icon:ni.a,name:"redo",tipPosition:"nw"},{name:"|"},{icon:Kr.a,name:"more",tipPosition:"e"},{hotkey:"⌘-'",icon:Or.a,name:"fullscreen",tipPosition:"nw"},{icon:br.a,name:"edit-mode",tipPosition:"nw"},{hotkey:"⌘-P",icon:lr.a,name:"both",tipPosition:"nw"},{icon:Xr.a,name:"preview",tipPosition:"nw"},{hotkey:"⌘-⇧-F",icon:Jr.a,name:"format",tipPosition:"nw"},{icon:V.a,name:"outline",tipPosition:"nw"},{icon:li.a,name:"content-theme",tipPosition:"nw"},{icon:fr.a,name:"code-theme",tipPosition:"nw"},{icon:dr.a,name:"devtools",tipPosition:"nw"},{icon:Ar.a,name:"info",tipPosition:"nw"},{icon:Lr.a,name:"help",tipPosition:"nw"},{name:"br"}],this.options=e}return e.prototype.merge=function(){this.options&&(this.options.upload&&(this.options.upload=Object.assign({},this.defaultOptions.upload,this.options.upload)),this.options.cache&&(this.options.cache=Object.assign({},this.defaultOptions.cache,this.options.cache)),this.options.classes&&(this.options.classes=Object.assign({},this.defaultOptions.classes,this.options.classes)),this.options.keymap&&(this.options.keymap=Object.assign({},this.defaultOptions.keymap,this.options.keymap)),this.options.preview&&(this.options.preview.hljs&&(this.options.preview.hljs=Object.assign({},this.defaultOptions.preview.hljs,this.options.preview.hljs)),this.options.preview.math&&(this.options.preview.math=Object.assign({},this.defaultOptions.preview.math,this.options.preview.math)),this.options.preview.markdown&&(this.options.preview.markdown=Object.assign({},this.defaultOptions.preview.markdown,this.options.preview.markdown)),this.options.preview=Object.assign({},this.defaultOptions.preview,this.options.preview)),this.options.hint&&(this.options.hint=Object.assign({},this.defaultOptions.hint,this.options.hint)),this.options.resize&&(this.options.resize=Object.assign({},this.defaultOptions.resize,this.options.resize)),this.options.counter&&(this.options.counter=Object.assign({},this.defaultOptions.counter,this.options.counter)),this.options.toolbarConfig&&(this.options.toolbarConfig=Object.assign({},this.defaultOptions.toolbarConfig,this.options.toolbarConfig)),this.options.toolbar?this.options.toolbar=this.mergeToolbar(this.options.toolbar):this.options.toolbar=this.mergeToolbar(this.defaultOptions.toolbar));var e=Object.assign({},this.defaultOptions,this.options);if(e.cache.enable&&!e.cache.id)throw new Error("need options.cache.id, see https://hacpai.com/article/1549638745630#options");return e},e.prototype.mergeToolbar=function(e){var t=this,n=[];return e.forEach((function(e){var r=e;t.toolbarItem.forEach((function(t){"string"==typeof e&&t.name===e&&(r=t),"object"==typeof e&&t.name===e.name&&(r=Object.assign({},t,e))})),e.toolbar&&(r.toolbar=t.mergeToolbar(e.toolbar)),n.push(r)})),n},e}(),pi=function(){function e(e){this.composingLock=!1;var t=document.createElement("div");t.className="vditor-wysiwyg",t.innerHTML='<pre class="vditor-reset" placeholder="'+e.options.placeholder+'"\n contenteditable="true" spellcheck="false"></pre>\n<div class="vditor-panel vditor-panel--none"></div>',this.element=t.firstElementChild,this.popover=t.lastElementChild,this.bindEvent(e),document.execCommand("DefaultParagraphSeparator",!1,"p"),Ne(e,this.element),He(e,this.element),Re(e,this.element),Ie(e,this.element)}return e.prototype.bindEvent=function(e){var t=this;(e.options.upload.url||e.options.upload.handler)&&this.element.addEventListener("drop",(function(t){if("Files"===t.dataTransfer.types[0]){var n=t.dataTransfer.items;n.length>0&&F(e,n),t.preventDefault()}})),window.addEventListener("scroll",(function(){if(b(e,["hint"]),"block"===t.popover.style.display){var n=parseInt(t.popover.getAttribute("data-top"),10);"auto"===e.options.height?e.options.toolbarConfig.pin&&(t.popover.style.top=Math.max(n,window.scrollY-e.element.offsetTop-8)+"px"):e.options.toolbarConfig.pin&&0===e.toolbar.element.getBoundingClientRect().top&&(t.popover.style.top=Math.max(window.scrollY-e.element.offsetTop-8,Math.min(n-e.wysiwyg.element.scrollTop,t.element.clientHeight-21))+"px")}})),this.element.addEventListener("scroll",(function(){if(b(e,["hint"]),"block"===t.popover.style.display){var n=parseInt(t.popover.getAttribute("data-top"),10)-e.wysiwyg.element.scrollTop,r=-8;e.options.toolbarConfig.pin&&0===e.toolbar.element.getBoundingClientRect().top&&(r=window.scrollY-e.element.offsetTop+r),t.popover.style.top=Math.max(r,Math.min(n,t.element.clientHeight-21))+"px"}})),this.element.addEventListener("copy",(function(t){var n=getSelection().getRangeAt(0);if(""!==n.toString()){t.stopPropagation(),t.preventDefault();var r=Object(w.f)(n.startContainer,"CODE"),i=Object(w.f)(n.endContainer,"CODE");if(r&&i&&i.isSameNode(r)){var o="";return o="PRE"===r.parentElement.tagName?n.toString():"`"+n.toString()+"`",t.clipboardData.setData("text/plain",o),void t.clipboardData.setData("text/html","")}var a=Object(w.f)(n.startContainer,"A"),s=Object(w.f)(n.endContainer,"A");if(a&&s&&s.isSameNode(a)){var l=a.getAttribute("title")||"";return l&&(l=' "'+l+'"'),t.clipboardData.setData("text/plain","["+n.toString()+"]("+a.getAttribute("href")+l+")"),void t.clipboardData.setData("text/html","")}var c=document.createElement("div");c.appendChild(n.cloneContents()),t.clipboardData.setData("text/plain",e.lute.VditorDOM2Md(c.innerHTML).trim()),t.clipboardData.setData("text/html","")}})),this.element.addEventListener("paste",(function(n){kt(e,n,{pasteCode:function(n){var r=Object(M.b)(t.element),i=document.createElement("template");i.innerHTML=n,r.insertNode(i.content.cloneNode(!0));var o=Object(w.d)(r.startContainer,"data-block","0");o?o.outerHTML=e.lute.SpinVditorDOM(o.outerHTML):e.wysiwyg.element.innerHTML=e.lute.SpinVditorDOM(e.wysiwyg.element.innerHTML),Object(M.f)(e.wysiwyg.element,r)}})})),this.element.addEventListener("compositionstart",(function(e){t.composingLock=!0})),this.element.addEventListener("compositionend",(function(t){var n=Object(E.a)(getSelection().getRangeAt(0).startContainer);n&&""===n.textContent?ct(e):We(e,getSelection().getRangeAt(0).cloneRange(),t)})),this.element.addEventListener("input",(function(n){if(t.preventInput)t.preventInput=!1;else if(!t.composingLock){var r=getSelection().getRangeAt(0),i=Object(w.c)(r.startContainer);if(i||(Se(e,r),i=Object(w.c)(r.startContainer)),i){for(var o=Object(M.c)(i,r).start,a=!0,s=o-1;s>i.textContent.substr(0,o).lastIndexOf("\n");s--)if(" "!==i.textContent.charAt(s)&&"\t"!==i.textContent.charAt(s)){a=!1;break}0===o&&(a=!1);var l=!0;for(s=o-1;s<i.textContent.length;s++)if(" "!==i.textContent.charAt(s)&&"\n"!==i.textContent.charAt(s)){l=!1;break}var c=Object(E.a)(getSelection().getRangeAt(0).startContainer);c&&""===c.textContent?ct(e):a&&!i.querySelector(".language-mindmap")||l||at(i.innerHTML)||st(i.innerHTML,e.options.preview.markdown.setext)||We(e,r,n)}}})),this.element.addEventListener("click",(function(n){if("INPUT"===n.target.tagName){var r=n.target;return r.checked?r.setAttribute("checked","checked"):r.removeAttribute("checked"),t.preventInput=!0,void K(e)}if("IMG"!==n.target.tagName){var i=Object(M.b)(t.element);if(n.target.isEqualNode(t.element)&&t.element.lastElementChild&&i.collapsed){var o=t.element.lastElementChild.getBoundingClientRect();if(n.y>o.top+o.height)return void("P"===t.element.lastElementChild.tagName?(i.selectNodeContents(t.element.lastElementChild),i.collapse(!1)):(t.element.insertAdjacentHTML("beforeend",'<p data-block="0">'+a.a.ZWSP+"<wbr></p>"),Object(M.f)(t.element,i)))}ze(e);var s=Object(w.e)(n.target,"vditor-wysiwyg__preview");s||(s=Object(w.e)(Object(M.b)(t.element).startContainer,"vditor-wysiwyg__preview")),s&&Ce(s,e)}else!function(e,t){var n=e.target;t.wysiwyg.popover.innerHTML="";var r=function(){n.setAttribute("src",o.value),n.setAttribute("alt",s.value),n.setAttribute("title",c.value)},i=document.createElement("span");i.setAttribute("aria-label",R.a[t.options.lang].imageURL),i.className="vditor-tooltipped vditor-tooltipped__n";var o=document.createElement("input");i.appendChild(o),o.className="vditor-input",o.setAttribute("placeholder",R.a[t.options.lang].imageURL),o.value=n.getAttribute("src")||"",o.oninput=function(){r()},o.onkeydown=function(e){Ae(t,e)};var a=document.createElement("span");a.setAttribute("aria-label",R.a[t.options.lang].alternateText),a.className="vditor-tooltipped vditor-tooltipped__n";var s=document.createElement("input");a.appendChild(s),s.className="vditor-input",s.setAttribute("placeholder",R.a[t.options.lang].alternateText),s.style.width="52px",s.value=n.getAttribute("alt")||"",s.oninput=function(){r()},s.onkeydown=function(e){Ae(t,e)};var l=document.createElement("span");l.setAttribute("aria-label","Title"),l.className="vditor-tooltipped vditor-tooltipped__n";var c=document.createElement("input");l.appendChild(c),c.className="vditor-input",c.setAttribute("placeholder","Title"),c.value=n.getAttribute("title")||"",c.oninput=function(){r()},c.onkeydown=function(e){Ae(t,e)},Ue(n,t),t.wysiwyg.popover.insertAdjacentElement("beforeend",i),t.wysiwyg.popover.insertAdjacentElement("beforeend",a),t.wysiwyg.popover.insertAdjacentElement("beforeend",l),Pe(t,n)}(n,e)})),this.element.addEventListener("keyup",(function(n){if(!n.isComposing&&!Object(u.d)(n)){"Enter"===n.key&&De(e),"Backspace"!==n.key&&"Delete"!==n.key||""===e.wysiwyg.element.innerHTML||1!==e.wysiwyg.element.childNodes.length||!e.wysiwyg.element.firstElementChild||"P"!==e.wysiwyg.element.firstElementChild.tagName||0!==e.wysiwyg.element.firstElementChild.childElementCount||""!==e.wysiwyg.element.textContent&&"\n"!==e.wysiwyg.element.textContent||(e.wysiwyg.element.innerHTML="");var r=Object(M.b)(t.element);if("Backspace"===n.key&&Object(u.e)()&&"\n"===r.startContainer.textContent&&1===r.startOffset&&(r.startContainer.textContent=""),Se(e,r),ze(e),"ArrowDown"===n.key||"ArrowRight"===n.key||"Backspace"===n.key||"ArrowLeft"===n.key||"ArrowUp"===n.key){"ArrowLeft"!==n.key&&"ArrowRight"!==n.key||e.hint.render(e);var i=Object(w.e)(r.startContainer,"vditor-wysiwyg__preview");if(!i&&3!==r.startContainer.nodeType&&r.startOffset>0)(a=r.startContainer).classList.contains("vditor-wysiwyg__block")&&(i=a.lastElementChild);if(i)if("none"!==i.previousElementSibling.style.display){var o=i.previousElementSibling;if("PRE"===o.tagName&&(o=o.firstElementChild),"ArrowDown"===n.key||"ArrowRight"===n.key){var a,s=function(e){for(var t=e;t&&!t.nextSibling;)t=t.parentElement;return t.nextSibling}(a=i.parentElement);if(s&&3!==s.nodeType){var l=s.querySelector(".vditor-wysiwyg__preview");if(l)return void Ce(l,e)}if(3===s.nodeType){for(;0===s.textContent.length&&s.nextSibling;)s=s.nextSibling;r.setStart(s,1)}else r.setStart(s.firstChild,0)}else r.selectNodeContents(o),r.collapse(!1)}else"ArrowDown"===n.key||"ArrowRight"===n.key?Ce(i,e):Ce(i,e,!1)}}}))},e}(),hi=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),fi=function(e){function t(t,n){var r=e.call(this)||this;r.version=a.b,"string"==typeof t&&(n?n.cache?n.cache.id||(n.cache.id="vditor"+t):n.cache={id:"vditor"+t}:n={cache:{id:"vditor"+t}},t=document.getElementById(t));var i=new ui(n).merge();if(!["en_US","ko_KR","zh_CN"].includes(i.lang))throw new Error("options.lang error, see https://hacpai.com/article/1549638745630#options");r.vditor={currentMode:i.mode,element:t,hint:new Ct,lute:void 0,options:i,originalInnerHTML:t.innerHTML,outline:new It(R.a[i.lang].outline),tip:new Vt},r.vditor.sv=new Wt(r.vditor),r.vditor.undo=new $n,r.vditor.wysiwyg=new pi(r.vditor),r.vditor.wysiwygUndo=new er,r.vditor.ir=new Nt(r.vditor),r.vditor.irUndo=new Yn,r.vditor.toolbar=new Gn(r.vditor),i.resize.enable&&(r.vditor.resize=new Kt(r.vditor)),r.vditor.toolbar.elements.devtools&&(r.vditor.devtools=new d),(i.upload.url||i.upload.handler)&&(r.vditor.upload=new U);var o=i.cdn+"/dist/js/lute/lute.min.js";return Object(c.a)(o,"vditorLuteScript").then((function(){r.vditor.lute=Object(Dt.a)({autoSpace:r.vditor.options.preview.markdown.autoSpace,chinesePunct:r.vditor.options.preview.markdown.chinesePunct,codeBlockPreview:r.vditor.options.preview.markdown.codeBlockPreview,emojiSite:r.vditor.options.hint.emojiPath,emojis:r.vditor.options.hint.emoji,fixTermTypo:r.vditor.options.preview.markdown.fixTermTypo,footnotes:r.vditor.options.preview.markdown.footnotes,headingAnchor:!1,inlineMathDigit:r.vditor.options.preview.math.inlineDigit,linkBase:r.vditor.options.preview.markdown.linkBase,listStyle:r.vditor.options.preview.markdown.listStyle,paragraphBeginningSpace:r.vditor.options.preview.markdown.paragraphBeginningSpace,sanitize:r.vditor.options.preview.markdown.sanitize,setext:r.vditor.options.preview.markdown.setext,toc:r.vditor.options.preview.markdown.toc}),r.vditor.preview=new qt(r.vditor),function(e){e.element.innerHTML="",e.element.classList.add("vditor"),ve(e),Object(me.a)(e.options.preview.markdown.theme,e.options.cdn),"number"==typeof e.options.height&&(e.element.style.height=e.options.height+"px"),"number"==typeof e.options.minHeight&&(e.element.style.minHeight=e.options.minHeight+"px"),"number"==typeof e.options.width?e.element.style.width=e.options.width+"px":e.element.style.width=e.options.width,e.element.appendChild(e.toolbar.element);var t=document.createElement("div");if(t.className="vditor-content",t.appendChild(e.outline.element),t.appendChild(e.wysiwyg.element.parentElement),t.appendChild(e.sv.element),t.appendChild(e.ir.element.parentElement),t.appendChild(e.preview.element),e.toolbar.elements.devtools&&t.appendChild(e.devtools.element),e.upload&&t.appendChild(e.upload.element),e.options.resize.enable&&t.appendChild(e.resize.element),t.appendChild(e.hint.element),t.appendChild(e.tip.element),e.element.appendChild(t),e.toolbar.elements.export&&e.element.insertAdjacentHTML("beforeend",'<iframe style="width: 100%;height: 0;border: 0"></iframe>'),Te(e,e.options.mode,ye(e,t)),navigator.userAgent.indexOf("iPhone")>-1&&void 0!==window.visualViewport){var n=!1,r=function(t){n||(n=!0,requestAnimationFrame((function(){n=!1;var t=e.toolbar.element;t.style.transform="none",t.getBoundingClientRect().top<0&&(t.style.transform="translate(0, "+-t.getBoundingClientRect().top+"px)")})))};window.visualViewport.addEventListener("scroll",r),window.visualViewport.addEventListener("resize",r)}}(r.vditor),i.after&&i.after()})),r}return hi(t,e),t.prototype.setTheme=function(e,t,n){this.vditor.options.theme=e,ve(this.vditor),t&&(this.vditor.options.preview.markdown.theme=t,Object(me.a)(t,this.vditor.options.cdn)),n&&(this.vditor.options.preview.hljs.style=n,Object(Qt.a)(n,this.vditor.options.cdn))},t.prototype.getValue=function(){return l(this.vditor)},t.prototype.getCurrentMode=function(){return this.vditor.currentMode},t.prototype.focus=function(){"sv"===this.vditor.currentMode?this.vditor.sv.element.focus():"wysiwyg"===this.vditor.currentMode?this.vditor.wysiwyg.element.focus():"ir"===this.vditor.currentMode&&this.vditor.ir.element.focus()},t.prototype.blur=function(){"sv"===this.vditor.currentMode?this.vditor.sv.element.blur():"wysiwyg"===this.vditor.currentMode?this.vditor.wysiwyg.element.blur():"ir"===this.vditor.currentMode&&this.vditor.ir.element.blur()},t.prototype.disabled=function(){b(this.vditor,["subToolbar","hint","popover"]),m(this.vditor.toolbar.elements,a.a.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","false")},t.prototype.enable=function(){f(this.vditor.toolbar.elements,a.a.EDIT_TOOLBARS.concat(["undo","redo","fullscreen","edit-mode"])),this.vditor.undo.resetIcon(this.vditor),this.vditor.wysiwygUndo.resetIcon(this.vditor),this.vditor[this.vditor.currentMode].element.setAttribute("contenteditable","true")},t.prototype.setSelection=function(e,t){"sv"!==this.vditor.currentMode?console.error("所见即所得模式暂不支持该方法"):Object(M.g)(e,t,this.vditor.sv.element)},t.prototype.getSelection=function(){return"wysiwyg"===this.vditor.currentMode?ue(this.vditor.wysiwyg.element):"sv"===this.vditor.currentMode?ue(this.vditor.sv.element):"ir"===this.vditor.currentMode?ue(this.vditor.ir.element):void 0},t.prototype.renderPreview=function(e){this.vditor.preview.render(this.vditor,e)},t.prototype.getCursorPosition=function(){return Object(M.a)(this.vditor[this.vditor.currentMode].element)},t.prototype.isUploading=function(){return this.vditor.upload.isUploading},t.prototype.clearCache=function(){localStorage.removeItem(this.vditor.options.cache.id)},t.prototype.disabledCache=function(){this.vditor.options.cache.enable=!1},t.prototype.enableCache=function(){if(!this.vditor.options.cache.id)throw new Error("need options.cache.id, see https://hacpai.com/article/1549638745630#options");this.vditor.options.cache.enable=!0},t.prototype.html2md=function(e){return fe(this.vditor,e)},t.prototype.getHTML=function(){return Ht(this.vditor)},t.prototype.tip=function(e,t){this.vditor.tip.show(e,t)},t.prototype.setPreviewMode=function(e){Zt(e,this.vditor)},t.prototype.deleteValue=function(){window.getSelection().isCollapsed||("sv"===this.vditor.currentMode?P(this.vditor,"","",!0):document.execCommand("delete",!1))},t.prototype.updateValue=function(e){"sv"===this.vditor.currentMode?P(this.vditor,e,"",!0):document.execCommand("insertHTML",!1,e)},t.prototype.insertValue=function(e,t){if(void 0===t&&(t=!0),"sv"===this.vditor.currentMode)P(this.vditor,e,"");else if("wysiwyg"===this.vditor.currentMode){Object(M.b)(this.vditor.wysiwyg.element).collapse(!0),this.vditor.wysiwyg.preventInput=!0,document.execCommand("insertHTML",!1,e),t&&We(this.vditor,getSelection().getRangeAt(0))}else if("ir"===this.vditor.currentMode){Object(M.b)(this.vditor.ir.element).collapse(!0),this.vditor.ir.preventInput=!0,document.execCommand("insertHTML",!1,e),t&&D(this.vditor,getSelection().getRangeAt(0),!0)}},t.prototype.setValue=function(e,t){void 0===t&&(t=!1),t&&this.clearStack(),"sv"===this.vditor.currentMode?z(this.vditor,e,{end:e.length,start:e.length},{enableAddUndoStack:!t,enableHint:!1,enableInput:!1}):"wysiwyg"===this.vditor.currentMode?we(this.vditor,e,{enableAddUndoStack:!t,enableHint:!1,enableInput:!1}):(this.vditor.ir.element.innerHTML=this.vditor.lute.Md2VditorIRDOM(e),Tt(this.vditor,{enableAddUndoStack:!t,enableHint:!1,enableInput:!1})),this.vditor.outline.render(this.vditor),e||(b(this.vditor,["emoji","headings","submenu","hint"]),this.vditor.wysiwyg.popover&&(this.vditor.wysiwyg.popover.style.display="none"),this.clearCache())},t.prototype.clearStack=function(){this.vditor.undo.clearStack(this.vditor),this.vditor.irUndo.clearStack(this.vditor),this.vditor.wysiwygUndo.clearStack(this.vditor)},t}(o.default);t.default=fi}]).default}));