import errMsgboxVue from './index.vue';

// 定义插件对象Err
const ErrMessageBox = {};
// vue的install方法，用于定义vue插件
ErrMessageBox.install = function(Vue, options) {
    const Instance = Vue.extend(errMsgboxVue);
    let currentMsg;
    const initInstance = () => {
        // 实例化vue实例
        currentMsg = new Instance();
        let errMsgBoxEl = currentMsg.$mount().$el;
        document.body.appendChild(errMsgBoxEl);
    };
    // 在Vue的原型上添加实例方法，以全局调用
    Vue.prototype.$errMsgBox = {
        show(options) {
            if (!currentMsg) {
                initInstance();
            }
            if (typeof options === 'string') {
                currentMsg.content = options;
            } else if (typeof options === 'object') {
                Object.assign(currentMsg, options);
            }
            return currentMsg.showMsgBox()
                .then(val => {
                    currentMsg = null;
                    return Promise.resolve(val);
                })
                .catch(err => {
                    currentMsg = null;
                    return Promise.reject(err);
                });
        },
        destroy() {
          if(currentMsg) {
            return currentMsg.destroyMsgBox()
              .then(val => {
                currentMsg = null;
                return Promise.resolve(val);
              })
              .catch(err => {
                currentMsg = null;
                return Promise.reject(err);
              });
          } else {
            return Promise.resolve();
          }
        }
    };
};
export default ErrMessageBox;
