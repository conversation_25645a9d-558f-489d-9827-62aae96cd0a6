// eslint-disable-next-line
import router from '@/core/router'
import api from '@/core/http/api'
import Cookies from 'js-cookie'
// eslint-disable-next-line
import { resetRouter } from '@/core/router'
import socket from '@/core/utils/websocket'
import store from '@/core/store'
import Vue from 'vue'
import { PopLoginBox } from '@/core/components/PopLoginBox'
import { getRefreshToken } from '@/core/utils/token'

/**
 * logout
 */
export function logout(refreshFailFlag) {
  // 清除定时器
  resetTimer()
  let url = window.location.href

  api.login.logout({redirectPath: url, type: refreshFailFlag}).then((res) => {
      Cookies.remove(config.cookieName)
      sessionStorage.removeItem('user')
      sessionStorage.removeItem('resetTime')
      sessionStorage.removeItem('spaceId')
      sessionStorage.removeItem('systemList')
      store.commit('setSpaceId', '')
      clearInterval(window.interval);
      window.isRefreshing = false;
      if (res.data && res.data.redirectUrl && res.data.redirectUrl.length > 0) {
          location.replace(res.data.redirectUrl);
          return;
      }
      if(config && config.sso && config.sso.onoff) {
          if(config.sso.isCompatible) {
              // 老统一用户
              window.location.href = config.sso.logoutUrl
              // window.location.replace(config.sso.logoutUrl + '?fsc=' + config.systemCode + '&redirectPath=' + encodeURIComponent(url))// 返回单点登录SSO页面
          } else {
              // 新统一用户
              if(config.sso.onoff) {
                const href = window.location.href
                let url = new URL(href)
                let redirectUri = encodeURIComponent(url.href)
                // 返回单点登录SSO页面
                if(top !== self) {
                  window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
                } else {
                  window.location.href = config.sso.logoutUrl + (config.sso.logoutUrl.indexOf('?') >= 0 ? '&' : '?') + 'client_id=' + config.client_id + '&redirect_uri=' + redirectUri
                }
              } else {
                // window.location.replace(config.sso.url + '?fsc=' + config.systemCode + '&redirectPath=' + url) // 返回单点登录SSO页面
                if(top !== self) {
                  window.top.postMessage(JSON.stringify({BaseUIType: 'logout', data: {}}), '*');
                } else {
                  window.location.href = config.sso.logoutUrl
                }
              }
          }
      } else {
          resetRouter()
          // 如果是业务自定义弹出需要销毁窗口
          if(config.errMessage.isCustomShowError) {
              Vue.prototype.$errMsgBox.destroy()
          }
          // 重定向到登录页面
          if (url === window.top.location.href) {
              router.push({name: '登录'})
          } else {
              top.window.location.reload()
          }
      }
      if(socket) {
          socket.closeWS(4500);
      }
  })
}

export function loginInit(obj) {
  let {userInfo: res, sysList: systemList = []} = obj
  Cookies.set(config.cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
  sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
  sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
  sessionStorage.setItem('systemList', JSON.stringify(systemList))
  store.commit('setCurrentUser', res)
  store.commit('menuRouteLoaded', false)
  store.commit('setSpaceId', '')
  setTimer()
}

export async function getSpaceId(userName) {
  await api.authority.getSystemInfo({'sysCode': config.systemCode}).then(res => {
    let spaceIdCookies = Cookies.get('spaceId');
    if(!spaceIdCookies) {
      spaceIdCookies = {}
    } else {
      spaceIdCookies = JSON.parse(Cookies.get('spaceId'));
    }
    if(res.data.tsysSpaces && res.data.tsysSpaces.length > 0) {
      // 从缓存中获取spaceId， 如果缓存中没有spaceId或者没有与其匹配的spaceId就默认取第一条空间的空间ID
      // let lastSpaceId = sessionStorage.getItem('spaceId')
      let lastSpaceId = spaceIdCookies[userName];
      if(lastSpaceId === "||empty||") {
        store.commit('setSpaceId', '||empty||')
        // sessionStorage.setItem('spaceId', '')
        spaceIdCookies[userName] = '||empty||';
        Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
      } else {
        let spaceModel = res.data.tsysSpaces.filter(i => i.spaceId === lastSpaceId)
        if(!lastSpaceId || spaceModel.length < 1) {
          store.commit('setSpaceId', res.data.tsysSpaces[0].spaceId)
          // sessionStorage.setItem('spaceId', res.data.tsysSpaces[0].spaceId)
          spaceIdCookies[userName] = res.data.tsysSpaces[0].spaceId;
          Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
        } else {
          store.commit('setSpaceId', lastSpaceId)
          // sessionStorage.setItem('spaceId', lastSpaceId)
          spaceIdCookies[userName] = lastSpaceId;
          Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
        }
      }
    } else {
      store.commit('setSpaceId', '')
      // sessionStorage.setItem('spaceId', '')
      spaceIdCookies[userName] = '';
      Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
    }
  })
}

/**
 * 设置定时器，定时检查token是否失效，失效则弹出用户重登录框
 */
export function setTimer() {
  if(!window.$baseUITimer && config.isOnUims) {
    window.$baseUITimer = setInterval(() => {
      // 获取超时时间
      let resetTime = sessionStorage.getItem('resetTime')

      if (config.tokenTimeOutMode === 'refresh') {
        if(resetTime && (Date.now() >= (resetTime - 5 * 60 * 1000))) {
          let cookieName = config.cookieName;
          let token = JSON.parse(Cookies.get(cookieName))
          getRefreshToken(token.refreshToken).then((res) => {
            if(res.redirectPath) {
              location.replace(res.redirectPath + '?code=' + res.code);
              return;
            }
            Cookies.set(cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
            res.userName = res.user_name
            sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
            sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
            store.commit('setCurrentUser', res)
            resetTimer()
          }).catch((e) => {
            if(e.code === '401') {
              Cookies.remove(cookieName)
            }
          }).then(() => {
            setTimer()
          })
        }
      } else {
        if(resetTime && (Date.now() >= resetTime)) {
          /**
           * 每秒轮询一次：当前时间>=超时时间，弹窗重登录窗口，并清除定时器
           */
          if (config.tokenTimeOutMode === 'logout') {
            logout(1);
            resetTimer()
          } else {
            PopLoginBox().then((res) => {
            }).catch(action => {
              logout(1);
            })
            resetTimer()
          }
        }
      }
    }, 1000)
  }
}

/**
 * 清除定时器
 */
export function resetTimer() {
  clearInterval(window.$baseUITimer)
  window.$baseUITimer = null
}
