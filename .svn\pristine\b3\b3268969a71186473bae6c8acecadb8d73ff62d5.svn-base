import axios from '../axios'
/*
 * 关于
 */

// 获取当前版本
export const versionList = () => {
  return axios({
    url: `/${config.appCode}/version/versionList`,
    method: 'POST',
    headers: {'isBaseApi': true}
  })
}

// 获取历史版本
export const versionLogList = () => {
  return axios({
    url: `/${config.appCode}/version/versionLogList`,
    method: 'POST',
    headers: {'isBaseApi': true}
  })
}

// 根据版本号获取历史版本信息
export function getVersionLogByNo (data) {
  return axios({
    url: `/${config.appCode}/version/getVersionLogByNo`,
    method: 'POST',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'isBaseApi': true}
  })
}
