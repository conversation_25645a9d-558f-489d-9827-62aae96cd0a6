<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
     <link rel="shortcut icon" type="image/x-icon" href="./favicon.ico">
<!--    <link rel="stylesheet" href="<%= process.env.BASE_URL %>config/orange/index.min.css">-->
<!--    <link rel="stylesheet" href="<%= process.env.BASE_URL %>config/thinvent/index.min.css">-->
<!--    <link rel="stylesheet" href="<%= process.env.BASE_URL %>config/blue/index.min.css">-->
<!--    <link rel="stylesheet" href="<%= process.env.BASE_URL %>config/green/index.min.css">-->
<!--    <link rel="stylesheet" href="<%= process.env.BASE_URL %>config/thinvent_darken/index.min.css">-->
    <script src="<%= process.env.BASE_URL %>config/config.js"></script>
    <title></title>
  </head>
  <body>
    <div id="app"></div>
  </body>
  <script>
    // 标题
    window.document.title = config.plat_title;
    // favicon.ico
    // var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
    // link.type = 'image/x-icon';
    // link.rel = 'shortcut icon';
    // link.href = config.css ? '<%= process.env.BASE_URL %>config/' + config.css + '/favicon.ico' : './<%= process.env.BASE_URL %>favicon.ico';
    // document.getElementsByTagName('head')[0].appendChild(link);

    // 引入风格CSS
    // if(config.css) {
    //   // 样式风格
    //   let thumbnail = null;
    //   if(localStorage.getItem('thumbnail')) {
    //     thumbnail = JSON.parse(localStorage.getItem('thumbnail'))
    //   }
    //   let skin = thumbnail ? thumbnail.thumbnailImg : config.css;
    //   window.document.body.setAttribute('class', skin );
    // }

    // 引入登录页CSS
    if(config.loginBg) {
      link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = '<%= process.env.BASE_URL %>config/login/' + config.loginBg + '/index.min.css';
      document.getElementsByTagName('head')[0].appendChild(link)
    }

  </script>
</html>
