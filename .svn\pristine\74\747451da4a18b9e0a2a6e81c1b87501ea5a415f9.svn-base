<template>
  <ul class="button-groups-ul" :style="btns2.length > 0 ? '' : 'margin-right: 0px'">
    <li v-for="button in btns1" :key="button.label" class="button-li">
      <perm-button
        :class="['btn_noBg', button.class]"
        :label="button.label"
        :title="button.title ? button.title : ''"
        :icon="button.icon"
        type="text"
        size="mini"
        :loading="button.loading"
        @click="button.clickFn(config.row, button)"
        :perms="button.perms"
        :disabled="button.disabled"
        :projectId="button.projectId"
      />
    </li>
    <li v-if="btns2.length > 0" class="button-li">
      <el-dropdown>
        <span class="el-dropdown-link rotate90">…</span>
        <el-dropdown-menu slot="dropdown" class="button-groups-dropdown-menu">
          <template v-for="button in btns2">
            <el-dropdown-item :key="button.label" @click.native="!hasPerms(button.perms, button.disabled, button.projectId) ? '' : button.clickFn(config.row, button)"
              :disabled="!hasPerms(button.perms, button.disabled, button.projectId)" v-if="hasPerms(button.perms, button.disabled, button.projectId) || isShowNoPermBtn">
              <svg-icon v-if="button.icon" :icon-class="button.icon" />
              <div v-if="button.icon" class="gap"></div>
              {{button.label}}
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </el-dropdown>
    </li>
  </ul>
</template>

<script>
import PermButton from '@/core/components/PermButton'
import {hasPermission} from '@/core/utils/permission.js'
import {hasForbidProjectPermission} from '@/biz/utils/permission.js'

export default {
  name: 'PermButtonGroup',
  components: { PermButton },
  props: {
    config: {
      type: Object,
      default: function () {
        return {row: {}, buttons: [], showNums: 3}
      }
    }
  },
  data () {
    return {
      isShowNoPermBtn: config.isShowNoPermBtn
    }
  },
  computed: {
    showNums () {
      return this.config.showNums || 3
    },
    btns1 () {
      // 不显示模式需要计算，被隐藏的按钮，把 ...中的按钮移出来
      if(config.isShowNoPermBtn === 0 || config.isShowNoPermBtn === '0') {
        let buttons = []
        this.config.buttons.forEach(item => {
          if(this.hasPerms(item.perms, item.disabled, item.projectId)) {
            buttons.push(item)
          }
        })
        return buttons.slice(0, this.showNums);
      } else {
        return this.config.buttons.slice(0, this.showNums);
      }
    },
    btns2 () {
      // 不显示模式需要计算，被隐藏的按钮，把 ...中的按钮移出来
      if(config.isShowNoPermBtn === 0 || config.isShowNoPermBtn === '0') {
        let buttons = []
        this.config.buttons.forEach(item => {
          if(this.hasPerms(item.perms, item.disabled, item.projectId)) {
            buttons.push(item)
          }
        })
        return buttons.slice(this.showNums);
      } else {
        return this.config.buttons.slice(this.showNums);
      }
    }
  },
  methods: {
    hasPerms: function (perms, disabled, projectId) {
      // 根据权限标识和外部指示状态进行权限判断
      if(projectId || String(projectId) === '0') {
        // 若项目优先，则由项目返回权限控制结果
        return hasForbidProjectPermission(perms, projectId)
      } else {
        // 按统一用户资源权限控制
        if (perms) {
            return hasPermission(perms) & !disabled
        } else{
            return !disabled
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ul.button-groups-ul {
    margin: 0;
    padding: 0;
    margin-right: 20px;
    font-size: 0;
    li {
      list-style: none;
      &.button-li {
        display: inline-block;
        height: 30px;
        line-height: 30px;
        vertical-align: middle;
        span.rotate90 {
          display: inline-block;
          // transform: rotate(90deg);
          position: absolute;
          top: -19px;
        }
      }
      .el-button {
        margin: 0;
        padding: 7px 10px;
        height: 30px;
        font-size: 14px;
        border-color: transparent;
        background-color: transparent;
      }
    }
  }
  .el-dropdown-menu__item .gap {
    display:inline-block;
    width: 2px;
  }
  .el-dropdown-link {
    cursor: pointer;
  }
  .el-dropdown-menu__item.is-disabled {
    cursor: not-allowed;
    pointer-events: auto;
    color: #C0C4CC!important;
  }
  .el-button.is-disabled {
    background-color: transparent;
  }
</style>
