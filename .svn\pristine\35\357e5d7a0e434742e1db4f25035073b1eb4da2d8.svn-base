!function(o){var r={};function a(t){if(r[t])return r[t].exports;var e=r[t]={i:t,l:!1,exports:{}};return o[t].call(e.exports,e,e.exports,a),e.l=!0,e.exports}a.m=o,a.c=r,a.d=function(t,e,o){a.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},a.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},a.t=function(e,t){if(1&t&&(e=a(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(a.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)a.d(o,r,function(t){return e[t]}.bind(null,r));return o},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,"a",e),e},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},a.p="",a(a.s=8)}([function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,o){"use strict";var r,a=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),C=this&&this.__read||function(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var r,a,n=o.call(t),i=[];try{for(;(void 0===e||0<e--)&&!(r=n.next()).done;)i.push(r.value)}catch(t){a={error:t}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(a)throw a.error}}return i},P=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],r=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var n,i=o(2),l=o(3),w=o(4),S=o(5),b=o(6),u=o(7),c=(n=l.CommandMap,a(p,n),p.prototype.remove=function(t){this.map.delete(t)},p);function p(){return null!==n&&n.apply(this,arguments)||this}function j(t,e,o,r){var a,n,i,l;if(b.Package.packages.has(t.options.require.prefix+o)){var u=t.options.autoload[o],c=C(2===u.length&&Array.isArray(u[0])?u:[u,[]],2),p=c[0],s=c[1];try{for(var f=P(p),d=f.next();!d.done;d=f.next()){var x=d.value;q.remove(x)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(n=f.return)&&n.call(f)}finally{if(a)throw a.error}}try{for(var y=P(s),h=y.next();!h.done;h=y.next()){var M=h.value;k.remove(M)}}catch(t){i={error:t}}finally{try{h&&!h.done&&(l=y.return)&&l.call(y)}finally{if(i)throw i.error}}t.i-=e.length+(r?0:7)}S.RequireLoad(t,o)}var q=new(e.AutoloadCommandMap=c)("autoload-macros",{},{}),k=new c("autoload-environments",{},{});e.AutoloadConfiguration=i.Configuration.create("autoload",{handler:{macro:["autoload-macros"],environment:["autoload-environments"]},options:{autoload:u.expandable({action:["toggle","mathtip","texttip"],amsCd:[[],["CD"]],bbox:["bbox"],boldsymbol:["boldsymbol"],braket:["bra","ket","braket","set","Bra","Ket","Braket","Set","ketbra","Ketbra"],cancel:["cancel","bcancel","xcancel","cancelto"],color:["color","definecolor","textcolor","colorbox","fcolorbox"],enclose:["enclose"],extpfeil:["xtwoheadrightarrow","xtwoheadleftarrow","xmapsto","xlongequal","xtofrom","Newextarrow"],html:["href","class","style","cssId"],mhchem:["ce","pu"],newcommand:["newcommand","renewcommand","newenvironment","renewenvironment","def","let"],unicode:["unicode"],verb:["verb"]})},config:function(t,e){var o,r,a,n,i,l,u=e.parseOptions,c=u.handlers.get("macro"),p=u.handlers.get("environment"),s=u.options.autoload;try{for(var f=P(Object.keys(s)),d=f.next();!d.done;d=f.next()){var x=d.value,y=s[x],h=C(2===y.length&&Array.isArray(y[0])?y:[y,[]],2),M=h[0],b=h[1];try{for(var m=(a=void 0,P(M)),_=m.next();!_.done;_=m.next()){var v=_.value;c.lookup(v)&&"color"!==v||q.add(v,new w.Macro(v,j,[x,!0]))}}catch(t){a={error:t}}finally{try{_&&!_.done&&(n=m.return)&&n.call(m)}finally{if(a)throw a.error}}try{for(var g=(i=void 0,P(b)),O=g.next();!O.done;O=g.next()){var J=O.value;p.lookup(J)||k.add(J,new w.Macro(J,j,[x,!1]))}}catch(t){i={error:t}}finally{try{O&&!O.done&&(l=g.return)&&l.call(g)}finally{if(i)throw i.error}}}}catch(t){o={error:t}}finally{try{d&&!d.done&&(r=f.return)&&r.call(f)}finally{if(o)throw o.error}}u.options.require.jax||S.RequireConfiguration.config(t,e)},configPriority:10,init:function(t){t.options.require||u.defaultOptions(t.options,S.RequireConfiguration.options)},priority:10})},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Configuration=MathJax._.input.tex.Configuration.Configuration,e.ConfigurationHandler=MathJax._.input.tex.Configuration.ConfigurationHandler},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractSymbolMap=MathJax._.input.tex.SymbolMap.AbstractSymbolMap,e.RegExpMap=MathJax._.input.tex.SymbolMap.RegExpMap,e.AbstractParseMap=MathJax._.input.tex.SymbolMap.AbstractParseMap,e.CharacterMap=MathJax._.input.tex.SymbolMap.CharacterMap,e.DelimiterMap=MathJax._.input.tex.SymbolMap.DelimiterMap,e.MacroMap=MathJax._.input.tex.SymbolMap.MacroMap,e.CommandMap=MathJax._.input.tex.SymbolMap.CommandMap,e.EnvironmentMap=MathJax._.input.tex.SymbolMap.EnvironmentMap},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Symbol=MathJax._.input.tex.Symbol.Symbol,e.Macro=MathJax._.input.tex.Symbol.Macro},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RequireLoad=MathJax._.input.tex.require.RequireConfiguration.RequireLoad,e.RequireMethods=MathJax._.input.tex.require.RequireConfiguration.RequireMethods,e.options=MathJax._.input.tex.require.RequireConfiguration.options,e.RequireConfiguration=MathJax._.input.tex.require.RequireConfiguration.RequireConfiguration},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PackageError=MathJax._.components.package.PackageError,e.Package=MathJax._.components.package.Package},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,o){"use strict";o.r(e);var r=o(0),a=o(1);Object(r.combineWithMathJax)({_:{input:{tex:{autoload:{AutoloadConfiguration:a}}}}})}]);