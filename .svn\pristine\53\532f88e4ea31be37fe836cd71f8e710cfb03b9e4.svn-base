export default {
  state: {
    system: {}, // 当前系统信息
    spacesList: [], // 空间集合
    spaceId: '', // 当前空间ID
    privModel: '' // 空间权限模式 0 一般模式 1 空间模式 2 共存
  },
  getters: {},
  mutations: {
    setSystem (state, system) { // 当前登录用户
      state.system = system
    },
    setSpacesList (state, spacesList) { // 联合用户集合
      state.spacesList = spacesList
    },
    setSpaceId (state, spaceId) { // 用户权限标识集合
      state.spaceId = spaceId
    },
    setPrivModel (state, privModel) {
      state.privModel = privModel
    }
  },
  actions: {}
}
