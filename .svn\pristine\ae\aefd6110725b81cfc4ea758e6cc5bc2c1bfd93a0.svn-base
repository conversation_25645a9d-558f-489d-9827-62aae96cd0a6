<template>
  <div class="point-wrapper">
    <img :src="require(`./point.svg`)" style="display:block;width:16px;height:16px;margin: 0 5px;" alt="" />
    <div class="el-tooltip__popper item">
      <div v-html="html" class="info-box" :style="tipStyle ? tipStyle : 'max-width: 250px;'"></div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'Point',
    data() {
      return {
        html: ""
      }
    },
    props: {
      tipInfo: {
        type: Object,
        default: undefined
      }, // 数据： 格式｛tipInfo:{titles:["","",""],infos:["","",""]}｝
      styleMode: {
        type: String,
        default: '1'
      }, // 样式模式：1模式默认样式
      tipStyle: {
        type: String,
        default: ''
      }
    },
    methods: {
      init() {
        let html = ""
        if(this.tipInfo) {
          let titles = this.tipInfo.titles
          let infos = this.tipInfo.infos
          if(infos && infos.length > 0) {
            for(let i = 0; i < infos.length; i++) {
              if(titles && i < titles.length) {
                html += this.getTitle(titles[i])
              }
              html += this.getInfo(infos[i])
            }
          }
        }
        this.html = html
      },
      getTitle(value) {
        let titleHtml = ''
        if(this.styleMode === '1') {
          titleHtml = "<p class=\"tip-title\">" + (!value ? "" : value) + "</p>"
        }
        return titleHtml
      },
      getInfo(value) {
        let infoHtml = ''
        if(this.styleMode === '1') {
          infoHtml = (!value ? "" : value)
        }
        return infoHtml
      }
    },
    mounted () {
      this.init()
    }
  }
</script>
<style lang="scss">
  .item.el-tooltip__popper {
    max-width: unset;
    .popper__arrow {
      display: none;
    }
  }
</style>
<style scoped lang="scss">
  .point-wrapper {
    display: flex;
    margin: 5px 0;
  }
  .el-tooltip__popper {
    border: 1px solid #303133 !important;
    border-radius: 6px !important;
    display: block;
    position: relative;
    flex: 1;
  }
  .info-box {
    // max-width: 250px;
    word-break: break-all;
  }
  >>> .tip-title {
    font-size: 14px;
    color: #4c4c4c;
    margin: 10px 0px 5px 0px;
    border-bottom: 1px solid #d8d8d8;
  }
</style>
