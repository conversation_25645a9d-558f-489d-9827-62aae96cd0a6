// import Vue from 'vue'
import Cookies from 'js-cookie'
let socket = null;
let lockReconnet = false; // 避免重复连接
let wsUrl = '';
let isReconnet = false;
let globalCallback = null;
let retryTime = 10000;
let sendData = null; // 把要发送给socket的数据和处理socket返回数据的回调保存起来
let createSocket = (url, callback) => { // 创建socket
    try {
        if ('WebSocket' in window) {
            socket = new WebSocket(url)
        }
        wsUrl = url
        // Vue.prototype.socket = socket
        // 需要主动关闭的话就可以直接调用this.socket.close()进行关闭，不需要的话这个可以去掉
        initSocket(callback)
    } catch (e) {
        reconnet(url, callback)
    }
}
let sendMsg = (data, callback) => { // 发送数据,接收数据
  // console.log('发送token', socket.readyState)
    if (socket.readyState === 1) {
        globalCallback = callback;
        sendData = data;

        data = JSON.stringify(data);
        socket.send(data);
    } else {
      // console.log('等待等待')
        setTimeout(() => {
            // console.log(socket, '等待socket链接成功')
            sendMsg(data, callback)
        }, 1500)
        return false
    }
    socket.onmessage = ev => {
        callback && callback(ev)
    }
}
let initSocket = callback => { // 初始化websocket
    socket.onopen = () => {
        // console.log('socket连接成功')
        // 心跳检测
        // heartCheck.reset().start()
        // console.log('开启连接')
        if (isReconnet) { // 执行全局回调函数
            // console.log('websocket重新连接了')
            sendData && globalCallback && sendMsg(sendData, globalCallback)
            isReconnet = false
        }
        let data = {"token": (Cookies.get(config.cookieName) && JSON.parse(Cookies.get(config.cookieName)).accessToken) || ''};
        sendMsg(data, callback)
    }

    socket.onmessage = (ev) => {
        // console.log(ev, '连接正常', wsUrl)
        // console.log('callback is :', callback)
        callback && callback(ev)
        // 心跳检测
        // heartCheck.reset().start()
    }

    socket.onerror = () => {
        // console.log('websocket服务出错了---onerror');
        reconnet(wsUrl, callback)
    }

    socket.onclose = (ev) => {
        // console.log('websocket服务关闭了+++onclose');
        // reconnet(wsUrl)
        // console.log("onclose", ev);
        // console.log('连接关闭了', ev.code)
        retryTime = 10000
        if(ev.code === 1005) {
          // code 1005 后端验证无效token返回1005异常，两分钟后重试
          retryTime = 120000
        }
        if (ev.code !== 4500) {
            // 4500为服务端在打开多tab时主动关闭返回的编码
            reconnet(wsUrl, callback); // 重连
        }
    }
}
let reconnet = (url, callback) => { // 重新连接websock函数
    if (lockReconnet) {
        return false
    }
    // console.log('重试时间', retryTime)

    isReconnet = true;
    lockReconnet = true
    setTimeout(() => {
        socket.close(4500);
        createSocket(url, callback)
        lockReconnet = false
    }, retryTime)
}
let closeWS = (code) => {
    if(socket) {
        socket.close(code);
        // console.log('websocket关闭了')
    }
}
/* let heartCheck = { // 心跳检测
    timeout: 60 * 1000,
    timeoutObj: null,
    serverTimeoutObj: null,
    reset() {
        clearTimeout(this.timeoutObj)
        clearTimeout(this.serverTimeoutObj)
        return this;
    },
    start() {
        let that = this;
        this.timeoutObj = setTimeout(() => {
            // 发送数据，如果onmessage能接收到数据，表示连接正常,然后在onmessage里面执行reset方法清除定时器
            socket.send('heart check')
            this.serverTimeoutObj = setTimeout(() => {
                socket.close()
            }, that.timeout)
        }, this.timeout)
    }
} */
// createSocket(wsUrl)
export default { createSocket, sendMsg, closeWS }
