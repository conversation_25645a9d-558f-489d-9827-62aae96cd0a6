import { hasPermission } from '@/core/utils/permission.js'

/**
 * 权限控制自定义指令
 * 用法：v-permission="'权限码'"
 * 或者：v-permission="['权限码1', '权限码2']" (数组形式，满足其中一个权限即可显示)
 */
const permission = {
  inserted: (el, binding) => {
    const { value } = binding

    if (!value) {
      console.warn('v-permission 指令需要传入权限值')
      return
    }

    let hasAuth = false

    if (Array.isArray(value)) {
      // 如果传入的是数组，只要有一个权限匹配就显示
      hasAuth = value.some((perm) => hasPermission(perm))
    } else {
      // 如果传入的是字符串，直接检查权限
      hasAuth = hasPermission(value)
    }

    if (!hasAuth) {
      // 没有权限则移除元素
      el.parentNode && el.parentNode.removeChild(el)
    }
  },

  update: (el, binding) => {
    const { value } = binding

    if (!value) {
      return
    }

    let hasAuth = false

    if (Array.isArray(value)) {
      hasAuth = value.some((perm) => hasPermission(perm))
    } else {
      hasAuth = hasPermission(value)
    }

    if (!hasAuth) {
      // 没有权限则移除元素
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}

export { permission }
