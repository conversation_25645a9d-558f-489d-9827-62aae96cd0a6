<template>
  <el-dialog v-dialogDrag title="查看业务日志" :visible.sync="dialogMainFormVisible" width="800px" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <el-form ref="dataForm" :model="temp" label-position="right" label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="发件状态">
                <el-input v-model="temp.status" :disabled="true"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否发件异常">
                <el-input v-model="temp.excepFlag" :disabled="true"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="发件方式">
                <el-input v-model="temp.sendType" :disabled="true"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="消息接收人">
                <el-input v-model="temp.receiverName" :disabled="true"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="邮箱地址">
                <el-input v-model="temp.email" :disabled="true"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号">
                <el-input v-model="temp.mobile" :disabled="true"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="发件时间">
                <el-date-picker
                  value-format="yyyy/MM/dd hh:mm:ss"
                  v-model="temp.sendTime"
                  type="datetime"
                  :disabled="true">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="消息内容">
                <el-input v-model="temp.content" :disabled="true" type="textarea" :rows="4"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-row :span="24">
              <el-form-item label="发件异常信息">
                <el-input v-model="temp.excepMsg" :disabled="true" type="textarea" :rows="6"/>
              </el-form-item>
            </el-row>
          </el-row>
        </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button icon="uims-icon-cancel" @click="dialogMainFormVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {resourceCode} from "@/biz/http/settings";

  export default {
    name: "LogView",
    data() {
      return {
        resCode: undefined,
        temp: {
          mbId: undefined,
          sendType: undefined,
          receiverName: undefined,
          email: undefined,
          mobile: undefined,
          content: undefined,
          params: undefined,
          sendTime: undefined,
          status: undefined,
          excepFlag: undefined,
          excepMsg: undefined
        },
        dialogMainFormVisible: false
      }
    },
    props: {
      dialogFormVisible: Boolean,
      mhId: String
    },
    methods: {
      destrory() {
        this.temp = {
          mbId: undefined,
          sendType: undefined,
          receiverName: undefined,
          email: undefined,
          mobile: undefined,
          content: undefined,
          params: undefined,
          sendTime: undefined,
          status: undefined,
          excepFlag: undefined,
          excepMsg: undefined
        }
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          // this.resCode = resourceCode.log_view
          this.$api.msgBiz.getBizHistoryByMhId({mhId: this.mhId}, resourceCode.msgBiz).then((res) => {
            this.temp = res.data
          })
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
</style>
