<template>
  <div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="567px" @close="destoryDialog" @open="initDialog">
      <el-container>
        <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :rules="dialogStatus === 'view'? {} : rules" :model="temp" label-position="right" label-width="107px">
            <el-input v-model="temp.delegateId" :disabled="dialogDisabled" class="display" />
            <el-input v-model="temp.delegateUserId" :disabled="dialogDisabled" class="display" />
            <el-input v-model="temp.delegatedUserId" :disabled="dialogDisabled" class="display" />
            <el-input v-model="temp.systemId" :disabled="dialogDisabled" class="display" />
            <el-form-item v-show="temp.systemCode === 'uims'? true : false" label="委托系统">
              <el-input v-model="temp.sysName" :disabled="true" />
            </el-form-item>
            <el-form-item label="委托用户">
              <el-input v-model="temp.delegateUserName" :disabled="true" />
            </el-form-item>
            <el-form-item label="受托用户" prop="delegatedUserName">
              <el-input v-model="temp.delegatedUserName" :disabled="true" class="input-with-select">
                <perm-button slot="append" label="选择" type="primary" icon="uims-icon-select" @click="openSelectUser('delegated')" v-if="!dialogDisabled" />
              </el-input>
            </el-form-item>
            <el-form-item label="委托起止时间" prop="beginTimeToEndTime">
              <el-date-picker v-model="temp.beginTimeToEndTime" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :picker-options="expireTimeOption" :disabled="dialogDisabled">
              </el-date-picker>
            </el-form-item>
            <el-form-item style="height: 400px">
              <el-tabs v-model="activeTabName">
                <el-tab-pane label="资源权限" name="fir">
                  <el-tree v-loading="privData.resourceListLoading" :data="privData.resourceList" :props="privData.resourceDefaultProps" node-key='id' highlight-current :expand-on-click-node="false" show-checkbox :default-checked-keys="privData.resourceChecked" ref="resourceTree" />
                </el-tab-pane>
<!--                <el-tab-pane label="列数据权限" name="tw">-->
<!--                  <el-tree v-loading="privData.colListLoading" :data="privData.colList" :props="privData.colDefaultProps" node-key='privId' highlight-current default-expand-all :expand-on-click-node="false" show-checkbox :default-checked-keys="privData.colChecked" ref="colTree" />-->
<!--                </el-tab-pane>-->
<!--                <el-tab-pane label="行数据权限" name="thr">-->
<!--                  <el-tree v-loading="privData.rowListLoading" :data="privData.rowList" :props="privData.rowDefaultProps" node-key='privId' highlight-current default-expand-all :expand-on-click-node="false" show-checkbox :default-checked-keys="privData.rowChecked" ref="rowTree" />-->
<!--                </el-tab-pane>-->
              </el-tabs>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button icon="uims-icon-cancel" @click="dialogMainFormVisible = false">
          {{dialogStatus==='view'?'关闭':'取消'}}
        </el-button>
        <el-button class="save-btn" type="primary" icon="uims-icon-save" @click="saveDelegate()" v-if="!dialogDisabled" :loading="okLoading">
          发布委托
        </el-button>
      </div>
    </el-dialog>
    <select-user-dialog :selectMode="selectUser.selectModel" :dialogFormVisible="selectUser.dialogFormVisible" :resCode="resCode" @getTemp="getTemp" @closeDialog="closeDialog"></select-user-dialog>
  </div>
</template>

<script>
import SelectUserDialog from "./SelectUserDialog";
import PermButton from '@/core/components/PermButton'
import { resourceCode } from "@/biz/http/settings"

export default {
  name: "DelegateMainDialog",
  components: { SelectUserDialog, PermButton },
  data() {
    var checkUserNames = (rule, value, callback) => {
      if (this.temp.delegateUserId === this.temp.delegatedUserId) {
        callback(new Error('不能选择两个同样的用户'))
      } else {
        callback()
      }
    };
    var checkTime = (rule, value, callback) => {
      if(new Date() > new Date(this.temp.beginTimeToEndTime[1])) {
        callback(new Error('结束时间不能小于当前时间'))
      } else {
        callback()
      }
    }
    return {
      activeTabName: 'fir',
      privData: {
        roleId: undefined,
        resourceListLoading: true,
        colListLoading: true,
        rowListLoading: true,
        resourceList: [],
        colList: [],
        rowList: [],
        resourceChecked: [],
        colChecked: [],
        rowChecked: [],
        resourceDefaultProps: {
          children: 'children',
          label: function (data, node) {
            return node.data.data.resourceName
          }
        },
        colDefaultProps: {
          children: 'children',
          label: 'dataruleName'
        },
        rowDefaultProps: {
          children: 'children',
          label: 'dataruleName'
        }
      },
      expireTimeOption: {
        disabledDate(date) {
          var now = new Date();
          var ny = now.getFullYear();
          var nm = now.getMonth();
          var nd = now.getDate();
          var y = date.getFullYear();
          var m = date.getMonth();
          var d = date.getDate();
          if (ny === y && nm === m && nd === d) {
            return false;
          }
          return (date.getTime() <= Date.now());
        }
      },
      resCode: '',
      okLoading: false,
      textMap: {
        update: '编辑委托',
        create: '新增委托',
        view: '查看委托'
      },
      temp: {
        delegateId: undefined,
        delegateUserId: undefined,
        delegatedUserId: undefined,
        delegateUserName: undefined,
        delegatedUserName: undefined,
        systemId: undefined,
        systemCode: undefined,
        sysName: undefined,
        beginTime: undefined,
        endTime: undefined,
        beginTimeToEndTime: []
      },
      // 表单校验规则
      rules: {
        delegateUserName: [{ required: true, message: '请选择委托用户', trigger: 'blur' }],
        delegatedUserName: [
          { required: true, message: '请选择受托用户', trigger: 'blur' },
          { validator: checkUserNames, trigger: 'blur' }],
        beginTimeToEndTime: [{ required: true, message: '请选择委托起止时间', trigger: 'blur' }, {validator: checkTime, trigger: 'blur'}]
      },
      selectUser: {
        selectModel: undefined,
        selectTreeRow: undefined,
        dialogFormVisible: false,
        list: [],
        listLoading: true,
        defaultProps: {
          children: 'children',
          label: 'userName'
        }
      },
      dialogMainFormVisible: false
    }
  },
  props: {
    dialogStatus: String,
    dialogFormVisible: Boolean,
    delegateArray: Array,
    systemId: String,
    systemName: String,
    currentsyscode: String,
    dialogDisabled: Boolean
  },
  methods: {
    initDialog() {
      this.activeTabName = 'fir';
      this.privData.resourceListLoading = false;
      this.privData.colListLoading = false;
      this.privData.rowListLoading = false;
    },
    destoryDialog() {
      this.resetTemp();
      this.okLoading = false;
      this.privData.resourceList = [];
      this.privData.colList = [];
      this.privData.rowList = [];
      this.privData.resourceChecked = [];
      this.privData.colChecked = [];
      this.privData.rowChecked = [];
    },
    closeDialog(val) {
      this.selectUser.dialogFormVisible = val;
    },
    getTemp(val1, val2) {
      this.temp.delegatedUserId = val1;
      this.temp.delegatedUserName = val2;
    },
    loadPrivInfo() {
      this.privData.resourceListLoading = true;
      this.$api.userDelegatePriv.getResourceTree({ userId: this.temp.delegateUserId, systemId: this.temp.systemId, systemCode: this.temp.systemCode },
        resourceCode.userDelegatePriv).then((res) => {
          this.privData.resourceList = res.data;
          this.privData.resourceListLoading = false
        });
      this.privData.colListLoading = true;
      this.$api.userDelegatePriv.getDataruleColTree({ userId: this.temp.delegateUserId, systemId: this.temp.systemId, systemCode: this.temp.systemCode },
        resourceCode.userDelegatePriv).then((res) => {
          this.privData.colList = res.data;
          this.privData.colListLoading = false
        });
      this.privData.rowListLoading = true;
      this.$api.userDelegatePriv.getDataruleRowTree({ userId: this.temp.delegateUserId, systemId: this.temp.systemId, systemCode: this.temp.systemCode },
        resourceCode.userDelegatePriv).then((res) => {
          this.privData.rowList = res.data;
          this.privData.rowListLoading = false
        });
    },
    makeResourceInfo(res) {
      for (let i = 0; i < res.length; i++) {
        let hasChildren = false;
        if (res[i].children && res[i].children.length > 0) {
          hasChildren = true
        }
        if (!hasChildren) {
          if (res[i].data.privFlag === '1') {
            this.privData.resourceChecked.push(res[i].id)
          }
        }
        res[i].disabled = true;
        if (hasChildren) {
          this.makeResourceInfo(res[i].children)
        }
      }
    },
    loadPrivedInfoForView() {
      this.privData.resourceListLoading = true;
      this.$api.userDelegatePriv.getDelegatedResourceTree({
        delegateId: this.temp.delegateId,
        userId: this.temp.delegateUserId,
        systemId: this.temp.systemId,
        systemCode: this.temp.systemCode
      }, resourceCode.userDelegatePriv_view).then((res) => {
        if (res && res.data) {
          this.makeResourceInfo(res.data)
        }
        this.privData.resourceList = res.data;
        this.privData.resourceListLoading = false
      });
      this.privData.colListLoading = true;
      this.$api.userDelegatePriv.getDelegatedDataruleColTree({
        delegateId: this.temp.delegateId,
        userId: this.temp.delegateUserId,
        systemId: this.temp.systemId,
        systemCode: this.temp.systemCode
      }, resourceCode.userDelegatePriv_view).then((res) => {
        if (res && res.data) {
          for (let i = 0; i < res.data.length; i++) {
            res.data[i].disabled = true;
            if (res.data[i].privFlag === '1') {
              this.privData.colChecked.push(res.data[i].privId)
            }
          }
        }
        this.privData.colList = res.data;
        this.privData.colListLoading = false
      });
      this.privData.rowListLoading = true;
      this.$api.userDelegatePriv.getDelegatedDataruleRowTree({
        delegateId: this.temp.delegateId,
        userId: this.temp.delegateUserId,
        systemId: this.temp.systemId,
        systemCode: this.temp.systemCode
      }, resourceCode.userDelegatePriv_view).then((res) => {
        if (res && res.data) {
          for (let i = 0; i < res.data.length; i++) {
            res.data[i].disabled = true;
            if (res.data[i].privFlag === '1') {
              this.privData.rowChecked.push(res.data[i].privId)
            }
          }
        }
        this.privData.rowList = res.data;
        this.privData.rowListLoading = false
      })
    },
    openSelectUser(model) {
      this.selectUser.selectModel = model;
      this.selectUser.dialogFormVisible = true
      // this.selectUser.listLoading = true
    },
    saveDelegate() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let resourceNodes = this.$refs.resourceTree.getCheckedNodes(false);
          let resourceHalfNodes = this.$refs.resourceTree.getHalfCheckedNodes(false);
          resourceNodes = resourceNodes.concat(resourceHalfNodes);
          // const colKeys = this.$refs.colTree.getCheckedKeys(false);
          // const rowKeys = this.$refs.rowTree.getCheckedKeys(false);
          let resourceIds = '';
          let drcIds = '';
          let drrIds = '';
          if (resourceNodes && resourceNodes.length > 0) {
            resourceNodes.forEach(node => {
              resourceIds += node.data.privId + ','
            })
          }
          // if (colKeys && colKeys.length > 0) {
          //   colKeys.forEach(ids => {
          //     drcIds += ids + ','
          //   })
          // }
          // if (rowKeys && rowKeys.length > 0) {
          //   rowKeys.forEach(ids => {
          //     drrIds += ids + ','
          //   })
          // }
          let obj = {}; // Object.assign({}, this.temp)
          obj.delegateId = this.temp.delegateId;
          obj.delegateUserId = this.temp.delegateUserId;
          obj.delegatedUserId = this.temp.delegatedUserId;
          obj.beginTime = new Date(this.temp.beginTimeToEndTime[0]);
          obj.endTime = new Date(this.temp.beginTimeToEndTime[1]);
          obj.resourceIds = resourceIds;
          obj.drcIds = drcIds;
          obj.drrIds = drrIds;
          this.okLoading = true;
          this.$api.userDelegatePriv.save(obj, this.resCode).then((res) => {
            this.okLoading = false;
            this.dialogMainFormVisible = false;
            this.$emit("getList");
            this.$notify({
              title: '操作成功',
              message: '新增委托成功',
              type: 'success',
              duration: 2000
            })
          }).catch((res) => {
            this.okLoading = false
          })
        }
      })
    },
    resetTemp() {
      this.temp = {
        delegateId: undefined,
        delegatedUserId: undefined,
        delegatedUserName: undefined,
        beginTime: undefined,
        endTime: undefined,
        beginTimeToEndTime: []
      }
    },
    timeFormatter(val) {
      var date = new Date(val);
      return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
    }
  },
  watch: {
    dialogFormVisible: function (newValue, oldValue) {
      this.dialogMainFormVisible = newValue;
      if (newValue) {
        if (this.dialogStatus === 'create') {
          this.resCode = resourceCode.userDelegatePriv_add;
          this.resetTemp();
          this.$nextTick(() => {
            this.$refs['dataForm'].clearValidate()
          })
          let userID = JSON.parse(sessionStorage.getItem('user')).userId;
          let userName = JSON.parse(sessionStorage.getItem('user')).userName;
          this.temp.systemId = this.systemId;
          this.temp.sysName = this.systemName;
          this.temp.systemCode = this.currentsyscode;
          this.temp.delegateUserId = userID;
          this.temp.delegateUserName = userName;
          // 加载委托用户权限信息
          this.loadPrivInfo();
        } else if (this.dialogStatus === 'view') {
          this.resCode = resourceCode.userDelegatePriv_view;
          // this.temp = Object.assign({}, this.delegateArray[0]);
          this.$api.userDelegatePriv.getById({ delegateId: this.delegateArray[0].delegateId }, this.resCode).then(res => {
            this.temp = Object.assign({}, res.data);
            this.temp.beginTimeToEndTime = [this.temp.beginTime, this.temp.endTime];
            if (this.systemName) {
              this.temp.sysName = this.systemName;
            }
            this.temp.systemCode = this.currentsyscode;
            this.loadPrivedInfoForView()
          });
          this.$nextTick(() => {
            this.$refs['dataForm'].clearValidate()
          })
        }
      }
    },
    dialogMainFormVisible: function (newV, oldV) {
      this.$emit('closeDialog', newV)
    }
  }
}
</script>

<style scoped lang="scss">
.display {
  display: none;
}

>>> .el-form-item__content {
  height: 100%;

  .el-tabs.el-tabs--top {
    height: 100%;

    .el-tabs__content {
      height: calc(100% - 40px);
      overflow: auto;
    }
  }
}
</style>
