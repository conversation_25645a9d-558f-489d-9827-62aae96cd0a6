<template>
  <div>
    <DialogPlus v-dialogDrag :visible.sync="visible" :center="true" :autoHeight="true" @open="handleOpen" @close="handleClose" title="提出需求" width="60%" :destroy-on-close="true" :append-to-body="true">
      <el-form ref="formRef" label-position="top" size="small">
        <el-collapse v-model="status.colName">
          <el-collapse-item name="1">
            <template slot="title">
              <div class="titleInfo"><span></span> <span>基本信息</span></div>
            </template>
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="资源名称">
                  <el-radio v-model="status.templateSelection" label="1">数据需求</el-radio>
                  <el-radio v-model="status.templateSelection" label="2">能力需求</el-radio>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 数据需求 -->
            <template v-if="status.templateSelection === '1'">
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="需求名称">
                    <el-input clearable placeholder="请输入需求名称" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="应用场景/相关依据">
                    <el-input clearable placeholder="请输入应用场景/相关依据" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="用数系统">
                    <el-input clearable placeholder="请输入用数系统" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <div style="padding-top: 32px">
                    <perm-button label="新增" type="primary" icon="add" />
                  </div>
                </el-col></el-row>
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="需求方联系人">
                    <el-input clearable placeholder="请输入联系人" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="需求方联系电话">
                    <el-input clearable placeholder="请输入联系电话" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="需求方联系邮箱">
                    <el-input clearable placeholder="请输入联系邮箱" maxlength="20" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="10">
                  <el-form-item label="数据来源依据">
                    <el-input clearable placeholder="请输入数据来源依据" maxlength="200" type="textarea" :autosize="{ minRows: 4 }" />
                  </el-form-item>
                </el-col>
                <el-col :span="1"><span style="visibility: hidden">1</span></el-col>
                <el-col :span="10">
                  <el-form-item label="数据来源依据">
                    <el-input clearable placeholder="请输入数据来源依据" maxlength="200" type="textarea" :autosize="{ minRows: 4 }" />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <!-- 能力需求 -->
            <template v-else>
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="需求名称">
                    <el-input clearable placeholder="请输入需求名称" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="能力形态">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="赋能应用">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <div style="padding-top: 32px">
                    <perm-button label="新增" type="primary" icon="add" />
                  </div>
                </el-col></el-row>
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="需求方联系人">
                    <el-input clearable placeholder="请输入联系人" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="需求方联系电话">
                    <el-input clearable placeholder="请输入联系电话" maxlength="20" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="需求方联系邮箱">
                    <el-input clearable placeholder="请输入联系邮箱" maxlength="20" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="10">
                  <el-form-item label="数据来源依据">
                    <el-input clearable placeholder="请输入数据来源依据" maxlength="200" type="textarea" :autosize="{ minRows: 4 }" />
                  </el-form-item>
                </el-col>
                <el-col :span="1"><span style="visibility: hidden">1</span></el-col>
                <el-col :span="10">
                  <el-form-item label="应用场景/相关依据">
                    <el-input clearable placeholder="请输入数据来源依据" maxlength="200" type="textarea" :autosize="{ minRows: 4 }" />
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <div style="padding-top: 32px">
                    <perm-button label="上传" type="primary" icon="add" />
                  </div>
                </el-col>
              </el-row>
            </template>
          </el-collapse-item>
        </el-collapse>
        <!-- 数据需求 第二部分 -->
        <template v-if="status.templateSelection === '1'">
          <el-collapse v-model="status.colName2">
            <el-collapse-item name="1">
              <template slot="title">
                <div class="titleInfo">
                  <span></span> <span>所需资源目录信息</span>
                </div>
              </template>
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="数据提供部门">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="数据目录">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="数据名称">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="7">
                  <el-form-item label="数据提供方式">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="共享方式">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="更新周期">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="21">
                  <table-plus key="table" ref="table" :data="[]" border height="135" fit stripe highlight-current-row style="margin-top: 10px">
                    <el-table-column prop="resourceName" width="200" label="需求信息项名称" show-overflow-tooltip fixed="left"></el-table-column>
                    <el-table-column prop="applyOrgName" width="200" label="信息项数据类型" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="resourceType" width="100" label="资源类型" align="center" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="state" width="100" label="状态" align="center" show-overflow-tooltip fixed="right"></el-table-column>
                    <el-table-column prop="message" label="操作" align="center" show-overflow-tooltip fixed="right"></el-table-column>
                  </table-plus>
                </el-col>
              </el-row>
            </el-collapse-item>
          </el-collapse>
        </template>
        <!-- 能力需求 第二部分 -->
        <template v-else>
          <el-collapse v-model="status.colName2">
            <el-collapse-item name="1">
              <template slot="title">
                <div class="titleInfo"><span></span> <span>详细信息</span></div>
              </template>
              <el-row :gutter="24">
                <el-col :span="10">
                  <el-form-item label="输入参数">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :span="1"><span style="visibility: hidden">1</span></el-col>
                <el-col :span="10">
                  <el-form-item label="输出参数">
                    <SelectPlus style="width: 100%" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="21">
                  <el-form-item label="其他要求">
                    <el-input clearable placeholder="请输入数据来源依据" maxlength="200" type="textarea" :autosize="{ minRows: 4 }" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
          </el-collapse>
        </template>
      </el-form>
      <template #footer>
        <el-button type="primary" size="mini">保存</el-button>
        <el-button type="primary" size="mini">保存并提交</el-button>
        <el-button class="close-btn" size="mini">关闭</el-button>
      </template>
    </DialogPlus>
  </div>
</template>

<script>
import { computed, defineComponent, reactive, getCurrentInstance, ref, nextTick } from 'vue'

import DialogPlus from '@/core/components/DialogPlus'
import PermButton from '@/core/components/PermButton'
import SelectPlus from '@/core/components/SelectPlus'
import TablePlus from '@/core/components/TablePlus'

export default defineComponent({
  name: 'DemandDialog',
  components: {
    DialogPlus,
    PermButton,
    SelectPlus,
    TablePlus
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowInfo: {
      type: Object
    }
  },
  setup(props, { emit }) {
    const { proxy } = getCurrentInstance()
    const permissionTableRef = ref()
    const valueTitle = ref('')
    const valueLabel = ref('')
    const visible = computed({
      get: () => props.visible,
      set: (val) => {
        emit('update:visible', val)
      }
    })

    const status = reactive({
      colName: '1',
      colName2: '1',
      templateSelection: '1',
      loading: false,
      options: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      total: 0,
      uploadLoading: false,
      treeList: [],
      list: [],
      selectTableRow: [],
      model: '异议提出',
      filters: {
        currentPage: 1,
        pageSize: 20,
        unit: '',
        filePath: '',
        fileName: '',
        fileType: '',
        fileSize: '',
        realName: ''
      }
    })
    const handleOpen = () => {}
    const handleClose = () => {
      visible.value = false
    }

    return {
      status,
      visible,
      handleOpen,
      handleClose
    }
  }
})
</script>

<style scoped lang="scss">
.titleInfo {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;

  span:nth-child(1) {
    display: inline-block;
    width: 6px;
    height: 14px;
    opacity: 1;
    border-radius: 15px;
    background: #0084ff;
    margin-right: 8px;
  }
}

/deep/ .el-dialog {
  margin-top: 3vh !important;
  height: auto !important;
}

/deep/ .el-dialog__header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-size: 20px;
  font-weight: bold;
}

/deep/ .el-form-item__label {
  padding: 0;
}

/deep/ .el-form-item {
  margin-bottom: 6px;
}
/deep/.el-table th.el-table__cell {
  background-color: #4EACFE;
  padding: 12px 0;
  .cell {
    background-color: #4EACFE;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color:#f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
