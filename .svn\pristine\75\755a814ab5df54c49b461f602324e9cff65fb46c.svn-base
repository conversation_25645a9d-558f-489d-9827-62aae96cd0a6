export class FileUtils {
  static fileDownload(data, fileName) {
    const uri = window.URL.createObjectURL(new Blob(data))
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = uri
    document.body.appendChild(link)
    link.setAttribute('download', fileName)
    link.target = '_blank'
    link.click()
    document.body.removeChild(link) // 下载完成移除元素
    window.URL.revokeObjectURL(uri) // 释放掉blob对象
  }
}
