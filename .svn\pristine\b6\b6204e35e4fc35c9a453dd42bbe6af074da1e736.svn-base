<template>
  <el-dialog v-dialogDrag title="选择收件人" :visible.sync="dialogMainFormVisible" @close="destrory()" @opened="initDialog">
    <el-container>

      <el-main style="box-shadow: none;overflow: hidden">
        <div style="display: flex;margin-bottom: 20px" v-if="isHadSpace()">
          <div style="line-height: 40px;white-space: nowrap">空间：</div>

          <el-select @change="spaceChange" v-model="spaceSelect" style="width: 100%;margin-right: 20px" multiple
                     placeholder="请选择">
            <el-option
              v-for="item in spaceList"
              :key="item"
              :label="item"
              :value="item">
            </el-option>
          </el-select>
          <el-button type="primary" @click="selectAllUser">全选用户</el-button>
        </div>

        <div class="tree-wrapper query list" style="height: 100%;margin-right: 15px;">
          <el-input v-model=filterText
                    placeholder="输入关键字进行过滤">
          </el-input>
          <el-scrollbar wrap-class="scrollbar-wrapper" style="height: calc(100% - 42px);border: 1px solid #dcdfe6;
    border-top: none;">
            <el-tree
              v-loading="treeLoading"
              :data="treeList"
              show-checkbox
              node-key="id"
              highlight-current-row
              stripe
              :props="defaultProps"
              ref="tree">
              <span slot-scope="{ node, data }" :title="node.label" class="treeLabel">
                {{node.label}}
              </span>
            </el-tree>
          </el-scrollbar>
        </div>
      </el-main>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">取消</el-button>
      <el-button type="primary" @click="selectUserOk()">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// import CircularJSON from 'circular-json'
let CircularJSON = require('circular-json-es6')
export default {
  name: "SelectUserDialog",
  data() {
    return {
      filterText: '',
      List: [],
      treeList: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      dialogMainFormVisible: false,
      spaceSelect: [],
      filterTree: [],
      treeLoading: true
    }
  },
  props: {
    resCode: String,
    dialogSelectUser: Boolean,
    spaceList: Array
  },
  methods: {
    // 全选
    selectAllUser() {
      const rootData = this.$refs.tree.root
      if (rootData.visible) {
        const childNodesStr = CircularJSON.stringify(rootData.childNodes)
        const childNodes = CircularJSON.parse(childNodesStr)
        this.filterTree = this.recursionNodes(childNodes)
        this.$refs.tree.setCheckedKeys(this.filterTree, true)
      } else {
        this.$refs.tree.setCheckedKeys([], true)
      }
    },
    /**
     * @description: 递归遍历数据
     * @param {array} nodes
     */
    recursionNodes(childNodes) {
      const nodes = childNodes
      let result = []
      for (const item of nodes) {
        if (item.visible && item.isLeaf) {
          result.push(item.data.id)
        }
        if (item.childNodes && item.childNodes.length) {
          const tempResult = this.recursionNodes(item.childNodes)
          result = result.concat(tempResult)
        }
      }
      return result
    },
    // 空间筛选
    spaceChange(val) {
      this.$refs.tree.filter({
        keyword: this.filterText,
        space: val
      });
    },
    selectUserOk() {
      // 接收两个 boolean 类型的参数，1. 是否只是叶子节点，默认值为 false 2. 是否包含半选节点，默认值为 false
      let receiver = this.$refs.tree.getCheckedNodes(true, false)
      let receiverList = [];
      let receiver2 = []
      if(this.filterTree && this.filterTree.length > 0) {
        this.filterTree.forEach(f => {
          let index = receiver.findIndex(r => r.id === f || r.id === f.id)
          if(index > -1) {
            receiver2.push(receiver[index])
          }
        })
        receiver = receiver2
      }
      for (let tem in receiver) {
        let temp = {}; // 输出API选项
        temp.code = receiver[tem].id;
        temp.name = receiver[tem].label;
        receiverList.push(temp);
      }
      if(receiverList.length) {
        this.$emit("selectUserRetr", receiverList)
        this.dialogMainFormVisible = false
      }else{
        this.$notify({
          title: '提示',
          message: '请选择收件人',
          type: 'success',
          duration: 2000
        })
      }
    },
    destrory() {
      this.List = []
      this.treeList = []
      this.filterText = ""
      this.dialogMainFormVisible = false
    },
    filterNode(value, data) {
      // this.$refs.tree.setCheckedKeys([], true)
      // this.filterTree = []
      // let mzIndex = 0
      // if (data.spaceName && data.spaceName.length && value.space && value.space.length) {
      //   value.space.forEach(e => {
      //     for(let i = 0; i < data.spaceName.length; i++) {
      //       if(e === data.spaceName[i]) {
      //         mzIndex++;
      //         break;
      //       }
      //     }
      //   })
      // }
      // if (value.space && value.space.length) {
      //   let res = ((data.label.toUpperCase().indexOf(value.keyword.toUpperCase()) !== -1) && (mzIndex === value.space.length));
      //   if (res) this.filterTree.push(data)
      //   return res
      // } else {
      //   let res = ((data.label.toUpperCase().indexOf(value.keyword.toUpperCase()) !== -1));
        // if (res) this.filterTree.push(data)
        // return res
      // }
    },
    // 判断是否存在空间
    isHadSpace() {
      let res = false;
      if (this.$store.state && this.$store.state.system && this.$store.state.system.spaceId) {
        res = true
      }
      return res;
    },
    initDialog() {
      this.getMsgReceiverGroup()
    },
    // 获取用户组及用户信息
    getMsgReceiverGroup() {
      this.$api.msgOpen.getMsgReceiverGroup({systemCode: config.systemCode}, this.resCode).then((res) => {
        let userGroupInfo = res.data
        for (let temp in userGroupInfo) {
          let json = {}
          json.id = temp
          json.label = temp
          if (userGroupInfo[temp] && userGroupInfo[temp].length > 0) {
            let userArray = []
            for (let userInfo in userGroupInfo[temp]) {
              let userJson = {}
              userJson.id = userGroupInfo[temp][userInfo].userId
              userJson.label = userGroupInfo[temp][userInfo].realName + "(" + userGroupInfo[temp][userInfo].userName + ")"
              userJson.spaceName = userGroupInfo[temp][userInfo].spaceName
              userArray.push(userJson)
            }
            json.children = userArray
          } else {
            json.disabled = "disabled"
            json.children = "isDisabled"
          }
          this.List.push(json)
        }
        this.treeList = this.List
        this.treeLoading = false
      }).catch(e => {
        this.treeLoading = false
      })
    }
  },
  watch: {
    filterText(val) {
      this.filterTree = []
      if(val) {
        this.treeList = []
        this.List.forEach(e => {
          let children = []
          if(e.children) {
            e.children.forEach(c => {
              if((c.label.toUpperCase().indexOf(val.toUpperCase()) !== -1)) {
                children.push(c)
              }
            })
          }
          if(children && children.length > 0) {
            let ee = Object.assign({}, e)
            ee.children = children
            this.treeList.push(ee)
          }
        })
      } else {
        this.treeList = this.List
      }
      // this.$refs.tree.filter({
      //   keyword: val,
      //   space: this.spaceSelect
      // });
    },
    dialogSelectUser: function (newValue, oldValue) {
      this.dialogMainFormVisible = newValue
      this.treeLoading = true
    },
    dialogMainFormVisible: function (newV, oldV) {
      this.$emit('closeSelectUserDialog', newV)
    }
  }
}
</script>

<style scoped>
.treeLabel {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
  display: inline-block;
}
</style>
