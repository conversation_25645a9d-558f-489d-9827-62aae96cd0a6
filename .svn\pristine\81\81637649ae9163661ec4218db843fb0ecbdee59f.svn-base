<template>
  <el-container class="page-container">
    <!--      <el-main style="padding-left: 0 !important;">-->
    <!-- <div class="searchCont">
          <div class="searchList">
            <h5>范围</h5>
            <div class="searchInp">
              <el-form :inline="true" :model="filters">
                <el-form-item label="目录名称或标签关键字">
                  <el-input v-model.trim="filters.name" clearable placeholder="请输入目录名称或标签关键字" @change="handleFilter" maxlength="20" />
                </el-form-item>
                <el-form-item label="来源系统">
                  <el-input v-model.trim="filters.systemName" clearable placeholder="请输入来源系统" @change="handleFilter" maxlength="20" />
                </el-form-item>
                <el-form-item label="提供单位" style="margin-left: 30px;">
                  <el-cascader clearable :filterable="true" :options="options" :props="{ checkStrictly: true }" v-model="orgIds" @change="handleOrgId" :value="orgIds" style="width: 100%">
                    <template slot-scope="{ node, data }">
                      <div class="nowrap" :title="data.label">{{ data.label }}</div>
                    </template>
</el-cascader>
</el-form-item>
</el-form>
</div>
</div>
<div class="searchList">
  <h5>更多筛选</h5>
  <div class="searchInp">
    <el-form :inline="true" :model="filters">
      <el-form-item label="">
        <SelectPlus collapseTags multiple dictType="CATALOG_TYPE" mode="gxsl" v-model="filters.catalogType"
          @input="handleFilter" clearable placeholder="数据目录类型" />
      </el-form-item>
      <el-form-item label="">
        <SelectPlus collapseTags multiple dictType="SHARD_TYPE" mode="gxsl" v-model="filters.shareType"
          @input="handleFilter" clearable placeholder="共享类型" />
      </el-form-item>
      <el-form-item label="">
        <SelectPlus collapseTags multiple dictType="ZYML_CATALOG_UPDATE_CYCLE" mode="gxsl" v-model="filters.updateCycle"
          placeholder="更新周期" @input="handleFilter" clearable></SelectPlus>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker value-format="yyyy-MM-dd" v-model="filters.dateRange" type="daterange" @change="handleFilter"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;">
        </el-date-picker>
      </el-form-item>
    </el-form>
  </div>
</div>
<div class="searchList">
  <h5>资源类型</h5>
  <div class="searchInp" style="display:flex;">
    <CheckPlus dictType="ZYML_RESOURCE_TYPE" mode="gxsl" v-model="checkListresFormatType" :filterType="'resFormatType'"
      :num="'1'" @change="handleChecked" ref="resFormatType"></CheckPlus>
  </div>
</div>
</div> -->
    <div class="dataCont">
      <div class="topSort">
        <el-checkbox v-model="filters.onlyShowNotOrder" @change="changeListStatus" :true-label="1" :false-label="0">
          仅显示未订阅</el-checkbox>
        <div class="sort">
          <div v-for="(item, index) in sortItems" :key="index">
            <span class="iconLabel" :class="{ 'active': item.active }" @click="sort(item)">
              {{ item.label }}
            </span>
            <span v-if="item.sort" class="iconList">
              <i class="el-icon-caret-top" :class="{ 'active': item.order === 1 }" @click="orderChange(item, 1)"></i>
              <i class="el-icon-caret-bottom" :class="{ 'active': item.order === -1 }"
                @click="orderChange(item, -1)"></i>
            </span>
          </div>
        </div>
      </div>
      <div class="borderSplit"></div>
      <div class="listCont" v-loading="listLoading">
        <div class="listItem" v-for="item in dataList" :key="item.id" @click="handleCheckDetail(item)">
          <div class="line1">
            <div class="title">
              <div class="ellis" :title="item.directoryName">{{ item.directoryName }}</div><span
                style="width: 23px; margin-left: 4px">
                <img v-if="item.rangeCode === '1'" src="~@/assets/img/icon-withOut.svg" alt="">
                <img v-else src="~@/assets/img/icon-withIn.svg" alt="">
              </span> <!-- 目录标识 -->
              <div v-if="item.catalogTypeCode && item.catalogTypeCode === '1'" class="info-list">政务信息目录</div>
              <div v-if="item.catalogTypeCode && item.catalogTypeCode === '2'" class="data-list">政务数据目录</div>
              <div v-if="item.catalogTypeCode && item.catalogTypeCode === '3'" class="huiliu-list">回流数据目录</div>
              <span class="info-list" v-if="item.isExistOrder && item.isExistOrder === '1'">已订阅</span>
            </div>
            <div class="itemStatus">
              <span class="info-tip" v-if="item.shardType && item.shardType !== ''">{{ item.shardType }}</span>
              <span class="info-tip" v-if="item.openType && item.openType !== ''">{{ item.openType }}</span>
            </div>
          </div>
          <div class="line2">
            <div class="itemMes">
              <span class="message">提供单位：<span>{{ item.providerDeptName }}</span></span>
              <span class="message">更新周期：<span>{{ item.updateCycle }}</span></span>
              <span class="message">发布时间：<span>{{ item.publishTime }}</span></span>
              <br>
              <span class="message">来源系统：<span>{{ item.systemName }}</span></span>
              <span class="message">更新时间：<span>{{ item.lastUpdatedTime }}</span></span>
              <span class="message" v-if="item.rowChangeTime">业务数据更新时间：<span>{{ item.rowChangeTime }}</span></span>
              <span class="message" v-if="item.labels">标签：<span>{{ item.labels }}</span>
              </span>
            </div>
            <!-- 右侧资源类型 -->
            <div class="right-info">
              <!-- 非回流数据目录 -->
              <template v-if="item.catalogTypeCode !== '3'">
                <div class="type-info" :class="{ 'active': item.resourceType && item.resourceType.indexOf('1') > -1 }">
                  <svg-icon icon-class='gxsl-kubiao' />
                  <p>库表</p>
                </div>
                <div class="type-info" :class="{ 'active': item.resourceType && item.resourceType.indexOf('2') > -1 }">
                  <svg-icon icon-class='gxsl-wenjian' />
                  <p>文件</p>
                </div>
                <div class="type-info" :class="{ 'active': item.resourceType && item.resourceType.indexOf('3') > -1 }">
                  <svg-icon icon-class='gxsl-jiekou' />
                  <p>接口</p>
                </div>
                <div class="type-info" :class="{ 'active': item.resourceType && item.resourceType.indexOf('5') > -1 }">
                  <svg-icon icon-class='gxsl-wenjianjia' />
                  <p>文件夹</p>
                </div>
              </template>
              <!-- 回流数据目录 -->
              <template v-else>
                <div class="type-info" :class="{ 'active': item.resourceType && item.resourceType.indexOf('4') > -1 }">
                  <svg-icon icon-class='gxsl-huiliu' />
                  <p>回流</p>
                </div>
              </template>
            </div>
          </div>
          <div class="line3">
            <div class="itemNum">
              <span class="num">{{ !!item.orderSum ? item.orderSum : 0 }}次<span>订阅</span></span>
              <span class="num">{{ !!item.browseSum ? item.browseSum : 0 }}次<span>访问</span></span>
              <span class="num">{{ !!item.collectSum ? item.collectSum : 0 }}次<span>收藏</span></span>
            </div>
            <div class="itemHandleBtn">
              <el-button class="icsp-button-grey" disabled
                v-if="currentUser && item.providerCode === currentUser.unitCode">
                <svg-icon icon-class="gxsl-apply-btn" />
                订阅
              </el-button>
              <el-button class="icsp-button-grey" v-else-if="!item.resourceType" :disabled="!item.resourceType">
                <svg-icon icon-class="gxsl-apply-btn" />
                订阅
              </el-button>
              <el-button v-else class="icsp-button" plain @click.stop="handleCataApply(item)">
                <svg-icon icon-class="gxsl-apply-btn" />
                订阅
              </el-button>

              <el-button class="icsp-button-grey" disabled
                v-if="currentUser && item.providerCode === currentUser.unitCode">
                <svg-icon icon-class="gxsl-shoppingCart" />
                加入选数车
              </el-button>
              <el-button class="icsp-button-grey" :disabled="!item.resourceType" v-else-if="!item.resourceType">
                <svg-icon icon-class="gxsl-shoppingCart" />
                {{ item.addSelect === '1' ? "取消加入选数车" : "加入选数车" }}
              </el-button>
              <el-button v-else :disabled="queryBt" :class="item.addSelect === '1' ? 'icsp-button2' : 'icsp-button'"
                plain @click.stop="
                  handleAddPowerToCart(item)
                  ">
                <svg-icon icon-class="gxsl-shoppingCart" />
                {{ item.addSelect === '1' ? "取消加入选数车" : "加入选数车" }}
              </el-button>

              <el-button class="icsp-button-grey" disabled
                v-if="currentUser && item.providerCode === currentUser.unitCode">
                <svg-icon icon-class="gxsl-collect" />
                收藏
              </el-button>
              <el-button v-else :disabled="queryBt" :class="item.collect ? 'icsp-button2' : 'icsp-button'" plain
                @click.stop="
                  handleCollect(item)
                  ">
                <svg-icon icon-class="gxsl-collect" />
                {{ item.collect ? "取消收藏" : "收藏" }}
              </el-button>

            </div>
          </div>
        </div>
      </div>
      <el-empty v-if="!listLoading && dataList.length === 0" description="暂无数据"></el-empty>
      <div class="icsp-pagination-wrap">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="filters.currentPage" :page-size="filters.pageSize"
          layout="total, prev, pager, next, sizes, jumper" :total="total"></el-pagination>
      </div>

    </div>

    <!-- 数据订阅 -->
    <SubscribeDialog :dialogVisible.sync="dialogVisibleApply" :type="type" :title="title" :data="dialogData">
    </SubscribeDialog>
    <ToolBar @onRefreshList="getList" />
    <!--      </el-main>-->
  </el-container>
</template>

<script>
import ToolBar from '@/biz/components/common/toolbar'
import PermButton from '@/core/components/PermButton'
import SelectExtend from '@/core/components/SelectExtend'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SplitBar from '@/core/components/SplitBar'
import SelectPlus from '@/core/components/SelectPlus/index.vue'
import CheckPlus from '@/biz/components/common/checkPlus'
import SubscribeDialog from './SubscribeDialog'
import Cookies from 'js-cookie'
export default {
  name: 'polyMonitor',
  components: {
    ToolBar,
    SelectPlus,
    CheckPlus,
    SplitBar,
    SelectExtend,
    PermButton,
    TableFooter,
    SubscribeDialog
  },
  data() {
    return {
      queryBt: false,
      checkAll: false,
      isIndeterminate: false,
      showList: true,
      type: undefined,
      title: '',
      dialogData: undefined,
      dialogVisibleApply: false,
      collectVisible: false,
      demandVisible: false,
      applyVisible: false,
      selectNumVisible: false,
      currentUser: sessionStorage.getItem('user') ? JSON.parse(sessionStorage.getItem('user')) : null,
      queryParamsList: [],
      options: [], // 所属部门
      orgIds: [],
      checkList: [
        {
          checkName: '一般',
          checkValue: 1
        },
        {
          checkName: '重要',
          checkValue: 2
        }
      ],
      checkOption: ['一般', '重要'],
      sortItems: [
        {
          label: '订阅量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 1
        },
        {
          label: '访问量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 2
        },
        {
          label: '收藏量',
          sort: true,
          order: 0,
          active: false,
          sortMode: 8
        },
        {
          label: '更新时间',
          sort: true,
          order: 0,
          active: false,
          sortMode: 4
        }
      ],
      state: {
        activeItem: null
      },
      // table参数
      size: 'mini',
      filterText: '',
      powerTreeData: {
        allNum: 0,
        allNumIn: 0,
        allNumOut: 0,
        treeDataIn: [],
        treeDataOut: []
      },
      dataList: [],
      treeDataIn: [],
      treeDataOut: [],
      defaultProps: {
        children: 'childList',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      listLoading: false,
      total: 0,
      currentRow: undefined,
      checkListresFormatType: [],
      filters: {
        dateRange: [],
        name: '',
        systemName: '',
        orgId: '',
        currentPage: 1,
        baseType: '',
        baseTypeInfo: '',
        onlyShowNotOrder: 0,
        pageSize: 10,
        sortMode: '',
        sortType: 'desc',
        catalogType: '1,2,3',
        updateCycle: '',
        shareType: '',
        resFormatType: undefined
      }
    }
  },
  methods: {
    handleChecked(val, type) {
      this.filters[type] = val
      this.handleFilter()
    },
    // 是否是登录用户
    isLogOn() {
      // 登陆判断
      if (!Cookies.get(config.cookieName)) {
        this.$confirm('未登录或登录失效，请先登录', '提示', {
          confirmButtonText: '前往登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const href = window.location.href
          let url = new URL(href)
          if (url.href === window.top.location.href) {
            this.$router.push('/login')
          } else {
            let params = {
              // 打开窗口类型 根据门户定义规则
              IGDPType: 'IGDP_CUR_WINDOW',
              // / 消息接受地址 根据门户定义规则
              IGDPUrl: '/login'
            }
            window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
          }
        })
        // return false
      } else {
        return true
      }
    },
    // 改变所属部门
    handleOrgId(val) {
      // console.log(val, 'val')
      if (val.length === 1) {
        this.filters.orgId = val[0]
      } else {
        this.filters.orgId = val[val.length - 1]
      }
      this.getList()
    },
    circle(data) {
      data.forEach((i) => {
        i.value = i.id
        i.label = i.name
        if (i.children && i.children.length > 0) {
          this.circle(i.children)
        }
      })
    },
    // 获取所属部门
    getOrgId() {
      this.$api.bizApi.common
        .getUnitsByUnitCode()
        .then((res) => {
          if (res.code === '200') {
            let a = res.data || []
            this.circle(a)
            this.options = a
            // 如果route中含有提供单位，需要反显选中
            if (this.filters.orgId) {
              let ary = this.getParentsById(this.options, this.filters.orgId)
              // console.log('ary' + ary)
              this.orgIds = ary
            }
          }
        })
        .catch((e) => {
          this.$message({
            message: e.message
          })
        })
    },
    // el-cascader数据提供部门多级回显
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i].id === id) {
          // 查询到就返回该数组对象的value
          return [list[i].id]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          // console.log('node' + node)
          if (node !== undefined) {
            // 如能查询到把父节点加到数组前面
            node.unshift(list[i].id)
            return node
          }
        }
      }
    },
    handleCheckAllChange(val) {
      console.log(val)
      this.filters.checkList = val ? this.checkOption : []
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      let checkedCount = val.length
      this.checkAll = checkedCount === this.checkOption.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkOption.length
    },
    handleNodeClick(data) {
      if (data.childList && data.childList.length) {
        if (data.type) {
          this.type = data.type
        }
        if (this.filters.baseType === data.id) {
          this.filters.baseType = undefined
          this.$refs.treeIn.setCurrentKey(null)
        } else {
          this.filters.baseType = data.id
          this.filters.baseTypeInfo = data.type ? data.type : this.type

          this.getList(data.rangeFlag)
        }
        const params = {
          resNameLike: '',
          labelName: '',
          showExistNode: '',
          type: data.type ? data.type : this.type,
          baseTypeId: data.id,
          rangeFlag: data.rangeFlag,
          ...this.filters
        }

        this.$api.bizApi.pageRequest.getBaseTypeTree(params).then((res) => {
          data.childList = res.data || []
        })
      } else {
        console.log(this.type, 'this.type', data.id, 'data.id')
        this.filters.baseType = data.id
        this.filters.baseTypeInfo = data.type ? data.type : this.type
        this.getList(data.rangeFlag)
        const params = {
          resNameLike: '',
          labelName: '',
          showExistNode: '',
          type: data.type ? data.type : this.type,
          baseTypeId: data.id,
          rangeFlag: data.rangeFlag,
          ...this.filters
        }

        this.$api.bizApi.pageRequest.getBaseTypeTree(params).then((res) => {
          data.childList = res.data || []
        })
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.includes(value)
    },
    sort(item) {
      if (!item.sort) return
      this.$nextTick(() => {
        if (this.state.activeItem !== item) {
          this.sortItems.forEach((_item) => {
            _item.active = false
            if (_item.sort) {
              _item.order = 0
            }
          })
          this.state.activeItem = item
          item.active = true
          item.order = 1 // 默认升序排列
        } else {
          item.order = -item.order
        }
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    orderChange(item, order) {
      this.$nextTick(() => {
        if (!item.sort) return
        item.order = order
        console.log(item)
        this.filters.sortMode = item.sortMode
        this.filters.sortType = item.order > 0 ? 'asc' : 'desc'
        this.handleFilter()
      })
    },
    changeListStatus(val) {
      this.handleFilter()
    },
    handleFilter() {
      this.filters.currentPage = 1
      this.getList()
    },
    getList(rangeFlag) {
      let param = this.$route.query
      if (param.portalSearchText) {
        this.filters.name = param.portalSearchText
      }
      if (param.systemName) {
        this.filters.systemName = param.systemName
      }
      this.listLoading = true
      const params = {
        ...this.filters,
        rangeFlag: rangeFlag,
        publishTimeStart: this.filters.dateRange?.length
          ? this.filters.dateRange[0] + ' 00:00:00'
          : undefined,
        publishTimeEnd: this.filters.dateRange?.length
          ? this.filters.dateRange[1] + ' 23:59:59'
          : undefined
      }
      this.$api.bizApi.pageRequest
        .getCatalogPage(params)
        .then((res) => {
          this.listLoading = false
          this.dataList = res.data ? res.data.records : []
          this.total = res.data ? res.data.total : 0
        })
        .catch((res) => {
          this.listLoading = false
        })
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    handlePowerSceneCode(val) {
      switch (val) {
        case '1':
          return '常用能力'
        case '2':
          return '可信授权'
        case '3':
          return '数据服务'
        case '4':
          return '数据安全'
        case '5':
          return '智能推送'
        case '6':
          return '一件事一次办'
        case '7':
          return 'WEB端'
        default:
          break
      }
    },
    // 查看详情
    handleCheckDetail(data) {
      const href = window.location.href
      let url = new URL(href)
      if (url.href === window.top.location.href) {
        let routeUrl = this.$router.resolve({
          path: '/detailCatalog/index',
          query: {
            cataId: data.cataId
          }
        })
        window.open(routeUrl.href, '_blank')
      } else {
        let params = {
          // 打开窗口类型 根据门户定义规则
          IGDPType: 'IGDP_OPEN_WINDOW',
          // / 消息接受地址 根据门户定义规则
          IGDPUrl: '/portal/market/dataDir/detail',
          // 普通方式传参数(参数在ur 后面) 的
          defaultParams: {
            cataId: data.cataId
          }
        }
        window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
      }
    },
    // 能力收藏
    handleCollect(data) {
      if (!data.collect) {
        this.queryBt = true
        this.$api.bizApi.pageRequest
          .addCatalogToFavorites({ cataId: data.cataId })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('收藏成功')
              // this.handleFilter()
              this.$set(data, 'collect', true)
              this.$set(data, 'collectSum', data.collectSum + 1)
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false
          })
      } else {
        this.$confirm('是否取消收藏该数据目录?', '取消收藏', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .cancelCatalogToFavorites({ cataId: data.cataId })
            .then((res) => {
              if (res.code === '200') {
                // this.handleFilter()
                this.$message({
                  type: 'success',
                  message: '取消收藏成功!'
                })
                this.$set(data, 'collect', false)
                this.$set(data, 'collectSum', data.collectSum - 1)
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    },
    // 目录订阅
    handleCataApply(data) {
      if (this.isLogOn()) {
        this.type = 'singeApply'
        this.title = '数据资源订阅'
        this.dialogData = data
        this.dialogVisibleApply = true
        this.dialogData.cataId = data.cataId
      }
    },
    // 加入/取消选数车
    handleAddPowerToCart(data, index) {
      if (!sessionStorage.getItem('user')) return
      console.log(sessionStorage.getItem('user'), 'ss')
      // const { userId, unitId, unitCode } = JSON.parse(sessionStorage.getItem('user'))
      if (data.addSelect !== '1') {
        this.queryBt = true
        this.$api.bizApi.pageRequest
          .addCatalogToCart({ cataId: data.cataId })
          .then((res) => {
            if (res.code === '200') {
              this.$message.success('加入选数车成功')
              this.handleFilter()
              window.refreshCountNum()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch((res) => {
            this.$message.error(res)
          })
          .finally(() => {
            this.queryBt = false;
          });
      } else {
        this.$confirm('是否从选数车中取消该数据目录?', '取消确定', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.bizApi.pageRequest
            .delCatalogFromCart({
              cataId: data.cataId // ,
              // userId,
              // unitId,
              // unitCode
            })
            .then((res) => {
              if (res.code === '200') {
                this.handleFilter()
                this.$message.success('取消成功!')
                window.refreshCountNum()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch((e) => {
              this.$message.error(e)
            })
        })
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeIn.filter(val)
      this.$refs.treeOut.filter(val)
    },
    $route: {
      handler(newVal, oldVal) {
        console.log(newVal, 'newValWatch1')
        console.log(newVal.path.includes('/search'), '是否')
        if (newVal.path.includes('/search')) {
          this.getList()
          document.documentElement.scrollTop = 0
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getOrgId()
    this.getList()
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/global.scss';
@import '@/assets/list.scss';

.page-container {
  margin-left: 0 !important;

  .title {
    display: flex;
    align-items: center;

    .ellis {
      max-width: 660px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

}

.data-list {
  margin-left: 10px;
  font-size: 16px;
  padding: 0 4px;
  border-radius: 4px;
  font-weight: 300;
  text-align: center;
  color: #fff;
  background-color: #ff8635;
}

.huiliu-list {
  @extend .data-list;
  background-color: #83c954;
}

.info-list {
  @extend .data-list;
  background-color: #3572ff;
}
</style>
