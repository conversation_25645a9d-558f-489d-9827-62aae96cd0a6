<template>
  <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="800px" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <el-form ref="dataForm" :model="temp" label-position="right" label-width="100px"
                 class="flow">
          <el-input v-model="temp.logId" class="display"/>
          <div class="div">
            <el-form-item label="系统名称">
              <el-input v-model="sysName" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="所属机构">
              <el-input v-model="temp.unitName" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="服务名称">
              <el-input v-model="temp.serviceId" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="服务IP">
              <el-input v-model="temp.serverIp" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="操作IP">
              <el-input v-model="temp.remoteIp" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="用户代理特性">
              <el-input v-model="temp.userAgent" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="方法类">
              <el-input v-model="temp.methodClass" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="操作参数">
              <el-input v-model.trim="temp.requestParam" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                        :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="开始时间">
              <el-date-picker
                value-format="yyyy/MM/dd hh:mm:ss"
                v-model="temp.startTime"
                type="datetime"
                :disabled="dialogDisabled">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="消耗时间" class="margin-bottom_0">
              <el-input v-model="temp.cost" :disabled="dialogDisabled"/>
            </el-form-item>
            <!--<el-form-item label="异常信息">-->
            <!--<el-input v-model="temp.message" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"-->

            <!--:disabled="dialogDisabled"/>-->
            <!--</el-form-item>-->
          </div>
          <div class="div">
            <el-form-item label="操作用户">
              <el-input v-model="temp.userName" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="日志名称">
              <el-input v-model="temp.title" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="服务主机名">
              <el-input v-model="temp.serverHost" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="访问环境">
              <el-input v-model="temp.env" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="请求URI">
              <el-input v-model="temp.requestUri" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="请求方法">
              <el-input v-model="temp.requestMethod" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="方法名">
              <el-input v-model="temp.methodName" :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="请求body">
              <el-input v-model="temp.requestBody" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                        :disabled="dialogDisabled"/>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="temp.endTime"
                value-format="yyyy/MM/dd hh:mm:ss"
                type="datetime"
                :disabled="dialogDisabled">
              </el-date-picker>
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
    </el-container>
    <!-- 查看 上一条 / 下一条 记录-->
    <PaginationRecord ref="pageRec" v-if="dialogStatus === 'view'"
      @refreshData="getDataById" @handlePageChg="handlePageChg"
      :tableRef="tableRef" :tableFooterRef="tableFooterRef">
    </PaginationRecord>
    <div slot="footer" class="dialog-footer">
      <el-button icon="uims-icon-cancel" @click="dialogMainFormVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {resourceCode} from "@/biz/http/settings";
  import PaginationRecord from '@/core/components/PaginationRecord';

  export default {
    name: "LogView",
    components: {PaginationRecord},
    data() {
      return {
        resCode: '',
        textMap: {
          view: '查看日志'
        },
        sysName: `${config.headerBar.logo.title ? config.headerBar.logo.title : config.headerBar.logo.top_title}`,
        temp: {
          logId: undefined,
          userName: undefined,
          unitName: undefined,
          title: undefined,
          serviceId: undefined,
          serverHost: undefined,
          serviceIp: undefined,
          env: undefined,
          remoteIp: undefined,
          requestUri: undefined,
          userAgent: undefined,
          requestMethod: undefined,
          methodClass: undefined,
          methodName: undefined,
          requestParam: undefined,
          requestBody: undefined,
          startTime: undefined,
          endTime: undefined,
          cost: undefined,
          message: undefined
        },
        dialogMainFormVisible: false
      }
    },
    props: {
      dialogStatus: String,
      dialogFormVisible: Boolean,
      dialogDisabled: Boolean,
      log: Array,
      // 关联的取数表格对象
      tableRef: Object,
      // 关联的取数表格翻页对象
      tableFooterRef: Object
    },
    methods: {
      // 获取列表新的分页数据
      handlePageChg(currentPage, cb) {
        this.$emit('handlePageChg', currentPage, cb)
      },
      // 返回记录是否可以查看
      isRecordCanView(logId) {
        /* console.log(logId)
        if(logId.endsWith('9')) {
          return false;
        } */
        return true;
      },
      // 查看窗口的业务数据变更
      getDataById(logId, cb) {
        if(typeof cb === "function" && !this.isRecordCanView(logId)) {
          // 不可以查看当前记录，继续往下翻
          let canView = false
          cb(canView);
        } else {
          // 可以查看当前记录
          this.$api.log.getLogById({logId: logId}, this.resCode).then((res) => {
            this.temp = res.data
            if(typeof cb === "function") {
              cb();
            }
          })
        }
      },
      destrory() {
        this.temp = {
          logId: undefined,
          sysName: undefined,
          userName: undefined,
          unitName: undefined,
          title: undefined,
          serviceId: undefined,
          serverHost: undefined,
          serviceIp: undefined,
          env: undefined,
          remoteIp: undefined,
          requestUri: undefined,
          userAgent: undefined,
          requestMethod: undefined,
          methodClass: undefined,
          methodName: undefined,
          requestParam: undefined,
          requestBody: undefined,
          startTime: undefined,
          endTime: undefined,
          cost: undefined,
          message: undefined
        }
      },
      timeFormatter(val) {
        if (val != null) {
          var date = new Date(val);
          return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
        }
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogStatus === 'view') {
            this.resCode = resourceCode.log_view
            // this.temp = this.log[0];
            // this.temp.startTime = this.timeFormatter(this.temp.startTime);
            // this.temp.endTime = this.timeFormatter(this.temp.endTime);
            this.$api.log.getLogById({logId: this.log[0].logId}, this.resCode).then((res) => {
              this.temp = res.data
            })
            this.$nextTick(() => {
              // PaginationRecord翻页组件初始化
              this.$refs.pageRec.setCurrentId(this.log[0].logId)
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
  .flow {
    overflow: auto
  }

  .display {
    display: none;
  }

  .div {
    width: 50%;
    float: left
  }
</style>
