<!--
 * @Description: 扩展ElInput组件(实现输入框睁眼/闭眼图标切换功能)
 * @Author: 刘芸
 * @Date: 2023-09-18 09:13:41
 * @LastEditTime: 2023-09-18 16:34:49
 * @LastEditors: 刘芸
-->
<script>
import { Input } from "element-ui";

export default {
  extends: Input, // 继承el-input
  name: "EyesInput",
  data() {
    return {
      openEyes: " fa fa-eye ",
      closeEyes: " fa fa-eye-slash "
    };
  },
  methods: {
    /**
     * @description: 切换睁眼闭眼状态图标
     * @Author: 刘芸
     * @Date: 2023-09-18 16:29:28
     * @param {*} isShowEyes
     * @return {*}
     */
    toggleEyes(isShowEyes) {
      this.$nextTick(function() {
        let is = this.$el.parentElement.getElementsByTagName("i");
        for(let i = 0; i < is.length; i++) {
          if(is[i].className.indexOf(' fa ') > -1) {
            if (!isShowEyes) {
              is[i].className = is[i].className.replace(
                this.openEyes,
                this.closeEyes
              );
            } else {
              is[i].className = is[i].className.replace(
                this.closeEyes,
                this.openEyes
              );
            }
            break;
          }
        }
      });
    },
    /**
     * @description: 初始化闭眼状态
     * @Author: 刘芸
     * @Date: 2023-09-18 16:30:19
     * @return {*}
     */
    init() {
      this.passwordVisible = false;
    }
  },
  watch: {
    passwordVisible: {
      immediate: true,
      handler: function(newVal, oldVal) {
        this.toggleEyes(newVal);
      }
    },
    showPwdVisible: {
      immediate: true,
      handler: function(newVal, oldVal) {
        this.$nextTick(function() {
          let is = this.$el.parentElement.getElementsByTagName("i");
          for(let i = 0; i < is.length; i++) {
            if (is[i].className.indexOf(" el-icon-view ") > -1) {
              // 组件初始化时将ElInput的默认图标el-icon替换为font-awesome的图标
              is[i].className = is[i].className.replace(
                " el-icon-view ",
                newVal ? this.closeEyes : this.openEyes
              ).replace(
                this.closeEyes,
                this.passwordVisible ? this.openEyes : this.closeEyes
              );
              break;
            }
          }
        });
      }
    }
  }
};
</script>
