<template>
  <fragment>
    <div :class="['pagination-record-btn-wrapper', 'prev', hidePrev]">
      <el-tooltip content="上一条记录"><div>
        <el-button type="primary" class="txtBtn saveAdd-btn is-circle" @click="getPreviousRecord" :disabled="disabled">
          <span><i class="el-icon-arrow-left"></i></span>
        </el-button>
      </div></el-tooltip>
    </div>
    <div :class="['pagination-record-btn-wrapper', 'next', hideNext]">
      <el-tooltip content="下一条记录"><div>
        <el-button type="primary" class="txtBtn saveAdd-btn is-circle" @click="getNextRecord" :disabled="disabled">
          <span><i class="el-icon-arrow-right"></i></span>
        </el-button>
      </div></el-tooltip>
    </div>
  </fragment>
</template>

<script>
import { Fragment } from 'vue-fragment'
export default {
  name: "PaginationRecord",
  components: { Fragment },
  props: {
    // 关联的取数表格对象
    tableRef: Object,
    // 关联的取数表格翻页对象
    tableFooterRef: Object
  },
  data() {
    return {
      // 取数表格的新主键值
      curId: undefined,
      // 是否隐藏上一条按钮
      hidePrev: undefined,
      // 是否隐藏下一条按钮
      hideNext: undefined,
      // 按钮是否可点击
      disabled: false
    };
  },
  computed: {
    // 关联的取数表格当前页数据
    dataOpts() {
      return this.tableRef._props.data;
    },
    // 关联的取数表格主键
    rowKey() {
      if(typeof this.tableRef.rowKey === "function") {
        return this.tableRef.rowKey();
      } else if(typeof this.tableRef.rowKey === "string") {
        return this.tableRef.rowKey;
      } else {
        console.error("TablePlus列表未设置【row-key】属性")
        return undefined;
      }
    }
  },
  methods: {
    // 获取上一条记录
    getPreviousRecord() {
      this.hideNext = undefined;
      let rowId = this.dataOpts.findIndex(data => this.curId === data[this.rowKey]);
      if (rowId === 0) {
        let { currentPage, pageSize } = this.tableFooterRef;
        if(currentPage === 1) {
          // 当前已是第一页第一条记录，按钮置灰
          this.hidePrev = 'hide';
        } else {
          // 上翻一页，取最后一条记录
          this.disabled = true;
          this.$emit('handlePageChg', currentPage - 1, () => {
            rowId = pageSize - 1;
            this.curId = this.dataOpts[rowId][this.rowKey];
            this.$emit("refreshData", this.curId, (canView) => {
              if(typeof canView === 'boolean' && canView === false) {
                this.getPreviousRecord();
              } else {
                this.setCurrentRow(rowId);
                this.disabled = false;
              }
            });
          })
        }
      } else {
        // 在取数范围内，直接取上一条记录id
        rowId = rowId - 1
        this.curId = this.dataOpts[rowId][this.rowKey];
        this.disabled = true;
        this.$emit("refreshData", this.curId, (canView) => {
          if(typeof canView === 'boolean' && canView === false) {
            this.getPreviousRecord();
          } else {
            this.setCurrentRow(rowId);
            this.disabled = false;
          }
        });
      }
    },
    // 获取下一条记录
    getNextRecord() {
      this.hidePrev = undefined;
      let rowId = this.dataOpts.findIndex(data => this.curId === data[this.rowKey]);
      if (rowId === this.dataOpts.length - 1) {
        let { currentPage, pageSize, total } = this.tableFooterRef;
        let realIdx = (currentPage - 1) * pageSize + rowId + 1;
        if (realIdx === total) {
          // 当前已是最后一页最后一条记录，按钮置灰
          this.hideNext = 'hide';
        } else {
          // 下翻一页，取第一条记录
          this.disabled = true;
          this.$emit('handlePageChg', currentPage + 1, () => {
            rowId = 0;
            this.curId = this.dataOpts[rowId][this.rowKey];
            this.$emit("refreshData", this.curId, (canView) => {
              if(typeof canView === 'boolean' && canView === false) {
                this.getNextRecord();
              } else {
                this.setCurrentRow(rowId);
                this.disabled = false;
              }
            });
          })
        }
      } else {
        // 在取数范围内，直接取下一条记录id
        rowId = rowId + 1;
        this.curId = this.dataOpts[rowId][this.rowKey];
        this.disabled = true;
        this.$emit("refreshData", this.curId, (canView) => {
          if(typeof canView === 'boolean' && canView === false) {
            this.getNextRecord();
          } else {
            this.setCurrentRow(rowId);
            this.disabled = false;
          }
        });
      }
    },
    /**
     * 设置表格的选中行
     * @param {string} rowId 行索引
     */
    setCurrentRow(rowId) {
      this.tableRef.clearSelection();
      let currentRow = this.dataOpts[rowId]
      this.tableRef.setCurrentRow(currentRow);
      let bodyWrapper = this.tableRef.$refs.bodyWrapper
      const tableRows = bodyWrapper.querySelectorAll('.el-table__body tbody .el-table__row')
      let scrollTop = 0
      for(let i = 0; i < tableRows.length; i++) {
        if(i === rowId) {
          break;
        }
        scrollTop += tableRows[i].offsetHeight;
      }
      bodyWrapper.scrollTop = scrollTop
      this.curId = this.dataOpts[rowId][this.rowKey];
      this.triggerBtn(rowId)
    },
    /**
     * 组件初始化方法
     * @param {string} selectedRowId 表格主键
     */
    setCurrentId(selectedRowId) {
      this.hideNext = undefined;
      this.hidePrev = undefined;
      this.curId = selectedRowId;
      let rowId = this.dataOpts.findIndex(data => selectedRowId === data[this.rowKey]);
      this.triggerBtn(rowId);
    },
    /**
     * 变更按钮显示状态
     * @param {string} rowId 表格行号索引
     */
    triggerBtn(rowId) {
      if(rowId === -1) {
        // 未找到当前记录索引
        this.hideNext = 'hide';
        this.hidePrev = 'hide';
        console.error("TablePlus列表未找到当前记录索引，请检查【row-key】属性是否设置正确")
      } else if(typeof this.tableFooterRef === "undefined") {
        // 父列表不分页
        if(rowId === this.dataOpts.length - 1) {
          // 当前已是最后一页最后一条记录，按钮置灰
          this.hideNext = 'hide';
        }
        if(rowId === 0) {
          // 当前已是第一页第一条记录，按钮置灰
          this.hidePrev = 'hide';
        }
      } else {
        let { currentPage, pageSize, total } = this.tableFooterRef;
        let realIdx = (currentPage - 1) * pageSize + rowId + 1;
        if (realIdx === total) {
          // 当前已是最后一页最后一条记录，按钮置灰
          this.hideNext = 'hide';
        }
        if(rowId === 0 && currentPage === 1) {
          // 当前已是第一页第一条记录，按钮置灰
          this.hidePrev = 'hide';
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.pagination-record-btn-wrapper {
  width: 82px;
  height: 200px;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  cursor: pointer;
  &.prev {
    left: 41px;
  }
  &.next {
    left: calc(100% - 41px);
  }
  &.hide {
    visibility: hidden;
  }
  &:hover .el-button {
    display: block;
  }
  .el-button {
    padding: 8px;
    border-radius: 100% !important;
    margin: 0 5px;
    display: none;
  }
}
</style>
