<template>
  <div class="widgetMyWork">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="item1 in tabList" :key="item1.name" :label="item1.label" :name="item1.name">
        <div class="mainConte">
          <div class="leftPart">
            <div class="navList" :class="{ active: activeIndexFirst === 0 }" @click="changeActiveIndexFirst(0)">
              <span></span>热门
            </div>
            <div class="navList" :class="{ active: activeIndexFirst === 1 }" @click="changeActiveIndexFirst(1)">
              <span></span>最新
            </div>
          </div>
          <div class="rightPart">
            <div class="rightContent">
              <div class="rightItem" v-for="(item, index) in list" :key="index" @click="routeToDetail(item)">
                <div class="title">
                  <img src="../../../../assets/img/pic-new.png" alt="" />
                  <span :title="item.cataName || item.directoryName || item.powerName || item.resourceName">
                    {{
                      item.cataName ||
                      item.directoryName ||
                      item.powerName ||
                      item.resourceName
                    }}
                  </span>
                </div>
                <p v-if="item.registerUnitName" :title="item.registerUnitName">
                  单位：{{
                    item.registerUnitName
                  }}
                </p>
                <p v-else :title="item.createUnitName || item.providerDeptName">
                  单位：{{
                    item.createUnitName ||
                    item.providerDeptName
                  }}
                </p>
                <p v-if="item1.name !== '1'">更新时间：{{ item.updateTime }}</p>
                <p v-if="item1.name === '1'">更新时间：{{ item.publishTime ? item.publishTime : item.updateTime }}</p>
                <p v-if="item1.name !== '2'">
                  更新周期：{{ item.updateCycle }}
                </p>
                <div class="itemNum">
                  <span class="num">{{ item.orderSum || 0 }}次<span>订阅</span></span>
                  <span class="num">{{
                      item.browseSum || 0
                    }}次<span>访问</span></span>
                  <span class="num">{{ item.collectSum || 0 }}次<span>收藏</span></span>
                </div>
                <p v-if="item1.name === '2'">
                  <br>
                </p>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div class="moreNews" @click="goMore">更多</div>
  </div>
</template>
<script>
export default {
  name: 'widgetMyWork',
  components: {},
  props: {},
  data() {
    return {
      itemLength: 10,
      activeName: '1',
      list: [],
      tabList: config.gxslInvokeSwitch
        ? [
          // { label: '数据目录', name: '0' },
          { label: '数据资源', name: '1' }]
        : [
          // { label: '数据目录', name: '0' },
          { label: '数据资源', name: '1' },
          { label: '能力', name: '2' }
        ],
      activeIndexFirst: 0
    }
  },
  methods: {
    handleClick(tab, event) {
      this.getList()
    },
    changeActiveIndexFirst(value) {
      this.activeIndexFirst = value
      this.getList()
    },
    getList() {
      // 数据目录
      if (this.activeName === '0') {
        // 热门
        if (this.activeIndexFirst === 0) {
          // 获取最热目录数据
          this.$api.bizApi.pageRequest.getHostDirList({}).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        } else {
          // 获取最新目录数据
          this.$api.bizApi.pageRequest.getNewDirList({}).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        }
      } else if (this.activeName === '1') {
        // 数据资源
        // 热门
        if (this.activeIndexFirst === 0) {
          // 获取最热资源数据
          this.$api.bizApi.pageRequest.getHostResourceApply({}).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        } else {
          // 获取最新资源数据
          this.$api.bizApi.pageRequest.getNewResourceList({ sortMode: '1' }).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        }
      } else {
        // 能力
        if (this.activeIndexFirst === 0) {
          // 获取最热能力数据
          this.$api.bizApi.pageRequest.getHotPowerList({}).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        } else {
          // 获取最新能力数据
          this.$api.bizApi.pageRequest.getLatestPowerList({}).then((res) => {
            if (res.code === '200' && res.data.length) {
              this.list = res.data.slice(0, 8)
            } else {
              this.list = []
            }
          }).catch(res => {
            this.list = []
          })
        }
      }

      console.log(this.list, 'list')
    },
    routeToDetail(data) {
      // 数据目录
      const href = window.location.href
      let url = new URL(href)
      if (this.activeName === '0') {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/detailCatalog/index',
            query: {
              cataId: data.cataId,
              type: '0'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataDir/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              cataId: data.cataId,
              type: '0'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeName === '1') {
        // 数据资源
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/detailResource/index',
            query: {
              cataId: data.cataId,
              resourceId: data.resourceId,
              type: '1',
              resType: data.resourceTypeCode
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataRes/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              cataId: data.cataId,
              resourceId: data.resourceId,
              type: '1',
              resType: data.resourceTypeCode
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/detail/index',
            query: {
              powerId: data.powerId
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/power/detail',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              powerId: data.powerId
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      }
    },
    goMore() {
      // 数据目录
      const href = window.location.href
      let url = new URL(href)
      if (this.activeIndexFirst === 0 && this.activeName === '0') {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/catalog/index',
            query: {
              sortMode: 1,
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataDir',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: 1,
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeIndexFirst === 1 && this.activeName === '0') {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/catalog/index',
            query: {
              sortMode: 4,
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataDir',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: 4,
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeIndexFirst === 0 && this.activeName === '1') {
        // 数据资源
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/resource/index',
            query: {
              sortMode: 1,
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataRes',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: 1,
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeIndexFirst === 1 && this.activeName === '1') {
        // 数据资源
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/resource/index',
            query: {
              sortMode: '',
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/dataRes',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: '',
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeIndexFirst === 0 && this.activeName === '2') {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/index/index',
            query: {
              sortMode: 2,
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/power',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: 2,
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      } else if (this.activeIndexFirst === 1 && this.activeName === '2') {
        if (url.href === window.top.location.href) {
          let routeUrl = this.$router.resolve({
            path: '/index/index',
            query: {
              sortMode: 4,
              sortType: 'desc'
            }
          })
          window.open(routeUrl.href, '_blank')
        } else {
          let params = {
            // 打开窗口类型 根据门户定义规则
            IGDPType: 'IGDP_OPEN_WINDOW',
            // / 消息接受地址 根据门户定义规则
            IGDPUrl: '/portal/market/power',
            // 普通方式传参数(参数在ur 后面) 的
            defaultParams: {
              sortMode: 4,
              sortType: 'desc'
            }
          }
          window.top.postMessage(JSON.stringify(params), '*') // 只能传字符串
        }
      }
    }
  },
  mounted() {
    this.getList()
  }
}
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.widgetMyWork {
  width: 100%;
  position: relative;

  >>> .el-tabs {
    // margin-top: 20px;

    .el-tabs__header {
      padding-left: 20px;
      margin: 0;
      background: #ffffff;
      border-bottom: 1px solid #ededed;

      .el-tabs__nav-wrap {
        &::after {
          background-color: transparent;
        }

        .el-tabs__item {
          //font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 24px;
          color: #333333;
          line-height: 48px;
        }

        .el-tabs__active-bar {
          //width: 46px !important;
          height: 3px;
          background: $colors;
          border-radius: 2px;
        }

        .el-tabs__nav {
          height: 50px;
        }
      }
    }

    .el-tabs__content {
      .mainConte {
        height: 360px;
        background: #ffffff;
        border-radius: 2px 7px 7px 2px;
        display: flex;

        .leftPart {
          width: 231px;
          height: 360px;
          background-image: url('../../../../assets/img/pic-hotAndNew.png');
          background-repeat: no-repeat;
          background-size: 100%;
          padding-top: 51px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .navList {
            width: 196px;
            height: 48px;
            display: flex;
            align-items: center;
            //font-family: Source Han Sans CN;
            font-weight: 500;
            font-size: 18px;
            color: #666666;
            line-height: 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;

            span {
              display: inline-block;
              margin-left: 18px;
              width: 0;
              height: 0;
              border-top: 8px solid transparent;
              border-right: 10px solid transparent;
              border-left: 12px solid #888888;
              border-bottom: 8px solid transparent;
            }

            &.active {
              color: #ffffff;
              background-color: $colors;
              box-shadow: 0px 5px 7px 0px rgba(26, 37, 54, 0.26);

              span {
                border-left: 12px solid #ffffff;
              }
            }
          }
        }

        .rightPart {
          width: calc(100% - 231px);

          .rightContent {
            padding: 20px 23px;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;

            .rightItem {
              display: flex;
              flex-direction: column;
              width: 317px;
              // height: 149px;
              background: #f1f5fa;
              // margin: 0px 20px 0px 20px;
              padding: 16px;
              margin: 0 18px 15px 0;
              cursor: pointer;

              .title {
                display: flex;
                align-items: center;
                margin-bottom: 6px;

                img {
                  margin-right: 7px;
                }

                span {
                  width: 240px;
                  height: 20px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  //font-family: Source Han Sans CN;
                  font-weight: 500;
                  font-size: 18px;
                  color: #333333;
                  line-height: 20px;
                }
              }

              p {
                //font-family: Source Han Sans CN;
                font-weight: 400;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 14px;
                color: #666666;
                line-height: 24px;
                margin-bottom: 2px;
              }

              .itemNum {
                .num {
                  font-size: 14px;
                  color: $colors;
                  margin-right: 4px;

                  span {
                    color: #666666;
                  }
                }
              }

              &:hover .title span {
                color: $themeColor;
              }

              &:nth-child(4),
              &:nth-child(8) {
                margin-right: 0;
              }
            }
          }
        }
      }
    }
  }

  .moreNews {
    cursor: pointer;
    //font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #3b3b3b;
    line-height: 20px;
    position: absolute;
    top: 20px;
    right: 25px;
  }

  .moreNews:hover {
    color: rgb(67, 67, 205);
  }
}
</style>
