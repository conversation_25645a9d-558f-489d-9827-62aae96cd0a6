<template>
  <div class="home-container" :class="`navbar_mode_${navbarMode === '2' ? 'horizontal' : 'vertical'}`">
    <!-- 头部区域 -->
    <head-bar v-if="!isFromIframe" @showChgUserDialog="showChgUserDialog" @showChgThemeDialog="showChgThemeDialog" @showChgPwdDialog="showChgPwdDialog"
              @showMsgDetailPanel="showMsgDetailPanel" :initData="initData" :msgTotal="msgTotal"></head-bar>
    <!-- 横向菜单栏 -->
    <top-nav-bar v-if="!isFromIframe"></top-nav-bar>
    <!-- 导航菜单栏 -->
    <!--<nav-bar class="sidebar-container"></nav-bar>-->
    <nav-bar v-if="!isFromIframe" :mainContent="this.$refs.mainContent"></nav-bar>
    <!-- 主内容区域 -->
    <main-content ref="mainContent"></main-content>
    <chg-pwd :show="chgPwdDialogVisible" @closeModal="closeChgPwdDialog"/>
    <user-center :show="chgUserDialogVisible" @closeModal="closeChgUserDialog"></user-center>
    <theme :show="chgThemeDialogVisible" @closeModal="closeChgThemeDialog"></theme>
    <el-drawer class="systemDrawer" ref="systemDrawer" @open="triggerSystemDrawer" size="300px" :modal="false"
               :visible.sync="systemDrawer" direction="ttb" :with-header="false">
      <div class="query-wrapper">
        <!--        <el-input placeholder="请输入系统名称进行过滤" v-model="filterText" maxlength="10" size="small" />-->
        <i class="el-icon-menu"></i>
        <p class="user-system-title">{{systemListTitle}}</p>
        <i :class="openSysList ? 'el-icon-s-fold' : 'el-icon-s-unfold'" aria-hidden="true" @mouseover="showSystemList"></i>
      </div>
      <div class="system-wrapper">
        <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
          <ul>
            <div class="ul-lidev" v-for="(item, index) in userConfigSystem" :key="item.systemId">
              <li class="list-wrapper" @click="chgSystem(item, index)" :title="item.sysName" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                <img
                  :src="item.systemIconUri ? baseURL + '/admin/files/get?path=' + item.systemIconUri :
                  item.icon ?  baseURL + '/admin/files/get?path=' + item.icon :  (item.isParentSys === '1' ?
                     (thumbnail.thumbnailImg === 'thinvent_darken' ? BASE_URL + 'navigation/li-icon-more-n.png' : BASE_URL + 'navigation/li-icon-more-l.png') :
                     (thumbnail.thumbnailImg === 'thinvent_darken' ? BASE_URL + 'navigation/li-icon-single-n.png' : BASE_URL + 'navigation/li-icon-single-l.png')
                     )"/>
                <span>{{ item.sysName }}</span>
              </li>
              <div class="close" @click="changeUserSystem(item.systemId, true, item.sysCat)">
                <svg-icon icon-class="add-icon"></svg-icon>
              </div>
            </div>
          </ul>
          <div class="back" v-show="childrenFlag" @click="chgSystem()">
            <i class="el-icon-s-fold"></i>
            返回上一级
          </div>
        </el-scrollbar>
      </div>
    </el-drawer>
    <el-drawer class="systemListDrawer" ref="systemListDrawer" @open="triggerSystemListDrawer" @close="closeSystemListDrawer" size="500px"
               :modal="false" :visible.sync="systemListDrawer" direction="ltr" :with-header="false">
      <div class="query-wrapper">
        <el-input placeholder="请输入系统名称进行过滤" v-model="filterText" maxlength="10" size="small"/>
        <i :class="filterText ? 'el-icon-circle-close' : 'el-icon-search'" aria-hidden="true" @click="clearQuery"></i>
      </div>
      <div class="system-wrapper">
        <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
          <div class="system-group-wrapper" v-for="(item, index) in systemGroupListFilters" :key="item.sysCat"
               :a="index">
            <p class="system-group-title">{{ item.sysCat }}</p>
            <div class="system-group-flex">
              <div class="system-group-list-wrapper" v-for="(itemSys, indexSys) in item.sysList"
                   :key="itemSys.systemId">
                <p @click="chgSystem(itemSys, indexSys)" :title="itemSys.sysName" style="width: 190px; overflow: hidden; white-space: nowrap;">
                  <img
                    :src="itemSys.icon ? baseURL + '/admin/files/get?path=' + itemSys.icon :
                    (itemSys.isParentSys === '1' ?
                     (thumbnail.thumbnailImg === 'thinvent_darken' ? BASE_URL + 'navigation/li-icon-more-n.png' : BASE_URL + 'navigation/li-icon-more-l.png') :
                     (thumbnail.thumbnailImg === 'thinvent_darken' ? BASE_URL + 'navigation/li-icon-single-n.png' : BASE_URL + 'navigation/li-icon-single-l.png')
                     )"/>
                  <span>{{ itemSys.sysName }}</span>
                </p>
                <perm-button size="mini"
                             class="user-config-button"
                             :type="itemSys.isUserConfig ? 'primary' : 'info'"
                             :plain="!itemSys.isUserConfig" :icon="itemSys.isUserConfig ? 'ysc' : 'sc'" circle
                             @click="changeUserSystem(itemSys.systemId, itemSys.isUserConfig, item.sysCat)"/>
              </div>
            </div>

          </div>
        </el-scrollbar>
      </div>
    </el-drawer>
    <el-drawer class="copyrightDrawer" :title="this.$store.state.app.appName" :visible.sync="copyrightDrawer"
               :modal="false" direction="rtl">
      <copyright></copyright>
    </el-drawer>
    <!-- 快捷工具栏 -->
    <right-panel v-if="showRightPanel">
      <shortcut-bar/>
    </right-panel>
    <message-detail-panel :msg="showMsgInfo" :show="msgDetailPanelDialogVisible" @closeModal="closeMsgDetailPanel"/>
  </div>
</template>

<script>
// eslint-disable-next-line
import Cookies from 'js-cookie'
import {mapState} from 'vuex'
import HeadBar from './HeadBar'
import PermButton from '@/core/components/PermButton'
import NavBar from './NavBar'
import ShortcutBar from '@/core/components/ShortcutBar'
import TopNavBar from "@/core/components/MenuTree/TopNavBar";
import RightPanel from '@/core/components/RightPanel'
import MainContent from './MainContent'
import ChgPwd from '@/core/views/Core/ChgPwd'
import UserCenter from '@/core/views/Core/UserCenter'
import Theme from '@/core/components/Theme'
import Copyright from '@/core/views/Core/Copyright'
import PersonalWebSocket from '@/core/components/PersonalWebSocket'
import LoginInfo from '@/core/components/LoginInfo'
import {singleNotify} from '@/core/utils/singleNotify'
import MessageDetailPanel from '@/core/views/Core/MessageDetailPanel'
import { getSpaceId } from "@/core/utils/logout"

export default {
  name: 'home',
  components: {
    HeadBar,
    NavBar,
    TopNavBar,
    RightPanel,
    ShortcutBar,
    MainContent,
    UserCenter,
    Theme,
    ChgPwd,
    PermButton,
    Copyright,
    PersonalWebSocket,
    LoginInfo,
    MessageDetailPanel
  },
  computed: {
    ...mapState({
      thumbnail: state => state.app.thumbnail,
      appName: state => state.app.appName,
      className: state => state.app.thumbnail.thumbnailImg,
      showRightPanel: state => state.shortcut.showRightPanel,
      currentUser: state => state.user.currentUser
    }),
    copyrightDrawer: {
      get() {
        return this.$store.state.app.copyrightDrawer
      },
      set(val) {
        this.$store.state.app.copyrightDrawer = val
      }
    },
    systemDrawer: {
      get() {
        return this.$store.state.app.systemDrawer
      },
      set(val) {
        this.$store.state.app.systemDrawer = val
      }
    },
    systemListDrawer: {
      get() {
        return this.$store.state.app.systemListDrawer
      },
      set(val) {
        this.$store.state.app.systemListDrawer = val
      }
    },
    // 导航栏模式
    navbarMode: function () {
      return config.navbar_mode
    }
  },
  data() {
    return {
      BASE_URL: process.env.BASE_URL,
      chgPwdDialogVisible: false,
      chgUserDialogVisible: false,
      chgThemeDialogVisible: false,
      msgDetailPanelDialogVisible: false,
      loading: false,
      filterText: '',
      openSysList: false,
      baseURL: this.global.baseURL,
      systemListTitle: config.headerBar.system.listTitle ? config.headerBar.system.listTitle : '系统列表',
      systems: [],
      userConfigSystem: [],
      systemGroupList: [],
      systemGroupListFilters: [],
      systemList: [],
      childrenSystem: {},
      childrenFlag: false,
      nowSystem: {}, // 当前登录系统信息
      nowSystemCFlag: false, // 当前登录系统是否子级系统
      newMsg: {},
      showMsgInfo: {},
      loginInfo: {},
      initData: '',
      msgTotal: 0,
      // 是否被iframe嵌套
      isFromIframe: false
    }
  },
  methods: {
    showChgPwdDialog: function () {
      this.chgPwdDialogVisible = true
    },
    showChgUserDialog: function () {
      this.chgUserDialogVisible = true
    },
    showChgThemeDialog: function () {
      this.chgThemeDialogVisible = true
    },
    showMsgDetailPanel: function (obj) {
      this.msgDetailPanelDialogVisible = true;
      this.showMsgInfo = obj.msg;
    },
    closeChgPwdDialog(...data) {
      this.chgPwdDialogVisible = data[0]
    },
    closeChgUserDialog(...data) {
      this.chgUserDialogVisible = data[0]
    },
    closeChgThemeDialog(...data) {
      this.chgThemeDialogVisible = data[0]
    },
    closeMsgDetailPanel(...data) {
      this.msgDetailPanelDialogVisible = data[0]
    },
    changeUserSystem: function (systemId, isConfigFlag, sysCat) {
      // 更新用户设置的快捷系统, 更新完成后刷新systemList 重新调用getSystemList方法
      this.$api.authority.saveUserConfigSys({systemId: systemId, configFlag: isConfigFlag ? '0' : '1'}).then((res) => {
        if (res.code === "200") {
          // 变更数据
          let parentSys = this.systemGroupListFilters.filter(item => item.sysCat === sysCat);
          if(parentSys && parentSys.length > 0) {
            parentSys[0].sysList.filter(i => i.systemId === systemId)[0].isUserConfig = !isConfigFlag
          } else {
            // 可能是子系统
            this.systemGroupListFilters.forEach(c => {
              let childSys = c.sysList.filter(item => item.systemId === systemId);
              if(childSys && childSys.length > 0) {
                childSys[0].isUserConfig = !isConfigFlag
              }
            })
          }
          if (isConfigFlag) {
            this.userConfigSystem.forEach(function (itme, index, array) {
              if (itme.systemId === systemId) {
                array.splice(index, 1)
              }
            })
          } else {
            let a = this.systemGroupListFilters.filter(item => item.sysCat === sysCat)[0].sysList.filter(i => i.systemId === systemId)[0]
            // 新增
            this.userConfigSystem.unshift(a)
          }
          this.$notify({
            title: '操作成功',
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
        }
      })
    },
    // 获取系统列表
    getSystemList: function () {
      this.systemList = []
      this.systems = []
      this.systemGroupList = []
      this.systemGroupListFilters = []
      getSpaceId(this.currentUser.userName).then(() => {
        this.$api.authority.getSystemList().then((res) => {
          for (let s of res.data) {
            // 依据接入指南返回的值 前端兼容复制相关属性
            s.sysCode = s.systemCode
            s.sysName = s.systemName
            s.sysCat = s.systemCat
            s.sysUri = s.systemUri
            this.systemList.push(s)
            this.systems.push(s)
          }
          // 是否子级系统
          this.childrenFlag = false
          let nowSystem = this.systemList.filter(i => i.sysCode === config.systemCode)[0]
          this.nowSystemCFlag = false
          if (!nowSystem) {
            // 找下级
            let pSystem = this.systemList.filter(i => i.childrenSystems && i.childrenSystems.length > 0)
            if (pSystem && pSystem.length > 0) {
              for (let ele of pSystem) {
                for (let cele of ele.childrenSystems) {
                  cele.sysCode = cele.systemCode
                  cele.sysName = cele.systemName
                  cele.sysCat = cele.systemCat
                  cele.sysUri = cele.systemUri
                  if (cele.sysCode === config.systemCode) {
                    nowSystem = cele
                    break
                  }
                }
              }
            }
          }
          this.nowSystem = nowSystem
          if (nowSystem && nowSystem.parentSystemId) {
            this.childrenFlag = true
            this.nowSystemCFlag = true
            // 获取子级系统列表
            this.systemList = this.systemList.filter(i => i.systemId === nowSystem.parentSystemId)[0].childrenSystems
          }
          this.$store.commit('appName', nowSystem.sysName)
          this.getUserConfigSystemList(config.systemCode)
        })
        if (!this.isFromIframe && (config.page ? config.page.showLoginInfo : true)) {
          // 弹出上次登录信息
          this.$api.authority.getLastLoginInfo().then((res) => {
            if(res.data) {
              this.showLoginInfo(res.data)
            }
          })
        }
      })
    },
    getUserConfigSystemList(systemCode, parentId) {
      // 用户收藏的系统列表
      this.$api.authority.getUserConfigSystemList().then((res) => {
        if (res.data && res.data.length > 0) {
          for (let s of res.data) {
            // 依据接入指南返回的值 前端兼容复制相关属性
            s.sysCode = s.systemCode
            s.sysName = s.systemName
            s.sysCat = s.systemCat
            s.sysUri = s.systemUri
          }
          this.userConfigSystem = res.data
          if (this.childrenFlag) {
            // 显示子级系统
            if(systemCode) {
              let system
              // 找下级
              let pSystem = this.systems.filter(i => i.childrenSystems && i.childrenSystems.length > 0)
              if (pSystem) {
                for (let ele of pSystem) {
                  for (let cele of ele.childrenSystems) {
                    if (cele.sysCode === systemCode) {
                      system = cele
                      break
                    }
                  }
                }
              }
              if(system) {
                let parentSystemId = system.parentSystemId
                this.userConfigSystem = res.data.filter(i => i.parentSystemId === parentSystemId)
              } else {
                this.userConfigSystem = []
              }
            } else {
              this.userConfigSystem = res.data.filter(i => i.parentSystemId === parentId)
            }
          } else {
            // 显示顶级系统
            this.userConfigSystem = res.data.filter(i => !i.parentSystemId)
          }
        }
        // 构建系统分组
        this.createSystemGroup()
      })
    },
    createSystemGroup() {
      this.systemGroupList = []
      for (let element of this.systemList) {
        element.sysCode = element.systemCode
        element.sysName = element.systemName
        element.sysCat = element.systemCat
        element.sysUri = element.systemUri
        if (!element.sysCat) {
          element.sysCat = '通用系统'
        }
        // 添加是否收藏属性
        let configFlag
        if (this.userConfigSystem && this.userConfigSystem.length > 0) {
          configFlag = this.userConfigSystem.filter(i => i.sysCode === element.sysCode)
        }
        element.isUserConfig = !!(configFlag && configFlag.length > 0)
        let flag = true
        for (let i = 0; i < this.systemGroupList.length; i++) {
          if (this.systemGroupList[i].sysCat === element.sysCat) {
            if (this.systemGroupList[i].sysList) {
              this.systemGroupList[i].sysList.push(element)
            } else {
              this.systemGroupList[i].sysList = []
              this.systemGroupList[i].sysList.push(element)
            }
            flag = false
            break
          }
        }
        if (flag) {
          this.systemGroupList.push({
            sysCat: element.sysCat,
            sysList: [element]
          })
        }
      }
      this.systemGroupListFilters = Object.assign([], this.systemGroupList);
    },
    // 切换系统
    chgSystem: async function (item, index) {
      // this.systemDrawer = false
      if (item) {
        let system = this.systems.filter(i => i.systemId === item.systemId);
        if (system && system.length > 0 && system[0].childrenSystems && system[0].childrenSystems.length > 0) {
          // 有子级，加载下级
          this.childrenSystem = system[0].childrenSystems[0]
          this.systemList = system[0].childrenSystems
          this.getUserConfigSystemList(undefined, this.childrenSystem.parentSystemId)
          // this.createSystemGroup()
          this.childrenFlag = true
          // 系统组变更
        } else {
          // 无子级，
          if (item.sysUri) {
            if (window.location.href.indexOf(item.sysUri) !== 0) {
              let toUrl = item.sysUri;
              // 当前是否是已经登录的状态，是登录状态携带一个参数跳过去，参数作用：如果条过去的页面路由是白名单路由有这个参数则走单点获取用户，否则不走不走单点自接进入白名单页面
              let user = sessionStorage.getItem("user")
              let tokenCookies = Cookies.get(config.cookieName)
              if(user && tokenCookies) {
                if(item.sysUri) {
                  if (item.sysCode === 'uums') {
                    // 老统一用户直接跳过去
                    window.location.href = item.sysUri
                  } else {
                    if (item.sysUimsVer === '5400') {
                      if (item.checkState === '1') {
                        // 强制状态验证，需要先去业务系统，业务系统颁发state
                        // window.location.href = item.sysUri
                        if (toUrl.indexOf("?") > 0) {
                          window.location.href = toUrl + "&hlf=ayl"
                        } else {
                          window.location.href = toUrl + "?hlf=ayl"
                        }
                      } else {
                        // 非强制状态验证，直接跳oauth/authorize接口
                        if (config.sso.onoff) {
                          window.location.href = config.sso.url + '?client_id=' + item.clientId + '&response_type=code&scope=all&redirect_uri=' + encodeURIComponent(item.sysUri)
                        } else {
                          window.location.href = toUrl + "?hlf=ayl"
                        }
                      }
                    } else {
                      if (config.isOnUims && config.sso.onoff) {
                        window.location.href = config.sso.url + '?client_id=' + item.clientId + '&response_type=token&scope=all&redirect_uri=' + encodeURIComponent(item.sysUri)
                      } else {
                        window.location.href = toUrl
                      }
                    }
                  }
                }
              } else {
                Cookies.remove(config.cookieName)
                window.location.href = toUrl
              }
            }
          }
        }
      } else {
        // 返回上级
        let system = this.systemList.filter(i => i.systemId === this.parentSystemId);
        this.childrenSystem = false
        this.systemList = Object.assign([], this.systems)
        this.getUserConfigSystemList(system.sysCode)
        // this.createSystemGroup()
        this.childrenFlag = false
      }
    },
    // 显示系统列表
    showSystemList() {
      if(!this.systemListDrawer) {
        this.openSysList = !this.openSysList
        this.$store.commit('onDrawerListSystem');
      }
    },
    clearQuery: function () {
      this.filterText = ""
    },
    showLoginInfo: function (loginInfo) {
      // this.initData = Math.random() + ''
      this.loginInfo = loginInfo;
      const h = this.$createElement;
      this.$notify({
        // duration: 0,
        title: '最近登录信息',
        position: 'bottom-right',
        offset: -15,
        message: h(LoginInfo, {
          props: {
            loginInfo: loginInfo,
            thumbnailImg: this.className
          }
        })
      });
    },
    showNotify: function (msg) {
      // this.initData = Math.random() + ''
      this.newMsg = msg;
      const h = this.$createElement;
      singleNotify({
        position: 'bottom-right',
        // duration: 0,
        offset: -15,
        message: h(PersonalWebSocket, {
          props: {
            newMsg: msg
          },
          on: {
            showMsgDetailPanel: this.showMsgDetailPanel,
            closeModal: this.closeMsgDetailPanel
          }
        }),
        customClass: 'instantMsgPanel'
      });
    },
    wsCallback(ev) {
      let msgList = JSON.parse(ev.data);
      // 即时消息提醒
      if (msgList.newMsg) {
        this.showNotify(msgList.newMsg);
      }
      // 刷新新消息总数
      this.msgTotal = msgList.initTotal;
      // 刷新消息列表
      this.$store.commit('setInstantMsg', msgList)
      // this.initData = Math.random() + ''
    },
    initWebSocket() {
      this.closeWS(4500);
      if (!sessionStorage.getItem('user')) return;
      let userId = JSON.parse(sessionStorage.getItem('user')).userId;
      let ls = `${config.msgAppCode}`
      if(!ls || ls === 'undefined') {
        ls = `${config.appCode}`
      }
      let url = config.wsUrl ? config.wsUrl : `ws://${window.location.host}/${config.appCode_uims}`;
      const wsUrl = `${url}/groupChat/${userId}`;
      let _this = this;
      // this.initWsUrl(wsUrl)
      this.createSocket(wsUrl, ev => {
        _this.wsCallback(ev);
      })
      // let data = {"token": Cookies.get(config.cookieName)};
      // this.sendMsg(data, ev => {
      //   console.log("1234", JSON.parse(ev.data))
      // })
      // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
      /* window.onbeforeunload = function() {
        socket.close();
      }; */
    },
    triggerSystemDrawer() {
      // let title = document.querySelector('.headbar-wrapper p.title');
      // if(title.offsetLeft > 200) {
      //   let systemDrawer = document.querySelector('.home-container .systemDrawer .el-drawer.ttb');
      //   systemDrawer.style.left = title.offsetLeft + 'px';
      // }
    },
    closeSystemListDrawer() {
      this.filterText = ''
    },
    triggerSystemListDrawer() {
      // let title = document.querySelector('.headbar-wrapper p.title');
      // if(title.offsetLeft > 200) {
      //   let systemListDrawer = document.querySelector('.home-container .systemListDrawer .el-drawer.ttb');
      //   systemListDrawer.style.left = title.offsetLeft + 'px';
      // }
    }
  },
  watch: {
    systemDrawer(newValue, oldValue) {
      if(!newValue) {
        if(this.systemListDrawer) {
          this.$store.commit('onDrawerListSystem');
        }
        // 初始化数据
        if(this.nowSystemCFlag) {
          // 返回子级状态
          let p = this.systems.filter(i => i.systemId === this.nowSystem.parentSystemId)
          if(p && p.length > 0) {
            this.chgSystem(p[0])
          }
        } else {
          // 返回顶级状态
          this.chgSystem()
        }
      }
    },
    filterText(newValue, oldValue) {
      if (newValue) {
        let systemGroups = JSON.parse(JSON.stringify(this.systemGroupList))
        let list = systemGroups.filter(item => {
          for (let ele of item.sysList) {
            if (ele.sysName.indexOf(newValue) >= 0) {
              return true
            }
          }
          return false
        })
        if (list && list.length > 0) {
          for (let element of list) {
            let newSysList = element.sysList.filter(item => {
              return item.sysName.indexOf(newValue) >= 0
            })
            element.sysList = newSysList
          }
        }
        this.systemGroupListFilters = list
      } else {
        this.systemGroupListFilters = JSON.parse(JSON.stringify(this.systemGroupList))
      }
    },
    currentUser(newValue, oldValue) {
      if(newValue.userId !== oldValue.userId) {
        this.initWebSocket()
      }
    }
  },
  mounted() {
    // this.chgPwdDialogVisible = JSON.parse(sessionStorage.getItem('user')).passwordStatus === '1'
    this.getSystemList()
    this.initWebSocket()
    this.$nextTick(() => {
      if(top !== self) {
        if(this.$route.query.skin) {
          document.body.className = this.$route.query.skin
        }
        document.body.classList.add('fromIframe')
        this.isFromIframe = true
      }
    })

    // if(this.$route.query.from === 'iframe') {
    //   if(this.$route.query.skin) {
    //     document.body.className = this.$route.query.skin
    //   }
    //   document.body.classList.add('fromIframe')
    //   this.isFromIframe = true
    // }
  },
  activated() {
    // this.chgPwdDialogVisible = JSON.parse(sessionStorage.getItem('user')).passwordStatus === '1'
    // this.getSystemList()
  },
  created() {
    this.$store.commit('closeDrawerSystem');
    this.$store.commit('closeDrawerListSystem');
  }
}
</script>

<style lang="scss">
body.fromIframe {
  .main-container div.tab-container {
    display: none;
  }
  .main-container.position-left,
  .main-container.position-left div.main-content {
    top: 0;
    left: 0!important;
    right: 0;
    bottom: 0;
  }
}
</style>
