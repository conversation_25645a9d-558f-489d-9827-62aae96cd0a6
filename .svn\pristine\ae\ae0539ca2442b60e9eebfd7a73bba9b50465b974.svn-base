import { hasPermission } from '@/core/utils/permission.js'
import store from '@/core/store'

/**
 * 检查iframe环境下的权限
 * 在iframe嵌套环境中，权限指令可能会偶尔失效，需要特殊处理
 */
function checkPermissionInIframe(perm) {
  // 首先尝试正常的权限检查
  if (hasPermission(perm)) {
    return true
  }

  // 如果在iframe环境中且store中有权限值，但权限检查失败，可能是权限指令失效
  // 这种情况下，如果store中确实有权限数据，我们认为权限检查应该通过
  const permissions = store.state.user.perms
  if (permissions && permissions.length > 0 && window.top !== window.self) {
    // 在iframe环境中，如果有权限数据，再次检查权限
    return permissions.includes(perm)
  }

  return false
}

/**
 * 权限控制自定义指令
 * 用法：v-permission="'权限码'"
 * 或者：v-permission="['权限码1', '权限码2']" (数组形式，满足其中一个权限即可显示)
 */
const permission = {
  inserted: (el, binding) => {
    const { value } = binding

    if (!value) {
      console.warn('v-permission 指令需要传入权限值')
      return
    }

    let hasAuth = false

    if (Array.isArray(value)) {
      // 如果传入的是数组，只要有一个权限匹配就显示
      hasAuth = value.some((perm) => checkPermissionInIframe(perm))
    } else {
      // 如果传入的是字符串，直接检查权限
      hasAuth = checkPermissionInIframe(value)
    }

    if (!hasAuth) {
      // 没有权限则移除元素
      el.parentNode && el.parentNode.removeChild(el)
    }
  },

  update: (el, binding) => {
    const { value } = binding

    if (!value) {
      return
    }

    let hasAuth = false

    if (Array.isArray(value)) {
      hasAuth = value.some((perm) => checkPermissionInIframe(perm))
    } else {
      hasAuth = checkPermissionInIframe(value)
    }

    if (!hasAuth) {
      // 没有权限则移除元素
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}

export { permission }
