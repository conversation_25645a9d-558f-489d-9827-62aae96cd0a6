<template>
  <div class="queryTree-wrapper" :style="contentStyleObj">
    <el-input
      placeholder="输入关键字进行过滤"
      v-model="filterText">
    </el-input>
    <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
      <el-tree
        class="filter-tree"
        :data="data"
        node-key="value"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        @node-click="nodeClick"
        ref="tree">
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script>
  import {area} from '@/core/utils/area'

  export default {
    name: 'index',
    data () {
      return {
        contentStyleObj: {
          height: ''
        },
        filterText: '',
        data: area,
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      }
    },
    methods: {
      filterNode (value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      getHeight () {
        this.contentStyleObj.height = window.innerHeight - 120 + 'px'
      },
      nodeClick (data, checked, node) {
        // 响应外部操作
        this.$emit('onSelectChange', data)
      }
    },
    watch: {
      filterText (val) {
        this.$refs.tree.filter(val)
      }
    },
    created () {
      window.addEventListener('resize', this.getHeight)
      this.getHeight()
    },
    destroyed () {
      window.removeEventListener('resize', this.getHeight)
    }
  }
</script>

<style scoped lang="scss">
  .queryTree-wrapper {
    display: flex;
    flex-direction: column;

    .el-input {
      flex: 0 0 40px;
    }

    .el-tree {
      flex: 1;
      overflow-y: auto;
      overflow-x: auto;
    }
  }
</style>
