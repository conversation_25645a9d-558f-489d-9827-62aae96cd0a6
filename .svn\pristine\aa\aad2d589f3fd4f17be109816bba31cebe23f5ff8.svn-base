import CryptoJS from 'crypto-js'

export default {
  // 加密
  encryptECB(text, textKey) {
    // 把私钥转换成16进制的字符串
    var key = CryptoJS.enc.Utf8.parse(textKey);
    // 模式为ECB padding为Pkcs7
    var encrypted = CryptoJS.DES.encrypt(text, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    // 加密出来是一个16进制的字符串
    return encrypted.ciphertext.toString();
  },
  // 解密
  decryptECB(ciphertext, textKey) {
    // 把私钥转换成16进制的字符串
    var key = CryptoJS.enc.Utf8.parse(textKey);
    // console.log(CryptoJS.enc.Utf8.stringify(key));
    // 把需要解密的数据从16进制字符串转换成字符byte数组
    var decrypted = CryptoJS.DES.decrypt({
      ciphertext: CryptoJS.enc.Hex.parse(ciphertext)
    }, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    // 以utf-8的形式输出解密过后内容
    return decrypted.toString(CryptoJS.enc.Utf8);
  }
}
