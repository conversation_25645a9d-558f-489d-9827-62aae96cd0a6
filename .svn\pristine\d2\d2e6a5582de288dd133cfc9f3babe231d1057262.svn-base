export default {
  state: {
    currentUser: {}, // 当前登录用户
    unionUsers: [], // 联合用户集合
    perms: [], // 用户权限标识集合
    instantMsg: {} // 用户的即时消息
  },
  getters: {},
  mutations: {
    setCurrentUser (state, currentUser) { // 当前登录用户
      state.currentUser = currentUser
    },
    setUnionUsers (state, unionUsers) { // 联合用户集合
      state.unionUsers = unionUsers
    },
    setPerms (state, perms) { // 用户权限标识集合
      state.perms = perms
    },
    setInstantMsg (state, msg) {
      state.instantMsg = msg
    }
  },
  actions: {}
}
