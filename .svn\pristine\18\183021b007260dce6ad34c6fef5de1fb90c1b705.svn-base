<template>
  <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="600px" @open="initDialog" @close="destrory()">
    <el-form ref="dataForm" :rules="rules" :model="tempDictType" label-position="right" label-width="110px">
      <!--<el-input v-model="tempDictType.dictId" style="display: none"/>-->
      <el-form-item label="字典类型编码" prop="code">
        <el-input v-model="tempDictType.code" :disabled="dialogStatus!=='create'?true:false" maxlength="20"/>
      </el-form-item>
      <el-form-item label="字典类型名称" prop="name">
        <el-input v-model="tempDictType.name" maxlength="25"/>
      </el-form-item>
      <el-form-item label="是否启用" prop="enenabled">
        <!--<el-select v-model="tempDictType.enabled" class="filter-item" placeholder="请选择启用状态">-->
          <!--<el-option v-for="item in dict.isEnable" :key="item.code" :label="item.name" :value="item.code"/>-->
        <!--</el-select>-->
        <select-plus dictType="IS_ENABLE" v-model="tempDictType.enabled"></select-plus>
      </el-form-item>
      <el-form-item label="备注" class="margin-bottom_0">
        <el-input v-model="tempDictType.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                  maxlength="125"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">
        取消
      </el-button>
      <el-button class="save-btn"  type="primary" @click="dialogStatus==='create'?createData():updateData()" :loading="okLoading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import SelectPlus from "@/core/components/SelectPlus";
  import {checkCode} from "@/biz/utils/validate"
  import { resourceCode } from "@/biz/http/settings"
  export default {
      name: "DictTypeDialog",
      components: {
        SelectPlus
      },
      data() {
        return {
          okLoading: false,
          textMap: {
            update: '编辑字典类型',
            create: '新增字典类型'
          },
          tempDictType: {
            dictId: undefined,
            dictType: "DICT_TYPE",
            code: undefined,
            name: undefined,
            fullName: undefined,
            orderNo: 1,
            enabled: "1",
            remark: undefined
          },
          dialogMainFormVisible: false,
          resCode: '',
          // 表单校验规则
          rules: {
            code: [{required: true, message: '字典编码为必填项', trigger: 'blur'}, {validator: checkCode, trigger: 'blur'}],
            name: [{required: true, message: '字典名称为必填项', trigger: 'blur'}],
            enabled: [{required: true, message: '是否启用为必选项'}]
          },
          // 字典
          dict: {
            isEnable: undefined
          }
        }
      },
      props: {
        dialogStatus: String,
        dialogFormVisible: Boolean,
        dictType: Object
      },
      methods: {
        destrory() {
          this.dialogMainFormVisible = false;
          this.okLoading = false;
          this.resetTemp()
        },
        initDialog() {
          // this.loadDict()
        },
        createData() {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              this.okLoading = true
              this.$api.dict.saveDict(this.tempDictType, this.resCode).then((res) => {
                this.okLoading = false
                this.dialogMainFormVisible = false;
                this.$emit("getList")
                this.$notify({
                  title: '操作成功',
                  message: '新增字典类型成功',
                  type: 'success',
                  duration: 2000
                })
              }).catch((res) => {
              this.okLoading = false
            })
            }
          })
        },
        updateData() {
          this.$refs['dataForm'].validate((valid) => {
            if (valid) {
              const tempData = Object.assign({}, this.tempDictType)
              this.okLoading = true
              this.$api.dict.updateDict(tempData, this.resCode).then((res) => {
                this.okLoading = false
                this.dialogMainFormVisible = false;
                this.$emit("getList")
                this.$notify({
                  title: '操作成功',
                  message: '编辑字典类型成功',
                  type: 'success',
                  duration: 2000
                })
              }).catch((res) => {
              this.okLoading = false
            })
            }
          })
        },
        resetTemp() {
          this.tempDictType = {
            dictId: undefined,
            dictType: "DICT_TYPE",
            code: undefined,
            name: undefined,
            fullName: undefined,
            orderNo: 1,
            enabled: "1",
            remark: undefined
          }
        }
      },
      watch: {
        dialogFormVisible: function(newValue, oldValue) {
          this.dialogMainFormVisible = newValue
          if(newValue) {
            if(this.dialogStatus === 'create') {
              this.resCode = resourceCode.dict_add
              this.resetTemp()
              this.$nextTick(() => {
                this.$refs['dataForm'].clearValidate()
              })
            } else if(this.dialogStatus === 'update') {
              this.resCode = resourceCode.dict_eidt
              // this.tempDictType = Object.assign({}, this.dictType);
              // this.tempDictType.enabled = this.tempDictType.enabledCode;
              this.$api.dict.getById(this.dictType.dictId, this.resCode).then((res) => {
                this.tempDictType = Object.assign({}, res.data)
              })
              this.$nextTick(() => {
                this.$refs['dataForm'].clearValidate()
              })
            }
            // else if(this.dialogStatus === 'view') {
            //   this.$api.bizApi.group.getById({groupId: this.groupId}).then(res => {
            //     this.temp = Object.assign({}, res.data)
            //   })
            // }
          }
        },
        dialogMainFormVisible: function(newV, oldV) {
          this.$emit('closeDialog', newV)
        }
      }
    }
</script>

<style scoped lang="scss">

</style>
