import axios from 'axios'
import httpConfig from './config'
import Cookies from 'js-cookie'
import router from '@/core/router'
// import { Message } from 'element-ui'
import settings from 'biz/http/settings'
import { logout, setTimer } from "../utils/logout"
import { singleMessage } from '@/core/utils/singleMessage'
import store from '@/core/store'
import { getRefreshToken, getRsaPub } from '@/core/utils/token' // 刷新token的接口与过期时间倒计时
import { blobToJson, rsa, aesEncryptStr, randomStr } from '@/core/utils/utils'

window.isRefreshing = false; // 用于判断是否刷新
let refreshSubscribers = []; // 被挂起的请求数组
let cookieName = httpConfig.cookieName
let appCodeAuth = httpConfig.appCode_auth
// push所有请求到数组中
function subscribeTokenRefresh (cb) {
  refreshSubscribers.push(cb)
}
// 使用新的token重新发起被挂起的请求
function onRefreshed (token) {
  refreshSubscribers.map(cb => cb(token));
}

export default async function $axios (options) {
  // 验证Uk 是否拔出
  let user = sessionStorage.getItem('user')
  let userObj;
  if(user) {
    userObj = JSON.parse(user)
  }
  if(userObj && userObj.certAuthed === '1') {
    if(httpConfig.certProvider === 'JXYM') {
      let obj;
      const instance = axios.create({});
      await instance({
        url: `http://127.0.0.1:38080/sys/getSerialNumber`,
        method: 'post',
        postType: "form"
      }).then(res => {
        if (res.data.code + '' !== '200') {
          singleMessage({message: 'Ukey异常，请确保Ukey不被拔出。', type: 'error'})
        } else {
          obj = doAxios(options)
        }
      }).catch(() => {
        singleMessage({message: 'Ukey异常，请确保Ukey不被拔出。', type: 'error'})
      });
      return obj;
    } else {
      return doAxios(options)
    }
  } else {
    return doAxios(options)
  }
}

async function doAxios(options) {
  if (options.postType === 'form') {
    if (options.headers) {
      options.headers['Content-Type'] = "application/x-www-form-urlencoded"
    } else {
      options.headers = {'Content-Type': "application/x-www-form-urlencoded"}
    }
    // 请求加密逻辑处理，配置了decrypt表示开启请求加密
    if(!options.decrypt) {
      options.transformRequest = [
        function (data) {
          let ret = ''
          for (let it in data) {
            if(data[it] || data[it] === 0 || data[it] === false) {
              ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
            }
          }
          ret = ret.substring(0, ret.lastIndexOf('&'));
          return ret
        }
      ]
    }
  } else if(options.postType === 'file') {
    if (options.headers) {
      options.headers['Content-Type'] = "multipart/form-data"
    } else {
      options.headers = {'Content-Type': "multipart/form-data"}
    }
    options.transformRequest = [
      function (data, headers) {
        const formData = new FormData()
        if (data) {
          for (const key of Object.keys(data)) {
            if (data[key] || data[key] === 0 || data[key] === false) {
              formData.append(key, data[key])
            }
          }
        }
        return formData
      }
    ]
  }
  if(options.decrypt) {
    if(options.method === 'get' ||
      (options.method === 'post' && (!options.headers['Content-Type'] || options.headers['Content-Type'].includes('application/x-www-form-urlencoded'))) ||
      (options.method === 'post' && (!options.headers['Content-Type'] || options.headers['Content-Type'].includes('application/json')))) {
      // get请求 post 表单提交和json提交才做处理
      let publicKey = sessionStorage.getItem('publicKey')
      if(!publicKey) {
        await getRsaPub().then(res => {
          publicKey = res.value
          sessionStorage.setItem('publicKey', publicKey)
        });
      }
      let dataJson;
      if(options.method === 'get') {
        if (!options.params) {
          options.params = {}
        }
        dataJson = "url#" + JSON.stringify(options.params);
        options.params = undefined
      } else if(options.method === 'post' && options.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
        dataJson = "form#" + JSON.stringify(options.data);
      } else {
        dataJson = "json#" + JSON.stringify(options.data);
      }
      let randomAesKey = randomStr(16)
      let aesKeyRsa = rsa(randomAesKey, publicKey);
      let jsonData = aesEncryptStr({
        data: dataJson,
        key: randomAesKey
      })
      let content = aesKeyRsa + "#" + jsonData
      if(options.method === 'get') {
        options.params = content
        if (!options.headers) {
          options.headers = {}
        }
        options.headers['e'] = 't'
      } else {
        options.data = content
      }
      options.postType = undefined
      options.headers['Content-Type'] = 'application/json'
    }
  }
  if (store.state.system.spaceId && store.state.system.spaceId !== '||empty||') {
    if (options.headers) {
      options.headers['X-space-id'] = store.state.system.spaceId
    } else {
      options.headers = {'X-space-id': store.state.system.spaceId}
    }
  }
  return new Promise((resolve, reject) => {
    const instance = axios.create({
      baseURL: httpConfig.baseURL,
      headers: httpConfig.headers,
      timeout: httpConfig.timeout,
      withCredentials: httpConfig.withCredentials
    })
    // request请求拦截器
    instance.interceptors.request.use(
      config => {
        let token = Cookies.get(cookieName)
        // let token = sessionStorage.getItem(cookieName)
        // 发送请求时携带token
        if (token) {
          token = JSON.parse(Cookies.get(cookieName))
          // settings
          let beareFlag = true
          if(settings.bearerWhiteList) {
            for(let e in settings.bearerWhiteList) {
              let wurl = settings.bearerWhiteList[e]
              if(settings.bearerWhiteList[e].lastIndexOf("/*") >= 0) {
                wurl = wurl.substr(0, wurl.length - 2)
                if(options.url.indexOf(wurl) >= 0) {
                  beareFlag = false
                  break;
                }
              }
            }
          }
          if(beareFlag) {
            config.headers.Authorization = `Bearer ${token.accessToken}`
          }
          let refreshToken = token.refreshToken
          let resetTime = sessionStorage.getItem('resetTime');
          if(refreshToken) {
            if(resetTime && Date.now() > (resetTime - 300000) && config.url.indexOf(`${appCodeAuth}/oauth/token`) === -1) { // 当token有效时间少于5分钟时，刷新token
              if(!window.isRefreshing) {
                window.isRefreshing = true;
                getRefreshToken(refreshToken).then(res => {
                  window.isRefreshing = false;
                  Cookies.set(cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
                  // Cookies.set(cookieName, res.access_token, { sameSite: 'strict', domatin: document.domain + ':' + window.location.port })
                  // sessionStorage.setItem(cookieName, res.access_token)
                  sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
                  sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
                  store.commit('setCurrentUser', res)
                  setTimer()
                  // 重新发起被挂起的请求
                  onRefreshed(res.access_token)
                  refreshSubscribers = []
                }).catch(() => {
                  /* refreshSubscribers = []
                  Cookies.remove(cookieName)
                  // sessionStorage.removeItem(cookieName)
                  sessionStorage.removeItem('user')
                  sessionStorage.removeItem('refreshToken')
                  sessionStorage.removeItem('resetTime')
                  clearInterval(window.interval);
                  window.isRefreshing = false; */
                  window.isRefreshing = false;
                  // 重新发起被挂起的请求
                  onRefreshed(token.accessToken)
                  refreshSubscribers = []
                });
              }
              // 请求挂起
              let retry = new Promise((resolve, reject) => {
                subscribeTokenRefresh((token) => {
                  config.headers.Authorization = `Bearer ${token}`
                  // 将请求挂起
                  resolve(config)
                })
              })
              return retry
            };
          }
        }
        return config
      },
      error => {
        // 请求发生错误时
        // console.log('request:', error)
        // 判断请求超时
        if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
          console.log('timeout请求超时。')
        }
        // 需要重定向到错误页面
        const errorInfo = error.response
        // console.log(errorInfo)
        if (errorInfo) {
          error = errorInfo.data // 页面catch时拿到详细的错误信息
          const errorStatus = errorInfo.status // 403 404 500 ...
          router.push({
            path: `/error/${errorStatus}`
          })
        }
        return Promise.reject(error) // 返回错误信息
      }
    )

    // response拦截器
    instance.interceptors.response.use(
      response => {
        if(response.data instanceof Blob) {
          return response
        }
        return response.data
      },
      async err => {
        // console.log('axios response返回的失败结果===', err)
        let error = {code: null, message: null, trace: null};
        let messageType401 = "error"
        if (err && err.response) {
          error.code = err.response.data && err.response.data.code ? err.response.data.code : err.response.status + ''
          switch (err.response.status) {
            case 401:
              // 先刷新token,若刷新失败，再重定向到登录页面
              if(sessionStorage.getItem('user')) {
                if(!window.isRefreshing) {
                  // 防止重复刷新token
                  window.isRefreshing = true
                  let refreshToken = (Cookies.get(cookieName) && JSON.parse(Cookies.get(cookieName)).refreshToken) || ''
                  getRefreshToken(refreshToken).then(res => {
                    Cookies.set(cookieName, {'accessToken': res.access_token, 'refreshToken': res.refresh_token})
                    sessionStorage.setItem('user', JSON.stringify(res)) // 保存用户到本地会话
                    sessionStorage.setItem('resetTime', Date.now() + res.expires_in * 1000) // 保存token过期时间到本地会话
                    store.commit('setCurrentUser', res)
                    window.isRefreshing = false
                    if (router.currentRoute.path !== '/login') {
                      // 重定向到登录页面
                      // if (window.location.href === window.top.location.href) {
                      //   router.push({path: router.currentRoute.path});
                      // } else {
                      //   top.window.location.reload()
                      // }
                      top.window.location.reload()
                      // router.replace({ path: '/login', query: { redirectPath: router.currentRoute.fullPath } });
                    }
                    // error.message = '用户token已刷新，请刷新页面。'
                  }).catch(function () {
                    Cookies.remove(cookieName)
                    // sessionStorage.removeItem(cookieName);
                    sessionStorage.removeItem('user')
                    sessionStorage.removeItem('resetTime')
                    clearInterval(window.interval);
                    window.isRefreshing = false;
                    if(config.isOnUims && config.sso.onoff) {
                      error.message = '用户信息失效，尝试刷新token。'
                    } else {
                      error.message = (err.response.data ? err.response.data.message : null) || '用户信息失效，请重新登录。'
                    }
                    logout(1);
                  });
                }
                // messageType401 = "warning"
                // error.message = (err.response.data ? err.response.data.message : null) || '用户信息失效，请重新登录。'
              } else {
                logout(1);
                error.message = (err.response.data ? err.response.data.message : null) || '用户信息失效，请重新登录。'
              }
              break
            case 403:
              if(err.data === 'user_forbidden' ||
                (err.response && err.response.data && err.response.data.data === 'user_forbidden')) {
                Cookies.remove(cookieName)
                sessionStorage.removeItem('user')
                sessionStorage.removeItem('resetTime')
                clearInterval(window.interval);
                router.replace({ path: '/login' });
              }
              error.message = (err.response.data ? err.response.data.message || err.response.data.error_description : null) || '拒绝访问。'
              error.trace = err.response.data ? err.response.data.trace : null
              break
            case 408:
              error.message = '请求超时。'
              error.trace = err.response.data ? err.response.data.trace : null
              break
            default:
              error.message = (err.response.data ? err.response.data.message : null) || '请求错误。'
              error.trace = err.response.data ? err.response.data.trace : null
              // 获取responseType='blob'时的json异常返回message
              if(err.response.data && err.response.data instanceof Blob && err.response.data.type.includes("application/json")) {
                await blobToJson(err.response.data).then(res => {
                  error.message = res.message || '请求错误。'
                }).catch(e => {})
              }
              break
          }
        }
        if(!httpConfig.isCustomShowError) {
          // 框架处理
          if (err && err.response && err.response.status === 401) {
            // singleMessage({message: error.message || err.response.data.message || err.response.data.error_description || 'This must be a mistake!', type: messageType401})
            let message = error.message || err.response.data.message || err.response.data.error_description || '用户信息失效，请重新登录。'
            singleMessage({message: message.replaceAll("unauthorized:", ""), type: messageType401})
          } else if(err.message && err.message.indexOf('timeout of ') === 0) {
            singleMessage({message: '网络请求超时。', type: 'error'})
          } else {
            let message = error.message || err.response.data.message || err.response.data.error_description || 'This must be a mistake!'
            singleMessage({message: message.replaceAll("unauthorized:", ""), type: 'error'})
          }
        } else {
          if(err && err.config && err.config.headers && err.config.headers.isBaseApi) {
            if(err.response && err.response.status === 401) {
              error.message = (err.response.data ? err.response.data.message : null) || '用户信息失效，请重新登录。'
            } else if(err.message && err.message.indexOf('timeout of ') === 0) {
              error.message = '网络请求超时。'
            } else if(err.message && err.message.indexOf('Request aborted') === 0) {
              error.message = '请求被中止。'
            } else if(!error.message) {
              error.message = err && err.message ? err.message : '未知的错误。'
            }
            singleMessage({message: error.message, type: 'error'})
          } else {
            if(err && err.response && err.response.status === 401) {
              error.message = (err.response.data ? err.response.data.message : null) || '用户信息失效，请重新登录。'
            } else if(err.message && err.message.indexOf('timeout of ') === 0) {
              error.message = '网络请求超时。'
            } else if(err.message && err.message.indexOf('Request aborted') === 0) {
              error.message = '请求被中止。'
            } else if(!error.message) {
                error.message = err && err.message ? err.message : '未知的错误。'
            }
          }
        }
        return Promise.reject(error) // 返回接口返回的错误信息
      }
    )
    // 请求处理
    instance(options).then(res => {
      resolve(res)
      return false
    }).catch(error => {
      reject(error)
    })
  })
}
