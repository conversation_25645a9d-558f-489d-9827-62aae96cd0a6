/*##################默认登录页面样式########################*/
.loginPage-container {
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  background: url('~@/assets/login_bg.jpg') center center no-repeat #080b10;
  background-size: auto auto;
  text-align: center;
  .form-wrapper {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    z-index: 1;
    .logo {
      position: relative;
      margin-top: -150px;
      margin-bottom: 65px;
      p {
        display: none;
      }
    }
    .noFieldset {
      margin-top: -206px;
      margin-bottom: 75px;
    }
    .login-container {
      margin: 0 auto;
      height:300px;
      width: 350px;
      .title {
        margin: 0px auto 30px auto;
        text-align: center;
        color: #00a7ff;
        span {
          /* border-bottom: 3px solid #00a7ff;
          padding-bottom: 5px;*/
          font-size: 18px;
          text-shadow: 0 0 0.2em #27a3d6;
        }
        hr {
          height: 3px;
          margin-top: 5px;
          border: 0px;
          box-shadow: 0 0 6px 0 #27a3d6;
          color: #00a7ff;
          background-color: #00a7ff;
          /*border-radius: 2px;*/
          &.hr_mobile {
            width: 100px;
          }
          &.hr_account, &.hr_email {
            width: 80px;
          }
        }
      }
      .el-form-item {
        border: 1px solid #00b5ff;
        border-radius: 8px;
        background-color: #2e3b4d;
        color: #454545;
        .el-form-item__content {
          padding-left: 25px;
          .svg-icon {
            font-size: 16px;
            color: #FFFFFF;
          }
          .vLine {
            border-left: solid 1px #FFF;
            height: 18px;
            vertical-align: middle;
            display: inline-block;
            margin-left: 5px;
          }
          .el-input {
            width: 85%;
            .el-input__inner {
              border: 0;
              padding-left: 5px;
              vertical-align: middle;
              font-size: 16px;
              background-color: #2e3b4d;
              color: #86919d;
            }
            &.pwd .el-input__inner {
              -webkit-text-security:disc;
            }
          }
        }
        &.captchaInput {
          .el-form-item__content {
            padding-left: 4px;
            .svg-container,.vLine {
              display: none;
            }
            .captchaImg {
              width: 70px;
              height: 20px;
              top: 7px;
              right: 35px;
              position: absolute;
              cursor: pointer;
            }
          }
        }
        &.mobileLoginItem, &.emailLoginItem {
          margin-bottom: 50px;
          .countDown {
            position: absolute;
            top: 2px;
            right: 35px;
            font-size: 12px;
            color: #F56C6C;
            b {
              width:26px;
              display: inline-block;
            }
          }
        }
      }
      .el-button {
        width: 100%;
        border-radius: 8px;
        background-color: #00a7ff;
        border-color: #00a7ff;
        &:hover {
          box-shadow: 0px 0px 5px 0.2px #27a3d6;
        }
        &.captchaBtn {
          width: 90px;
          height: 20px;
          position: absolute;
          top: 7px;
          right: 35px;
          padding: 0;
          vertical-align: middle;
          border-radius: 10px;
        }
        &.accountLoginBtn,&.mobileLoginBtn,&.emailLoginBtn {
          font-size: 14px;
        }
        &.accountLoginBtn {
          margin-top: 43px;
        }
        &.mobileLoginBtn, &.emailLoginBtn {
          margin-top: 33px;
        }
      }
    }
    .copyright {
      bottom: -305px;
      position: relative;
      font-size: 12px;
      color: #cdcdcd;
    }
  }
  .svg-container {
    // padding: 6px 5px 6px 15px;
    color: #89a3ff;
    vertical-align: middle;
    // width: 30px;
    display: inline-block;
  }
  fieldset {
    width:320px;
    margin:0 auto;
    padding:10px;
    border: 0;
    border-top: #9fabb7 solid 1px;
    text-align: center;
    color:#9fabb7;
    margin-bottom: -40px;
    margin-top: 20px;
    legend {
      margin: 0 auto;
      padding: 0 10px;
      text-align: center;
      font-size: 12px;
      color:#9fabb7;
    }
    ul {
      padding-left: 0px;
      margin: 0px;
      li {
        display: inline;
        padding: 0 6px;
        list-style-type:none;
        font-size: 36px;
        a {
          outline: none;
        }
        .svg-container {
          width:37px;
          .svg-icon {
            color: #FFFFFF;
          }
          .svg-icon.cur {
            border-radius: 50%;
            box-shadow: 0px 0px 5px 0.2px #27a3d6;
            transform: scale(1);
            transition: box-shadow 0.6s, transform 0.5s;
            font-size: 40px;
            color: #00a7ff;
          }
        }
        &.dis .svg-icon {
          color: #666666;
          cursor: default;
        }
      }
      li.dis {
        color: #666;
      }
      li:not(.dis) :hover {
        color: #00a7ff;
        .svg-icon {
          border-radius: 50%;
          box-shadow: 0px 0px 5px 0.2px #27a3d6;
          transform: scale(1);
          transition: box-shadow 0.6s, transform 0.5s;
          font-size: 40px;
          color: #00a7ff;
        }
      }
    }
  }
  .ani {
    top: calc((100% - 1080px) / 2);
    left: calc((100% - 1920px) / 2);
    position: absolute;
  }
}
