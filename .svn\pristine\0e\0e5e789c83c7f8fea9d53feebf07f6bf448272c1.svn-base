<template>
  <div>
    <DialogPlus custom-class="icsp-dialog" :title="title" :autoHeight="true" :visible.sync="_dialogVisible"
      width="1150px" @open="openDialog" @closed="closedDialog">
      <el-container>
        <el-form ref="leaveAMessageForm" :model="formState" :rules="rules" style="">
          <el-form-item prop="resourceId" label="资源" label-width="100px">
            <el-select v-model="formState.resourceId" placeholder="请选择资源" clearable
              style="width: 900px;">
              <el-option v-for="item of resourceList" :key="item.resourceId" :label="item.resourceName"
               :value="item.resourceId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="leaveAMessage" label="留言内容" label-width="100px">
            <el-input type="textarea" :rows="5" placeholder="请输入留言" v-model="formState.leaveAMessage"
              style="width: 900px;display: flex; justify-content: center;" :maxlength="200"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDialog" :loading="loading">提交</el-button>
        <el-button type="plain" @click="closedDialog">取消</el-button>
      </div>
    </DialogPlus>
  </div>
</template>

<script>
import Tip from '@/core/components/Tip'
import DialogPlus from '@/core/components/DialogPlus'
import TablePlus from '@/core/components/TablePlus'
import SelectPlus from '@/core/components/SelectPlus'
import PermButton from '@/core/components/PermButton'
import TitleInfo from 'biz/components/common/t-titleInfo'
import { mapState } from 'vuex'

export default {
  name: 'leaveAMessage',
  components: {
    DialogPlus,
    TablePlus,
    SelectPlus,
    PermButton,
    TitleInfo,
    Tip
  },
  data() {
    return {
      loading: false, // 防止多次点击确定导致多次调用接口
      formState: {
        resourceId: undefined,
        leaveAMessage: undefined
      },
      resourceList: [],
      rules: {
        resourceId: [{ required: true, message: '请选择资源', trigger: 'change' }],
        leaveAMessage: [{ required: true, message: '输入留言内容', trigger: 'change' }]
      }
    }
  },
  props: {
    step: {
      type: Number,
      default: 1
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String
    },
    resourceId: {
      type: String,
      default: undefined
    },
    cataId: {
      type: String,
      default: undefined
    }
  },
  computed: {
    _dialogVisible: {
      get: function () {
        return this.dialogVisible
      },
      set: function (val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    ...mapState({
      currentUser: (state) => state.user.currentUser,
      systemConfig: (state) => state.icsp.systemConfig
    })
  },
  methods: {
    // 打开弹框
    async openDialog() {
      // 通过cataId获取资源列表
    const { code, data, message } = await this.$api.bizApi.resApply.getResourceDictList({cataId: this.cataId})
      if (code === '200') {
        this.resourceList = data
      } else {
        this.$message.error(message);
      }

      // 获取该目录下的资源信息
      this.formState.resourceId = this.resourceId
    },
    // 关闭弹框
    closedDialog() {
      this.$emit('resetFilter')
      this._dialogVisible = false
      if (this.$refs.formRef) {
        // 对整个表单进行重置，将所有字段值重置为初始值并移除校验结果
        this.$refs.formRef.resetFields()
      }
      this.handleReset()
    },
    // 重置
    handleReset() {
      this.formState = {
      }
    },
    // 提交订阅 数据处理
    async submitDialog() {
      await this.$refs.leaveAMessageForm.validate();
      const { code, message } = await this.$api.bizApi.resApply.saveLeaveAMessage(this.formState)
      if (code === '200') {
        this.$message.success('留言成功')
        this.loading = false
        this.closedDialog()
      } else {
        this.$message.error(message);
      }
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="scss">
</style>
