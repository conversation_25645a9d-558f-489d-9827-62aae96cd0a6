const trigger = (el, type) => {
  let ev = document.createEvent('HTMLEvents'); // 创建HTML事件
  ev.initEvent(type, true, true); // 初始化事件，type为事件类型，如input
  el.dispatchEvent(ev); // 派发事件
}

const specialLimit = {
  bind: function (el, binding, vnode) {
    let isInputZh = false;
    // 自定义正则表达式
    // eslint-disable-next-line no-useless-escape
    var regRule = /[~@#$^&"<>\[\]*+=|\\/\r\n]/g;
    el.$handle = function () {
      if (!isInputZh) {
        let val = el.querySelector(':first-child').value
        el.querySelector(':first-child').value = val.replace(regRule, '');// 替换所有匹配项
        const expressions = vnode.data.model.expression.split('.')
        const propsName = expressions.slice(0, expressions.length - 1).join('.');
        const key = expressions[expressions.length - 1]
        // 将格式化后的值重新赋值给v-model绑定的值
        vnode.context[propsName][key] = el.querySelector(':first-child').value
        trigger(el.querySelector(':first-child'), 'input')
      }
    }
    el.$isTrue = function () {
      isInputZh = true
    }
    el.$isFalse = function () {
      isInputZh = false
    }
    el.addEventListener("compositionstart", el.$isTrue)
    el.addEventListener("compositionend", el.$isFalse)
    el.addEventListener('keyup', el.$handle)
  },
  unbind: function (el) {
    el.removeEventListener("compositionstart", el.$isTrue)
    el.removeEventListener("compositionend", el.$isFalse)
    el.removeEventListener('keyup', el.$handle)
  }
}

export { specialLimit }
