<template>
  <el-dialog title="" :visible.sync="dialogVisible" width="800px" :modal="true" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
    <div slot="title">
      <SvgIcon icon-class="progressInfo" class="themeColor" style="vertical-align: middle;"></SvgIcon> 进度信息
    </div>
    <div class="content">
      <div class="flex justify-content top">
        <span>{{ error + finish === total ? '执行完成':'正在执行中......' }}</span>
        <span>{{percent + '%'}}</span>
      </div>
      <el-progress :percentage="percent" :color="getColor" :format="() => ''"></el-progress>
      <div class='flex infoBox' style="">
        <span class="info">数据共：{{total}}条</span>
        <span class="info">已成功：<span class="themeColor">{{finish}}</span>条</span>
        <span class="info" v-if="isError">
          <span>
            失败：<span style="color: red">{{error}}</span>条</span>
        </span>
      </div>
      <table-plus key="table" ref="table" :data="errorList" border height="300" fit stripe highlight-current-row v-if="errorList.length">
        <el-table-column label="名称" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{row.powerName || row.resourceName}}
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{row.powerShape || row.resourceType}}
          </template>
        </el-table-column>
        <el-table-column prop="state" label="订阅结果" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column prop="message" label="失败原因" align="center" show-overflow-tooltip v-if="isError"></el-table-column>
      </table-plus>
    </div>
    <div slot="footer">
      <el-button type="primary" size="mini" v-show="isError || (finish + error === total)" @click="close">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import SvgIcon from '@/core/components/SvgIcon'
import TablePlus from '@/core/components/TablePlus'

export default {
  components: {
    TablePlus,
    SvgIcon
  },
  props: {
    type: { type: String, default: 'singleApply' },
    errorList: { type: Array, default: () => [] },
    isShow: { type: Boolean, default: false },
    total: { type: Number, default: 1 },
    finish: { type: Number, default: 1 },
    error: { type: Number, default: 1 },
    isError: { type: Boolean, default: false }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  computed: {
    percent() {
      return ~~(((this.finish + this.error) / this.total) * 100)
    }
  },
  methods: {
    getColor() {
      let body = document.getElementsByTagName('body')[0]
      let color = ''
      switch (body.className.split(' ')[0]) {
        case 'thinvent':
          color = '#008aff'
          break
        case 'thinvent_darken':
          color = '#1287fc'
          break
        case 'orange':
          color = '#fe7200'
          break
        case 'blue':
          color = '#0e88eb'
          break
        case 'green':
          color = '#1db2c8'
          break
        default:
          break
      }
      return color
    },
    close() {
      this.$emit('close')
    },
    change(o) {
      this[o.key] = o.value
    }
  },
  watch: {
    isShow(newV, oldV) {
      this.dialogVisible = newV
    },
    finish: function (newVal, oldVal) {
      if (this.total === newVal) {
        // this.close();
      }
    }
  }
}
</script>
<style lang='scss' scoped>
>>> .el-dialog {
  margin-top: 25vh !important;
  // height: 260px !important;
  .el-progress-bar {
    margin-right: 0;
    padding-right: 0;
  }
}
.content {
  height: 100%;
  padding-top: 12px;
  box-sizing: border-box;
}
.flex {
  display: flex;
}
.justify-content {
  justify-content: space-between;
}
.infoBox {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 10px;
}
.thinvent_darken {
  .infoBox,
  .top {
    color: #bababa;
  }
  .info {
    border-color: #999;
  }
}
.info {
  text-align: center;
  flex: 1;
  border-right: 1px solid #ddd;
  &:last-child {
    border: 0;
  }
}
/deep/.el-table th.el-table__cell {
  background-color: #4EACFE;
  padding: 12px 0;
  .cell {
    background-color: #4EACFE;
    color: #fff;
  }
}

/deep/ .el-table__body tr:hover > td {
  background-color: #deeeff !important;
}

/deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color:#f7f7f7;
}

/deep/.el-table--border .el-table__cell {
  border-right: 1px solid #eee;
  border-bottom: 2px solid #eee;
}
</style>
