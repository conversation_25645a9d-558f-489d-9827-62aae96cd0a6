<template>
  <div>
    <mavon-editor v-model="content" :toolbars="toolbars" :toolbarsFlag = "false"
                  :editable="false" :subfield = "false" defaultOpen="preview"
                  :externalLink="externalLink"></mavon-editor>
  </div>
</template>

<script>
  import {mavonEditor} from "mavon-editor";
  import "mavon-editor/dist/css/index.css";

  export default {
    name: "MarkDown",
    components: {mavonEditor},
    data() {
      return {
        doc: '',
        toolbars: {
        },
        externalLink: {
          markdown_css: () => 'mavon-editor/dist/markdown/github-markdown.min.css',
          hljs_js: () => 'mavon-editor/dist/highlightjs/highlight.min.js',
          hljs_css: (css) => 'mavon-editor/dist/highlightjs/styles/' + css + '.min.css',
          hljs_lang: (lang) => 'mavon-editor/dist/highlightjs/languages/' + lang + '.min.js',
          katex_css: () => 'mavon-editor/dist/katex/katex.min.css',
          katex_js: () => 'mavon-editor/dist/katex/katex.min.js'
        }
      }
    },
    props: {
      content: {
        type: String,
        default: ''
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
