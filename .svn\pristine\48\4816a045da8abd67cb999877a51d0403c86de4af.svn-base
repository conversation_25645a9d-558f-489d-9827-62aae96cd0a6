<template>
  <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogMainFormVisible" width="500px" @close="destrory()">
    <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="52px">
      <el-input v-model="temp.configId" class="display"/>
      <el-form-item label="编码" prop="code">
        <el-input v-model="temp.code" maxlength="16" :disabled="true"/>
      </el-form-item>
      <el-form-item label="取值" prop="value">
        <el-input v-model="temp.value" maxlength="25"/>
      </el-form-item>
      <el-form-item label="备注" class="margin-bottom_0">
        <el-input v-model="temp.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea"
                  maxlength="125"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">
        取消
      </el-button>
      <el-button class="save-btn" type="primary" @click="updateData()" :loading="okLoading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {checkCode} from "@/biz/utils/validate"
  import {resourceCode} from "@/biz/http/settings";

  export default {
    name: "UpdateParamsDialog",
    data() {
      return {
        okLoading: false,
        textMap: {
          update: '编辑参数',
          create: '新增参数'
        },
        // 新增/编辑界面临时数据存放
        temp: {
          configId: undefined,
          code: undefined,
          value: undefined,
          modifyUserId: undefined,
          modifyTime: undefined,
          remark: undefined
        },
        // 表单校验规则
        rules: {
          code: [{required: true, message: '编码为必填项', trigger: 'blur'}, {validator: checkCode, trigger: 'blur'}],
          value: [{required: true, message: '取值为必填项', trigger: 'blur'}]
        },
        dialogMainFormVisible: false,
        resCode: ''
      }
    },
    props: {
      dialogStatus: String,
      dialogFormVisible: Boolean,
      paramsArray: Array
    },
    methods: {
      destrory() {
        this.okLoading = false;
        this.temp = {
          configId: undefined,
          code: undefined,
          value: undefined,
          modifyUserId: undefined,
          modifyTime: undefined,
          remark: undefined
        }
      },
      timeFormat() {
        var date = new Date();
        return date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' ' + date.getHours() + ':' + date.getMinutes() + ':' + date.getSeconds();
      },
      updateData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            const tempData = Object.assign({}, this.temp)
            tempData.modifyTime = this.timeFormat()
            this.okLoading = true
            this.$api.config.save(tempData, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList");
              this.$notify({
                title: '操作成功',
                message: '编辑参数成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogStatus === 'update') {
            this.resCode = resourceCode.config_eidt
            // this.temp = Object.assign({}, this.paramsArray[0]);
            this.$api.config.getById({configId: this.paramsArray[0].configId}, this.resCode).then((res) => {
              this.temp = res.data
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
  .display {
    display: none;
  }
</style>
