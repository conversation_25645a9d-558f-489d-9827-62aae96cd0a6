<template>
  <!--表格显示列界面-->
  <el-dialog
    title="筛选表格显示列"
    class="table-column-filter-dialog"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="625px"
  >
    <el-transfer ref="transfer" v-model="value" :data="data" :titles="['备选字段', '已选字段']"></el-transfer>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click.native="handleClose">取消</el-button>
      <el-button size="mini" type="primary" @click.native="handleFilterColumns">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: "TableColumnFilterDialog",
  props: {
    tableRef: Object
  },
  data() {
    return {
      dialogVisible: false,
      data: [],
      value: [],
      selections: [] // 列表选中列
    };
  },
  computed: {
      ...mapState({
          currentUser: state => state.user.currentUser
      })
  },
  methods: {
    // 根据表格当前显示列刷新备选（已选）字段范围
    filterLableClassName() {
      let _this = this;
      _this.data.length = 0;
      _this.value.length = 0;
      const cols = this.tableRef.getAllColumnsFilterDynamic();
      cols.forEach((item, index) => {
        if (!item.label || !item.property) {
          return false;
        }
        _this.data.push({
          label: item.label,
          key: item.property
        });
      });
      this.tableRef.getRealColumns().forEach((item, index) => {
        if (!item.label || !item.property) {
          return false;
        }
        _this.value.push(item.property);
        _this.selections = _this.value;
      });
    },
    // 设置可见性
    setDialogVisible: function(visible) {
      this.dialogVisible = visible;
      // 打开窗体时，重新刷新备选字段范围、已选字段范围
      visible && this.filterLableClassName();
    },
    handleClose: function() {
      this.value = this.selections;
      this.dialogVisible = false;
    },
    // 处理表格列过滤显示
    handleFilterColumns: function() {
      if (this.value.length === 0) {
        this.$alert("请选择左侧备选字段", "提示", {
          confirmButtonText: "确定"
        });
        return;
      }
      this.selections = this.value;
      this.tableRef.showColumns(this.value);
      let cookieName = `com.thinvent.${config.systemCode}.${this.currentUser.userName}`;
      let userCookies = (localStorage.getItem(cookieName) && JSON.parse(localStorage.getItem(cookieName))) || {};
      userCookies[this.tableRef.id] = userCookies[this.tableRef.id] || {};
      // 显示列
      userCookies[this.tableRef.id].showCols = this.value;
      // 隐藏列
      userCookies[this.tableRef.id].hideCols = this.$refs.transfer.sourceData.map(o => o.key);
      localStorage.setItem(cookieName, JSON.stringify(userCookies))
      this.setDialogVisible(false);
      let _this = this
      setTimeout(function () {
        _this.tableRef.doLayout()
      }, 500)
    },
    filterMethod(query, item) {
      return item.label.indexOf(query) > -1;
    },
    updateColumns() {
      let _this = this;
      _this.data.length = 0;
      _this.value.length = 0;
      this.tableRef.getRealColumns().forEach((item, index) => {
        if (!item.label || !item.property) {
          return false;
        }
        _this.data.push({
          label: item.label,
          key: item.property
        });
        _this.value.push(item.property);
        _this.selections = _this.value;
      });
    }
  },
  watch: {
    tableRef: function(newValue, oldValue) {
      let _this = this;
      this.tableRef.getAllColumns().forEach((item, index) => {
        if (!item.label || !item.property) {
          return false;
        }
        _this.data.push({
          label: item.label,
          key: item.property
        });
      });
      this.tableRef.getRealColumns().forEach((item, index) => {
        if (!item.label || !item.property) {
          return false;
        }
        _this.value.push(item.property);
        _this.selections = _this.value;
      });
    }
  }
};
</script>
