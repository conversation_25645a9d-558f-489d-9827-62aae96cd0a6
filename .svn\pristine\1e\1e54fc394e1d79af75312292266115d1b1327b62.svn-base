<template>
  <el-dialog custom-class="msgDetail" v-dialogDrag v-show="dialogVisible" :title="title" width="900px" :visible.sync="dialogVisible" :close-on-click-modal="false" append-to-body @close='closeDialog' @open="init">
    <el-container style="display: block;" :class="instantMsg.msgType === '0' ? ' type_0' : instantMsg.msgType === '1' ? ' type_1' : ' type_2'">
<!--      <el-scrollbar :class="'scrollbar-wrapper' + instantMsg.msgType === '0' ? ' type_0' : instantMsg.msgType === '1' ? ' type_1' : ' type_2'" style="height:100%">-->
        <!-- 通知公告 -->
        <template v-if="instantMsg.msgType === '0'">
          <h1 class="title" :title="instantMsg.subject">{{instantMsg.subject}}</h1>
          <div class="time">
            <span>日期：<em>{{instantMsg.receiveTime}}</em></span>
            <span>来源：<em>{{instantMsg.senderName}}</em></span>
          </div>
          <div class="content" :style="{height: scrollerHeight, overflow: 'auto'}">
<!--            <el-scrollbar ref="type0Scrollbar" wrap-class="scrollbar-wrapper" style="height:100%">-->
              <mark-down ref="mkd" :content="instantMsg.content"></mark-down>
<!--            </el-scrollbar>-->
          </div>
        </template>
        <!-- 私人发信 -->
        <template v-else-if="instantMsg.msgType === '1'">
          <div class="top">
            <h1 class="title" :title="instantMsg.subject">{{instantMsg.subject}}</h1>
            <div><label>收件人</label><em class="name">{{instantMsg.receiverName}}</em></div>
            <div><label v-html="'时&nbsp;&nbsp;&nbsp;间'"></label><em>{{instantMsg.receiveTime}}</em></div>
            <div><label>发件人</label><em class="name">{{instantMsg.senderName}}</em></div>
          </div>
          <div class="content" :style="{height: scrollerHeight, overflow: 'auto'}">
<!--            <el-scrollbar ref="type1Scrollbar" wrap-class="scrollbar-wrapper" style="height:100%">-->
              <mark-down ref="mkd" :content="instantMsg.content"></mark-down>
<!--            </el-scrollbar>-->
          </div>
          <div v-if="instantMsg.msgType === '1' && instantMsg.needReceipt && instantMsg.needReceipt === '1'" class="receipt">
            发件人希望得到您的回执，是否发送？
          </div>
        </template>
        <!-- 业务消息 -->
        <template v-else-if="instantMsg.msgType === '2'">
          <div class="top">
            <h1 class="title" :title="instantMsg.subject">{{instantMsg.subject}}</h1>
            <div><label>收件人</label><em class="name">{{instantMsg.receiverName}}</em></div>
            <div><label v-html="'时&nbsp;&nbsp;&nbsp;间'"></label><em>{{instantMsg.receiveTime}}</em></div>
            <!-- <div><label>发件人</label><em class="name">{{instantMsg.senderName}}</em></div> -->
          </div>
          <div class="content" :style="{height: scrollerHeight, overflow: 'auto'}">
<!--            <el-scrollbar ref="type2Scrollbar" wrap-class="scrollbar-wrapper" style="height:100%">-->
              <mark-down ref="mkd" :content="instantMsg.content"></mark-down>
<!--            </el-scrollbar>-->
          </div>
        </template>
<!--      </el-scrollbar>-->
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button icon="uims-icon-nonSend" @click="closeDialog">
        关闭
      </el-button>
      <el-button v-if="instantMsg.msgType === '1' && instantMsg.needReceipt && instantMsg.needReceipt === '1'" class="save-btn" icon="uims-icon-send" type="primary" @click="sendReceipt" :loading="okLoading" v-show="showSendReceipt">
        发送回执
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import MarkDown from "@/core/components/MarkDown";
export default {
  name: 'MessageDetailPanel',
  components: {
    MarkDown
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    msg: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      scrollerHeight: "100px",
      dialogVisible: this.show,
      instantMsg: this.msg,
      okLoading: false,
      showSendReceipt: true
    }
  },
  computed: {
    title() {
      const T = ['通知公告', '私人发信', '业务消息'];
      return T[this.msg.msgType];
    }
  },
  methods: {
    init() {
      this.instantMsg = this.msg
      // 获取消息详情
      this.$api.user.getInstantMsg({ miId: this.instantMsg.miId }).then((res) => {
        // this.$set(this.instantMsg, 'content', res.data.content)
        // this.instantMsg = Object.assign({}, this.instantMsg, {content: res.data.content})
        if(res.data) {
          this.instantMsg = res.data;
        }
      }).then((msgType) => {
        // let h2 = document.documentElement.clientHeight - 380;
        let h = 410;
        if(this.instantMsg.msgType === '1') {
          h = h - 76;
        }
        h = h <= 300 ? 338 : h;
        this.scrollerHeight = `${h}px`;
        // if(this.$refs.mkd.$el.clientHeight > h2 || this.$refs.mkd.$el.clientHeight > 300) {
        //   this.$refs[`type${msgType}Scrollbar`].$el && this.$refs[`type${msgType}Scrollbar`].$el.classList.add('minusScrllobar');
        // } else {
        //   this.$refs[`type${msgType}Scrollbar`].$el && this.$refs[`type${msgType}Scrollbar`].$el.classList.remove('minusScrllobar');
        // }
      })
    },
    closeDialog() {
      this.$emit('closeModal', false);
    },
    sendReceipt() {
      this.okLoading = true
      this.$api.user.sendReceipt({ miId: this.instantMsg.miId }).then((res) => {
        this.okLoading = false
        this.showSendReceipt = false
        this.$emit('closeModal', false);
      }).catch((res) => {
        this.okLoading = false
      });
    }
  },
  watch: {
    show: function (newV, oldV) {
      this.dialogVisible = newV;
      if(this.dialogVisible) {
        this.showSendReceipt = true
      }
    },
    msg: function (newV, oldV) {
      this.instantMsg = newV;
    }
  }
}
</script>
<style lang="scss" scoped>
  >>>.el-dialog__body h1.title{
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
    display: inline-block;
  }
  >>>.el-dialog .el-dialog__body {
    height: calc(100% - 108px);
  }
</style>
