<template>
  <div class="resource-tab">
    <el-carousel class="mainConte" :autoplay="false" indicator-position="none">
      <el-carousel-item v-for="(list, index) in 1" :key="index">
   <!--     <div class="workItem">
          <img :src="imgList[0].imgSrc" alt="" />
          <div class="content">
            <div class="name">接入部门</div>
            <div class="count">
              <span class="num">{{ dataObj.accessDept || 0 }}</span>个
            </div>
          </div>
        </div>
        <div class="workItem">
          <img :src="imgList[1].imgSrc" alt="" />
          <div class="content">
            <div class="name">接入业务系统</div>
            <div class="count">
              <span class="num">{{ dataObj.accessSys|| 0 }}</span>个
            </div>
          </div>
        </div>-->
<!--        <div class="workItem">-->
<!--          <img :src="imgList[2].imgSrc" alt="" />-->
<!--          <div class="content">-->
<!--            <div class="name">数据目录总数</div>-->
<!--            <div class="count">-->
<!--              <div style="display: flex; align-items: center">-->
<!--                <span class="num">{{ dataObj.dirTotal || 0 }}</span>个-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="new">今日新增{{ dataObj.todayDirTotal || 0 }}个</div>-->
<!--          </div>-->
<!--        </div>-->
        <div class="workItem">
          <img :src="imgList[3].imgSrc" alt="" />
          <div class="content">
            <div class="name">数据资源总数</div>
            <div class="count">
              <div style="display: flex; align-items: center">
                <span class="num">{{ dataObj.resTotal || 0 }}</span>个
              </div>
            </div>
            <div class="new">今日新增{{ dataObj.todayResTotal || 0 }}个</div>
          </div>
        </div>
      <!--  <div class="workItem">
          <img :src="imgList[4].imgSrc" alt="" />
          <div class="content">
            <div class="name">数据项</div>
            <div class="count">
              <span class="num">{{ dataObj.itemColTotal || 0 }}</span>个
            </div>
          </div>
        </div>-->
        <div class="workItem">
          <img :src="imgList[5].imgSrc" alt="" />
          <div class="content">
            <div class="name">共享申请总数</div>
            <div class="count">
              <span class="num">{{ dataObj.orderTotal || 0 }}</span>条
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
export default {
  name: 'resourceTab',
  components: {},
  props: {},
  data() {
    return {
      imgList: [
        {
          imgSrc: require('../../../../assets/img/nav-1.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-2.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-3.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-4.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-5.png')
        },
        {
          imgSrc: require('../../../../assets/img/nav-6.png')
        }
      ],

      dataObj: {}
    }
  },
  methods: {
    getData() {
      if (!sessionStorage.getItem('user')) return
      const { userId, unitId, unitCode } = JSON.parse(
        sessionStorage.getItem('user')
      )
      this.$api.bizApi.pageRequest
        .getCntToPower({ userId, unitId, unitCode })
        .then((res) => {
          this.dataObj = res.data
        })
    }
  },
  mounted() {
    this.getData()
  }
}
</script>

<style scoped lang="scss">
@import '../../../../assets/global.scss';

.resource-tab {
  width: 100%;
  background: #fff;
  padding: 0 20px;

  .mainConte {
    height: 132px;
    border-radius: 2px;
    display: flex;
    align-items: center;

    .workItem {
      width: calc((100% - 60px) / 6);
      min-width: calc((100% - 60px) / 6);
      max-width: calc((100% - 60px) / 6);
      height: 103px;
      background: linear-gradient(180deg, #f2f2f2 0%, #fafdff 100%);
      box-shadow: 0px 5px 9px 0px rgba(206, 206, 206, 0.43);
      border-radius: 4px;
      border: 1px solid rgba(206, 206, 206, 0.43);
      display: flex;
      align-items: center;
      padding: 18px 24px;
      margin-right: 12px;

      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        margin-left: 15px;

        .name {
          font-size: 18px;
          color: rgba(51, 51, 51, 1);
          font-weight: 600;
        }

        .count {
          color: rgba(102, 102, 102, 1);
          font-size: 16px;

          .num {
            color: rgba(39, 117, 255, 1);
            font-size: 24px;
            font-weight: bold;
          }
        }

        .new {
          height: 22px;
          min-width: 96px;
          font-size: 14px;
          color: rgba(32, 132, 246, 1);
          padding: 0 9px;
          margin-left: 7px;
          background: #cbdfff;
          border-radius: 11px;
        }
      }
    }

    &.flexLeft {
      justify-content: space-around;
    }
  }
}

>>> .el-carousel__container {
  width: 100%;
  height: 132px;

  .el-carousel__arrow {
    display: none;
  }

  .el-carousel__item {
    display: flex;
    align-items: center;
  }
}

>>> .el-carousel__indicator--horizontal {
  padding: 6px;
  background-color: transparent;
  border-radius: 50%;

  .el-carousel__button {
    width: 11px;
    height: 11px;
    background-color: #666666;
    border-radius: 50%;
  }

  &.is-active {
    .el-carousel__button {
      background-color: $colors;
    }
  }
}
</style>
