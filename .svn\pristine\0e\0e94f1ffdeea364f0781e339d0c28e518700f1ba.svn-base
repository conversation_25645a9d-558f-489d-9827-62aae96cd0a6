<template>
  <div style="display: flex; align-items: center">
    <!-- <span class="option" :class="{ active: checkList.length === optionsArr.length  }" @click="handleCheckAllChange(filterType, num)">全部</span> -->
    <span
      class="option"
      :class="{
        active:
          checkList === undefined || checkList.length === optionsArr.length,
        disabled: disabled
      }"
      @click="handleCheckAllChange(filterType, num)"
      >全部</span
    >
    <el-checkbox-group
      v-model="checkList"
      @change="
        (val) => {
          handleChecked(val, filterType)
        }
      "
    >
      <el-checkbox
        v-for="(value, key) in options"
        :key="key"
        :label="value"
        :disabled="disabled"
      ></el-checkbox>
    </el-checkbox-group>
  </div>
</template>

<script>
import { setDictCache, getDictCache } from '@/core/utils/tabCache'
export default {
  name: 'CheckP<PERSON>',
  model: {
    prop: 'value',
    event: 'inputFun'
  },
  props: {
    dictType: String,
    filterType: String,
    mode: { type: String, default: 'sjnl' },
    num: String,
    value: {
      type: Array,
      require: false,
      default: () => []
    },
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: false },
    initFunction: Function,
    data: Object
  },
  data() {
    return {
      options: [], // 循环的内容 通过字典拿到
      checkList: [], // v-model
      optionsArr: []
    }
  },
  methods: {
    // 选中全部
    handleCheckAllChange(filterType, num) {
      const value = this.optionsArr.map((i) => i[1])
      if (this.checkList.length === value.length) {
        this.checkList = []
        this.$emit('change', '', filterType)
      } else {
        this.checkList = [...value]
        const paramsList = this.optionsArr.map((j) => j[0])
        const params = paramsList.join(',')
        this.$emit('change', params, filterType)
      }
    },
    // 选中单个
    handleChecked(val, filterType) {
      let checkedCount = val.length
      let params = ''
      if (checkedCount === this.optionsArr.length) {
        this.optionsArr.forEach((i) => {
          params === '' ? (params = i[0]) : (params = params + ',' + i[0])
        })
      } else {
        val.forEach((j) => {
          this.optionsArr.forEach((k) => {
            if (j === k[1]) {
              params === '' ? (params = k[0]) : (params = params + ',' + k[0])
            }
          })
        })
      }
      this.$emit('change', params, filterType)
    }
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.checkList = newVal
        }
      },
      immediate: true
    }
    // optionsArr() {
    //   // 在组件加载完成的时候选中全部
    //   if (this.optionsArr.length > 0) {
    //     this.handleCheckAllChange(this.filterType, this.num)
    //   }
    // }
  },
  created() {
    if (this.dictType) {
      let that = this
      // 如果外界传入data
      if (this.data) {
        if (this.initFunction) {
          this.initFunction(this.data)
        }
        that.options = this.data
        that.optionsArr = Object.entries(this.data)
        if (that.value) {
          let o = that.optionsArr.filter((a) => a[0] === that.value)
          that.model = o && o[0] ? o[0][1] : null
          // that.$emit("input", o && o[0] ? o[0][0] : null);
        }
      } else {
        // 如果依靠字典
        if (getDictCache(this.dictType)) {
          let data = getDictCache(this.dictType)
          if (this.initFunction) {
            this.initFunction(data)
          }
          that.options = data
          that.optionsArr = Object.entries(data)
          if (that.value) {
            let o = that.optionsArr.filter((a) => a[0] === that.value)
            that.model = o && o[0] ? o[0][1] : null
            that.$emit('input', o && o[0] ? o[0][0] : null)
          }
        } else {
          if (this.mode === 'sjnl') {
            this.$api.dict
              .getDictByType({ type: this.dictType })
              .then((res) => {
                // if (res.code === "200") {
                setDictCache(this.dictType, res.data)
                if (this.initFunction) {
                  this.initFunction(res.data)
                }
                that.options = res.data
                that.optionsArr = Object.entries(res.data)
                if (that.value) {
                  let o = that.optionsArr.filter((a) => a[0] === that.value)
                  that.model = o && o[0] ? o[0][1] : null
                  that.$emit('input', o && o[0] ? o[0][0] : null)
                }
                // }
              })
          } else {
            this.$api.dict
              .getDictByTypeWithGxsl({ type: this.dictType })
              .then((res) => {
                // if (res.code === "200") {
                setDictCache(this.dictType, res.data)
                if (this.initFunction) {
                  this.initFunction(res.data)
                }
                that.options = res.data
                that.optionsArr = Object.entries(res.data)
                if (that.value) {
                  let o = that.optionsArr.filter((a) => a[0] === that.value)
                  that.model = o && o[0] ? o[0][1] : null
                  that.$emit('input', o && o[0] ? o[0][0] : null)
                }
                // }
              })
          }
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.option {
  cursor: pointer;
  display: block;
  padding: 0px 10px;
  margin-right: 10px;
  line-height: 28px;
  &.active {
    background: #3572ff !important;
    border-radius: 6px;
    color: #fff;
  }
  &.disabled {
    background: #ccc;
    border-radius: 6px;
  }
}
.el-checkbox-group {
  // margin-left: 10px;
  .el-checkbox {
    margin-right: 10px;
    >>> .el-checkbox__label {
      padding-left: 3px;
      font-size: 14px;
    }
  }
  /deep/.el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #3572ff !important;
    border-color: #3572ff !important;
  }
}
</style>
