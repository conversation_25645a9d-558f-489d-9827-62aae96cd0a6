<template>
  <el-container class="page-container">
    <!-- 右侧列表 -->
    <el-header v-if="pageMode==='full'"></el-header>
    <el-container>
      <!--列表工具栏-->
      <el-header>
        <div class="toolbar-wrapper">
        <perm-button label="新增" type="text" icon="add" :perms="systemCode + 'x99004001'" @click="handleCreate"/>
<!--        <perm-button label="编辑" type="text" icon="edit" :perms="systemCode + 'x99004002'" @click="handleUpdate"/>-->
<!--        <perm-button label="删除" type="text" icon="delete" :perms="systemCode + 'x99004003'" @click="handleDelete"/>-->
<!--        <perm-button label="查看" type="text" icon="see" :perms="systemCode + 'x99004004'" @click="handleView"/>-->
        </div>
        <!--列表查询区-->
        <el-form :inline="true" :model="filters" :size="size">
          <el-form-item label="编码规则名称">
            <el-input v-model="filters.name" maxlength="100" placeholder="请输入编码规则名称" clearable @keyup.enter.native="handleFilter"/>
          </el-form-item>
          <el-form-item label="编码规则分类">
            <select-plus dictType="RULE_TYPE" v-model="filters.type" style="width: 150px" clearable></select-plus>
          </el-form-item>
          <perm-button type="primary" label="查询" icon="uims-icon-query" @click="handleFilter"/>
          <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
        </el-form>
      </el-header>
      <!--列表表格区-->
      <el-main>
        <table-plus id="Coderule" :data="list" ref="multipleTable" border fit stripe highlight-current-row v-loading="listLoading" @row-click="clickRow" @selection-change="onSelected">
          <el-table-column type="selection" width="60" align="center"></el-table-column>
          <el-table-column prop="ruleId" label="编码规则ID" width="260" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" label="编码规则名称" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="规则分类" align="center" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sortPattern" label="分类模板" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rulePattern" label="规则模板" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column class="czBox" fixed="right" label="操作" header-align="center" align="center" width="220">
            <template slot-scope="scope">
              <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
            </template>
          </el-table-column>
        </table-plus>
      </el-main>
      <el-footer>
        <table-footer ref="tableFooter" excelName="规则编码表" :showToolBar="true" :showPage="true" :tableRef="this.$refs.multipleTable" @sizeChange="handleSizeChange" @currentChange="handleCurrentChange" :currentPage="filters.currentPage" :pageSizes="[10, 20, 50, 100]" :pageSize="filters.pageSize" :total="total"
        url="/codeRule/list"
        :filters="filters">
        </table-footer>
      </el-footer>
    </el-container>
    <code-rule-main-dialog @closeDialog="closeDialog" @getList="getList" :dialogStatus="dialogStatus" :codeRuleArray="selectTableRow" :dialogFormVisible="dialogFormVisible" :contentVisible="contentVisible">
    </code-rule-main-dialog>
  </el-container>
</template>

<script>
import PermButtonGroup from "@/core/components/PermButtonGroup";
import PermButton from '@/core/components/PermButton'
import TablePlus from "@/core/components/TablePlus";
import CodeRuleMainDialog from "./Dialog/CodeRuleMainDialog";
import TableFooter from "@/core/components/TablePlus/TableFooter";
import SelectPlus from "@/core/components/SelectPlus";
import { resourceCode } from "@/biz/http/settings"
export default {
  components: { TablePlus, CodeRuleMainDialog, PermButton, TableFooter, SelectPlus, PermButtonGroup },
  data() {
    return {
      queryBt: true,
      systemCode: config.systemCode,
      dialogFormVisible: false,
      dialogStatus: '',
      contentVisible: true,
      // table参数
      size: 'mini',
      list: [],
      listLoading: true,
      total: 0,
      currentRow: undefined,
      selectTableRow: [],
      // table查询参数
      filters: {
        currentPage: 1,
        pageSize: 20,
        name: undefined,
        type: undefined
      },
      // 字典项
      dict: {
        ruleType: undefined
      },
      // 新增/编辑列表
      temp: {
        ruleId: undefined,
        systemId: undefined,
        systemName: undefined,
        name: undefined,
        type: undefined,
        sortPattern: undefined,
        rulePattern: undefined,
        serialLen: undefined,
        serialStart: undefined,
        serialStep: undefined,
        remark: undefined
      }
    }
  },
  computed: {
    pageMode() {
      return config.page_mode;
    }
  },
  // 执行方法
  methods: {
    getButtons(row) {
      let buts = [
        {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99004002'},
        {label: "删除", icon: "delete", clickFn: this.handleDelete, perms: this.systemCode + 'x99004003'},
        {label: "查看", icon: "see", clickFn: this.handleView, perms: this.systemCode + 'x99004004'}
      ];
      return {
        row: row,
        buttons: buts,
        showNums: 2
      }
    },
    // 监听表头拖拽事件
    handleHeaderDrag(newWidth, oldWidth, column, event) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    closeDialog(val) {
      this.dialogFormVisible = val
    },
    handleCreate() {
      this.dialogStatus = 'create';
      this.dialogFormVisible = true;
      this.contentVisible = false;
    },
    handleUpdate(row) {
      this.selectTableRow = [row]
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.contentVisible = false
    },
    handleView(row) {
      this.selectTableRow = [row]
      this.dialogStatus = 'view'
      this.dialogFormVisible = true
      this.contentVisible = true
    },
    handleDelete(row) {
      let ruleIds = row.ruleId
      this.$confirm('您确认要删除该编码规则吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$api.codeRule.del({ id: ruleIds }, resourceCode.codeRule_del).then((res) => {
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '删除编码规则成功',
            type: 'success',
            duration: 2000
          })
        })
      }).catch(() => {
        this.$notify({
          message: '已取消删除',
          type: 'info',
          duration: 2000
        })
      })
    },
    handleFilter(resetFlag) {
      this.filters.currentPage = 1
      if(resetFlag === true) {
        this.filters.name = undefined
        this.filters.type = undefined
      }
      this.getList()
    },
    getList() {
      if(!this.queryBt) {
        return;
      }
      this.queryBt = false
      this.listLoading = true
      this.$api.codeRule.getList(this.filters, resourceCode.codeRule).then((res) => {
        this.queryBt = true
        this.listLoading = false
        this.list = res.data.records
        this.total = res.data.total
      }).catch(res => {
        this.queryBt = true
      })
    },
    clickRow(row, event, column) {
      if (this.currentRow === row) {
        this.currentRow = undefined
        this.$refs.multipleTable.clearSelection();
      } else {
        this.currentRow = row
        this.$refs.multipleTable.clearSelection();
        this.$refs.multipleTable.toggleRowSelection(row)
      }
    },
    onSelected(row, event, column) {
      this.selectTableRow = Object.assign([], row);
    },
    handleSizeChange(val) {
      this.filters.pageSize = val
      this.filters.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.filters.currentPage = val
      this.getList()
    },
    ruleTypeFormatter(row) {
      if (!row || !row.type || !this.dict.ruleType) {
        return ''
      }
      for (let item of this.dict.ruleType) {
        if (item.code === row.type) {
          return item.name
        }
      }
    }
  },
  created() {
    this.getList()
  },
  watch: {
    total() {
      if (this.total === (this.filters.currentPage - 1) * this.filters.pageSize && this.total !== 0) {
        this.filters.currentPage -= 1;
        this.getList();
      }
    }
  }
}
</script>
