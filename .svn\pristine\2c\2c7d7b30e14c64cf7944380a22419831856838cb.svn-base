import Vue from 'vue'

// v-dialogDrag: 弹窗拖拽
Vue.directive('dialogDrag', {
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    const dragDom = el.querySelector('.el-dialog')
    dialogHeaderEl.style.cursor = 'move'

    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null)

    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop
      const minDragDomLeft = dragDom.offsetLeft;
      const maxDragDomLeft = document.body.clientWidth - minDragDomLeft - dragDom.offsetWidth;
      const minDragDomTop = dragDom.offsetTop;
      const maxDragDomTop = document.documentElement.clientHeight - minDragDomTop - dragDom.offsetHeight;

      // 获取到的值带px 正则匹配替换
      let styL, styT
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (sty.left.includes('%')) {
        styL = +document.body.clientWidth * (+sty.left.replace(/\\%/, '') / 100)
        styT = +document.body.clientHeight * (+sty.top.replace(/\\%/, '') / 100)
      } else {
        if (sty.left && sty.left.indexOf('px') > 0) {
          styL = +sty.left.replace(/(px)/, '')
        } else {
          styL = +0
        }
        if (sty.top && sty.top.indexOf('px') > 0) {
          styT = +sty.top.replace(/(px)/, '')
        } else {
          styT = +0
        }
      }
      //  console.log("styL:" + styL + ";styT:" + styT)

      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离
        let l = e.clientX - disX
        let t = e.clientY - disY

        // 边界处理
        if (-l > minDragDomLeft) {
          l = -minDragDomLeft;
        } else if (l > maxDragDomLeft) {
          l = maxDragDomLeft;
        }
        if (-t > minDragDomTop) {
          t = -minDragDomTop;
        } else if (t > maxDragDomTop) {
          t = maxDragDomTop;
        }

        // 移动当前元素
        dragDom.style.left = `${l + styL}px`
        dragDom.style.top = `${t + styT}px`

        // 将此时的位置传出去
        // binding.value({x:e.pageX,y:e.pageY})
      }

      document.onmouseup = function (e) {
        document.onmousemove = null
        document.onmouseup = null
      }
    }

    /**
     * 增加一个fullscreen按钮
     */
    if(binding.value && (binding.value.zoom || binding.value.zoomMx)) {
      const fullScreenBtn = document.createElement('button')
      fullScreenBtn.setAttribute('type', 'button')
      fullScreenBtn.className = "el-dialog__headerbtn"
      fullScreenBtn.style.setProperty('right', '42px')
      fullScreenBtn.setAttribute('aria-label', 'Close')
      const fullScreenBtnI = document.createElement('i')
      fullScreenBtnI.classList.add("el-icon-full-screen", "el-icon", "r180")
      fullScreenBtn.appendChild(fullScreenBtnI)
      let closeBtn = dialogHeaderEl.querySelector('button')
      dialogHeaderEl.insertBefore(fullScreenBtn, closeBtn)
      let dialogBodyEl = dialogHeaderEl.parentNode
      let style = dialogBodyEl.style
      let w = style.width
      // let h = style.height
      let maginTop = style['margin-top']
      let viewHeight = document.documentElement.clientHeight
      fullScreenBtn.onclick = (e) => {
        fullScreenBtnI.classList.toggle("el-icon-full-screen")
        fullScreenBtnI.classList.toggle("el-icon-copy-document")
        dialogBodyEl.classList.toggle("is-fullscreen")
        if(fullScreenBtnI.classList.contains("el-icon-full-screen")) {
          // 还原
          dialogBodyEl.style.setProperty("margin-top", maginTop)
          binding.value.zoomMx && dialogBodyEl.style.setProperty("width", w)
          dialogBodyEl.style.setProperty("height", 'auto')
          if(viewHeight * parseInt(maginTop) * 2 / 100 + dialogBodyEl.scrollHeight > viewHeight) {
            dialogBodyEl.style.setProperty("height", `calc(100% - ${parseInt(maginTop) * 2}${maginTop.match(/\d+\.*\d+(\S*)/)[1]}`)
          } else {
            dialogBodyEl.style.setProperty("height", 'auto')
          }
        } else {
          // 最大化
          dialogBodyEl.style.setProperty("margin-top", 0)
          binding.value.zoomMx && dialogBodyEl.style.setProperty("width", '100%')
          dialogBodyEl.style.setProperty("height", '100%')
        }
        dialogBodyEl.style.setProperty("left", 0)
        dialogBodyEl.style.setProperty("top", 0)
      }
    }
  }
})
