<template>
  <el-dialog v-dialogDrag :title="textMapDict[dialogDictStatus] + text" :visible.sync="dialogMainFormVisible"
             width="600px" @open="initDialog" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper">
        <el-form ref="dataDictForm" :rules="rules" :model="tempDict" label-position="right" label-width="120px"
                 @keyup.enter.native="dialogDictStatus==='create'?createDictData('1'):nullFlag()">
          <el-form-item v-show="showDictType" label="字典类型">
            <el-input v-model="tempDict.dictType" disabled />
          </el-form-item>
<!--          <el-form-item :label="'父级编码'" prop="pCode" v-if="tempDict.pCode">-->
<!--            <el-input v-model="tempDict.pCode" :disabled="true" maxlength="2" />-->
<!--          </el-form-item>-->

          <el-form-item :label="text + '编码'" prop="cCode">
            <el-input v-model="tempDict.cCode" :disabled="dialogDictStatus!=='create'?true:false" maxlength="2"
                      minlength="2" />
          </el-form-item>
          <el-form-item :label="text + '名称'" prop="name">
            <el-input v-model="tempDict.name" maxlength="25" />
          </el-form-item>
          <el-form-item label="排序号">
            <el-input-number v-model="tempDict.orderNo" controls-position="right" :min="1" :max="9999" />
          </el-form-item>
          <el-form-item label="是否启用" prop="enabled" v-show="showEnabledType">
            <select-plus dictType="IS_ENABLE" v-model="tempDict.enabled"></select-plus>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="tempDict.remark" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" maxlength="125" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDict">
        取消
      </el-button>
      <el-button class="saveAdd-btn" type="primary" @click="createDictData('1')" v-if="dialogDictStatus==='create'"
                 :loading="okLoading">
        新增保存
      </el-button>
      <el-button class="save-btn" type="primary"
                 @click="dialogDictStatus==='create'?createDictData('0'):updateDictData()" :loading="okLoading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import SelectPlus from "@/core/components/SelectPlus";
  import {checkCode} from "@/biz/utils/validate"
  import {resourceCode} from "@/biz/http/settings"

  export default {
    name: "DictDialog",
    components: {SelectPlus},
    data() {
      return {
        okLoading: false,
        textMapDict: {
          update: '编辑',
          create: '新增'
        },
        // 新增/编辑字典项界面临时数据存放
        tempDict: {
          pCode: undefined,
          dictId: undefined,
          dictType: this.defDictType,
          code: undefined,
          cCode: undefined,
          name: undefined,
          fullName: undefined,
          orderNo: 1,
          enabled: '1',
          remark: undefined
        },
        dialogMainFormVisible: false,
        resCode: '',
        // 表单校验规则
        rules: {
          cCode: [{required: true, message: '字典编码为必填项', trigger: 'blur'}, {
            validator: checkCode,
            trigger: 'blur'
          }, {min: 2, max: 2, message: '长度必须为2个字符'}],
          name: [{required: true, message: '字典名称为必填项', trigger: 'blur'}],
          enabled: [{required: this.showEnabledType, message: '是否启用为必选项'}]
        },
        // 字典
        dict: {
          isEnable: undefined
        }
      }
    },
    props: {
      dialogDictStatus: String,
      dialogDictVisible: Boolean,
      dictTypeTemp: Object,
      text: String,
      defDictType: String,
      showEnabledType: {
        type: Boolean,
        default: true
      },
      showDictType: {
        type: Boolean,
        default: true
      },
      selectDiztId: String
    },
    methods: {
      destrory() {
        this.dialogMainFormVisible = false;
        this.okLoading = false;
        this.resetTemp()
      },
      closeDict() {
        this.dialogMainFormVisible = false;
      },
      nullFlag() {
      },
      initDialog() {
        this.tempDict.dictType = this.defDictType
      },
      createDictData(val) {
        this.$refs['dataDictForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            if (this.tempDict.pCode) {
              this.tempDict.code = this.tempDict.pCode + '' + this.tempDict.cCode
            } else {
              this.tempDict.code = this.tempDict.cCode;
            }
            this.$api.bizDict.save(this.tempDict, this.resCode).then((res) => {
              this.okLoading = false
              if (val === '1') {
                this.$emit("getList");
              } else {
                this.$emit("getList");
                this.resetTemp()
                this.dialogMainFormVisible = false;
                this.$nextTick(() => {
                  this.$refs['dataDictForm'].clearValidate()
                })
              }
              this.$notify({
                title: '操作成功',
                message: '新增' + this.text + '成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      updateDictData() {
        this.$refs['dataDictForm'].validate((valid) => {
          if (valid) {
            if (this.tempDict.pCode) {
              this.tempDict.code = this.tempDict.pCode + '' + this.tempDict.cCode
            } else {
              this.tempDict.code = this.tempDict.cCode;
            }
            const tempData = Object.assign({}, this.tempDict)
            this.okLoading = true
            this.$api.bizDict.save(tempData, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false;
              this.$emit("getList");
              this.$notify({
                title: '操作成功',
                message: '编辑' + this.text + '成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.tempDict = {
          pCode: undefined,
          dictId: undefined,
          dictType: this.dictTypeTemp.code,
          code: undefined,
          cCode: undefined,
          name: undefined,
          fullName: undefined,
          orderNo: 1,
          enabled: '1',
          remark: undefined
        }
      }
    },
    watch: {
      dialogDictVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogDictStatus === 'create') {
            this.resCode = resourceCode.dict_addC
            this.resetTemp()
            // this.tempDict.pCode = this.dictTypeTemp.code;
            this.$nextTick(() => {
              this.$refs['dataDictForm'].clearValidate()
            })
          } else if (this.dialogDictStatus === 'update') {
            this.resCode = resourceCode.dict_eidtC
            this.$api.bizDict.getById({dictId: this.selectDiztId}, this.resCode).then((res) => {
              this.tempDict = Object.assign({}, res.data)
              if (res.data.code && res.data.code.length > 2) {
                this.tempDict.cCode = res.data.code
                // this.tempDict.pCode = res.data.code.slice(0, res.data.code.length - 2)
              } else {
                this.tempDict.cCode = res.data.code
              }
            })
            this.$nextTick(() => {
              this.$refs['dataDictForm'].clearValidate()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
</style>
