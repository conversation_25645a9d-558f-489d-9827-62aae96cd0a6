hljs.registerLanguage("routeros",function(e){var r="foreach do while for if from to step else on-error and or not in",i="global local beep delay put len typeof pick log time set find environment terminal error execute parse resolve toarray tobool toid toip toip6 tonum tostr totime",o="add remove enable disable set get print export edit find run debug error info warning",t="true false yes no nothing nil null",s="traffic-flow traffic-generator firewall scheduler aaa accounting address-list address align area bandwidth-server bfd bgp bridge client clock community config connection console customer default dhcp-client dhcp-server discovery dns e-mail ethernet filter firewall firmware gps graphing group hardware health hotspot identity igmp-proxy incoming instance interface ip ipsec ipv6 irq l2tp-server lcd ldp logging mac-server mac-winbox mangle manual mirror mme mpls nat nd neighbor network note ntp ospf ospf-v3 ovpn-server page peer pim ping policy pool port ppp pppoe-client pptp-server prefix profile proposal proxy queue radius resource rip ripng route routing screen script security-profiles server service service-port settings shares smb sms sniffer snmp snooper socks sstp-server system tool tracking type upgrade upnp user-manager users user vlan secret vrrp watchdog web-access wireless pptp pppoe lan wan layer7-protocol lease simple raw",n={cN:"variable",v:[{b:/\$[\w\d#@][\w\d_]*/},{b:/\$\{(.*?)}/}]},p={cN:"string",b:/"/,e:/"/,c:[e.BE,n,{cN:"variable",b:/\$\(/,e:/\)/,c:[e.BE]}]},a={cN:"string",b:/'/,e:/'/};return{aliases:["routeros","mikrotik"],cI:!0,l:/:?[\w-]+/,k:{literal:t,keyword:r+" :"+r.split(" ").join(" :")+" :"+i.split(" ").join(" :")},c:[{v:[{b:/^@/,e:/$/},{b:/\/\*/,e:/\*\//},{b:/%%/,e:/$/},{b:/^'/,e:/$/},{b:/^\s*\/[\w-]+=/,e:/$/},{b:/\/\//,e:/$/},{b:/^\[\</,e:/\>\]$/},{b:/<\//,e:/>/},{b:/^facet /,e:/\}/},{b:"^1\\.\\.(\\d+)$",e:/$/}],i:/./},e.C("^#","$"),p,a,n,{b:/[\w-]+\=([^\s\{\}\[\]\(\)]+)/,r:0,rB:!0,c:[{cN:"attribute",b:/[^=]+/},{b:/=/,eW:!0,r:0,c:[p,a,n,{cN:"literal",b:"\\b("+t.split(" ").join("|")+")\\b"},{b:/("[^"]*"|[^\s\{\}\[\]]+)/}]}]},{cN:"number",b:/\*[0-9a-fA-F]+/},{b:"\\b("+o.split(" ").join("|")+")([\\s[(]|])",rB:!0,c:[{cN:"builtin-name",b:/\w+/}]},{cN:"built_in",v:[{b:"(\\.\\./|/|\\s)(("+s.split(" ").join("|")+");?\\s)+",r:10},{b:/\.\./}]}]}});