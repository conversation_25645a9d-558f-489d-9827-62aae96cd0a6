<template>
  <div>
    <!-- 新增Form -->
    <el-dialog v-dialogDrag title="消息订阅" :visible.sync="dialogMainFormVisible" width="calc(100% - 100px)" min-width="800px"
               @open="initDialog" @close="destrory()">
      <el-container>
        <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="120px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="业务消息编码" prop="mbId">
                  <el-input :disabled="true" v-model="temp.mbId" maxlength="32"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="业务消息名称" prop="bizName">
                  <el-input :disabled="true" v-model="temp.bizName" maxlength="50"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="订阅用户">
              <el-tooltip class="item" effect="light" content="选择用户" placement="bottom">
                <perm-button type="text" icon="add" size="mini" @click="handleSelectUsers"/>
              </el-tooltip>
              <el-tooltip class="item" effect="light" content="删除" placement="bottom">
                <perm-button type="text" icon="delete" size="mini" @click="handleDelUsers"/>
              </el-tooltip>
              <el-table
                border
                :data="temp.subscripts"
                :row-class-name="tableRowClassName"
                @selection-change="selectUsersTableRow"
              >
                <el-table-column width="60" type="selection" label="" header-align="center" align="center">
                </el-table-column>
                <el-table-column width="60" type="index" label="序号" header-align="center" align="left">
                </el-table-column>
                <el-table-column prop="userName" width="200" label="用户名称" header-align="center" align="left" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="realName" width="120" label="用户实名" header-align="center" align="left" show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="isSendMsg" width="100" label="站内消息" header-align="center" align="center">
                  <template slot-scope="{row}">
                    <el-checkbox :disabled="temp.isSendMsg === '0' || temp.isForceMsg === '1'" v-model="row.isSendMsg"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="isSendEmail" width="100" label="邮箱消息" header-align="center" align="center">
                  <template slot-scope="{row}">
                    <el-checkbox :disabled="temp.isSendEmail === '0' || temp.isForceEmail === '1'" v-model="row.isSendEmail"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column prop="isSendSms" width="100" label="短信消息" header-align="center" align="center">
                  <template slot-scope="{row}">
                    <el-checkbox :disabled="temp.isSendSms === '0' || temp.isForceSms === '1'" v-model="row.isSendSms"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column v-for="item in temp.otherConfig" :prop="item.type" width="100" :label="`${item.typeName}消息`" :key="item.type" header-align="center" align="center">
                  <template slot-scope="{row}">
                    <el-checkbox :disabled="item.isSend === '0' || item.isForce === '1'" v-model="row.otherConfig[`${item.type}`]"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMainFormVisible = false">取消</el-button>
        <el-button class="save-btn" type="primary" @click="save()" :loading="okLoading">保存
        </el-button>
      </div>
    </el-dialog>
    <select-user
      @closeDialog="closeSelectUserDialog"
      @selectedUsers="selectedUsers"
      :allUsers="allUsers"
      :selectUsers="selectUserIds"
      :dialogFormVisible="dialogSelectUserVisible"
    >
    </select-user>
  </div>
</template>

<script>
  import SelectPlus from "@/core/components/SelectPlus";
  import PermButton from '@/core/components/PermButton'
  import SelectUser from './SelectUser'
  import {resourceCode} from "@/biz/http/settings"
  export default {
    components: {SelectPlus, PermButton, SelectUser},
    name: "MainDialog",
    data() {
      return {
        activeCollapse: ['1', '2', '3'],
        okLoading: false,
        resCode: '',
        allUsers: [],
        modeMap: {
          create: '新增',
          update: '编辑',
          view: '查看'
        },
        // 新增/编辑列表
        oldSubscripts: [], // 编辑前的users 用于找到新增、删除、编辑的用户订阅
        temp: {
          subscripts: [],
          bizName: undefined,
          mbId: undefined,
          isSendMsg: undefined,
          isForceMsg: undefined,
          isSendEmail: undefined,
          isForceEmail: undefined,
          isSendSms: undefined,
          isForceSms: undefined
        },
        // 表单校验规则
        rules: {
          mbId: [{required: true, message: '此项为必填项', trigger: 'blur'}],
          bizName: [{required: true, message: '此项为必填项', trigger: 'blur'}]
        },
        selectUsers: [],
        selectUserIds: [],
        dialogMainFormVisible: false,
        dialogSelectUserVisible: false
      }
    },
    props: {
      dialogFormVisible: Boolean,
      mbId: String
    },
    methods: {
      closeSelectUserDialog(val) {
        this.dialogSelectUserVisible = val
      },
      selectedUsers(selectUsers) {
        if(selectUsers && selectUsers.length > 0) {
          if(this.temp.subscripts && this.temp.subscripts.length > 0) {
            for(let i = 0; i < selectUsers.length; i++) {
              let flag = true
              for(let j = 0; j < this.temp.subscripts.length; j++) {
                if(this.temp.subscripts[j].userId === selectUsers[i].userId) {
                  flag = false
                }
              }
              if(flag) {
                // 新增的用户
                if(!this.temp.subscripts) {
                  this.temp.subscripts = []
                }
                let otherConfig = {}
                this.temp.otherMsgConfig && JSON.parse(this.temp.otherMsgConfig).forEach(item => {
                  otherConfig[item.type] = item.isForce === '1' ? true : item.isSend === '1'
                })
                this.temp.subscripts.push({
                  userId: selectUsers[i].userId,
                  realName: selectUsers[i].realName,
                  userName: selectUsers[i].userName,
                  isSendMsg: this.temp.isForceMsg === '1' ? true : this.temp.isSendMsg === '1',
                  isSendEmail: this.temp.isForceEmail === '1' ? true : this.temp.isSendEmail === '1',
                  isSendSms: this.temp.isForceSms === '1' ? true : this.temp.isSendSms === '1',
                  otherConfig
                })
              }
            }
            // 反向找到删除的用户
            for(let j = 0; j < this.temp.subscripts.length; j++) {
              let flag = true
              for(let i = 0; i < selectUsers.length; i++) {
                if(this.temp.subscripts[j].userId === selectUsers[i].userId) {
                  flag = false
                }
              }
              if(flag) {
                // 删除de用户
                this.temp.subscripts.splice(j, 1)
                j--
              }
            }
           } else {
            // 全增加
            this.temp.subscripts = []
            for(let i = 0; i < selectUsers.length; i++) {
              let otherConfig = {}
              this.temp.otherMsgConfig && JSON.parse(this.temp.otherMsgConfig).forEach(item => {
                otherConfig[item.type] = item.isForce === '1' ? true : item.isSend === '1'
              })
              this.temp.subscripts.push({
                userId: selectUsers[i].userId,
                realName: selectUsers[i].realName,
                userName: selectUsers[i].userName,
                isSendMsg: this.temp.isForceMsg === '1' ? true : this.temp.isSendMsg === '1',
                isSendEmail: this.temp.isForceEmail === '1' ? true : this.temp.isSendEmail === '1',
                isSendSms: this.temp.isForceSms === '1' ? true : this.temp.isSendSms === '1',
                otherConfig
              })
            }
          }
        } else {
          // 全删除
          this.temp.subscripts = []
        }
      },
      tableRowClassName({row, rowIndex}) {
         // 把每一行的索引放进row
        row.index = rowIndex;
       },
      selectUsersTableRow(row, event, column) {
        this.selectUsers = Object.assign([], row)
      },
      handleSelectUsers() {
        this.selectUserIds = this.temp.subscripts.map(item => item.userId)
        this.dialogSelectUserVisible = true
      },
      handleDelUsers() {
        this.selectUsers && this.selectUsers.forEach(user => {
          this.temp.subscripts.splice(this.temp.subscripts.findIndex(item => item.userId === user.userId), 1)
        })
      },
      destrory() {
        this.resetTemp();
        this.okLoading = false;
      },
      initDialog() {
      },
      save() {
        this.$refs['dataForm'].validate((valid, obj) => {
          if (valid) {
            this.okLoading = true
            this.temp.oldSubscript = Object.assign([], this.oldSubscripts)
            let tempObj = Object.assign({}, this.temp)
            if(tempObj.subscripts && tempObj.subscripts.length > 0) {
              for(let i = 0; i < tempObj.subscripts.length; i++) {
                if(tempObj.subscripts[i].isSendEmail === true) {
                  tempObj.subscripts[i].isSendEmail = "1"
                } else {
                  if(tempObj.subscripts[i].isSendEmail !== "1") {
                    tempObj.subscripts[i].isSendEmail = "0"
                  }
                }
                if(tempObj.subscripts[i].isSendMsg === true) {
                  tempObj.subscripts[i].isSendMsg = "1"
                } else {
                  if(tempObj.subscripts[i].isSendMsg !== "1") {
                    tempObj.subscripts[i].isSendMsg = "0"
                  }
                }
                if(tempObj.subscripts[i].isSendSms === true) {
                  tempObj.subscripts[i].isSendSms = "1"
                } else {
                  if(tempObj.subscripts[i].isSendSms !== "1") {
                    tempObj.subscripts[i].isSendSms = "0"
                  }
                }
                const otherConfigs = tempObj.subscripts[i].otherConfig
                let configs = Object.keys(otherConfigs).map(key => {
                  return {
                    type: key,
                    isSend: otherConfigs[key] ? "1" : "0"
                  }
                })
                tempObj.subscripts[i].otherMsgSendConfig = JSON.stringify(configs)
              }
            }
            if(tempObj.oldSubscript && tempObj.oldSubscript.length > 0) {
              for(let j = 0; j < tempObj.oldSubscript.length; j++) {
                if(tempObj.oldSubscript[j].isSendEmail === true) {
                  tempObj.oldSubscript[j].isSendEmail = "1"
                } else {
                  if(tempObj.oldSubscript[j].isSendEmail !== "1") {
                    tempObj.oldSubscript[j].isSendEmail = "0"
                  }
                }
                if(tempObj.oldSubscript[j].isSendMsg === true) {
                  tempObj.oldSubscript[j].isSendMsg = "1"
                } else {
                  if(tempObj.oldSubscript[j].isSendMsg !== "1") {
                    tempObj.oldSubscript[j].isSendMsg = "0"
                  }
                }
                if(tempObj.oldSubscript[j].isSendSms === true) {
                  tempObj.oldSubscript[j].isSendSms = "1"
                } else {
                  if(tempObj.oldSubscript[j].isSendSms !== "1") {
                    tempObj.oldSubscript[j].isSendSms = "0"
                  }
                }
              }
            }
            this.$api.msgBiz.saveSubscript(this.temp, resourceCode.msgBiz).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$notify({
                title: '操作成功',
                message: '设置订阅成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.temp = {
          users: [],
          bizName: undefined,
          mbId: undefined,
          isSendMsg: undefined,
          isForceMsg: undefined,
          isSendEmail: undefined,
          isForceEmail: undefined,
          isSendSms: undefined,
          isForceSms: undefined
        }
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          this.$api.msgOpen.getSubscriptUserList({}, resourceCode.msgBiz).then((res) => {
            this.allUsers = res.data
            this.$api.msgBiz.getSubscriptByMbId({mbId: this.mbId}, resourceCode.msgBiz).then((res) => {
              let oldUsers = []
              if(res.data.subscripts) {
                for(let i = 0; i < res.data.subscripts.length; i++) {
                  for(let j = 0; j < this.allUsers.length; j++) {
                    if(res.data.subscripts[i].userId === this.allUsers[j].userId) {
                      res.data.subscripts[i].isSendEmail = res.data.subscripts[i].isSendEmail === "1"
                      res.data.subscripts[i].isSendMsg = res.data.subscripts[i].isSendMsg === "1"
                      res.data.subscripts[i].isSendSms = res.data.subscripts[i].isSendSms === "1"
                      res.data.subscripts[i].otherConfig = {}
                      res.data.subscripts[i].otherMsgSendConfig && JSON.parse(res.data.subscripts[i].otherMsgSendConfig).forEach(item => {
                        res.data.subscripts[i].otherConfig[item.type] = item.isSend === "1"
                      })
                      oldUsers.push(res.data.subscripts[i])
                      break
                    }
                  }
                }
              }
              this.oldSubscripts = oldUsers
              this.temp = {
                subscripts: Object.assign([], oldUsers),
                bizName: res.data.bizName,
                mbId: res.data.mbId,
                isSendMsg: res.data.isSendMsg,
                isForceMsg: res.data.isForceMsg,
                isSendEmail: res.data.isSendEmail,
                isForceEmail: res.data.isForceEmail,
                isSendSms: res.data.isSendSms,
                isForceSms: res.data.isForceSms,
                otherMsgConfig: res.data.otherMsgConfig,
                otherConfig: (res.data.otherMsgConfig && JSON.parse(res.data.otherMsgConfig)) || []
              }
            })
          })
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
