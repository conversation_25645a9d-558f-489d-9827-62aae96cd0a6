<template>
  <div>
    <el-row :gutter="24">
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>登录密码</span>
          </div>
          <div class="block-info">
            <span class="block-info-c" style="background: #3d4247;height: 40px;width: 179px;display: inline-block;border-radius: 20px;"><b></b><b></b><b></b><b></b><b></b><b></b><b></b></span>
          </div>
          <div class="dialog-footer">
            <perm-button label="编辑密码" type="primary" icon="edit" :perms="systemCode + 'x98002001'" @click="chgPwdDialogVisible=true" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>手机号</span>
          </div>
          <div class="block-info">
            <span class="block-info-c" v-text="user.mobile"></span><span v-if="!user.mobile">(未绑定)</span><span v-if="user.mobile && user.mobileAuthed !== '1'">(未绑定)</span>
            <el-switch v-if="user.mobileAuthed === '1'" v-model="user.mobileLogin" active-text="启用手机登录" @change="mobileLoginChange()"></el-switch>
            <!--            <el-checkbox class="block-info-c" key="mobile" v-if="user.mobileAuthed === '1'" v-model="user.mobileLogin" @change="mobileLoginChange()">启用手机登录</el-checkbox>-->
          </div>
          <div class="dialog-footer">
            <perm-button v-if="user.mobileAuthed === '1'" label="编辑手机号" type="primary" icon="edit" :perms="systemCode + 'x98002002'" @click="handleChangeMobile" />
            <perm-button v-else label="绑定手机号" type="primary" icon="edit" :perms="systemCode + 'x98002003'" @click="bindMobile" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>电子邮箱</span>
          </div>
          <div class="block-info">
            <span class="block-info-c" v-text="user.email"></span><span v-if="!user.email">(未绑定)</span><span v-if="user.email && user.emailAuthed !== '1'">(未绑定)</span>
            <el-switch v-if="user.emailAuthed === '1'" v-model="user.emailLogin" active-text="启用电子邮箱登录" @change="emailLoginChange()"></el-switch>
            <!--            <el-checkbox class="block-info-c" key="email" v-if="user.emailAuthed === '1'" v-model="user.emailLogin" @change="emailLoginChange()">启用电子邮箱登录</el-checkbox>-->
          </div>
          <div class="dialog-footer">
            <perm-button v-if="user.emailAuthed === '1'" label="编辑电子邮箱" type="primary" icon="edit" :perms="systemCode + 'x98002005'" @click="handleChangeEmail" />
            <perm-button v-else label="绑定电子邮箱" type="primary" icon="edit" :perms="systemCode + 'x98002004'" @click="bindEmail" />
          </div>
        </el-card>
      </el-col>
    </el-row>
    <chg-pwd :show="chgPwdDialogVisible" @closeModal="closeChgPwdDialog" />
    <el-dialog v-dialogDrag :title="'绑定' + textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="750px" @close="closeBind()">
      <el-form ref="dataForm" :rules="rules" :model="binding" label-position="right" label-width="90px">
        <el-form-item :label="textMap[dialogStatus]" prop="account">
          <el-input v-model="binding.account" class="input-with-select" :maxlength="dialogStatus === 'mobile' ? 18 : 32">
            <el-button slot="append" v-show="dialogStatus === 'mobile' ? binding.show_m : binding.show_e" type="primary" @click="sendVisibleCode">发送验证码</el-button>
            <el-button style="margin-left: 0px; width: 122px;"
                       slot="append"
                       v-show="dialogStatus === 'mobile' ? !binding.show_m : !binding.show_e"
            >{{ (dialogStatus === 'mobile' ? binding.sec_m : binding.sec_e) + '秒后重发'}}</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="checkVisibleCode">
          <el-input v-model="binding.visibleCode" maxlength="4" />
        </el-form-item>
        <el-form-item>
          <el-switch v-model="binding.loginFlag" :active-text="'启用' + textMap[dialogStatus] + '登录'"></el-switch>
          <!--          <el-checkbox v-model="binding.loginFlag"></el-checkbox>-->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button icon="uims-icon-save" type="primary" @click="saveBind">
          绑定{{textMap[dialogStatus]}}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog v-dialogDrag :title="'编辑' + textMap[dialogStatus]" :visible.sync="change.dialogFormVisible" @closed="initChangeBindDialog" width="750px">
      <el-container>
        <el-header>
          <el-steps :active="change.active" :space="200" finish-status="success" align-center>
            <el-step title="选择认证方式"></el-step>
            <el-step title="身份确认"></el-step>
            <el-step title="变更绑定"></el-step>
          </el-steps>
        </el-header>
        <el-main>
          <div v-show="change.active === 1 && change.visibleType === 'mobile'">
            <el-form ref="changeMobileVisibleDataForm" :rules="rules" :model="change" label-position="right" label-width="90px">
              <el-form-item label="原手机号">
                <el-input v-model="change.oldAccount" :disabled="true" class="input-with-select" maxlength="18">
                  <!--<el-button slot="append" v-show="binding.show" type="primary" @click="sendVisibleCode">发送验证码-->
                  <!--</el-button>-->
                  <perm-button slot="append" label="发送验证码" type="primary" icon="uims-icon-send" @click="sendVisibleCode" v-show="binding.show_m" />
                  <el-button style="margin-left: 0px; width: 122px;" slot="append" v-show="!binding.show_m">{{binding.sec_m + '秒后重发'}}</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="checkVisibleCode">
                <el-input v-model="change.visibleCode" maxlength="4" />
              </el-form-item>
            </el-form>
          </div>
          <div v-show="change.active === 1 && change.visibleType === 'email'">
            <el-form ref="changeEmailVisibleDataForm" :rules="rules" :model="change" label-position="right" label-width="90px">
              <el-form-item label="原邮箱地址">
                <el-input v-model="change.oldAccount" :disabled="true" class="input-with-select" maxlength="32">
                  <!--<el-button slot="append" v-show="binding.show" type="primary" @click="sendVisibleCode">发送验证码-->
                  <!--</el-button>-->
                  <perm-button slot="append" label="发送验证码" type="primary" icon="uims-icon-send" @click="sendVisibleCode" v-show="binding.show_e" />
                  <el-button style="margin-left: 0px; width: 122px;" slot="append" v-show="!binding.show_e">{{binding.sec_e + '秒后重发'}}</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="checkVisibleCode">
                <el-input v-model="change.visibleCode" maxlength="4" />
              </el-form-item>
            </el-form>
          </div>
          <div v-show="change.active === 2">
            <el-form ref="changeVisibleDataForm" :rules="rules" :model="change" label-position="right" label-width="100px">
              <el-form-item :label="'新' + textMap[dialogStatus]" prop="newAccount">
                <el-input v-model="change.newAccount" class="input-with-select" :maxlength="dialogStatus === 'mobile' ? 18 : 32">
                  <!--<el-button slot="append" v-show="binding.show" type="primary" @click="sendVisibleCode">发送验证码-->
                  <!--</el-button>-->
                  <perm-button slot="append" label="发送验证码" type="primary" icon="uims-icon-send" @click="sendVisibleCode" v-show="dialogStatus === 'mobile' ? binding.show_m : binding.show_e" />
                  <el-button style="margin-left: 0px; width: 122px;" slot="append" v-show="dialogStatus === 'mobile' ? !binding.show_m : !binding.show_e">{{ (dialogStatus === 'mobile' ? binding.sec_m : binding.sec_e) + '秒后重发'}}</el-button>
                </el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="checkVisibleCode">
                <el-input v-model="change.visibleCode" maxlength="4" />
              </el-form-item>
              <el-form-item>
                <el-switch v-model="change.loginFlag" :active-text="'启用' + textMap[dialogStatus] + '登录'"></el-switch>
                <!--                <el-checkbox v-model="change.loginFlag">启用{{textMap[dialogStatus]}}登录</el-checkbox>-->
              </el-form-item>
            </el-form>
          </div>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <div v-show="change.active === 0">
          <el-button type="primary" @click="selectVisibleType('mobile')" class="save-btn">手机认证</el-button>
          <el-button type="primary" @click="selectVisibleType('email')" class="save-btn">邮箱认证</el-button>
        </div>
        <div v-show="change.active === 1 && change.visibleType === 'mobile'">
          <el-button icon="uims-icon-save" type="primary" @click="change.active = 0" class="save-btn">上一步</el-button>
          <el-button icon="uims-icon-save" type="primary" @click="visibleOk" class="save-btn">确认</el-button>
        </div>
        <div v-show="change.active === 1 && change.visibleType === 'email'">
          <el-button icon="uims-icon-save" type="primary" @click="change.active = 0" class="save-btn">上一步</el-button>
          <el-button icon="uims-icon-save" type="primary" @click="visibleOk" class="save-btn">确认</el-button>
        </div>
        <div v-show="change.active === 2">
          <el-button icon="uims-icon-save" type="primary" @click="change.active = 1" class="save-btn">上一步</el-button>
          <el-button icon="uims-icon-save" type="primary" @click="saveChange" class="save-btn">变更绑定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import PermButton from '@/core/components/PermButton'
import ChgPwd from '@/core/views/Core/ChgPwd'
import { isMobile, isEmail } from '@/core/utils/validate'
import { resourceCode } from "@/biz/http/settings";
export default {
  components: { PermButton, ChgPwd },
  data() {
    var checkNewAccount = (rule, value, callback) => {
      if (this.dialogStatus === 'mobile') {
        // console.log(value)
        if (!isMobile(value)) {
          callback(new Error('请输入正确的手机号'))
        } else {
          callback()
        }
      } else {
        // eslint-disable-next-line no-useless-escape
        if (!isEmail(value)) {
          callback(new Error('请输入正确的邮箱地址'))
        } else {
          callback()
        }
      }
    }
    return {
      systemCode: config.systemCode,
      // 编辑密码组件显示隐藏
      chgPwdDialogVisible: false,
      interval_e: undefined,
      interval_m: undefined,
      dialogStatus: '',
      textMap: {
        mobile: '手机号',
        email: '电子邮箱'
      },
      binding: {
        visibleCode: '',
        account: '',
        sec_m: 300,
        sec_e: 300,
        loginFlag: false,
        show_m: true,
        show_e: true
      },
      levels: [{ name: '弱', color: 'level1' }, { name: '中', color: 'def-level' }, { name: '强', color: 'def-level' }],
      level: '1',
      dialogFormVisible: false,
      user: {
        userName: undefined,
        userId: undefined,
        userType: undefined,
        mobile: undefined,
        mobileAuthed: undefined,
        mobileLogin: undefined,
        address: undefined,
        email: undefined,
        emailAuthed: undefined,
        emailLogin: undefined,
        remark: undefined
      },
      rules: {
        account: [{ required: true, message: '此项为必填项', trigger: 'blur' }, {
          validator: checkNewAccount,
          trigger: 'blur'
        }],
        // checkVisibleCode: [{required: true, message: '此项为必填项', trigger: 'blur'}], // validator: checkVisibleCode
        newAccount: [{ required: true, message: '此项为必填项', trigger: 'blur' }, {
          validator: checkNewAccount,
          trigger: 'blur'
        }]
      },
      change: {
        newAccount: '',
        dialogFormVisible: false,
        active: 0,
        visibleType: undefined,
        oldAccount: '',
        visibleCode: '',
        loginFlag: false,
        sec_e: 300,
        sec_m: 300,
        show: true
      }
    }
  },
  methods: {
    closeBind() {
      this.binding.visibleCode = ''
      this.binding.loginFlag = false
    },
    handleChangeMobile() {
      this.change.dialogFormVisible = true
      this.dialogStatus = 'mobile'
      this.$nextTick(() => {
        this.$refs['changeVisibleDataForm'].clearValidate()
      })
    },
    handleChangeEmail() {
      this.change.dialogFormVisible = true
      this.dialogStatus = 'email'
      this.$nextTick(() => {
        this.$refs['changeVisibleDataForm'].clearValidate()
      })
    },
    getUserInfo() {
      let userId = JSON.parse(sessionStorage.getItem('user')).userId
      this.$api.user.getById({ userId: userId }, resourceCode.userSafe).then((res) => {
        this.user = Object.assign({}, res.data)
        this.user.mobileLogin = this.user.mobileLogin === '1'
        this.user.emailLogin = this.user.emailLogin === '1'
      })
    },
    mobileLoginChange() {
      let userId = this.user.userId
      this.$api.user.mobileLoginChange({
        userId: userId,
        enabled: this.user.mobileLogin ? '1' : '0'
      }, resourceCode.userSafe).then((res) => {
        this.getUserInfo()
        this.$notify({
          title: '操作成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    emailLoginChange() {
      let userId = this.user.userId
      this.$api.user.emailLoginChange({
        userId: userId,
        enabled: this.user.emailLogin ? '1' : '0'
      }, resourceCode.userSafe).then((res) => {
        this.getUserInfo()
        this.$notify({
          title: '操作成功',
          message: '操作成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    bindMobile() {
      this.dialogFormVisible = true
      this.dialogStatus = 'mobile'
      this.binding.account = this.user.mobile
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    bindEmail() {
      this.dialogFormVisible = true
      this.dialogStatus = 'email'
      this.binding.account = this.user.email
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    sendVisibleCode() {
      if (this.dialogFormVisible) {
        this.$refs['dataForm'].validateField('account', (valid) => {
          if (!valid) {
            if (this.dialogStatus === 'mobile') {
              // 校验是否被绑定
              this.$api.user.checkMobileBounded({ code: this.binding.account }, resourceCode.userSafe).then((res) => {
                if (!res.data) {
                  this.toSendMobileVisibleCode(this.binding.account)
                } else {
                  this.$notify({
                    title: '操作失败',
                    message: '该手机号已被其他用户绑定',
                    type: 'fail',
                    duration: 2000
                  })
                }
              })
            } else if (this.dialogStatus === 'email') {
              // 校验是否被绑定
              this.$api.user.checkEmailBounded({ code: this.binding.account }, resourceCode.userSafe).then((res) => {
                if (!res.data) {
                  this.toSendEmailVisibleCode(this.binding.account)
                } else {
                  this.$notify({
                    title: '操作失败',
                    message: '该邮箱已被其他用户绑定',
                    type: 'fail',
                    duration: 2000
                  })
                }
              })
            }
          }
        })
      } else {
        if (this.change.active === 1 && this.change.visibleType === 'mobile') {
          this.toSendMobileVisibleCode(this.change.oldAccount)
        } else if (this.change.active === 1 && this.change.visibleType === 'email') {
          this.toSendEmailVisibleCode(this.change.oldAccount)
        } else if (this.change.active === 2) {
          this.$refs['changeVisibleDataForm'].validateField('newAccount', (valid) => {
            if (!valid) {
              if (this.dialogStatus === 'mobile') {
                this.toSendMobileVisibleCode(this.change.newAccount)
              } else if (this.dialogStatus === 'email') {
                this.toSendEmailVisibleCode(this.change.newAccount)
              }
            }
          })
        }
      }
    },
    toSendMobileVisibleCode(val) {
      this.$api.login.sendSMSCaptcha({'sms': val, 'ignore': 'true'}).then((res) => {
        this.$notify({
          message: '短信已发送，请注意查收短信！',
          type: 'success',
          duration: 2000
        })
        // this.$message({ message: '短信已发送，请注意查收短信！', type: 'success' })
        // 隐藏按钮开始 60秒倒计时
        let me = this;
        me.binding.sec_m = config.codeDuration
        me.binding.show_m = false
        this.interval_m = window.setInterval(function () {
          me.binding.sec_m--
          if (me.binding.sec_m === 0) {
            me.binding.sec_m = config.codeDuration
            me.binding.show_m = true
            window.clearInterval(me.interval_m);
          }
        }, 1000);
        this.loading = false
      }).catch((res) => {
        this.loading = false
      })
    },
    toSendEmailVisibleCode(val) {
      this.$api.login.sendEmailCaptcha({ 'email': val }).then((res) => {
        // if (res.code === "200") {
        this.$notify({
          message: '验证码已发送，请注意查收邮箱！',
          type: 'success',
          duration: 2000
        })
        // this.$message({ message: '验证码已发送，请注意查收邮箱！', type: 'success' })
        // 隐藏按钮开始 60秒倒计时
        let me = this;
        me.binding.sec_e = config.codeDuration
        me.binding.show_e = false
        this.interval_e = window.setInterval(function () {
          me.binding.sec_e--
          if (me.binding.sec_e === 0) {
            me.binding.sec_e = config.codeDuration
            me.binding.show_e = true
            window.clearInterval(me.interval_e);
          }
        }, 1000);
        // } else {
        //   this.$message({message: res.msg, type: 'error'})
        // }
        this.loading = false
      }).catch((res) => {
        this.loading = false
      })
    },
    saveBind() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let data
          if (this.dialogStatus === 'mobile') {
            data = {
              userId: this.user.userId,
              mobile: this.binding.account,
              mobileAuthed: '1',
              mobileLogin: this.binding.loginFlag ? '1' : '0'
            }
            this.$api.user.checkMobileVisibleCode({ mobile: this.binding.account, code: this.binding.visibleCode }, resourceCode.userSafe).then(result => {
              this.$api.user.saveMobileBind(data, resourceCode.userSafe).then((res) => {
                this.dialogFormVisible = false
                this.binding.visibleCode = ''
                this.getUserInfo()
                this.binding.show_m = true
                if (this.interval) {
                  window.clearInterval(this.interval);
                }
                this.$notify({
                  title: '操作成功',
                  message: '绑定成功',
                  type: 'success',
                  duration: 2000
                })
              })
            })
          } else {
            data = {
              userId: this.user.userId,
              email: this.binding.account,
              emailAuthed: '1',
              emailLogin: this.binding.loginFlag ? '1' : '0'
            }
            this.$api.user.checkEmailVisibleCode({ email: this.binding.account, code: this.binding.visibleCode }, resourceCode.userSafe).then(result => {
              this.$api.user.saveEmailBind(data, resourceCode.userSafe).then((res) => {
                this.dialogFormVisible = false
                this.binding.visibleCode = ''
                this.getUserInfo()
                this.binding.show_e = true
                if (this.interval) {
                  window.clearInterval(this.interval);
                }
                this.$notify({
                  title: '操作成功',
                  message: '绑定成功',
                  type: 'success',
                  duration: 2000
                })
              })
            })
          }
        }
      })
    },
    visibleOk() {
      if (this.change.visibleType === 'mobile') {
        this.$api.user.checkMobileVisibleCode({ mobile: this.change.oldAccount, code: this.change.visibleCode }, resourceCode.userSafe).then(result => {
          this.$refs['changeMobileVisibleDataForm'].validate((valid) => {
            if (valid) {
              this.change.visibleCode = ''
              this.change.active = 2
              this.binding.show_m = true
              if (this.interval) {
                window.clearInterval(this.interval);
              }
            }
          })
        })
      } else {
        this.$api.user.checkEmailVisibleCode({ email: this.change.oldAccount, code: this.change.visibleCode }, resourceCode.userSafe).then(result => {
          this.$refs['changeEmailVisibleDataForm'].validate((valid) => {
            if (valid) {
              this.change.visibleCode = ''
              this.change.active = 2
              this.binding.show_e = true
              if (this.interval) {
                window.clearInterval(this.interval);
              }
            }
          })
        })
      }
    },
    saveChange() {
      this.$refs['changeVisibleDataForm'].validate((valid) => {
        if (valid) {
          let data
          if (this.dialogStatus === 'mobile') {
            data = {
              userId: this.user.userId,
              mobile: this.change.newAccount,
              mobileAuthed: '1',
              mobileLogin: this.change.loginFlag ? '1' : '0'
            }
            this.$api.user.checkMobileVisibleCode({ mobile: this.change.newAccount, code: this.change.visibleCode }, resourceCode.userSafe).then(result => {
              this.$api.user.saveMobileBind(data, resourceCode.userSafe).then((res) => {
                this.change.dialogFormVisible = false
                this.change.active = 0
                this.change.newAccount = ''
                this.getUserInfo()
                this.binding.show_m = true
                if (this.interval) {
                  window.clearInterval(this.interval);
                }
                this.$notify({
                  title: '操作成功',
                  message: '绑定成功',
                  type: 'success',
                  duration: 2000
                })
              })
            })
          } else {
            data = {
              userId: this.user.userId,
              email: this.change.newAccount,
              emailAuthed: '1',
              emailLogin: this.change.loginFlag ? '1' : '0'
            }
            this.$api.user.checkEmailVisibleCode({ email: this.change.newAccount, code: this.change.visibleCode }, resourceCode.userSafe).then(result => {
              this.$api.user.saveEmailBind(data, resourceCode.userSafe).then((res) => {
                this.change.dialogFormVisible = false
                this.change.active = 0
                this.change.newAccount = ''
                this.getUserInfo()
                this.binding.show_e = true
                if (this.interval) {
                  window.clearInterval(this.interval);
                }
                this.$notify({
                  title: '操作成功',
                  message: '绑定成功',
                  type: 'success',
                  duration: 2000
                })
              })
            })
          }
        }
      })
    },
    initChangeBindDialog() {
      this.change.active = 0
      this.change.visibleCode = ''
      this.change.loginFlag = false
      this.change.sec_m = config.codeDuration
      this.change.sec_e = config.codeDuration
      this.change.show = true
      this.change.visibleType = undefined
      this.change.oldAccount = undefined
    },
    selectVisibleType(code) {
      this.change.active = 1
      this.change.visibleType = code
      if (code === 'email') {
        this.change.oldAccount = this.user.email
      } else {
        this.change.oldAccount = this.user.mobile
      }
      this.$nextTick(() => {
        this.$refs['changeMobileVisibleDataForm'].clearValidate()
      })
    },
    closeChgPwdDialog(...data) {
      this.chgPwdDialogVisible = data[0]
    }
  },
  watch: {
    level: function (newValue, oldValue) {
      for (let i = 0; i < 3; i++) {
        if (i < newValue) {
          this.$set(this.levels[i], `color`, `level` + (i + 1))
        } else {
          this.$set(this.levels[i], `color`, `def-level`)
        }
      }
    }
  },
  created() {
    this.getUserInfo()
  }
}
</script>
<style scoped lang="scss">
.block-info {
  height: 120px;
  padding-top: 1px;
  text-align: center;
}

.block-info-c {
  display: block;
  margin: 20px 0px;
}

.block-info-c b {
  width: 10px;
  height: 10px;
  display: inline-block;
  background: #cccccc;
  border-radius: 5px;
  margin: 5px;
  margin-top: 15px;
}

.dialog-footer {
  text-align: center;
}
</style>
