<template>
  <div>
    <DialogPlus v-dialogDrag :visible.sync="_dialogVisible" @close="handleClose" title="问题反馈" width="70%">
      <div class="content" style="margin-bottom: 40px">
        <div class="content-title">问题信息</div>
        <el-form ref="problemRef" :model="problemData" :rules="problemRules" size="small" label-width="120px">
          <el-form-item label="问题类型" prop="objectionClassification">
            <CheckButton type="radio" style="width: 100%" :disabled="problemData === 'change'" dictType="PROBLEM_TYPE"
              v-model="problemData.objectionClassification">
            </CheckButton>
          </el-form-item>
          <el-form-item label="问题标题" prop="objectionTitle">
            <el-input v-model.trim="problemData.objectionTitle" v-removeIllegalChars clearable placeholder="请输入问题标题"
              maxlength="25" />
          </el-form-item>
          <el-form-item label="问题详情" prop="objectionDesc">
            <el-input v-model.trim="problemData.objectionDesc" v-removeIllegalChars clearable type="textarea"
              placeholder="请输入问题详情" maxlength="200" show-word-limit />
          </el-form-item>
          <el-form-item label="附件" prop="file">
            <template>
              <div style="display: flex">
                <el-button size="small" type="primary" @click="selectFile">点击上传</el-button>
                <span style="margin-left: 5px; color: #f7ab42; font-size: 13px">
                  仅限20M以内后缀为doc/docx/xlsx/xls/txt/png/gif/文件！
                </span>
              </div>
              <template v-if="problemData.file">
                <el-link type="primary" :underline="false">
                  {{ problemData.annex }}&nbsp;&nbsp;&nbsp;&nbsp;
                </el-link>
                <el-link type="primary" :underline="false">
                  <i class="el-icon-delete" @click="deleteFile"></i>
                </el-link>
              </template>
            </template>
            <input v-show="false" type="file" id="fbFile" name="fbFile" value="" ref="fileInput"
              @change="handleFileChange($event)" accept="*.*" />
          </el-form-item>
        </el-form>
      </div>
      <div class="content">
        <div class="content-title">反馈人信息</div>
        <el-form ref="humanRef" :model="humanData" :rules="humanRules" size="small" label-width="120px">
          <el-form-item label="反馈人" prop="filledName">
            <el-input v-model="humanData.filledName" clearable maxlength="25" disabled @change="changeLinkMan" />
          </el-form-item>
          <el-form-item label="单位" prop="presenterDeptName">
            <el-input v-model="humanData.presenterDeptName" disabled />
          </el-form-item>
          <el-form-item label="联系方式" prop="filledContact">
            <el-input v-model="humanData.filledContact" clearable placeholder="请输入联系方式" maxlength="25" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button class="plain" @click="handleClose">取消</el-button>
        <el-button type="primary" v-loading="submitLoading" :disabled="submitLoading"
          @click="handleSubmit">提交</el-button>
      </template>
    </DialogPlus>
  </div>
</template>

<script>
import DialogPlus from '@/core/components/DialogPlus'
import PermButton from '@/core/components/PermButton'
import SelectPlus from '@/core/components/SelectPlus'
import TablePlus from '@/core/components/TablePlus'
import TitleInfo from './common/t-titleInfo.vue'
import CheckButton from '@/core/components/CheckButton'
import { mapState } from 'vuex'

export default {
  name: 'FeedbackDialog',
  components: {
    CheckButton,
    TitleInfo,
    DialogPlus,
    PermButton,
    SelectPlus,
    TablePlus
  },
  data() {
    return {
      problemData: {
        objectionClassification: '',
        objectionTitle: '',
        objectionDesc: '',
        file: ''
      },
      submitLoading: false,
      humanData: {
        filledId: '',
        filledName: '',
        presenterDeptCode: '',
        presenterDeptName: '',
        filledContact: ''
      },
      problemRules: {
        objectionClassification: [{ required: true, message: '问题类型为必选项', trigger: 'blur' }],
        objectionTitle: [{ required: true, message: '问题标题为必填项', trigger: 'blur' }],
        objectionDesc: [{ required: true, message: '问题详情为必填项', trigger: 'blur' }]
      },
      humanRules: {
        filledName: [{ required: true, message: '反馈人为必填项', trigger: 'blur' }],
        presenterDeptName: [{ required: true, message: '单位为必填项', trigger: 'blur' }],
        filledContact: [
          { required: true, message: '联系方式为必填项', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    _dialogVisible: {
      get: function () {
        return this.dialogVisible
      },
      set: function (val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    ...mapState({
      currentUser: (state) => state.user.currentUser,
      systemConfig: (state) => state.icsp.systemConfig
    })
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => { }
    },
    id: {
      type: Array,
      default: null
    }
  },
  watch: {
    dialogVisible(newVal) {
      this.dialogVisible = newVal
    }
  },
  methods: {
    openDialog() {
      this.humanData.presenterDeptName = this.currentUser.unitName
      this.humanData.presenterDeptCode = this.currentUser.unitId
      this.humanData.filledName = this.currentUser.realName
      this.humanData.filledId = this.currentUser.userId
      this.humanData.filledContact = this.currentUser.mobile
    },
    handleClose() {
      this._dialogVisible = false
      if (this.$refs.problemRef || this.$refs.humanRef) {
        this.$refs.problemRef.resetFields()
        this.$refs.humanRef.resetFields()
      }
    },
    selectFile() {
      this.$refs.fileInput.click()
    },
    handleFileChange(event) {
      this.uploadLoading = true
      let file = event.target.files[0]
      if (!this.checkFile(file)) {
        this.problemData.file = file
        this.problemData.annex = file.name
        this.$message.success('上传成功！')
        this.uploadLoading = false
      }
      // this.uploadLoading = true
      // let tempData = Object.assign({}, {file: file, uploadType: '1'})
      // this.$api.bizApi.upload
      //   .uploadFile(tempData)
      //   .then((res) => {
      //     console.log(res, 'res')
      //     this.$message.success('上传成功！');
      //     this.problemData.path = res.data.path ? res.data.path : res.data.filPath
      //     // this.problemData.annex = res.data.annexId
      //     this.problemData.annex = res.data.fileName
      //     this.problemData.fileType = res.data.fileType
      //     this.problemData.fileSize = res.data.fileSize
      //     this.problemData.realName = res.data.realName
      //     this.uploadLoading = false
      //   })
      //   .catch((e) => {
      //     this.uploadLoading = false
      //     this.$message({
      //       message: e.message
      //     })
      //   })
    },
    checkFile(file) {
      let suffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
      let format = ['doc', 'docx', 'xls', 'xlsx', 'txt', 'png', 'gif']
      if (format.indexOf(suffix) < 0) {
        this.$message('文件格式不正确，请检查文件后缀是否为doc/docx/xlsx/xls/txt/png/gif')
        return true
      }
      if (file.size > 20 * 1024 * 1024) {
        this.$message('上传的文件不能超过20M')
        return true
      }
      if (file.size === 0) {
        this.$message('文件为空，请检查')
        return true
      }
    },
    deleteFile() {
      this.problemData.file = ''
      this.problemData.annex = undefined
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = ''
      }
      this.$refs.problemRef.validate('file')
    },
    // 更改联系人
    changeLinkMan() {
      if (this.humanData.linkMan === this.currentUser.realName) {
        this.humanData.linkPhone = this.currentUser.mobile
      } else {
        this.humanData.linkPhone = undefined
      }
    },
    async handleSubmit() {
      await this.$refs.problemRef.validate()
      await this.$refs.humanRef.validate()
      this.submitLoading = true
      const formData = Object.assign({}, this.problemData, this.humanData)
      this.$api.bizApi.questionFeedback
        .addFeedback(formData)
        .then((res) => {
          if (res.code === '200') {
            this.$message.success('操作成功!')
            this.handleClose()
          } else {
            this.$message.error(res.message)
          }
          // 设置一个定时器
          setTimeout(() => {
            this.submitLoading = false
          }, 2000); // 2秒延迟
        })
        .catch((e) => {
          this.submitLoading = false
          this.$message({
            message: e
          })
        })
    }
  },
  created() {
    this.openDialog()
  }
}
</script>

<style scoped lang="scss">
.content {
  margin: 0 120px;

  .content-title {
    margin-bottom: 30px;
    font-size: 18px;
    font-weight: bold;
    color: black;
  }
}

/deep/ .el-input__inner {
  border-radius: 1px;
}

.titleInfo {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;

  span:nth-child(1) {
    display: inline-block;
    width: 6px;
    height: 14px;
    opacity: 1;
    border-radius: 15px;
    background: #0084ff;
    margin-right: 8px;
  }
}

/deep/ .el-dialog {
  margin-top: 3vh !important;
  height: auto !important;
}

/deep/ .el-dialog__header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #ccc;
  font-size: 20px;
  font-weight: bold;
}

/deep/ .el-form-item__label {
  padding: 0 10px;
  font-size: 14px;
  font-weight: 500;
  color: black;
}

/deep/ .el-form-item {
  margin-bottom: 20px;
}
</style>
