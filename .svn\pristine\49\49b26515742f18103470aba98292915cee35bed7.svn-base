import { computed } from "vue";
import store from '@/core/store'

const showOrNotEl = (val) => {
  const systemConfigList = computed(() => store.state.biz.systemConfigList)
  const tempArr = systemConfigList.value?.filter((item) => item.code === val)
  if (tempArr.length && tempArr[0].value === '1') {
    return true
  } else {
    return false
  }
}
const auth = {
  inserted: (el, binding) => {
    const flag = showOrNotEl(binding.value[0])
    if (!flag) {
      el.remove();
    }
  },
  update: (el, binding) => {
    const flag = showOrNotEl(binding.value[0])
    if (!flag) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  }
}
export { auth }
