<template>
  <div>
    <!-- Generator: Adobe Illustrator 24.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
    <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
         y="0px"
         viewBox="0 0 1920 1080" style="enable-background:new 0 0 1920 1080;" xml:space="preserve">
        <g transform="translate(0,0)">
          <path id="signal_7" stroke="url(#linear)" stroke-width="4px" d="M169,0l26,676" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_4" stroke="url(#linear)" stroke-width="9px" d="M478,0l33,1080" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_6" stroke="url(#linear)" stroke-width="3px" d="M544,0l9,428" filter="url(#f1)" opacity="0.2">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_3" stroke="url(#linear)" stroke-width="4px" d="M690,0l20,1013" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_2" stroke="url(#linear)" stroke-width="3px" d="M1213,496.8l6.9-618.6" filter="url(#f1)" opacity="0.2">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_1" stroke="url(#linear)" stroke-width="3px" d="M1503,0l-13,692" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_5" stroke="url(#linear)" stroke-width="4px" d="M1600,0l-19,893" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
          <path id="signal_8" stroke="url(#linear)" stroke-width="3px" d="M1793,0l-12,559" filter="url(#f1)" opacity="0.5">
            <animate attributeName="filter" begin='0s' values="url(#f1);url(#f3);url(#f5);url(#f6);url(#f5);url(#f3);url(#f1)" dur="2s" repeatCount="indefinite" />
          </path>
<!--          <path id="signal_1" class="st0" stroke="#40E0D0" d="M1489.2,666l19.6-1033"/>-->
                    <!--          <path id="signal_2" class="st0" stroke="#40E0D0" d="M1213,496.8l6.9-618.6"/>-->
                    <!--          <path id="signal_3" class="st0" stroke="#40E0D0" d="M709,999.5l-23-1231"/>-->
                    <!--          <path id="signal_4" class="st0" stroke="#40E0D0" d="M509.9,1065.8L476-42.6"/>-->
                    <!--          <path id="signal_5" class="st0" stroke="#40E0D0" d="M1573.4,984.9l25.2-1108"/>-->
                    <!--          <path id="signal_6" class="st0" stroke="#40E0D0" d="M553.1,431.5L549-6.5"/>-->
                    <!--          <path id="signal_7" class="st0" stroke="#40E0D0" d="M193.1,673.6L166.9-25.2"/>-->
                    <!--          <path id="signal_8" class="st0" stroke="#40E0D0" d="M1778,565.5l13-586.9"/>-->
          <!--          -->
          <path id="signal_15" stroke="url(#linear)" opacity="0.3" d="M169,0l26,676" filter="url(#f0)"/>
          <path id="signal_12" stroke="url(#linear)" d="M478,0l33,1080" filter="url(#f0)"/>
          <path id="signal_14" stroke="url(#linear)" opacity="0.1" d="M544,0l9,428" filter="url(#f0)"/>
          <path id="signal_11" stroke="url(#linear)" opacity="0.3" d="M690,0l20,1013" filter="url(#f0)"/>
          <path id="signal_10" stroke="url(#linear)" opacity="0.1" d="M1213,496.8l6.9-618.6" filter="url(#f0)"/>
          <path id="signal_9" stroke="url(#linear)" opacity="0.1" d="M1503,0l-13,692" filter="url(#f0)"/>
          <path id="signal_13" stroke="url(#linear)" opacity="0.3" d="M1600,0l-19,893" filter="url(#f0)"/>
          <path id="signal_16" stroke="url(#linear)" opacity="0.1" d="M1793,0l-12,559" filter="url(#f0)"/>
          <!--          <path id="signal_2" class="st0" stroke="#40E0D0" d="M1213,496.8l6.9-618.6"/>-->
          <!--          <path id="signal_3" class="st0" stroke="#40E0D0" d="M709,999.5l-23-1231"/>-->
          <!--          <path id="signal_4" class="st0" stroke="#40E0D0" d="M509.9,1065.8L476-42.6"/>-->
          <!--          <path id="signal_5" class="st0" stroke="#40E0D0" d="M1573.4,984.9l25.2-1108"/>-->
          <!--          <path id="signal_6" class="st0" stroke="#40E0D0" d="M553.1,431.5L549-6.5"/>-->
          <!--          <path id="signal_7" class="st0" stroke="#40E0D0" d="M193.1,673.6L166.9-25.2"/>-->
          <!--          <path id="signal_8" class="st0" stroke="#40E0D0" d="M1778,565.5l13-586.9"/>-->
        </g>
      <defs>
            <radialGradient id="radial_opacity" fx="50%" fy="100%" cx="50%" cy="100%" r="50%">
              <stop offset="0%" stop-color="#40E0D0" stop-opacity="0.6"/>
              <stop offset="40%" stop-color="#40E0D0" stop-opacity="0.4"/>
              <stop offset="80%" stop-color="#40E0D0" stop-opacity="0.2"/>
              <stop offset="100%" stop-color="#40E0D0" stop-opacity="0"/>
            </radialGradient>
            <radialGradient id="radial1" fx="50%" fy="50%" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stop-color="#ffffff" stop-opacity="1"/>
              <stop offset="100%" stop-color="#00f4fd" stop-opacity="0.1"/>
            </radialGradient>
            <linearGradient id="linear" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stop-color="#00fff0" stop-opacity="1"/>
              <stop offset="100%" stop-color="#696969" stop-opacity="0.2"/>
            </linearGradient>
        </defs>
      <defs>
            <filter id="f1" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="4"/>
            </filter>
            <filter id="f3" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
            </filter>
            <filter id="f5" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="2.5"/>
            </filter>
            <filter id="f6" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="1.5"/>
            </filter>
            <filter id="f0" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="0.7"/>
            </filter>
            <filter id="f4" x="0" y="0">
              <feGaussianBlur in="SourceGraphic" stdDeviation="6"/>
            </filter>
            <filter id="f2" x="0" y="0">
              <feOffset result="offOut" in="SourceGraphic" dx="0" dy="0"></feOffset>
              <feGaussianBlur result="blurOut" in="offOut" stdDeviation="10"></feGaussianBlur>
              <feBlend in="SourceGraphic" in2="blurOut" mode="multiply"></feBlend>
            </filter>
            <filter x="-100%" y="-100%" width="300%" height="300%" id="filter-1">
              <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
              <feGaussianBlur stdDeviation="7" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
              <feColorMatrix values="0 0 0 0 0.266666667   0 0 0 0 0.694117647   0 0 0 0 0.607843137  0 0 0 0.7 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
<!--              <feMerge>-->
<!--                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>-->
<!--                <feMergeNode in="SourceGraphic"></feMergeNode>-->
<!--              </feMerge>-->
            </filter>
      </defs>
      <g transform="translate(0,0)">
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="x" begin='0s' from="-6" to="-2" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='0s' from="-6" to="-2" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='0s' from="6" to="2" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='0s' from="6" to="2" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='0s' from="12" to="4" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='0s' from="12" to="4" dur="4.5s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M169,0l26,676" begin="0s" dur="4.5s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="4" ry="100" fill="url(#radial1)" transform="rotate(-2)" filter="url(#filter1)" opacity="0.8">
          <animateMotion path="M169,0l26,676" begin="1s" dur="4.5s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='1s' from="4" to="0" dur="4.5s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='1s' from="100" to="0" dur="4.5s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='1s' from="0.7" to="0" dur="4.5s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="opacity" begin='2.5s' from="1" to="0" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='2.5s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='2.5s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='2.5s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='2.5s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='2.5s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='2.5s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M478,0l33,1080" begin="2.5s" dur="5s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="6" ry="120" fill="url(#radial1)" transform="rotate(-2)" filter="url(#filter1)">
          <animateMotion path="M478,0l33,1080" begin="2.5s" dur="5s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='2.5s' from="6" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='2.5s' from="120" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='2.5s' from="0.8" to="0" dur="5s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="opacity" begin='1s' from="0.6" to="0" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='1s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='1s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='1s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='1s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='1s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='1s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M544,0l9,428" begin="1s" dur="5s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="3" ry="60" fill="url(#radial1)" transform="rotate(-1)" filter="url(#filter1)">
          <animateMotion path="M544,0l9,428" begin="1s" dur="5s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='1s' from="3" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='1s' from="60" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='1s' from="0.3" to="0" dur="5s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="x" begin='1s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='1s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='1s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='1s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='1s' from="12" to="4" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='1s' from="12" to="4" dur="4s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M690,0l20,1013" begin="1s" dur="4s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="4" ry="100" fill="url(#radial1)" transform="rotate(-1)" filter="url(#filter1)">
          <animateMotion path="M690,0l20,1013" begin="1s" dur="4s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='1s' from="4" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='1s' from="100" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='1s' from="0.7" to="0" dur="4s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="opacity" begin='1.2s' from="0.6" to="0" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='1.2s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='1.2s' from="-6" to="-2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='1.2s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='1.2s' from="6" to="2" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='1.2s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='1.2s' from="12" to="4" dur="5s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M1220,0l-2,443" begin="1.2s" dur="5s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="3" ry="60" fill="url(#radial1)" transform="rotate(1)" filter="url(#filter1)">
          <animateMotion path="M1217,0l-2,443" begin="1.2s" dur="5s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='1.2s' from="3" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='1.2s' from="60" to="0" dur="5s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='1.2s' from="0.3" to="0" dur="5s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--              <animate attributeName="opacity" begin='0s' from="0.6" to="0" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="opacity" begin='0s' from="1" to="0" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='0s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='0s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='0s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='0s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='0s' from="12" to="4" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='0s' from="12" to="8" dur="4s" repeatCount="indefinite" />-->
<!--          <animateMotion path="M1503,0l-13,692" begin="0s" dur="4s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="3" ry="60" fill="url(#radial1)" transform="rotate(1)" filter="url(#filter1)">
          <animateMotion path="M1503,0l-13,692" begin="0.5s" dur="4s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='0.5s' from="3" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='0.5s' from="60" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='0.5s' from="0.3" to="0" dur="4s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="opacity" begin='0s' from="1" to="0" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='0s' from="-6" to="-2" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='0s' from="-6" to="-2" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='0s' from="6" to="2" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='0s' from="6" to="2" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='0s' from="12" to="4" dur="3s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='0s' from="12" to="4" dur="3s" repeatCount="indefinite" />-->
<!--                       <animateMotion path="M1600,0l-19,893" begin="0s" dur="3s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="4" ry="100" fill="url(#radial1)" transform="rotate(1)" filter="url(#filter1)">
          <animateMotion path="M1600,0l-19,893" begin="0.5s" dur="3s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='0.5s' from="4" to="0" dur="3s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='0.5s' from="100" to="0" dur="3s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='0.5s' from="0.7" to="0" dur="3s" repeatCount="indefinite" />
        </ellipse>
<!--        <rect fill="url(#radial_opacity)" x="-6" y="-6" width="12" height="12" rx="6" ry="6">-->
<!--          <animate attributeName="opacity" begin='1s' from="0.6" to="0" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="x" begin='1s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="y" begin='1s' from="-6" to="-2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="rx" begin='1s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="ry" begin='1s' from="6" to="2" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="width" begin='1s' from="12" to="4" dur="4s" repeatCount="indefinite" />-->
<!--          <animate attributeName="height" begin='1s' from="12" to="4" dur="4s" repeatCount="indefinite" />-->
<!--                       <animateMotion path="M1793,0l-12,559" begin="1s" dur="4s" repeatCount="indefinite"/>-->
<!--                    </rect>-->
        <ellipse cy="-100" rx="3" ry="60" fill="url(#radial1)" transform="rotate(1)" filter="url(#filter1)">
          <animateMotion path="M1793,0l-12,559" begin="1s" dur="4s" repeatCount="indefinite"/>
          <animate attributeName="rx" begin='1s' from="3" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="ry" begin='1s' from="60" to="0" dur="4s" repeatCount="indefinite" />
          <animate attributeName="opacity" begin='1s' from="0.3" to="0" dur="4s" repeatCount="indefinite" />
        </ellipse>
          </g>
      </svg>
  </div>
</template>

<script>
  export default {
    name: "SvgSignal"
  }
</script>

<style scoped>
  .color {
    color: red;
  }
</style>
