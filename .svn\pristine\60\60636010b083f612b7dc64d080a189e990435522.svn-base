<template>
  <div ref="bar"
    :class="['splitBar', direction]"
    :style="direction === 'row' ? `width: ${size}px` : `height: ${size}px`"
    @mousedown="handleMouseDown">
    <span v-if="direction === 'row'">||</span>
    <span v-else>=</span>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "SplitBar",
  props: {
    // 唯一标识(用于缓存)
    id: String,
    // 分隔条左(上)区域的默认宽度(高度)
    value: Number,
    // 方向：row、column
    direction: {
      type: String,
      default: "row"
    },
    // 分隔条的宽度(高度)
    size: {
      type: Number,
      default: 10
    },
    // 分隔条左(上)区域的宽度(高度)的调整范围最小值
    min: {
      type: Number,
      default: 200
    },
    // 分隔条左上区域的宽度(高度)的调整范围最大值
    max: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      // 分隔条的左(上)区域
      previousEl: undefined,
      // 分隔条的右(下)区域
      nextEl: undefined,
      // 分隔条的左(上)区域宽度(高度)
      offset: undefined,
      triggerLeftOffset: 0 // 鼠标距滑动器左(上)侧偏移量
    };
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser
    })
  },
  methods: {
    // 按下滑动器
    handleMouseDown(e) {
      let target = e.currentTarget;
      this.previousEl = target.previousElementSibling;
      this.nextEl = target.nextElementSibling;
      document.addEventListener("mousemove", this.handleMouseMove);
      document.addEventListener("mouseup", this.handleMouseUp);

      if (this.direction === "row") {
        this.triggerLeftOffset = e.pageX - target.getBoundingClientRect().left;
      } else {
        this.triggerLeftOffset = e.pageY - target.getBoundingClientRect().top;
      }
      this.$refs.bar.classList.add('down')
    },

    // 按下滑动器后移动鼠标
    handleMouseMove(e) {
      const clientRect = this.previousEl.getBoundingClientRect();

      if (this.direction === "row") {
        this.offset =
          e.pageX -
          clientRect.left -
          this.triggerLeftOffset +
          this.size / 2;
        if (this.offset < this.min) {
          this.offset = this.min;
        }
        if (this.offset > this.max) {
          this.offset = this.max;
        }
        this.previousEl.style.setProperty(
          "width",
          this.offset + "px",
          "important"
        );
      } else {
        this.offset =
          e.pageY - clientRect.top - this.triggerLeftOffset + this.size / 2;
        if (this.offset < this.min) {
          this.offset = this.min;
        }
        if (this.offset > this.max) {
          this.offset = this.max;
        }
        this.previousEl.style.setProperty(
          "height",
          this.offset + "px",
          "important"
        );
      }
    },

    // 松开滑动器
    handleMouseUp() {
      document.removeEventListener("mousemove", this.handleMouseMove);
      document.removeEventListener("mouseup", this.handleMouseUp);
      // 存缓存
      if(this.id) {
        let cookieName = `com.thinvent.${config.systemCode}.${this.currentUser.userName}.splitBar.${this.id}`;
        localStorage.setItem(cookieName, this.offset);
      }
      this.$refs.bar.classList.remove('down')
    }
  },
  mounted() {
    // 取缓存
    if(this.id) {
      let cookieName = `com.thinvent.${config.systemCode}.${this.currentUser.userName}.splitBar.${this.id}`;
      this.offset = localStorage.getItem(cookieName) || this.value;
      if (this.offset) {
        this.previousEl = this.$el.previousElementSibling;
        this.nextEl = this.$el.nextElementSibling;
        if (this.direction === "row") {
          this.previousEl.style.setProperty(
            "width",
            this.offset + "px",
            "important"
          );
        } else {
          this.previousEl.style.setProperty(
            "height",
            this.offset + "px",
            "important"
          );
        }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.splitBar {
  width: 100%;
  height: 100%;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: e-resize;
  user-select: none;
  transition: background-image 2s linear;
  span {
    font-size: 20px;
    font-weight: bold;
    color: #a5a5a5;
    visibility: hidden;
  }
  &.down span, &:hover span {
    visibility: visible;
  }
}
.splitBar.column {
  cursor: n-resize;
  span {
    top: -1px;
    position: relative;
  }
}
.splitBar.row.down, .splitBar.row:hover {
  background-image: linear-gradient(#f2f2f2, #e6e5e5, #f2f2f2);
}
.splitBar.column.down, .splitBar.column:hover {
  background-image: radial-gradient(circle, #e6e5e5, #f2f2f2);
}
body.thinvent_darken {
  .splitBar.row.down, .splitBar.row:hover {
    background-image: linear-gradient(#1a1b20, #4b5054, #1a1b20);
  }
  .splitBar.column.down, .splitBar.column:hover {
    background-image: radial-gradient(circle, #4b5054, #1a1b20);
  }
}
</style>
