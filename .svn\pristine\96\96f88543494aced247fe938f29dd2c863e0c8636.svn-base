/* var global = typeof window === 'undefined' ? {} : window;

var requestAnimationFrame = global.requestAnimationFrame || global.mozRequestAnimationFrame || global.webkitRequestAnimationFrame || global.msRequestAnimationFrame || function (callback) {
  return global.setTimeout(callback, 1000 / 60);
}; */
function TrackLine(userOptions) {
  // 全局参数
  var self = this
  var canvas = document.getElementById('myCanvas')
  var context = canvas.getContext('2d')
  var width = canvas.width || window.innerWidth
  var height = canvas.height || window.innerHeight
  // lines = []; // 所有线条
  canvas.style.cssText = "position:absolute;" + "left:0;" + "top:0;" + "z-index:0;user-select:none;";

  // 默认参数
  var options = {
    lineWidth: 1,
    strokeStyle: '#fff',
    isShowLine: true,
    isAnimate: true,
    radius: 2,
    speed: 1
  };
  // 参数合并
  var merge = function merge(userOptions, options) {
    Object.keys(userOptions).forEach(function (key) {
      options[key] = userOptions[key];
    });
  };
  // 线条
  function Line(options) {
    this.options = options;
    this.fillStyle = options.fillStyle;
    this.step = 0;
    this.pointList = options.pointList;
  }

  Line.prototype.drawMoveCircle = function (context) {
    var pointList = this.pointList || this.getPointList();

    context.save();
    context.fillStyle = this.fillStyle;
    // context.shadowColor = options.shadowColor;
    // context.shadowBlur = options.shadowBlur;
    context.beginPath();
    context.arc(pointList[this.step][0], pointList[this.step][1], options.radius, 0, Math.PI * 2, true);
    context.fill();
    context.closePath();
    context.restore();
    this.step += options.speed;
    if (this.step >= pointList.length) {
      this.step = 0;
    }
  };
  // 初始化线条
  var createLine = function createLine() {
    var lines = self.lines = [];
    for (var i = 0; i < options.data.length; i++) {
      lines.push(new Line(options.data[i]));
    }
  };
  // 渲染
  var render = function render() {
    context.fillStyle = 'rgba(0,0,0,.9)';
    var prev = context.globalCompositeOperation;
    context.globalCompositeOperation = 'destination-in';
    context.fillRect(0, 0, width, height);
    context.globalCompositeOperation = prev;

    var lines = self.lines;
    // 动画渲染
    for (var j = 0; j < lines.length; j++) {
      lines[j].drawMoveCircle(context); // 移动圆点
    }
  };
  // 初始化
  var init = function init(options) {
    merge(userOptions, options);

    createLine();

    (function drawFrame() {
      requestAnimationFrame(drawFrame);
      render();
    })();
  };
  init(options);
  self.options = options;
};
export {
  TrackLine
}
