hljs.registerLanguage("pony",function(e){var r={keyword:"actor addressof and as be break class compile_error compile_intrinsicconsume continue delegate digestof do else elseif embed end errorfor fun if ifdef in interface is isnt lambda let match new not objector primitive recover repeat return struct then trait try type until use var where while with xor",meta:"iso val tag trn box ref",literal:"this false true"},t={cN:"string",b:'"""',e:'"""',r:10},c={cN:"string",b:'"',e:'"',c:[e.BE]},i={cN:"string",b:"'",e:"'",c:[e.BE],r:0},n={cN:"type",b:"\\b_?[A-Z][\\w]*",r:0},s={b:e.IR+"'",r:0},a={cN:"class",bK:"class actor",e:"$",c:[e.TM,e.CLCM]},o={cN:"function",bK:"new fun",e:"=>",c:[e.TM,{b:/\(/,e:/\)/,c:[n,s,e.CNM,e.CBCM]},{b:/:/,eW:!0,c:[n]},e.CLCM]};return{k:r,c:[a,o,n,t,c,i,s,e.CNM,e.CLCM,e.CBCM]}});