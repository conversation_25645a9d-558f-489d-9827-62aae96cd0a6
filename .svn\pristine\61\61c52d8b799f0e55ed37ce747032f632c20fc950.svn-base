<template>
  <div class="err-message-box__wrapper">
    <transition name="v-mask">
      <div class="mask" v-show="isShowMessageBox"></div>
    </transition>
    <transition>
      <div class="err-message-box" v-show="isShowMessageBox">
        <div class="err-message-box__header">
          <div class="err-message-box__title">
            <span>{{title}}</span>
          </div>
          <button type="button" class="err-message-box__headerbtn" @click="cancel">
            <i class="err-message-box__close el-icon-close"></i>
          </button>
        </div>
        <div class="err-message-box__content">
          <div class="err-message-box__abstract">
            <div class="err-message-box__status noBrief"></div>
            <div class="err-message-box__abstractdiv">
              <p class="err-message-box__message">{{message}}</p>
              <p class="err-message-box__brief noBrief">{{brief}}</p>
            </div>
          </div>
          <div class="err-message-box__detail">
            <div class="err-message-box__label2">
              <el-button class="txtBtn saveAdd-btn" @click="showContent = !showContent" v-if="isShowErrorTrace && content">详细信息</el-button>
              <el-button class="txtBtn" @click="cancel">确定</el-button>
            </div>
            <!-- <transition name="content"> -->
              <div class="err-message-box__detail border" v-show="showContent">
                <!-- <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
                  <div class="err-message-box__detailContent">{{ content }}</div>
                </el-scrollbar> -->
                <el-input type="textarea" resize="none" :rows="15" v-model="content" wrap="off"></el-input>
              </div>
            <!-- </transition> -->
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "错误提示"
    },
    text: String,
    error: {
      type: Object,
      default: function () {
        return {
          message: undefined,
          trace: undefined,
          brief: undefined,
          content: undefined
        }
      }
    }
  },
  computed: {
    message() {
      return this.error.message ? (this.text + '，' + this.error.message) : (this.text + '。')
    },
    brief() {
      return this.error.brief
    },
    content() {
      return this.error.trace || this.error.content
    },
    isShowErrorTrace() {
      return config.errMessage ? config.errMessage.isShowErrorTrace : false
    }
  },
  data() {
    return {
      config,
      isShowMessageBox: false,
      showContent: false,
      resolve: "",
      reject: "",
      promise: "" // 保存promise对象
    };
  },
  methods: {
    // 取消,将promise断定为reject状态
    cancel: function() {
      this.isShowMessageBox = false;
      this.resolve();
      this.remove();
    },
    // 弹出messageBox,并创建promise对象
    showMsgBox: function() {
      this.isShowMessageBox = true;
      this.promise = new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
      // 返回promise对象
      return this.promise;
    },
    destroyMsgBox: function() {
      this.isShowMessageBox = false;
      this.resolve();
      this.remove();
      this.promise = new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
      // 返回promise对象
      return this.promise;
    },
    remove: function() {
      setTimeout(() => {
        this.destroy();
      }, 500);
    },
    destroy: function() {
      this.$destroy();
      document.body.removeChild(this.$el);
    }
  }
};
</script>
<style lang="scss" scoped>
.err-message-box__status.noBrief{
  top: 70px!important;
  display: none;
}
.err-message-box__brief.noBrief {
  display: none;
}
.err-message-box__label span:hover {
  cursor: pointer;
}
.err-message-box__label2 {
  text-align: right;
}
.el-dialog {
  height: unset;
  margin: unset;
}
.el-dialog__header {
  padding: 0;
}
.content-enter-active,
.content-leave-active {
  transition: opacity 0.5s ease;
}
.content-enter-from,
.content-leave-to {
  opacity: 0;
}
.el-scrollbar__bar.is-vertical {
  margin-right: 30px;
}
.err-message-box__detail.border{
  border: 1px solid #d1d1d1;
  padding: 12px;
  margin-right: 0px;
  margin-top: 10px;
  background: #f6f6f6;
}
.el-scrollbar {
  margin-right: -10px;
  padding-bottom: 10px;
}
</style>
