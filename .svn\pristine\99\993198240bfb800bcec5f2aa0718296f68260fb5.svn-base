import axios from '@/core/http/axios'

/*
 * 参数
 */

// 获取参数列表
export const getList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/config/list`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

// 保存系统参数
export const save = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/config/save`,
    method: 'post',
    headers: {
      'X-resource-code': resourceCode, 'isBaseApi': true
    },
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
  })
}

// 删除系统参数
export const del = (params, resourceCode) => {
  return axios({
    url: `/${config.appCode}/config/del`,
    method: 'get',
    params,
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getById = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/config/getById`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

export const getSysConfig = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/config/open/getSysConfig`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
