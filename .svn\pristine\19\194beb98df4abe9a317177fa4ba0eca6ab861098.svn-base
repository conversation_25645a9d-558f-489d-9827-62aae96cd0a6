/**
 * 重置message，防止重复点击重复弹出message弹框
 */
import { Message } from 'element-ui'
let messageInstance = null
const message = (options) => {
    if (messageInstance) {
        messageInstance.close()
    }
    messageInstance = Message(options)
}
;['error', 'success', 'info', 'warning'].forEach(type => {
    message[type] = options => {
        if (typeof options === 'string') {
            options = {
                message: options,
                duration: 100
            }
        }
        options.type = type
        return message(options)
    }
})
export const singleMessage = message
