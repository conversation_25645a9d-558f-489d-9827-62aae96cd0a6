import axios from "@/core/http/axios";

export function getSystemPage(params) {
    return axios({
        url: `/${config.appCode}/system/page`,
        method: 'get',
        params
    });
}

export function getSystemInfo(params) {
    return axios({
        url: `/${config.appCode}/system/getInfo`,
        method: 'get',
        params
    });
}

export function deleteSystem(data) {
    return axios({
        url: `/${config.appCode}/system/deleteSystem`,
        method: 'post',
        data
    });
}

// 根据登录用户单位获取系统信息
export const getSystemByLoginUnit = (data, resourceCode) => {
  return axios({
    url: (config.gxslInvokeSwitch === true)
      ? `/${config.appCode}/system/getSystemByLoginUnit?fromPowerInvoke=1`
      : `/${config.appCode}/innerGxsl/system/getSystemByLoginUnit`,
    method: 'get',
    params: data
  })
}
