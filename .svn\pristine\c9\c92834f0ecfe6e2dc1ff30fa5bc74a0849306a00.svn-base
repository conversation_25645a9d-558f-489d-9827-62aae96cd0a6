/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 验证手机号
 * @param str
 * @returns {boolean}
 */
export function isMobile(str) {
  const reg = /^1[3-9][0-9]\d{8}$/
  return reg.test(str)
}

export function validMobile(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入电话号码'))
  } else if (!isMobile(value)) {
    callback(new Error('请输入正确的11位手机号码'))
  } else {
    callback()
  }
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function isEmail(email) {
  // eslint-disable-next-line no-useless-escape
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

export function validEmail(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入邮箱地址!'))
  } else if (!isEmail(value)) {
    callback(new Error('请输入正确的邮箱地址!'))
  } else {
    callback()
  }
}

// 手机号正则
export const checkPhone = (rule, value, callback) => {
  let phone = /^[1][0-9][0-9]{9}$/;
  if (value) {
    if (!phone.test(value)) {
      return callback(new Error('请输入正确的手机号!'))
    } else {
      callback()
    }
  } else {
    callback()
  }
};

// 邮箱正则
export const checkEmail = (rule, value, callback) => {
  let email = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z]{2,5}$/
  if (value) {
    if (!email.test(value)) {
      return callback(new Error('请输入正确的邮箱!'))
    } else {
      callback()
    }
  } else {
    callback()
  }
};

// 最大长度校验
export const checkMaxLength = (rule, value, callback, source, options) => {
  if(value) {
    var len = 0;
    for (var i = 0; i < value.length; i++) {
        var c = value.charCodeAt(i);
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
            len++;
        } else {
            len += 2;
        }
    }
    if(rule.maxLength && len > rule.maxLength) {
      return callback(new Error(`请输入少于或等于${rule.maxLength}个非空字符!`));
    } else {
      callback();
    }
  }else {
    callback();
  }
}

// 只能输入字母和数字
export const checkAlphanum = (rule, value, callback, source, options) => {
  let rgx = /^[a-zA-Z0-9]*$/
  if (value) {
    if (!rgx.test(value)) {
      return callback(new Error('只能输入字母和数字!'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 验证IP
export const checkIP = (rule, value, callback, source, options) => {
  let rgx = /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/
  if (value) {
    if (!rgx.test(value)) {
      return callback(new Error('请输入正确的IP!'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 验证端口号
export const checkPort = (rule, value, callback, source, options) => {
  let rgx = /^([1-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[1-4]\d{3}|65[1-4]\d{2}|655[1-2]\d|6553[1-5])$/
  if (value) {
    if (!rgx.test(value)) {
      return callback(new Error('请输入正确的端口号!'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}
