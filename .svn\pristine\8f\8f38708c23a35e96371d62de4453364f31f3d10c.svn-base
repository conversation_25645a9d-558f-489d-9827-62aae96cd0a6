import axios from "@/core/http/axios";

// 能力树
export function addFeedback(data) {
  return axios({
    url: `/${config.appCode}/questionFeedback/addFeedback`,
    method: "post",
    data,
    transformRequest: function (data, headers) {
      const formData = new FormData()
      for (const key of Object.keys(data)) {
        if(data[key]) {
          formData.append(key, data[key])
        }
      }
      return formData
    }
  });
}
