<template>
  <el-tooltip popper-class="item" effect="light" placement="right-start">
    <div slot="content" v-html="html" class="info-box" :style="tipStyle ? tipStyle : 'max-width: 250px;'">
    </div>
    <perm-button label="" type="text" icon="tip" style="font-size: 18px; width: 18px;"></perm-button>
  </el-tooltip>
</template>
<script>
  import PermButton from '@/core/components/PermButton';
  export default {
    components: {PermButton},
    data() {
      return {
        html: ""
      }
    },
    props: {
      tipInfo: {
        type: Object,
        default: undefined
      }, // 数据： 格式｛tipInfo:{titles:["","",""],infos:["","",""]}｝
      styleMode: {
        type: String,
        default: '1'
      }, // 样式模式：1模式默认样式
      tipStyle: {
        type: String,
        default: ''
      }
    },
    methods: {
      init(info) {
        let html = ""
        let tipInfo = info || this.tipInfo
        if(tipInfo) {
          let titles = tipInfo.titles
          let infos = tipInfo.infos
          if(infos && infos.length > 0) {
            for(let i = 0; i < infos.length; i++) {
              if(titles && i < titles.length) {
                html += this.getTitle(titles[i])
              }
              html += this.getInfo(infos[i])
            }
          }
        }
        this.html = html
      },
      getTitle(value) {
        let titleHtml = ''
        if(this.styleMode === '1') {
          titleHtml = "<p class=\"tip-title\">" + (!value ? "" : value) + "</p>"
        }
        return titleHtml
      },
      getInfo(value) {
        let infoHtml = ''
        if(this.styleMode === '1') {
          infoHtml = (!value ? "" : value)
        }
        return infoHtml
      }
    },
    mounted () {
      this.init()
    },
    watch: {
      tipInfo: {
        handler(newValue, oldValue) {
          this.init(newValue);
      },
      deep: true,
      immediate: true
      }
    }
  }
</script>
<style lang="scss">
  .item.el-tooltip__popper {
    max-width: unset;
    .popper__arrow {
      display: none;
    }
  }
</style>
<style scoped lang="scss">
  .el-tooltip__popper.is-light {
    border: 1px solid #303133 !important;
    border-radius: 6px !important;
  }
  .info-box {
    // max-width: 250px;
    word-break: break-all;
  }
  >>> .tip-title {
    font-size: 14px;
    color: #4c4c4c;
    margin: 10px 0px 5px 0px;
    border-bottom: 1px solid #d8d8d8;
  }
</style>
