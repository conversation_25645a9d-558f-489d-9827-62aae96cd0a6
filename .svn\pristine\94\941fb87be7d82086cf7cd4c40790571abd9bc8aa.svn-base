import axios from '../axios'
/*
 * 系统登录模块
 */

// 登录
export const login = data => {
  // data.Authorization = 'Basic ' + Base64.encode(config.client_id + ":" + config.client_secret)
  return axios({
    url: `/${config.appCode}/security-frame/token`,
    method: 'POST',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data
    // params: data
    /* params: data,
    data: JSON.stringify(data) */
  })
}

// 登出
export const logout = (data) => {
  return axios({
    url: `/${config.appCode_auth}/token/logout`,
    method: 'post',
    postType: "form",
    data,
    headers: {'isBaseApi': 1}
  })
}

// 获取短信验证码
export function sendSMSCaptcha (params) {
  return axios({
    url: `/${config.appCode_auth}/code/sms`,
    method: 'get',
    params,
    headers: {'isBaseApi': 1}
  })
}

// 获取邮箱验证码
export function sendEmailCaptcha (params) {
  return axios({
    url: `/${config.appCode_auth}/code/email`,
    method: 'get',
    params,
    headers: {'isBaseApi': 1}
  })
}
