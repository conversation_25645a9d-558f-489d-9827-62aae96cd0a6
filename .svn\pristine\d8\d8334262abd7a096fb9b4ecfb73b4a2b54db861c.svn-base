<template>
  <div>
    <!-- 新增Form -->
    <el-dialog v-dialogDrag :title="modeMap[dialogModes]" :visible.sync="dialogMainFormVisible"
               width="calc(100% - 100px)"
               min-width="800px" @opened="initDialog"
               @close="destrory()">
      <el-container>
        <el-scrollbar class="scrollbar-wrapper">
          <el-form ref="dataForm" :rules="dialogModes === 'view'? {} : rules" :model="temp" label-position="right"
                   label-width="100px">
            <el-form-item label="收件人" prop="receiver">
              <el-select v-model="temp.receiver" placeholder="请点击右侧选择按钮选择收件人" :disabled="dialogModes === 'view'"
                         :style="dialogModes !== 'view' ? 'width: calc(100% - 155px)' : 'width: 100%'"
                         no-data-text = '请点击右侧选择按钮选择收件人'
                         multiple
                         clearable
                         @change="$forceUpdate()">
                <el-option v-for="item in receiverOption" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
              <el-button v-if="dialogModes !== 'view'" icon="uims-icon-reset" type="primary" @click="clearSelectUser">
                置空
              </el-button>
              <el-button v-if="dialogModes !== 'view'" icon="uims-icon-select" type="primary" @click="openSelectUser">
                选择
              </el-button>
            </el-form-item>
            <el-form-item label="主题" prop="subject">
              <el-input v-model="temp.subject" :disabled="dialogModes === 'view'" maxlength="50" />
            </el-form-item>
            <el-form-item label="定时发送" prop="sendTimer">
              <el-date-picker v-model="temp.sendTimer" type="datetime" align="right"
                              placeholder="选择定时发送时间"
                              is-range
                              :picker-options="sendTimeOption"
                              :disabled="dialogModes === 'view'">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="发送方式" prop="sendTypeArr">
              <el-checkbox-group v-model="sendTypeArr" @change="sendTypeChange" :disabled="dialogModes === 'view'">
                <el-checkbox label="0">消息通知</el-checkbox>
                <el-checkbox label="1">短信通知</el-checkbox>
                <el-checkbox label="2">邮件通知</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否需要回执" v-if="sendTypeArr.indexOf('0')>-1">
              <el-checkbox v-model="temp.needReceipt"
                           :disabled="dialogModes === 'view' || sendTypeArr.indexOf('0')=== -1"></el-checkbox>
            </el-form-item>
            <el-form-item label="内容" prop="content" class="margin-bottom_0">
              <mark-down v-if="dialogModes === 'view'" :content="temp.content"></mark-down>
              <div v-show="!(dialogModes === 'view')" id="vditor" style="height: 500px"></div>
              <el-input v-model="temp.content" :autosize="{ minRows: 5, maxRows: 10}" type="hidden"
                        :disabled="dialogModes === 'view'" />
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMainFormVisible = false">{{dialogModes==='view'?'关闭':'取消'}}</el-button>
        <el-button class="commit-btn" type="success" @click="dialogModes==='create'?save(1):update(1)"
                   v-if="!(dialogModes === 'view')" :loading="okLoading">发送
        </el-button>
        <el-button class="save-btn" type="primary" @click="dialogModes==='create'?save(0):update(0)"
                   v-if="!(dialogModes === 'view')" :loading="okLoading">保存
        </el-button>
      </div>
    </el-dialog>
    <select-user-dialog
      :dialogSelectUser="dialogSelectUser"
      :spaceList="spaceList"
      :resCode="resCode"
      @selectUserRetr="selectUserRetr"
      @closeSelectUserDialog="closeSelectUserDialog">
    </select-user-dialog>
  </div>
</template>

<script>
  import Vditor from 'vditor'
  import MarkDown from "@/core/components/MarkDown";
  import "@/core/styles/vditor/scss/index.scss"
  import {resourceCode} from "@/biz/http/settings";
  import SelectUserDialog from "./SelectUserDialog";

  export default {
    components: {MarkDown, SelectUserDialog},
    name: "MsgOutBoxMainDialog",
    data() {
      var checkTime = (rule, value, callback) => {
        if (this.temp.sendTimer) {
          if (new Date() > this.temp.sendTimer) {
            callback(new Error('发送时间不能小于当前时间'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }
      var checkSendType = (rule, value, callback) => {
        if (!this.sendTypeArr || !this.sendTypeArr.length) {
          callback(new Error('请选择通知类型'))
        } else {
          callback()
        }
      }
      return {
        sendTypeArr: [],
        contentEditor: undefined,
        resCode: '',
        okLoading: false,
        modeMap: {
          create: '新增发件箱信息',
          update: '编辑发件箱信息',
          view: '查看发件箱信息'
        },
        // 新增/编辑列表
        temp: {
          receiver: [],
          receiverNames: undefined,
          receiverIds: undefined,
          subject: undefined,
          status: undefined,
          content: undefined,
          effectiveDate: undefined,
          expireDate: undefined,
          sendTimer: undefined,
          createTime: undefined,
          needReceipt: false,
          sendType: 0,
          beginTimeToEndTime: []
        },
        expireTimeOption: {
          disabledDate(date) {
            var now = new Date();
            var ny = now.getFullYear();
            var nm = now.getMonth();
            var nd = now.getDate();
            var y = date.getFullYear();
            var m = date.getMonth();
            var d = date.getDate();
            if (ny === y && nm === m && nd === d) {
              return false;
            }
            return (date.getTime() <= Date.now());
          }
        },
        sendTimeOption: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        },
        // 表单校验规则
        rules: {
          receiver: [
            {required: true, message: '请选择收件人', trigger: 'blur'}
          ],
          content: [
            {required: true, message: '请输入发件内容', trigger: 'blur'}
          ],
          subject: [
            {required: true, message: '请输入主题', trigger: 'blur'},
            {min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur'}
          ],
          beginTimeToEndTime: [
            {required: true, message: '请选择有效期', trigger: 'blur'}
          ],
          sendTimer: [{validator: checkTime, trigger: 'blur'}],
          sendTypeArr: [{validator: checkSendType, trigger: 'change'}]

        },
        receiverOption: [], // 待确定收件人
        userGroupInfo: [],
        spaceList: [],
        dialogMainFormVisible: false,
        dialogSelectUser: false
      }
    },
    props: {
      dialogModes: String,
      dialogFormVisible: Boolean,
      msgOutBoxArray: Array
    },
    methods: {
      sendTypeChange(val) {
        this.temp.sendType = val.toString()
      },
      destrory() {
        this.okLoading = false;
        this.contentEditor.setValue('')
        this.resetTemp()
        this.$refs['dataForm'].clearValidate()
      },
      initDialog() {
        this.contentEditor = new Vditor('vditor', {
          cdn: 'vditor',
          toolbar: [
            'headings',
            'bold',
            'italic',
            'strike',
            'link',
            '|',
            'list',
            'ordered-list',
            'check',
            'outdent',
            'indent',
            '|',
            'line',
            'insert-before',
            'insert-after',
            '|',
            'table',
            '|',
            'undo',
            'redo'
          ],
          mode: 'wysiwyg', // 可选模式：sv, ir, wysiwyg
          width: '100%',
          toolbarConfig: {
            pin: true
          },
          cache: {
            enable: false
          },
          counter: {
            enable: true, // 是否启用计数器
            max: 3000, // 允许输入的最大值
            type: 'text' // 统计类型:md,text
          },
          after: () => {
            this.contentEditor.setValue(this.temp.content ? this.temp.content : '')
          }
        })
        if (this.dialogModes === 'view') {
          this.contentEditor.disabled()
        } else {
          this.contentEditor.enable()
        }
      },
      // 获取空间列表
      getMsgSpaceList() {
        this.$api.msgOpen.getMsgSpaceReceiver({systemCode: config.systemCode}, this.resCode).then((res) => {
          let users = res.data;
          let unitArray = [];
          for (let temp in users) {
            unitArray.push(temp);
          }
          this.spaceList = unitArray;
        });
      },
      // 获取用户组及用户信息
      getMsgReceiverGroup() {
        this.$api.msgOpen.getMsgReceiverGroup({systemCode: config.systemCode}, this.resCode).then((res) => {
          this.userGroupInfo = res.data
        });
      },
      // 反显收件人 编辑
      loadMsgReceiver_update() {
        /* this.$api.msgOpen.getMsgReceiverGroup({systemCode: config.systemCode}, this.resCode).then((res) => {
          this.userGroupInfo = res.data
          for (let tempGroup in this.userGroupInfo) {
            let userGroup = this.userGroupInfo[tempGroup];
            for (let tempUser in userGroup) {
              let tempOption = {}
              if (this.temp.receiverIds.indexOf(userGroup[tempUser].userId) !== -1) {
                let flag = false
                for (let i = 0; i < this.receiverOption.length; i++) {
                  if (this.receiverOption[i].code === userGroup[tempUser].userId) {
                    flag = true
                    break
                  }
                }
                if (flag) {
                  break
                }
                tempOption.code = userGroup[tempUser].userId;
                tempOption.name = userGroup[tempUser].realName + "(" + userGroup[tempUser].userName + ")"
                this.receiverOption.push(tempOption)
              }
            }
          }

          this.temp.receiver = this.temp.receiverIds.split(",")
        }); */
        let ids = this.temp.receiverIds.split(',')
        let names = this.temp.receiverNames.split(',')
        ids.forEach((id, index) => {
          this.receiverOption.push({
            code: id,
            name: names[index]
          })
        })
        this.temp.receiver = this.temp.receiverIds.split(",")
      },
      view() {
        this.temp.receiver = this.temp.receiverNames.split(",")
      },
      save(statusVal) {
        let c = this.contentEditor.getValue()
        if (c === null || c.length <= 1 || c === undefined) {
          this.contentEditor.focus()
          this.$notify({
            title: '提示',
            message: '请输入内容',
            type: 'success',
            duration: 2000
          })
          return;
        }
        if(c.length > 3001) {
          this.$notify({
            title: '提示',
            message: '消息内容操作限制，最多3000字',
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp.content = c
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            if (this.temp.needReceipt && this.sendTypeArr.indexOf('0') > -1) {
              this.temp.needReceipt = "1";
            } else {
              this.temp.needReceipt = "0";
            }
            this.temp.status = statusVal
            let notifymsg = '保存发件消息成功'
            if (statusVal === 1) {
              notifymsg = '消息发送成功'
            }

            // 获取当前已选中的收件人
            let receiverStr = '';
            this.temp.receiver.find(function (item) {
              receiverStr += item + ","
            })
            // 获取userId和realName(userName), 且用户ID和用户名称必需同时取值，以保证两者存储在库中的顺序一致。
            let userName = '';
            let userId = '';
            this.receiverOption.find(function (item) {
              if (receiverStr.indexOf(item.code) !== -1) {
                userId += item.code + ","
                userName += item.name + ","
              }
            })
            this.temp.receiverIds = userId.substring(0, userId.length - 1)
            this.temp.receiverNames = userName.substring(0, userName.length - 1)
            this.temp.msgType = 1
            this.temp.sendType = this.sendTypeArr.toString()
            if (this.temp.deleted === '否') {
              this.temp.deleted = '0'
            } else {
              this.temp.deleted = '1'
            }
            if (this.temp.receiverType === '用户') {
              this.temp.receiverType = '0'
            }
            this.$api.msgOutBox.save(this.temp, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: notifymsg,
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      update(statusVal) {
        let c = this.contentEditor.getValue()
        if (c === null || c.length <= 1 || c === undefined) {
          this.contentEditor.focus()
          this.$notify({
            title: '提示',
            message: '请输入内容',
            type: 'success',
            duration: 2000
          })
          return;
        }
        if(c.length > 3001) {
          this.$notify({
            title: '提示',
            message: '消息内容操作限制，最多3000字',
            type: 'warning',
            duration: 2000
          })
          return;
        }
        this.temp.content = c
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.okLoading = true
            if (this.temp.needReceipt && this.sendTypeArr.indexOf('0') > -1) {
              this.temp.needReceipt = "1";
            } else {
              this.temp.needReceipt = "0";
            }
            this.temp.status = statusVal
            let notifymsg = '编辑发件消息成功'
            if (statusVal === 1) {
              notifymsg = '消息发送成功'
            }

            // 获取当前已选中的收件人
            let receiverStr = '';
            this.temp.receiver.find(function (item) {
              receiverStr += item + ","
            })
            // 获取userId和realName(userName), 且用户ID和用户名称必需同时取值，以保证两者存储在库中的顺序一致。
            let userName = '';
            let userId = '';
            this.receiverOption.find(function (item) {
              if (receiverStr.indexOf(item.code) !== -1) {
                userId += item.code + ","
                userName += item.name + ","
              }
            })
            this.temp.receiverIds = userId.substring(0, userId.length - 1)
            this.temp.receiverNames = userName.substring(0, userName.length - 1)
            this.temp.msgType = 1
            this.temp.sendType = this.sendTypeArr.toString()
            if (this.temp.deleted === '否') {
              this.temp.deleted = '0'
            } else {
              this.temp.deleted = '1'
            }
            if (this.temp.receiverType === '用户') {
              this.temp.receiverType = '0'
            }
            const tempData = Object.assign({}, this.temp)
            this.$api.msgOutBox.save(tempData, this.resCode).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: notifymsg,
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.temp = {
          receiver: [],
          receiverNames: undefined,
          receiverIds: undefined,
          subject: undefined,
          status: undefined,
          content: undefined,
          effectiveDate: undefined,
          expireDate: undefined,
          sendTimer: undefined,
          createTime: undefined,
          needReceipt: false,
          sendType: undefined,
          beginTimeToEndTime: []
        };
        this.sendTypeArr = ["0"]
        this.receiverOption = []
      },
      closeSelectUserDialog(val) {
        this.dialogSelectUser = val
      },
      openSelectUser() {
        this.dialogSelectUser = true
      },
      selectUserRetr(addOption) {
        let userId = '';
        this.temp.receiver.find(function (item) {
          userId += item + ","
        })
        for (let newTemp in addOption) {
          let newCode = addOption[newTemp].code
          let receiverOptionflag = true
          for (let oldTemp in this.receiverOption) {
            if (this.receiverOption[oldTemp].code === newCode) { // 判断待选收件人已经存在
              receiverOptionflag = false
              break
            }
          }
          if (receiverOptionflag) {
            this.receiverOption.push(addOption[newTemp])
          }
          if (userId.indexOf(addOption[newTemp].code) === -1) {
            userId += addOption[newTemp].code + ","
          }
        }
        // 根据更改后的结果 重新设置默认值
        this.temp.receiver = userId.substring(0, userId.length - 1).split(",")
      },
      clearSelectUser() {
        this.receiverOption = []
        this.temp.receiver = []
      },
      // 判断是否存在空间
      isHadSpace() {
        let res = false;
        if (this.$store.state && this.$store.state.system && this.$store.state.system.spaceId) {
          res = true
        }
        return res;
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogModes === 'create') {
            this.resCode = resourceCode.msgOutBox
            this.resetTemp()
            if (this.isHadSpace()) {
              // 加载空间列表
              this.getMsgSpaceList()
            }

            // 加载用户组及用户信息
            this.getMsgReceiverGroup()
          } else if (this.dialogModes === 'update') {
            this.resCode = resourceCode.msgOutBox
            this.$api.msgOutBox.getById({moId: this.msgOutBoxArray[0].moId}, this.resCode).then((res) => {
              this.receiverOption = []
              this.temp = res.data
              // 此处需将时间数据进行转化后再转回后台 否则更新失败
              this.temp.createTime = new Date(this.temp.createTime)
              this.temp.effectiveDate = undefined
              this.temp.expireDate = undefined
              if (this.temp.sendTimer != null) {
                this.temp.sendTimer = new Date(this.temp.sendTimer)
              } else {
                this.temp.sendTimer = undefined;
              }
              this.temp.needReceipt = this.temp.needReceipt === '1';
              if (this.temp.sendType) {
                this.sendTypeArr = this.temp.sendType.split(",")
              }
              if (this.contentEditor) {
                this.contentEditor.setValue(this.temp.content)
              } else {
              }
              // 加载用户组及用户信并反显
              this.loadMsgReceiver_update()
            })
          } else if (this.dialogModes === 'view') {
            this.resCode = resourceCode.msgOutBox
            this.$api.msgOutBox.getById({moId: this.msgOutBoxArray[0].moId}, this.resCode).then((res) => {
              this.temp = res.data
              if (this.temp.sendTimer != null) {
                this.temp.sendTimer = new Date(this.temp.sendTimer)
              } else {
                this.temp.sendTimer = undefined;
              }
              this.temp.needReceipt = this.temp.needReceipt === '1';
              if (this.temp.sendType) {
                this.sendTypeArr = this.temp.sendType.split(",")
              }
              if (this.contentEditor) {
                this.contentEditor.setValue(this.temp.content)
              } else {
              }
              // 反显
              this.view()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
//>>>.vditor-panel.vditor-panel--none {
//  display: none !important;
//}
>>> button[data-type="remove"] {
  display: none !important;
}
</style>
