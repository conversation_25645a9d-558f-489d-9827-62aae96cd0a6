<template>
  <div ref="rightPanel" :class="{show:show}" class="rightPanel-wrapper">
    <div class="rightPanel-container">
      <div class="rightPanel-background" @click="closePanel" />
      <div class="rightPanel">
        <div class="handle-button" ref="handleBtn" @click="show=!show" v-drag:[cookieName]>
          <svg-icon :icon-class="show?'shortcut-shrink':'shortcut'" />
        </div>
        <div class="rightPanel-items">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'RightPanel',
  props: {
    clickNotClose: {
      default: false,
      type: Boolean
    },
    buttonTop: {
      default: 250,
      type: Number
    }
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
    ...mapState({
      currentUser: state => state.user.currentUser,
      cookieName: state => `com.thinvent.${config.systemCode}.${state.user.currentUser.userName}.shortcut.position`,
      positionTop: state => localStorage.getItem(`com.thinvent.${config.systemCode}.${state.user.currentUser.userName}.shortcut.position`)
    })
  },
  created() {
    this.$on("closePanel", this.closePanel);
  },
  mounted() {
    this.insertToBody();
    let shortcutY = localStorage.getItem(this.cookieName);
    if(localStorage.getItem(this.cookieName)) {
      this.$refs.handleBtn.style.top = shortcutY + 'px';
    }
  },
  beforeDestroy() {
    const elx = this.$refs.rightPanel
    elx.remove()
  },
  methods: {
    closePanel() {
      this.show = false;
    },
    /* addEventClick() {
      window.addEventListener('click', this.closeSidebar)
    },
    closeSidebar(evt) {
      const parent = evt.target.closest('.rightPanel')
      if (!parent) {
        this.show = false
        window.removeEventListener('click', this.closeSidebar)
      }
    }, */
    insertToBody() {
      const elx = this.$refs.rightPanel
      const body = document.querySelector('body')
      body.insertBefore(elx, body.firstChild)
    }
  },
  // 注册局部组件指令
  directives: {
    drag: function (el, binding) {
      let dragBox = el; // 获取当前元素
      dragBox.onmousedown = e => {
        // 算出鼠标相对元素的位置
        let disY = e.clientY - dragBox.offsetTop;
        document.onmousemove = e => {
          // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
          let top = e.clientY - disY;
          top = top < 0 ? 0 : top;
          let maxTop = document.documentElement.clientHeight - dragBox.clientHeight;
          top = top > maxTop ? maxTop : top;
          // 移动当前元素
          dragBox.style.top = top + "px";
          localStorage.setItem(binding.arg, top);
        };
        document.onmouseup = e => {
          // 鼠标弹起来的时候不再移动
          document.onmousemove = null;
          // 预防鼠标弹起来后还会循环（即预防鼠标放上去的时候还会移动）
          document.onmouseup = null;
        };
      };
    }
  }
}
</script>
