<template>
  <el-container class="page-container">
    <el-header v-if="pageMode==='full'"></el-header>
    <el-container>
      <el-aside>
        <div class="toolbarTree-wrapper">
          <div class="toolbar-wrapper">
            <el-tooltip class="item" effect="light" content="新增" placement="bottom">
              <perm-button class="iconBtn" type="text" icon="add" size="mini" :perms="systemCode + 'x99005004001'" @click="handleBizTypeCreate"/>
            </el-tooltip>
            <el-tooltip class="item" effect="light" content="编辑" placement="bottom">
              <perm-button class="iconBtn" type="text" icon="edit" size="mini" :perms="systemCode + 'x99005004002'" @click="handleBizTypeUpdate"/>
            </el-tooltip>
            <el-tooltip class="item" effect="light" content="删除" placement="bottom">
              <perm-button class="iconBtn" type="text" icon="delete" size="mini" :perms="systemCode + 'x99005004003'" @click="handleBizTypeDelete"/>
            </el-tooltip>
          </div>
          <el-input placeholder="输入关键字进行过滤" v-model="filterText" size="small">
          </el-input>
          <!--<div class="tree-wrapper">-->
          <el-scrollbar wrap-class="scrollbar-wrapper" style="height: 100%;width:100%;">
            <el-tree class="list" :data="bizTypeList" :props="defaultProps" node-key='dictId' default-expand-all
                     v-loading="treeLoading" :filter-node-method="filterNode" check-on-click-node highlight-current
                     ref="tree" @check-change="handleCheckChange">
            <span slot-scope="{ node, data }">
            <span :title="node.label">
              {{node.label}}
            </span>
          </span>
            </el-tree>
          </el-scrollbar>
          <!--</div>-->
        </div>
      </el-aside>
      <SplitBar :value="200" :min="200" :max="500" :size="10" :id="resourceCode"></SplitBar>
      <!-- 右侧列表 -->
      <el-container>
        <!--列表工具栏-->
        <el-header class="marginLeft0">
          <div class="toolbar-wrapper">
            <perm-button label="新增" type="text" :perms="systemCode + 'x99005004004'" icon="add" @click="handleCreate"/>
<!--            <perm-button label="编辑" type="text" :perms="systemCode + 'x99005004005'" icon="edit" @click="handleUpdate"/>-->
<!--            <perm-button label="删除" type="text" :perms="systemCode + 'x99005004006'" icon="delete" @click="handleDelete"/>-->
<!--            <perm-button label="查看" type="text" :perms="systemCode + 'x99005004007'" icon="see" @click="handleView"/>-->
<!--            <perm-button label="设置订阅" type="text" :perms="systemCode + 'x99005004008'" icon="uims-icon-generate" @click="handleSubscript"/>-->
<!--            <perm-button label="消息日志" type="text" :perms="systemCode + 'x99005004009'" icon="uims-icon-query" @click="handleMsgLog"/>-->
          </div>
          <!--列表查询区-->
          <el-form :inline="true" :model="filters" :size="size">
            <el-form-item label="业务消息名称">
              <el-input v-model="filters.bizName" maxlength="100" placeholder="请输入业务消息名称" @keyup.enter.native="handleFilter"/>
            </el-form-item>
            <el-form-item label="业务消息编码">
              <el-input v-model="filters.mbId" maxlength="100" placeholder="请输入业务消息编码" @keyup.enter.native="handleFilter"/>
            </el-form-item>
            <el-form-item label="是否启用">
              <select-plus dictType="IS_FLAG" v-model="filters.enabled" clearable style="width: 150px"></select-plus>
            </el-form-item>
            <perm-button type="primary" label="查询" icon="uims-icon-query" @click="handleFilter"/>
            <perm-button style="margin-left: 10px" label="重置" type="primary" icon="uims-icon-query" @click="handleFilter(true)"/>
          </el-form>
        </el-header>
        <!--列表表格区-->
        <el-main class="marginLeft0">
          <table-plus
            :data="list"
            ref="multipleTable"
            border
            id="1"
            fit
            highlight-current-row
            v-loading="listLoading"
            tooltip-effect="light"
            @row-click="clickRow"
            @header-dragend="handleHeaderDrag"
            @selection-change="selectMainTableRow"
          >
            <el-table-column type="selection" width="60" header-align="center" align="center"></el-table-column>
            <el-table-column width="220" show-overflow-tooltip prop="mbId" label="业务消息编码" header-align="center"
                             align="left"></el-table-column>
            <el-table-column width="310" show-overflow-tooltip prop="bizName" label="业务消息名称" header-align="center"
                             align="left"></el-table-column>
            <el-table-column width="310" show-overflow-tooltip prop="msgBizType" label="业务类型" header-align="center"
                             align="left"></el-table-column>
            <el-table-column width="130" show-overflow-tooltip prop="enabled" label="是否启用" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="200" show-overflow-tooltip prop="createTime" label="创建时间" header-align="center"
                             align="center"></el-table-column>
            <el-table-column width="220" label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <perm-button-group :config="getButtons(scope.row)"></perm-button-group>
              </template>
<!--              <template slot-scope="{row}">-->
<!--                <perm-button :label="row.enabledCode === '1' ? '停用' : '启用'" :perms="systemCode + 'x99005004010'" size="mini" icon="enable" :type="row.enabledCode === '1' ? 'danger' : 'success' " @click="handleEnabled(row)"/>-->
<!--              </template>-->
            </el-table-column>
          </table-plus>
        </el-main>
        <el-footer class="marginLeft0">
          <table-footer ref="tableFooter"
                        :showToolBar="true"
                        excelName="业务消息配置"
                        :showPage="true"
                        :tableRef="this.$refs.multipleTable"
                        @sizeChange="handleSizeChange"
                        @currentChange="handleCurrentChange"
                        :currentPage="filters.currentPage"
                        :pageSizes="[10, 20, 50, 100]"
                        :pageSize="filters.pageSize"
                        url="/msgBiz/list"
                        :filters="filters"
                        :resourceCode="resourceCode"
                        :total="total">
          </table-footer>
        </el-footer>
        <main-dialog
          @closeDialog="closeDialog"
          @getList="getList"
          :dialogModes="dialogModes"
          :mbId="mbIdSelect"
          :dictTypeTemp="treeTemp"
          :dialogFormVisible="dialogFormVisible"
          :viewDisabled="viewDisabled">
        </main-dialog>
        <biz-dict-dialog
          @closeDialog="closeDictDialog"
          @getList="getDictList"
          text="业务类型"
          :resourceCode="resourceCode"
          :showDictType="false"
          :showEnabledType="false"
          :dialogDictStatus="dialogDictStatus"
          :dictTypeTemp="treeTemp"
          defDictType="MSG_BIZ_TYPE"
          :selectDiztId="selectDiztId"
          :dialogDictVisible="dialogDictVisible">
        </biz-dict-dialog>
        <subscript-dialog
          @closeDialog="closeSubscriptDialog"
          :mbId="mbIdSelect"
          :dialogFormVisible="dialogSubscriptVisible"
        >
        </subscript-dialog>
        <subscript-log
          @closeDialog="closeLogDialog"
          :mbId="mbIdSelect"
          :dialogFormVisible="dialogLogVisible"
        >
        </subscript-log>
      </el-container>
    </el-container>
  </el-container>
</template>

<script>
  import PermButton from '@/core/components/PermButton'
  import PermButtonGroup from "@/core/components/PermButtonGroup";
  import TablePlus from "@/core/components/TablePlus"
  import MainDialog from "./Dialog/MainDialog"
  import SubscriptLog from "./Dialog/SubscriptLog"
  import SubscriptDialog from "./Dialog/SubscriptDialog"
  import BizDictDialog from "@/core/views/BizDict/Dialog/DictDialog"
  import TableFooter from "@/core/components/TablePlus/TableFooter"
  import SelectPlus from "@/core/components/SelectPlus"
  import {resourceCode} from "@/biz/http/settings"
  import SplitBar from "@/core/components/SplitBar";

  export default {
    components: {
      TablePlus,
      PermButton,
      MainDialog,
      TableFooter,
      SelectPlus,
      BizDictDialog,
      SubscriptDialog,
      SubscriptLog,
      PermButtonGroup,
      SplitBar
    },
    data() {
      return {
        queryBt: true,
        // 👇 左树 业务类型 👇
        resourceCode: resourceCode.msgBiz,
        systemCode: config.systemCode,
        filterText: "",
        selectDiztId: undefined,
        selectDizName: undefined,
        dialogDictVisible: false,
        dialogDictStatus: '',
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        treeTemp: {},
        bizTypeList: [],
        treeLoading: true,
        // 👆 左树 业务类型 👆
        // table参数
        api: 'tSysMsgBiz',
        size: 'mini',
        list: [],
        listLoading: true,
        total: 0,
        currentRow: undefined,
        selectTableRow: [],
        mbIdSelect: "",
        // table查询参数
        filters: {
          currentPage: 1,
          pageSize: 20,
          enabled: undefined,
          bizName: undefined,
          msgBizType: undefined,
          mbId: undefined,
          order: ""
        },
        // 表单的参数设置
        dialogFormVisible: false,
        dialogModes: '',
        viewDisabled: false,
        dialogSubscriptVisible: false,
        dialogLogVisible: false
      }
    },
    computed: {
      pageMode() {
        return config.page_mode;
      }
    },
    // 执行方法
    methods: {
      getButtons(row) {
        let buts = [
          {label: "编辑", icon: "edit", clickFn: this.handleUpdate, perms: this.systemCode + 'x99005004005'},
          {label: "删除", icon: "delete", clickFn: this.handleDelete, perms: this.systemCode + 'x99005004006'},
          {label: "设置订阅", icon: "uims-icon-generate", clickFn: this.handleSubscript, perms: this.systemCode + 'x99005004008'},
          {label: "消息日志", icon: "uims-icon-query", clickFn: this.handleMsgLog, perms: this.systemCode + 'x99005004009'},
          {label: "查看", icon: "see", clickFn: this.handleView, perms: this.systemCode + 'x99005004007'}
        ];
        return {
          row: row,
          buttons: buts,
          showNums: 2
        }
      },
      handleHeaderDrag(newWidth, oldWidth, column, event) {
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout();
        })
      },
      handleEnabled(row) {
        let p = {}
        if(row.enabledCode === '1') {
          // 执行停用操作
          p = {
            mbId: row.mbId,
            enabled: '0'
          }
        } else if(row.enabledCode === '0') {
          p = {
            mbId: row.mbId,
            enabled: '1'
          }
        } else {
          // no status
          return
        }
        // 执行启用操作
        this.$api.msgBiz.changeEnabled(p, resourceCode.msgBiz).then((res) => {
          this.treeTemp = {};
          this.getList();
          this.$notify({
            title: '操作成功',
            message: '操作成功',
            type: 'success',
            duration: 2000
          })
        })
      },
      // 订阅
      closeSubscriptDialog(val) {
        this.dialogSubscriptVisible = val
      },
      closeLogDialog(val) {
        this.dialogLogVisible = val
      },
      // 👇 左树 业务类型 👇
      closeDictDialog(val) {
        this.dialogDictVisible = val
      },
      filterNode(value, dictTypeList) {
        if (!value) return true;
        return dictTypeList.name.indexOf(value) !== -1;
      },
      handleCheckChange() {
        let res = this.$refs.tree.getCurrentNode()
        this.treeTemp = res;
        this.selectDiztId = this.treeTemp.dictId
        this.selectDizName = this.treeTemp.name
        this.filters = {
          currentPage: 1,
          pageSize: 20,
          name: undefined,
          code: undefined,
          enabled: undefined,
          msgBizType: undefined
        }
        this.filters.msgBizType = res.dictId;
        this.getList()
      },
      getDictList() {
        this.treeLoading = true;
        this.$api.bizDict.getDictsByType({dictType: "MSG_BIZ_TYPE"}, resourceCode.msgBiz).then((res) => {
          this.treeLoading = false;
          this.bizTypeList = res.data;
        })
      },
      handleBizTypeCreate() {
        this.dialogDictStatus = 'create';
        this.dialogDictVisible = true;
      },
      handleBizTypeUpdate() {
        if (this.treeTemp && this.treeTemp.dictId) {
          this.dialogDictStatus = 'update'
          this.dialogDictVisible = true
        } else {
          this.$notify({
            title: '提示',
            message: '请选择字典类型',
            type: 'info',
            duration: 2000
          })
        }
      },
      handleBizTypeDelete() {
        if (this.treeTemp && this.treeTemp.dictId) {
          this.$confirm('您确认要删除该业务消息类型吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$api.msgBiz.delBizDict({dictId: this.treeTemp.dictId}, resourceCode.msgBiz).then((res) => {
              this.treeTemp = {};
              this.getDictList();
              this.$notify({
                title: '操作成功',
                message: '删除字典类型成功',
                type: 'success',
                duration: 2000
              })
            })
          }).catch(() => {
            this.$notify({
              message: '已取消删除',
              type: 'info',
              duration: 2000
            })
          })
        } else {
          this.$notify({
            title: '提示',
            message: '请选择字典类型',
            type: 'info',
            duration: 2000
          })
        }
      },
      // 👆 左树 业务类型 👆
      closeDialog(val) {
        this.dialogFormVisible = val
      },
      handleFilter(resetFlag) {
        this.filters.currentPage = 1
         if(resetFlag === true) {
          this.filters.enabled = undefined
          this.filters.bizName = undefined
          this.filters.msgBizType = undefined
          this.filters.mbId = undefined
        }
        this.getList()
      },
      getList() {
        if(!this.queryBt) {
          return;
        }
        this.queryBt = false
        this.listLoading = true
        this.$api.msgBiz.getList(this.filters, resourceCode.msgBiz).then((res) => {
          this.queryBt = true
          this.listLoading = false
          this.list = res.data ? res.data.records : []
          this.total = res.data ? res.data.total : 0
        }).catch(res => {
          this.queryBt = true
        })
      },
      selectMainTableRow(row, event, column) {
        this.selectTableRow = Object.assign([], row)
      },
      clickRow(row, event, column) {
        if (this.currentRow === row) {
          this.currentRow = undefined
          this.$refs.multipleTable.clearSelection();
        } else {
          this.currentRow = row
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(row)
        }
      },
      handleSizeChange(val) {
        this.filters.pageSize = val
        this.filters.currentPage = 1;
        this.getList()
      },
      handleCurrentChange(val) {
        this.filters.currentPage = val
        this.getList()
      },
      handleCreate() {
        if (this.treeTemp && this.treeTemp.dictId) {
          this.dialogModes = 'create'
          this.dialogFormVisible = true
          this.viewDisabled = false
        } else {
          this.$notify({
            title: '提示',
            message: '请先选择业务类型',
            type: 'info',
            duration: 2000
          })
        }
      },
      handleUpdate(row) {
        this.selectTableRow = [row]
        this.mbIdSelect = this.selectTableRow[0].mbId
        this.dialogModes = 'update'
        this.dialogFormVisible = true
        this.viewDisabled = false
      },
      handleView(row) {
        this.selectTableRow = [row]
        this.mbIdSelect = this.selectTableRow[0].mbId
        this.dialogModes = 'view'
        this.dialogFormVisible = true
        this.viewDisabled = true
      },
      handleSubscript(row) {
        this.selectTableRow = [row]
        this.mbIdSelect = this.selectTableRow[0].mbId
        this.dialogSubscriptVisible = true
      },
      handleMsgLog(row) {
        this.selectTableRow = [row]
        this.mbIdSelect = this.selectTableRow[0].mbId
        this.dialogLogVisible = true
      },
      handleDelete(row) {
        this.selectTableRow = [row]
        let mbId = this.selectTableRow[0].mbId
        this.$confirm('您确认要删除该条记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$api.msgBiz.del({mbId: mbId}, resourceCode.msgBiz).then((res) => {
            this.getList()
            this.$notify({
              title: '操作成功',
              message: '删除业务消息成功',
              type: 'success',
              duration: 2000
            })
          })
        }).catch(() => {
          this.$notify({
            message: '已取消删除',
            type: 'info',
            duration: 2000
          })
        })
      }
    },
    created() {
      this.getDictList()
      this.getList()
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      },
      total() {
        if (this.total === (this.filters.currentPage - 1) * this.filters.pageSize && this.total !== 0) {
          this.filters.currentPage -= 1;
          this.getList();
        }
      }
    }
  }
</script>

<style scoped lang="scss">
body .page-container .marginLeft0 {
  margin-left: 0;
}
</style>
