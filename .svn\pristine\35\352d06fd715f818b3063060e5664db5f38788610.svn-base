<template>
  <div class="drawer-container">
    <div>
      <h3 class="drawer-title">工作台</h3>
      <div class="role-shortcut-wrapper">
        <el-scrollbar ref="lscrollbar" wrap-class="scrollbar-wrapper lscrollbar" style="height: 100%;">
          <ul class="role-shotcut-container">
            <li v-for="menu in roleShortcuts" :key="menu.resourceCode" @click="clickMenu(menu)" class="drawer-item">
              <el-badge :value="menu.count" class="item">
                <svg-icon v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" icon-class='menu'/>
                <svg-icon v-else-if="menu.icon.indexOf('/icons/svg/')==0" :icon-class="menu.icon.replace('/icons/svg/', '')" />
                <svg-icon v-else-if="menu.icon.indexOf('.svg')>0" :file-path="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`" icon-class='menu'/>
                <svg-icon v-else-if="menu.icon.indexOf('noneDisplay')==0" icon-class='noneDisplay' />
                <img v-else :src="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`"/>
                <div class="name"><span :title="menu.resourceName.length > 4 ? menu.resourceName : ''">{{menu.resourceName}}</span></div>
              </el-badge>
            </li>
          </ul>
        </el-scrollbar>
      </div>
      <div class="drawer-divider"><el-divider></el-divider></div>
      <h3 class="drawer-title common">常用模块
        <perm-button type="text" icon="edit" @click="showResourceDialog" class="iconBtn"/>
      </h3>
      <div class="user-shortcut-wrapper">
        <el-scrollbar ref="rscrollbar" wrap-class="scrollbar-wrapper rscrollbar" style="height: 100%;">
          <ul class="role-shotcut-container">
            <li v-for="menu in userShortcuts" :key="menu.resourceCode" @click="clickMenu(menu)" class="drawer-item">
                <svg-icon v-if="!menu.icon || menu.icon === null ||  menu.icon === 'null'" icon-class='menu'/>
                <svg-icon v-else-if="menu.icon.indexOf('/icons/svg/')==0" :icon-class="menu.icon.replace('/icons/svg/', '')" />
                <svg-icon v-else-if="menu.icon.indexOf('.svg')>0" :file-path="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`" icon-class='menu'/>
                <svg-icon v-else-if="menu.icon.indexOf('noneDisplay')==0" icon-class='noneDisplay' />
                <img v-else :src="`${this.global.baseURL}/${appCode_uims}/files/get?path=${menu.icon}`"/>
              <div class="name"><span :title="menu.resourceName.length > 4 ? menu.resourceName : ''">{{menu.resourceName}}</span></div>
            </li>
          </ul>
        </el-scrollbar>
      </div>
    </div>
    <resource-dialog ref="resourceDialog" :show="isShowDialog" v-on:setUserShortCut="setUserShortCut"></resource-dialog>
  </div>
</template>

<script>
import SvgIcon from '@/core/components/SvgIcon/index.vue'
import PermButton from "@/core/components/PermButton";
import ResourceDialog from './ResourceDialog'
export default {
  components: { PermButton, SvgIcon, ResourceDialog },
  data() {
    return {
      roleShortcuts: [], // 角色快捷菜单
      userShortcuts: [], // 用户自定义快捷菜单
      baseUrl: this.global.baseURL,
      isShowDialog: false,
      listLoading: false,
      m: []
    }
  },
  watch: {
    m(val) {
      this.roleShortcuts = val;
    }
  },
  methods: {
    // 获取用户当前角色快捷菜单
    findRoleShortcutMenu: async function() {
      this.$api.authority.getRoleShortcutMenu({ 'sysCode': config.systemCode }).then(res => {
        return res.data;
      }).then(async data => {
        this.roleShortcuts = await this.getTodoCount(data);
      })
    },
    getTodoCount(data) {
      return new Promise(async resolve => {
        await data.map(async (d, index) => {
          /* await this.$api.bizApi.todo.getTodoCount({ 'resourceCode': d.resourceCode }).then(res => {
            d.count = res.data > 0 ? res.data : '';
            this.$set(this.roleShortcuts, index, d)
          }) */
        })
        resolve(data);
      })
    },
    // 获取用户快捷菜单
    findUserShortcutMenu() {
      this.$api.authority.getUserShortcutMenu({ 'sysCode': config.systemCode }).then(res => {
        this.listLoading = false;
        this.userShortcuts = res.data.map(function (n, index) {
          // n.resourceName = n.data.resourceName;
          n.name = n.resourceName;
          n.id = n.resourceId;
          // n.url = n.data.url;
          // n.icon = n.data.icon;
          return n;
        })
        this.$refs.resourceDialog.init(this.userShortcuts);
      });
    },
    showResourceDialog() {
      this.isShowDialog = true;
      this.$refs.resourceDialog.setDialogVisible(true);
    },
    // 设置用户自定义快捷菜单
    setUserShortCut: function (data) {
      // 刷新展现
      // this.userShortcuts = data;
      this.userShortcuts = data.map(function (n, index) {
        n.resourceName = n.name;
        n.resourceId = n.id;
        // n.url = n.data.url;
        // n.icon = n.data.icon;
        return n;
      })
      let newData = data.map((d, index) => {
        return {"resourceId": d.resourceId ? d.resourceId : d.id, "orderNo": index + 1}
      });
      // 保存到数据库
      this.$api.authority.saveUserShortcutMenu(newData).then(res => {
        this.$notify({
          title: '操作成功',
          message: '设置成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    // 快捷菜单点击事件
    clickMenu: function (data) {
      this.$router.push({ name: data.resourceName })
      this.$parent.$emit("closePanel");
    }
  },
  mounted() {
    this.findRoleShortcutMenu();
    this.findUserShortcutMenu();
  }
}
</script>
