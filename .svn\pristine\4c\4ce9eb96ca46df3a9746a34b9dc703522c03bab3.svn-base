<template>
  <div class="myMsg-panel">
    <el-tabs v-model="tabName" ref="messageTabs" @tab-click="handleClick">
      <el-tab-pane name="all" v-if="messageConfig ? (messageConfig.showNotice + messageConfig.showMessage + messageConfig.showBusiness > 1) : true">
        <span slot="label" v-if="initTotal">全部 (<em :class="myAll.length > 0 ? 'emphasize' : ''">{{initTotal}}</em>)</span>
        <span slot="label" v-else>全部</span>
        {{myAll.length > 0 ? "" : "暂无数据"}}
        <ul v-if="myAll.length > 0">
          <li v-for="item in myAll" :key="item.miId" @click="handleItemClick(item)">
            <span :class="['svg-container', 'type_' + item.msgType]">
              <svg-icon :icon-class="`msg-item-${item.msgType}`" />
            </span>
            <div class="message-content-wrapper">
              <div class="message-content">
                {{ item.subject }}
              </div>
              <div class="time">
                {{ item.receiveTime }}
              </div>
            </div>
          </li>
        </ul>
        <div v-if="myAll.length > 0" class="message-footer">
          <div class="more" @click="showMore('00')">
            查看更多
            <span class="svg-container">
              <svg-icon icon-class="double-right-arrow" />
            </span>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="notice" v-if="messageConfig ? messageConfig.showNotice : true">
        <span slot="label" v-if="initTotalNotice">通知公告 (<em :class="myNotice.length > 0 ? 'emphasize' : ''">{{initTotalNotice}}</em>)</span>
        <span slot="label" v-else>通知公告</span>
        {{myNotice.length > 0 ? "" : "暂无数据"}}
        <ul v-if="myNotice.length > 0">
          <li v-for="item in myNotice" :key="item.miId" @click="handleItemClick(item)">
            <span :class="['svg-container', 'type_' + item.msgType]">
              <svg-icon :icon-class="`msg-item-${item.msgType}`" />
            </span>
            <div class="message-content-wrapper">
              <div class="message-content">
                {{ item.subject }}
              </div>
              <div class="time">
                {{ item.receiveTime }}
              </div>
            </div>
          </li>
        </ul>
        <div v-if="myNotice.length > 0" class="message-footer">
          <div class="more" @click="showMore('0')">
            查看更多
            <span class="svg-container">
              <svg-icon icon-class="double-right-arrow" />
            </span>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="message" v-if="messageConfig ? messageConfig.showMessage : true">
        <span slot="label" v-if="initTotalUser">私人消息 (<em :class="myMsg.length > 0 ? 'emphasize' : ''">{{initTotalUser}}</em>)</span>
        <span slot="label" v-else>私人消息</span>
        {{myMsg.length > 0 ? "" : "暂无数据"}}
        <ul v-if="myMsg.length > 0">
          <li v-for="item in myMsg" :key="item.miId" @click="handleItemClick(item)">
            <span :class="['svg-container', 'type_' + item.msgType]">
              <svg-icon :icon-class="`msg-item-${item.msgType}`" />
            </span>
            <div class="message-content-wrapper">
              <div class="message-content">
                {{ item.subject }}
              </div>
              <div class="time">
                {{ item.receiveTime }}
              </div>
            </div>
          </li>
        </ul>
        <div v-if="myMsg.length > 0" class="message-footer">
          <div class="more" @click="showMore('1')">
            查看更多
            <span class="svg-container">
              <svg-icon icon-class="double-right-arrow" />
            </span>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="todo" v-if="messageConfig ? messageConfig.showBusiness : true">
        <span slot="label" v-if="initTotalBiz">业务消息 (<em :class="myTodo.length > 0 ? 'emphasize' : ''">{{initTotalBiz}}</em>)</span>
        <span slot="label" v-else>业务消息</span>
        {{myTodo.length > 0 ? "" : "暂无数据"}}
        <ul v-if="myTodo.length > 0">
          <li v-for="item in myTodo" :key="item.miId" @click="handleItemClick(item)">
            <span :class="['svg-container', 'type_' + item.msgType]">
              <svg-icon :icon-class="`msg-item-${item.msgType}`" />
            </span>
            <div class="message-content-wrapper">
              <div class="message-content">
                {{ item.subject }}
              </div>
              <div class="time">
                {{ item.receiveTime }}
              </div>
            </div>
          </li>
        </ul>
        <div v-if="myTodo.length > 0" class="message-footer">
          <div class="more" @click="showMore('2')">
            查看更多
            <span class="svg-container">
              <svg-icon icon-class="double-right-arrow" />
            </span>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'MessagePanel',
  data() {
    return {
      // 全部消息
      myAll: [],
      // 业务消息
      myTodo: [],
      // 通知公告
      myNotice: [],
      // 私人发信
      myMsg: [],
      initTotal: 0,
      initTotalNotice: 0,
      initTotalUser: 0,
      initTotalBiz: 0,
      tabName: 'all'
    }
  },
  activated() {
    this.tabName = this.messageConfig ? this.messageConfig.showNotice + this.messageConfig.showMessage + this.messageConfig.showBusiness > 1 ? 'all' : this.messageConfig.showNotice ? 'notice' : this.messageConfig.showMessage ? 'message' : 'todo' : 'all'
  },
  computed: {
    messageConfig() {
      return this.$store.state.app.configs.headerBar.message
    },
    instantMsg() {
      return this.$store.state.user.instantMsg
    }
  },
  watch: {
    instantMsg() {
      if (this.instantMsg) {
        this.myAll = this.instantMsg.initInfo ? this.instantMsg.initInfo : [];
        this.myNotice = this.instantMsg.initNoticeInfo ? this.instantMsg.initNoticeInfo : [];
        this.myTodo = this.instantMsg.initBizInfo ? this.instantMsg.initBizInfo : [];
        this.myMsg = this.instantMsg.initUserInfo ? this.instantMsg.initUserInfo : [];
        this.initTotal = this.instantMsg.initTotal ? this.instantMsg.initTotal : 0;
        this.initTotalNotice = this.instantMsg.initTotalNotice ? this.instantMsg.initTotalNotice : 0;
        this.initTotalUser = this.instantMsg.initTotalUser ? this.instantMsg.initTotalUser : 0;
        this.initTotalBiz = this.instantMsg.initTotalBiz ? this.instantMsg.initTotalBiz : 0;
      }
    }
  },
  methods: {
    handleClick(tab, event) {
      // 按钮操作处理函数
      // this.$emit('click', {})
    },
    handleItemClick(item) {
      let newItem = Object.assign(item)
      this.$emit('showMsgDetailPanel', {msg: newItem});
    },
    showMore(type) {
      this.$router.push('/msgInBox/index')
      this.$emit('showMsgDetailPanel');
    }
  }
}
</script>
<style scoped>
>>> .el-tabs__active-bar {
  display: none;
}
</style>
