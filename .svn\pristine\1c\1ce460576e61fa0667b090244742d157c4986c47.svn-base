<template>
    <el-dialog v-dialogDrag v-show="dialogVisible" title="切换用户" width="530px" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-container>
        <el-header>
          <div>当前登录账户为{{currentUser.userName}}，请选择需切换的机构。</div>
        </el-header>
        <el-main style="margin:0;">
          <el-scrollbar wrap-class="scrollbar-wrapper">
            <ul class="display-flex">
              <li class="list-wrapper currentUser" @click="clearChoose()">
                <el-card class="box-card">
                  <div class="unit" :title="currentUser.unitName">{{currentUser.unitName}}</div>
                  <div class="user" :title="currentUser.userName">{{currentUser.userName}}</div>
                </el-card>
              </li>
              <li class="list-wrapper" v-for="(item, index) in unionUsers" :key="item.userId" @click="chooseUser(item, index)"
                  :class="[{'currentUser': item.userId === currentUser.userId}, {'choosedUser': isactive === index && item.userId !== currentUser.userId}]">
                <el-card class="box-card">
                  <div class="unit" :title="currentUser.unitName">{{item.unitName}}</div>
                  <div class="user" :title="currentUser.userName">{{item.userName}}</div>
                </el-card>
              </li>
            </ul>
          </el-scrollbar>
        </el-main>
      </el-container>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.native="chgUser" :loading="loading">切换</el-button>
      </div>
    </el-dialog>
</template>

<script>
    import { mapState } from 'vuex'
    import { resetDynamicMenuAndRoutes } from '@/core/router'
    export default {
        name: "index",
        props: {
            show: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                dialogVisible: this.show,
                loading: false,
                chgUserId: '',
                chgUserObj: {},
                isactive: '',
                systemId: ''
            }
        },
        watch: {
            dialogVisible: function (newV, oldV) {
                // 告诉父元素改变show的值
                this.$emit('closeModal', newV)
                this.clearChoose()
            },
            show: function (newV, oldV) {
                this.dialogVisible = newV
            }
        },
        methods: {
            // 选择切换用户
            chooseUser: function (item, index) {
                this.chgUserObj = item
                this.chgUserId = item.userId
                this.isactive = index
            },
            // 清除选择
            clearChoose: function() {
                this.chgUserId = ''
                this.isactive = ''
            },
            // 切换用户
            chgUser: function () {
                if (this.chgUserId === '' || this.chgUserId === this.currentUser.userId) {
                    // this.$message({message: '请选择不同用户进行切换！'})
                    this.$notify({ title: "提示", message: "请选择不同用户进行切换！", type: "info", duration: 2000 });
                } else {
                    // 切换用户
                    resetDynamicMenuAndRoutes(this.chgUserObj.userName)
                }
                this.dialogVisible = false
            }
        },
        computed: {
            ...mapState({
                currentUser: state => state.user.currentUser,
                unionUsers: state => state.user.unionUsers
            })
        }
    }
</script>
<style scoped>
.unit, .user {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
