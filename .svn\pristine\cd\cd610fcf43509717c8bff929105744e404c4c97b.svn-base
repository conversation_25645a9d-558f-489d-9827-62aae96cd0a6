import Vue from 'vue';
import popLoginBoxVue from './main.vue';

const PopLoginBoxConstructor = Vue.extend(popLoginBoxVue);

let currentMsg, instance;
let msgQueue = [];

const defaultCallback = action => {
  if (currentMsg) {
    if (currentMsg.resolve) {
      if (action === 'confirm') {
        currentMsg.resolve(action);
      } else if (currentMsg.reject && action === 'cancel') {
        currentMsg.reject(action);
      }
    }
  }
};

const initInstance = () => {
  instance = new PopLoginBoxConstructor({
    el: document.createElement('div')
  });

  instance.callback = defaultCallback;
};

const showNextMsg = () => {
  if (!instance) {
    initInstance();
  }
  instance.action = '';

  if (!instance.visible) {
    if (msgQueue.length > 0) {
      currentMsg = msgQueue.shift();

      instance.callback = defaultCallback;

      let oldCb = instance.callback;
      instance.callback = (action, instance) => {
        oldCb(action, instance);
      };
      document.body.appendChild(instance.$el);

      Vue.nextTick(() => {
        instance.visible = true;
      });
    }
  }
};

const PopLoginBox = function() {
  if(msgQueue.length > 0) {
    return
  }
  return new Promise((resolve, reject) => {
    msgQueue.push({
      resolve: resolve,
      reject: reject
    });
    showNextMsg();
  });
};

PopLoginBox.close = () => {
  if(instance) {
    instance.doClose();
    instance.visible = false;
    msgQueue = [];
    currentMsg = null;
  }
};

export default PopLoginBox;
export { PopLoginBox };
