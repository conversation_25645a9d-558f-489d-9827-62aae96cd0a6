import axios from '../axios'

/*
 * 获取权限模块
 */

// 从后台拉取前端配置
export const getConfig = () => {
  return axios({
    url: `/${config.appCode}/authority/getConfig`,
    method: 'post'
  })
}

// 查找导航菜单树
export const findAuthority = (params) => {
  return axios({
    url: `/${config.appCode_uims}/authority/getSysResource`,
    method: 'post',
    postType: 'form',
    data: params,
    headers: {'isBaseApi': true}
  })
}

// 查找联合用户
export const findUnionUser = (params) => {
  return axios({
    url: `/${config.appCode_uims}/authority/getUnionUser`,
    method: 'post',
    postType: 'form',
    data: params,
    headers: {'isBaseApi': true}
  })
}

export function getColsByDataRule (params) {
  return axios({
    url: `/${config.appCode_uims}/authority/getSysDataruleCol`,
    method: 'post',
    postType: 'form',
    data: params,
    headers: {'isBaseApi': true}
  })
}

// 切换用户
export const switchUser = (data) => {
  return axios({
    url: `/${config.appCode_auth}/token/switch`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: "form",
    data,
    headers: {'isBaseApi': true}
  })
}

// 获取上次登录信息
export function getLastLoginInfo () {
  return axios({
    url: `/${config.appCode_uims}/authuser/getLastLoginInfo`,
    method: 'post',
    headers: {'isBaseApi': true}
  })
}

// 获取系统列表
export function getSystemList () {
  return axios({
    url: config.isOnUims ? `/${config.appCode}/security-frame/get-system-list` : `/${config.appCode_uims}/authuser/getSystemList`,
    method: 'post',
    headers: {'isBaseApi': true}
  })
}

export function userSystem () {
  return axios({
    url: '/admin/authuser/userSystem',
    method: 'get'
  })
}

// 获取快捷系统列表
export function getUserConfigSystemList () {
  return axios({
    url: `/${config.appCode_uims}/authuser/getUserConfigSystemList`,
    method: 'post',
    headers: {'isBaseApi': true}
  })
}

// 保存快捷系统
export function saveUserConfigSys (data) {
  return axios({
    url: `/${config.appCode_uims}/authuser/saveUserConfigSys`,
    method: 'post',
    postType: 'form',
    data,
    headers: {'isBaseApi': true}
  })
}

// 获取系统信息以及空间信息
export function getSystemInfo (data) {
  return axios({
    url: `/${config.appCode_uims}/authuser/getSystemInfo`,
    method: 'post',
    postType: 'form',
    data,
    headers: {'isBaseApi': true}
  })
}
// 根据token获取用户信息
export const getUserInfoByToken = (data) => {
  if(data.ticket || data.code) {
    if(config.isOnUims) {
      return axios({
        url: `/${config.appCode}/security-frame/getTokenByCode`,
        method: 'post',
        headers: {'isBaseApi': true},
        params: {
          code: data.code,
          state: data.state,
          url: data.url,
          // 用于同一用户多终端登录的参数
          xep: new Date().getTime()
        }
      })
    } else {
      return axios({
        url: `/${config.appCode_auth}/token/exchange`,
        method: 'post',
        postType: 'form',
        data
      })
    }
  } else {
    return axios({
      url: `/${config.appCode_auth}/token/retrieve`,
      method: 'post',
      headers: {'isBaseApi': true, 'Authorization': `Bearer ${data.token}`}
    })
  }
}

// 根据token获取用户信息
export function getState (data) {
  return axios({
      url: `/${config.appCode_uims}/security-frame/generateState`,
      method: 'post',
      postType: 'form',
      data,
      headers: {'isBaseApi': true}
  })
}

// 获取角色默认快捷菜单
export const getRoleShortcutMenu = (data) => {
  return axios({
    url: `/${config.appCode_uims}/customPriv/getCorePrivByUserId`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: 'form',
    data,
    headers: {'isBaseApi': true}
  })
}

// 获取用户自定义的快捷菜单
export const getUserShortcutMenu = (data) => {
  return axios({
    url: `/${config.appCode_uims}/customPriv/getCustomPrivByUserId`,
    method: 'post',
    // 连后台时放开postType: "form"注释
    postType: 'form',
    data,
    headers: {'isBaseApi': true}
  })
}

// 用户自定义的快捷菜单保存入库
export const saveUserShortcutMenu = (data) => {
  return axios({
    url: `/${config.appCode_uims}/customPriv/save`,
    method: 'post',
    data,
    headers: {'isBaseApi': true}
  })
}

// 保存用户风格
export const saveTheme = (data) => {
  return axios({
    url: `/${config.appCode_uims}/theme/saveTheme`,
    method: 'post',
    data,
    headers: {'isBaseApi': true}
  })
}

// 获取用户风格
export const getTheme = () => {
  return axios({
    url: `/${config.appCode_uims}/theme/getTheme`,
    method: 'post',
    headers: {'isBaseApi': true}
  })
}

export const logout = () => {
  return axios({
    url: `/${config.appCode_auth}/oauth/logout`,
    method: 'post'
  })
}

export const getUimsPath = () => {
  return axios({
    url: '/oauth/uimsPath',
    method: 'get'
  })
}

export const expExcel = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/base-framework/exp-excel`,
    method: 'post',
    data,
    timeout: 120000,
    postType: "form",
    responseType: 'blob',
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
};

export function getResetPwdReason (data) {
  return axios({
    url: '/oauth/fx/getResetPwdReason',
    method: 'post',
    params: data
  })
}
