<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1133298" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">皮肤</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">删 除</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">编 辑</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d5;</span>
                <div class="name">108服务器管理</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67b;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe67b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe67d;</span>
                <div class="name">选择</div>
                <div class="code-name">&amp;#xe67d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ca;</span>
                <div class="name">重置</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78a;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe78a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">选择</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">确认</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">授权</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">查看 copy 2</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">解绑</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b6;</span>
                <div class="name">变更管理</div>
                <div class="code-name">&amp;#xe6b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe703;</span>
                <div class="name">企业吊销原因查询</div>
                <div class="code-name">&amp;#xe703;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">确定</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fd;</span>
                <div class="name">最新-发送验证码-toast</div>
                <div class="code-name">&amp;#xe6fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">短信</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ba;</span>
                <div class="name">账户</div>
                <div class="code-name">&amp;#xe6ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">取消</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">待办</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe630;</span>
                <div class="name">bao</div>
                <div class="code-name">&amp;#xe630;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">包3</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">待办</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">我的工作区-权限委托</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">用户组管理</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">岗位管理</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">资源管理</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">用户管理</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">系统管理</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a2;</span>
                <div class="name">社区（村）区划管理</div>
                <div class="code-name">&amp;#xe6a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">接口</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">数据权限</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">用户管理</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f0;</span>
                <div class="name">机构管理</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">字典管理</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">编码规则管理</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">日志查看</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">系统管理-字典管理</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9c8;</span>
                <div class="name">参数管理</div>
                <div class="code-name">&amp;#xe9c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">在线用户</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">绑定</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">退出</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">证书申请</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">生成 </div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">停用</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">提交 (1)</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72f;</span>
                <div class="name">启用</div>
                <div class="code-name">&amp;#xe72f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">箭头 左双</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">箭头 右双-01</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6fe;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe6fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">QQ登录</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60f;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe60f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">验证码</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">手机号登录</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">邮箱登录</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">微信登录</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">账号登录</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">邮箱</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">微博登录</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">excel</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">excel</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">手机</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">qq</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">微信</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">新浪</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">提示</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">信息</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">22</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">12</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">12</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">减号</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c3;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe6c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">删除／数字面板编辑态</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">基础数据管理</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">主数据管理</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">提示</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe99f;</span>
                <div class="name">提示</div>
                <div class="code-name">&amp;#xe99f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78f;</span>
                <div class="name">信息交换</div>
                <div class="code-name">&amp;#xe78f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">配置-2</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ad;</span>
                <div class="name">架构</div>
                <div class="code-name">&amp;#xe6ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">服务器</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">配置</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconpifu"></span>
            <div class="name">
              皮肤
            </div>
            <div class="code-name">.iconpifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshanchu"></span>
            <div class="name">
              删 除
            </div>
            <div class="code-name">.iconshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbianji1"></span>
            <div class="name">
              编 辑
            </div>
            <div class="code-name">.iconbianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfuwuqiguanli"></span>
            <div class="name">
              108服务器管理
            </div>
            <div class="code-name">.iconfuwuqiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondaochu"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.icondaochu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxuanze1"></span>
            <div class="name">
              选择
            </div>
            <div class="code-name">.iconxuanze1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongzhi"></span>
            <div class="name">
              重置
            </div>
            <div class="code-name">.iconzhongzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbaocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.iconbaocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxuanze"></span>
            <div class="name">
              选择
            </div>
            <div class="code-name">.iconxuanze
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqueren"></span>
            <div class="name">
              确认
            </div>
            <div class="code-name">.iconqueren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqiehuan-"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.iconqiehuan-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouquan"></span>
            <div class="name">
              授权
            </div>
            <div class="code-name">.iconshouquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchakancopy"></span>
            <div class="name">
              查看 copy 2
            </div>
            <div class="code-name">.iconchakancopy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiebang"></span>
            <div class="name">
              解绑
            </div>
            <div class="code-name">.iconjiebang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbiangengguanli"></span>
            <div class="name">
              变更管理
            </div>
            <div class="code-name">.iconbiangengguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqiyediaoxiaoyuanyinchaxun"></span>
            <div class="name">
              企业吊销原因查询
            </div>
            <div class="code-name">.iconqiyediaoxiaoyuanyinchaxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqueding"></span>
            <div class="name">
              确定
            </div>
            <div class="code-name">.iconqueding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfasongyanzhengma-toast"></span>
            <div class="name">
              最新-发送验证码-toast
            </div>
            <div class="code-name">.iconfasongyanzhengma-toast
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyoujian"></span>
            <div class="name">
              短信
            </div>
            <div class="code-name">.iconyoujian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhanghu"></span>
            <div class="name">
              账户
            </div>
            <div class="code-name">.iconzhanghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquxiao"></span>
            <div class="name">
              取消
            </div>
            <div class="code-name">.iconquxiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouji2"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.iconshouji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondaiban1"></span>
            <div class="name">
              待办
            </div>
            <div class="code-name">.icondaiban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbao2"></span>
            <div class="name">
              bao
            </div>
            <div class="code-name">.iconbao2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbao"></span>
            <div class="name">
              包3
            </div>
            <div class="code-name">.iconbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondaiban"></span>
            <div class="name">
              待办
            </div>
            <div class="code-name">.icondaiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxinzeng"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.iconxinzeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwodegongzuoqu-quanxianweituo"></span>
            <div class="name">
              我的工作区-权限委托
            </div>
            <div class="code-name">.iconwodegongzuoqu-quanxianweituo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghuzuguanli"></span>
            <div class="name">
              用户组管理
            </div>
            <div class="code-name">.iconyonghuzuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongangweiguanli"></span>
            <div class="name">
              岗位管理
            </div>
            <div class="code-name">.icongangweiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconziyuanguanli"></span>
            <div class="name">
              资源管理
            </div>
            <div class="code-name">.iconziyuanguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghuguanli1"></span>
            <div class="name">
              用户管理
            </div>
            <div class="code-name">.iconyonghuguanli1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxitongguanli"></span>
            <div class="name">
              系统管理
            </div>
            <div class="code-name">.iconxitongguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshequcunquhuaguanli"></span>
            <div class="name">
              社区（村）区划管理
            </div>
            <div class="code-name">.iconshequcunquhuaguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiekou"></span>
            <div class="name">
              接口
            </div>
            <div class="code-name">.iconjiekou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshujuquanxian"></span>
            <div class="name">
              数据权限
            </div>
            <div class="code-name">.iconshujuquanxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghuguanli"></span>
            <div class="name">
              用户管理
            </div>
            <div class="code-name">.iconyonghuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjigouguanli"></span>
            <div class="name">
              机构管理
            </div>
            <div class="code-name">.iconjigouguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzidianguanli"></span>
            <div class="name">
              字典管理
            </div>
            <div class="code-name">.iconzidianguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbianmaguizeguanli"></span>
            <div class="name">
              编码规则管理
            </div>
            <div class="code-name">.iconbianmaguizeguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrizhi"></span>
            <div class="name">
              日志查看
            </div>
            <div class="code-name">.iconrizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxitongguanli-zidianguanli"></span>
            <div class="name">
              系统管理-字典管理
            </div>
            <div class="code-name">.iconxitongguanli-zidianguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcanshuguanli"></span>
            <div class="name">
              参数管理
            </div>
            <div class="code-name">.iconcanshuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzaixianyonghu"></span>
            <div class="name">
              在线用户
            </div>
            <div class="code-name">.iconzaixianyonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbangdingcheliang"></span>
            <div class="name">
              绑定
            </div>
            <div class="code-name">.iconbangdingcheliang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfenxiang"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.iconfenxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuichu1"></span>
            <div class="name">
              退出
            </div>
            <div class="code-name">.icontuichu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhengshushenqing"></span>
            <div class="name">
              证书申请
            </div>
            <div class="code-name">.iconzhengshushenqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshengcheng"></span>
            <div class="name">
              生成 
            </div>
            <div class="code-name">.iconshengcheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontingyong1"></span>
            <div class="name">
              停用
            </div>
            <div class="code-name">.icontingyong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontijiao"></span>
            <div class="name">
              提交 (1)
            </div>
            <div class="code-name">.icontijiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqiyong"></span>
            <div class="name">
              启用
            </div>
            <div class="code-name">.iconqiyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantouzuoshuang"></span>
            <div class="name">
              箭头 左双
            </div>
            <div class="code-name">.iconjiantouzuoshuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconarrow2-right"></span>
            <div class="name">
              箭头 右双-01
            </div>
            <div class="code-name">.iconarrow2-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchakan"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.iconchakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.iconshouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconQQdenglu"></span>
            <div class="name">
              QQ登录
            </div>
            <div class="code-name">.iconQQdenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouji1"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.iconshouji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmima1"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.iconmima1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanzhengma"></span>
            <div class="name">
              验证码
            </div>
            <div class="code-name">.iconyanzhengma
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshoujihaodenglu"></span>
            <div class="name">
              手机号登录
            </div>
            <div class="code-name">.iconshoujihaodenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyouxiangdenglu"></span>
            <div class="name">
              邮箱登录
            </div>
            <div class="code-name">.iconyouxiangdenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghu2"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.iconyonghu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweixindenglu"></span>
            <div class="name">
              微信登录
            </div>
            <div class="code-name">.iconweixindenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhanghaodenglu"></span>
            <div class="name">
              账号登录
            </div>
            <div class="code-name">.iconzhanghaodenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyouxiang"></span>
            <div class="name">
              邮箱
            </div>
            <div class="code-name">.iconyouxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweibodenglu"></span>
            <div class="name">
              微博登录
            </div>
            <div class="code-name">.iconweibodenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconexcel1"></span>
            <div class="name">
              excel
            </div>
            <div class="code-name">.iconexcel1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconexcel"></span>
            <div class="name">
              excel
            </div>
            <div class="code-name">.iconexcel
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouji"></span>
            <div class="name">
              手机
            </div>
            <div class="code-name">.iconshouji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqq"></span>
            <div class="name">
              qq
            </div>
            <div class="code-name">.iconqq
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconweixin"></span>
            <div class="name">
              微信
            </div>
            <div class="code-name">.iconweixin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxinlang"></span>
            <div class="name">
              新浪
            </div>
            <div class="code-name">.iconxinlang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontishi2"></span>
            <div class="name">
              提示
            </div>
            <div class="code-name">.icontishi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghu1"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.iconyonghu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon--copy-copy"></span>
            <div class="name">
              信息
            </div>
            <div class="code-name">.iconicon--copy-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test2"></span>
            <div class="name">
              22
            </div>
            <div class="code-name">.iconicon-test2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test"></span>
            <div class="name">
              12
            </div>
            <div class="code-name">.iconicon-test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test1"></span>
            <div class="name">
              12
            </div>
            <div class="code-name">.iconicon-test1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjianhao"></span>
            <div class="name">
              减号
            </div>
            <div class="code-name">.iconjianhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxinzeng1"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.iconxinzeng1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchuyidong1"></span>
            <div class="name">
              删除／数字面板编辑态
            </div>
            <div class="code-name">.iconchuyidong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.iconshuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjichushujuguanli"></span>
            <div class="name">
              基础数据管理
            </div>
            <div class="code-name">.iconjichushujuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhushujuguanli"></span>
            <div class="name">
              主数据管理
            </div>
            <div class="code-name">.iconzhushujuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontishi"></span>
            <div class="name">
              提示
            </div>
            <div class="code-name">.icontishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontishi1"></span>
            <div class="name">
              提示
            </div>
            <div class="code-name">.icontishi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconinterchange"></span>
            <div class="name">
              信息交换
            </div>
            <div class="code-name">.iconinterchange
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpeizhi_2"></span>
            <div class="name">
              配置-2
            </div>
            <div class="code-name">.iconpeizhi_2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiagou"></span>
            <div class="name">
              架构
            </div>
            <div class="code-name">.iconjiagou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.iconmima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghu"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.iconyonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfuwuqi"></span>
            <div class="name">
              服务器
            </div>
            <div class="code-name">.iconfuwuqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpeizhi-copy"></span>
            <div class="name">
              配置
            </div>
            <div class="code-name">.iconpeizhi-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.iconbianji
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpifu"></use>
                </svg>
                <div class="name">皮肤</div>
                <div class="code-name">#iconpifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu"></use>
                </svg>
                <div class="name">删 除</div>
                <div class="code-name">#iconshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbianji1"></use>
                </svg>
                <div class="name">编 辑</div>
                <div class="code-name">#iconbianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfuwuqiguanli"></use>
                </svg>
                <div class="name">108服务器管理</div>
                <div class="code-name">#iconfuwuqiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondaochu"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#icondaochu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxuanze1"></use>
                </svg>
                <div class="name">选择</div>
                <div class="code-name">#iconxuanze1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongzhi"></use>
                </svg>
                <div class="name">重置</div>
                <div class="code-name">#iconzhongzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#iconbaocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxuanze"></use>
                </svg>
                <div class="name">选择</div>
                <div class="code-name">#iconxuanze</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqueren"></use>
                </svg>
                <div class="name">确认</div>
                <div class="code-name">#iconqueren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqiehuan-"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#iconqiehuan-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouquan"></use>
                </svg>
                <div class="name">授权</div>
                <div class="code-name">#iconshouquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakancopy"></use>
                </svg>
                <div class="name">查看 copy 2</div>
                <div class="code-name">#iconchakancopy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiebang"></use>
                </svg>
                <div class="name">解绑</div>
                <div class="code-name">#iconjiebang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbiangengguanli"></use>
                </svg>
                <div class="name">变更管理</div>
                <div class="code-name">#iconbiangengguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqiyediaoxiaoyuanyinchaxun"></use>
                </svg>
                <div class="name">企业吊销原因查询</div>
                <div class="code-name">#iconqiyediaoxiaoyuanyinchaxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqueding"></use>
                </svg>
                <div class="name">确定</div>
                <div class="code-name">#iconqueding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfasongyanzhengma-toast"></use>
                </svg>
                <div class="name">最新-发送验证码-toast</div>
                <div class="code-name">#iconfasongyanzhengma-toast</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyoujian"></use>
                </svg>
                <div class="name">短信</div>
                <div class="code-name">#iconyoujian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhanghu"></use>
                </svg>
                <div class="name">账户</div>
                <div class="code-name">#iconzhanghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquxiao"></use>
                </svg>
                <div class="name">取消</div>
                <div class="code-name">#iconquxiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouji2"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#iconshouji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondaiban1"></use>
                </svg>
                <div class="name">待办</div>
                <div class="code-name">#icondaiban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbao2"></use>
                </svg>
                <div class="name">bao</div>
                <div class="code-name">#iconbao2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbao"></use>
                </svg>
                <div class="name">包3</div>
                <div class="code-name">#iconbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondaiban"></use>
                </svg>
                <div class="name">待办</div>
                <div class="code-name">#icondaiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxinzeng"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#iconxinzeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwodegongzuoqu-quanxianweituo"></use>
                </svg>
                <div class="name">我的工作区-权限委托</div>
                <div class="code-name">#iconwodegongzuoqu-quanxianweituo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghuzuguanli"></use>
                </svg>
                <div class="name">用户组管理</div>
                <div class="code-name">#iconyonghuzuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongangweiguanli"></use>
                </svg>
                <div class="name">岗位管理</div>
                <div class="code-name">#icongangweiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconziyuanguanli"></use>
                </svg>
                <div class="name">资源管理</div>
                <div class="code-name">#iconziyuanguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghuguanli1"></use>
                </svg>
                <div class="name">用户管理</div>
                <div class="code-name">#iconyonghuguanli1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxitongguanli"></use>
                </svg>
                <div class="name">系统管理</div>
                <div class="code-name">#iconxitongguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshequcunquhuaguanli"></use>
                </svg>
                <div class="name">社区（村）区划管理</div>
                <div class="code-name">#iconshequcunquhuaguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiekou"></use>
                </svg>
                <div class="name">接口</div>
                <div class="code-name">#iconjiekou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshujuquanxian"></use>
                </svg>
                <div class="name">数据权限</div>
                <div class="code-name">#iconshujuquanxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghuguanli"></use>
                </svg>
                <div class="name">用户管理</div>
                <div class="code-name">#iconyonghuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjigouguanli"></use>
                </svg>
                <div class="name">机构管理</div>
                <div class="code-name">#iconjigouguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzidianguanli"></use>
                </svg>
                <div class="name">字典管理</div>
                <div class="code-name">#iconzidianguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbianmaguizeguanli"></use>
                </svg>
                <div class="name">编码规则管理</div>
                <div class="code-name">#iconbianmaguizeguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrizhi"></use>
                </svg>
                <div class="name">日志查看</div>
                <div class="code-name">#iconrizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxitongguanli-zidianguanli"></use>
                </svg>
                <div class="name">系统管理-字典管理</div>
                <div class="code-name">#iconxitongguanli-zidianguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcanshuguanli"></use>
                </svg>
                <div class="name">参数管理</div>
                <div class="code-name">#iconcanshuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzaixianyonghu"></use>
                </svg>
                <div class="name">在线用户</div>
                <div class="code-name">#iconzaixianyonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbangdingcheliang"></use>
                </svg>
                <div class="name">绑定</div>
                <div class="code-name">#iconbangdingcheliang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfenxiang"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#iconfenxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuichu1"></use>
                </svg>
                <div class="name">退出</div>
                <div class="code-name">#icontuichu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhengshushenqing"></use>
                </svg>
                <div class="name">证书申请</div>
                <div class="code-name">#iconzhengshushenqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshengcheng"></use>
                </svg>
                <div class="name">生成 </div>
                <div class="code-name">#iconshengcheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontingyong1"></use>
                </svg>
                <div class="name">停用</div>
                <div class="code-name">#icontingyong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontijiao"></use>
                </svg>
                <div class="name">提交 (1)</div>
                <div class="code-name">#icontijiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqiyong"></use>
                </svg>
                <div class="name">启用</div>
                <div class="code-name">#iconqiyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantouzuoshuang"></use>
                </svg>
                <div class="name">箭头 左双</div>
                <div class="code-name">#iconjiantouzuoshuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconarrow2-right"></use>
                </svg>
                <div class="name">箭头 右双-01</div>
                <div class="code-name">#iconarrow2-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakan"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#iconchakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#iconshouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconQQdenglu"></use>
                </svg>
                <div class="name">QQ登录</div>
                <div class="code-name">#iconQQdenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouji1"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#iconshouji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmima1"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#iconmima1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanzhengma"></use>
                </svg>
                <div class="name">验证码</div>
                <div class="code-name">#iconyanzhengma</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshoujihaodenglu"></use>
                </svg>
                <div class="name">手机号登录</div>
                <div class="code-name">#iconshoujihaodenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyouxiangdenglu"></use>
                </svg>
                <div class="name">邮箱登录</div>
                <div class="code-name">#iconyouxiangdenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghu2"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#iconyonghu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweixindenglu"></use>
                </svg>
                <div class="name">微信登录</div>
                <div class="code-name">#iconweixindenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhanghaodenglu"></use>
                </svg>
                <div class="name">账号登录</div>
                <div class="code-name">#iconzhanghaodenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyouxiang"></use>
                </svg>
                <div class="name">邮箱</div>
                <div class="code-name">#iconyouxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweibodenglu"></use>
                </svg>
                <div class="name">微博登录</div>
                <div class="code-name">#iconweibodenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconexcel1"></use>
                </svg>
                <div class="name">excel</div>
                <div class="code-name">#iconexcel1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconexcel"></use>
                </svg>
                <div class="name">excel</div>
                <div class="code-name">#iconexcel</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouji"></use>
                </svg>
                <div class="name">手机</div>
                <div class="code-name">#iconshouji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqq"></use>
                </svg>
                <div class="name">qq</div>
                <div class="code-name">#iconqq</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconweixin"></use>
                </svg>
                <div class="name">微信</div>
                <div class="code-name">#iconweixin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxinlang"></use>
                </svg>
                <div class="name">新浪</div>
                <div class="code-name">#iconxinlang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontishi2"></use>
                </svg>
                <div class="name">提示</div>
                <div class="code-name">#icontishi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghu1"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#iconyonghu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon--copy-copy"></use>
                </svg>
                <div class="name">信息</div>
                <div class="code-name">#iconicon--copy-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test2"></use>
                </svg>
                <div class="name">22</div>
                <div class="code-name">#iconicon-test2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test"></use>
                </svg>
                <div class="name">12</div>
                <div class="code-name">#iconicon-test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test1"></use>
                </svg>
                <div class="name">12</div>
                <div class="code-name">#iconicon-test1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjianhao"></use>
                </svg>
                <div class="name">减号</div>
                <div class="code-name">#iconjianhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxinzeng1"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#iconxinzeng1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchuyidong1"></use>
                </svg>
                <div class="name">删除／数字面板编辑态</div>
                <div class="code-name">#iconchuyidong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#iconshuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjichushujuguanli"></use>
                </svg>
                <div class="name">基础数据管理</div>
                <div class="code-name">#iconjichushujuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhushujuguanli"></use>
                </svg>
                <div class="name">主数据管理</div>
                <div class="code-name">#iconzhushujuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontishi"></use>
                </svg>
                <div class="name">提示</div>
                <div class="code-name">#icontishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontishi1"></use>
                </svg>
                <div class="name">提示</div>
                <div class="code-name">#icontishi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconinterchange"></use>
                </svg>
                <div class="name">信息交换</div>
                <div class="code-name">#iconinterchange</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpeizhi_2"></use>
                </svg>
                <div class="name">配置-2</div>
                <div class="code-name">#iconpeizhi_2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiagou"></use>
                </svg>
                <div class="name">架构</div>
                <div class="code-name">#iconjiagou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#iconmima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghu"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#iconyonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfuwuqi"></use>
                </svg>
                <div class="name">服务器</div>
                <div class="code-name">#iconfuwuqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpeizhi-copy"></use>
                </svg>
                <div class="name">配置</div>
                <div class="code-name">#iconpeizhi-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#iconbianji</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
