import axios from '@/core/http/axios'

export const getSubscriptUserList = (data, resourceCode) => {
  data.systemCode = config.systemCode
  return axios({
    url: `/${config.appCode_uims}/msgOpen/getSubscriptUserList`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
export const getMsgReceiverGroup = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/msgOpen/getMsgReceiverGroup`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
// 获取收件人（单位+柜员）
export const getMsgReceiver = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/msgOpen/getMsgReceiver`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
// 获取收件人（单位)
export const getMsgReceiverUnit = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/msgOpen/getMsgReceiverUnit`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}

// 获取收件人（空间)
export const getMsgSpaceReceiver = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode_uims}/msgOpen/getMsgSpaceReceiver`,
    method: 'post',
    data,
    // 连后台时放开postType: "form"注释
    postType: "form",
    headers: {'X-resource-code': resourceCode, 'isBaseApi': true}
  })
}
