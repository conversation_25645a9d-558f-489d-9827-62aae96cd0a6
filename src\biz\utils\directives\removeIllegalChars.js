const trigger = (el, type) => {
  let ev = document.createEvent('HTMLEvents'); // 创建HTML事件
  ev.initEvent(type, true, true); // 初始化事件，type为事件类型，如input
  el.dispatchEvent(ev); // 派发事件
}

const removeIllegalChars = {
  bind: function (el, binding, vnode) {
    let isInputZh = false;
    // 自定义正则表达式
    var regRule = binding.value ? binding.value : /[<>!@#$^&*()]/g;
    el.$handle = function () {
      if (!isInputZh) {
        let val = el.querySelector(':first-child').value
        el.querySelector(':first-child').value = val.replace(regRule, '');// 替换所有匹配项
        const propsName = vnode.data.model.expression.split('.')[0]
        const key = vnode.data.model.expression.split('.')[1]
        // 将格式化后的值重新赋值给v-model绑定的值
        vnode.context[propsName][key] = el.querySelector(':first-child').value
        trigger(el.querySelector(':first-child'), 'input')
      }
    }
    el.$isTrue = function () {
      isInputZh = true
    }
    el.$isFalse = function () {
      isInputZh = false
    }
    el.addEventListener("compositionstart", el.$isTrue)
    el.addEventListener("compositionend", el.$isFalse)
    el.addEventListener('keyup', el.$handle)
  },
  unbind: function (el) {
    el.removeEventListener("compositionstart", el.$isTrue)
    el.removeEventListener("compositionend", el.$isFalse)
    el.removeEventListener('keyup', el.$handle)
  }
}

export { removeIllegalChars }
