<template>
  <!-- 新增Form -->
  <el-dialog v-dialogDrag :title="modeMap[dialogModes]" :visible.sync="dialogMainFormVisible" width="calc(100% - 100px)"
             min-width="800px"
             @opened="initDialog" @close="destrory()">
    <el-container>
      <el-scrollbar class="scrollbar-wrapper" ref="scrollbar">
        <el-form ref="dataForm" :model="temp" label-position="right" label-width="120px">
          <el-row>
            <el-col :span="8">
              <el-form-item label="业务消息编码" prop="mbId"
                :rules="dialogModes === 'view'? [] : [
            {required: true, message: '此项为必填项', trigger: 'blur'},
            {validator: checkAlphanum, trigger: 'blur'}]">
                <el-input :disabled="viewDisabled || dialogModes === 'update'" v-model="temp.mbId" maxlength="32"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务消息名称" prop="bizName"
                :rules="dialogModes === 'view'? [] : [{required: true, message: '此项为必填项', trigger: 'blur'}]">
                <el-input :disabled="viewDisabled" v-model="temp.bizName" maxlength="50"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务消息类型" prop="msgBizTypeName"
                :rules="dialogModes === 'view'? [] : [{required: true, message: '此项为必填项', trigger: 'blur'}]">
                <el-input :disabled="true" v-model="temp.msgBizTypeName" maxlength="32"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="入参" prop="params"
            :rules="dialogModes === 'view'? [] : [{validator: validateParams, trigger: 'blur'}]">
            <el-tooltip class="item" effect="light" content="新增" placement="bottom" v-if="!viewDisabled">
              <perm-button type="text" icon="add" size="mini" @click="handleAddParam"/>
            </el-tooltip>
            <el-tooltip class="item" effect="light" content="删除" placement="bottom" v-if="!viewDisabled">
              <perm-button type="text" icon="delete" size="mini" @click="handleDelParam"/>
            </el-tooltip>
            <el-table
              :data="temp.params"
              border
              :row-class-name="tableRowClassName"
              @selection-change="selectParamsTableRow"
            >
              <el-table-column v-if="!viewDisabled" width="50" type="selection" label="" header-align="center"
                               align="center">
              </el-table-column>
              <el-table-column width="50" type="index" label="序号" header-align="center" align="left">
              </el-table-column>
              <el-table-column prop="paramName" width="120" label="参数名称" header-align="center" align="left" show-overflow-tooltip>
                <template slot-scope="{row}">
                  <el-input :disabled="viewDisabled" v-model="row.paramName" maxlength="32"/>
                </template>
              </el-table-column>
              <el-table-column prop="remark" width="150" label="描述" header-align="center" align="left" show-overflow-tooltip>
                <template slot-scope="{row}">
                  <el-input :disabled="viewDisabled" v-model="row.remark" maxlength="32"/>
                </template>
              </el-table-column>
              <el-table-column prop="dataType" width="120" label="参数类型" header-align="center" align="left">
                <template slot-scope="{row}">
                  <select-plus :disabled="viewDisabled" dictType="DATA_TYPE" style="width: 100%;" v-model="row.dataType"
                               @change="changeFormat(row)"></select-plus>
                </template>
              </el-table-column>
              <el-table-column prop="isRequired" width="100" label="是否必填" header-align="center" align="left">
                <template slot-scope="{row}">
                  <select-plus :disabled="viewDisabled" dictType="IS_FLAG" style="width: 100%;"
                               v-model="row.isRequired"></select-plus>
                </template>
              </el-table-column>
              <el-table-column prop="format" width="150" label="格式" header-align="center" align="left">
                <template slot-scope="{row}">
                  <el-input v-if="row.dataType !== 'date' && row.dataType !== 'time'"
                            :disabled="viewDisabled || (row.dataType !== 'date' && row.dataType !== 'time')"
                            v-model="row.format" maxlength="32"/>
                  <select-plus v-else :disabled="viewDisabled" style="width: 100%;" dictType="TIME_FORMATTER"
                               v-model="row.format"></select-plus>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-collapse v-model="activeCollapse">
            <el-collapse-item name="1">
              <template slot="title">
                <div class="title">
                  <span>站内消息设置</span>
                </div>
              </template>
              <el-form-item label="">
                <el-checkbox v-model="temp.isSendMsg" :disabled="viewDisabled || temp.isForceMsg">是否启用</el-checkbox>
                <el-checkbox v-model="temp.isForceMsg" :disabled="viewDisabled" @change="hdlMsg">是否强制</el-checkbox>
              </el-form-item>
              <el-form-item prop="msgTempalte"
                :rules="dialogModes === 'view'? [] : [{required: checkTemplateRequired('msg'), validator: (rule, value, callback) => checkTemplate(rule, value, callback, 'msg'), trigger: 'blur'}]">
                <span slot="label">
                  <span class="span-box">
                  <span>模板</span>
                    <tip :tipInfo='{titles:[], infos:["该编辑器为Markdown编辑器，支持即时渲染。" +
                   "可通过点击工具栏格式工具编辑格式，也可通过使用Markdown语法编辑。" +
                    "1.无序/有序列表编辑模式下双击Enter退出列表编辑模式；" +
                     "2.引用编辑模式下按下Alt+Enter退出引用编辑模式；" +
                      "3.点击插入参数工具向光标处插入参数占位字符串；" +
                       "4.编辑器中使用velocity语法时请勿使用大括号(｛｝)，$符号前必须加上转义符(\\)，否则velocity语法可能与markdown语法产生冲突。"]}'></tip>
                  </span>
                </span>
                <div v-if="!viewDisabled" id="vditor" style="height: 300px"></div>
                <mark-down v-else :content="temp.msgTempalte"></mark-down>
                <el-input v-model="temp.msgTempalte" :autosize="{ minRows: 5, maxRows: 10}" type="hidden"
                          :disabled="viewDisabled" class="msg"/>
                <!--                <el-input :disabled="viewDisabled" v-model="temp.msgTempalte" maxlength="4000" type="textarea" :rows="4"/>-->
              </el-form-item>
              <el-form-item prop="actions"
                :rules="dialogModes === 'view'? [] : [{validator: validateActions, trigger: 'blur'}]">
                <span slot="label">
                  <span class="span-box">
                  <span>业务动作</span>
                     <tip :tipInfo='{titles:["", "", "", ""], infos:["前端路由仅支持使用窗口组件，且组件必置于biz目录中；"
                      , "路径格式示例：/views/Test/Dialog/TestDialog，注意文件后缀.vue不需要,且路径以/开头；"
                      , "dialog中必须包含props参数‘dialogOpenVisible’和‘params’，‘dialogOpenVisible’用于监听控制dialog是否显示；‘params’用于接收传入的参数，dialog中对参数的具体运用由业务系统自身实现；"
                      , "dialog中必须监听窗口关闭事件，关闭窗口时执行函数‘this.$emit(\"closeDialog\", false)’。"]}'></tip>
                  </span>
                </span>
                <el-tooltip class="item" effect="light" content="新增" placement="bottom" v-if="!viewDisabled">
                  <perm-button type="text" icon="add" size="mini" @click="handleAddAction"/>
                </el-tooltip>
                <el-tooltip class="item" effect="light" content="删除" placement="bottom" v-if="!viewDisabled">
                  <perm-button type="text" icon="delete" size="mini" @click="handleDelAction"/>
                </el-tooltip>
                <el-table
                  :data="temp.actions"
                  border
                  :row-class-name="tableRowActionClassName"
                  @selection-change="selectActionTableRow"
                >
                  <el-table-column v-if="!viewDisabled" width="50" type="selection" label="" header-align="center"
                                   align="center">
                  </el-table-column>
                  <el-table-column width="50" type="index" label="序号" header-align="center" align="left">
                  </el-table-column>
                  <el-table-column prop="actionCode" width="120" label="动作编码" header-align="center" align="left">
                    <template slot-scope="{row}">
                      <el-input :disabled="viewDisabled" v-model="row.actionCode" maxlength="50"/>
                    </template>
                  </el-table-column>
                  <el-table-column prop="actionName" width="120" label="动作名称" header-align="center" align="left">
                    <template slot-scope="{row}">
                      <el-input :disabled="viewDisabled" v-model="row.actionName" maxlength="200"/>
                    </template>
                  </el-table-column>
                  <el-table-column prop="actionType" width="120" label="动作类型" header-align="center" align="left">
                    <template slot-scope="{row}">
                      <select-plus :disabled="viewDisabled" dictType="ACTION_TYPE" style="width: 100%"
                                   v-model="row.actionType"></select-plus>
                    </template>
                  </el-table-column>
                  <el-table-column prop="actionRoute" width="120" label="动作路由" header-align="center" align="left">
                    <template slot-scope="{row}">
                      <el-input :disabled="viewDisabled" v-model="row.actionRoute" maxlength="200"/>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template slot="title">
                <div class="title">
                  <span>邮箱消息设置</span>
                </div>
              </template>
              <el-form-item label="">
                <el-checkbox v-model="temp.isSendEmail" :disabled="viewDisabled || temp.isForceEmail">是否启用</el-checkbox>
                <el-checkbox v-model="temp.isForceEmail" :disabled="viewDisabled" @change="hdlEmail">是否强制</el-checkbox>
              </el-form-item>
              <el-form-item label="模板" prop="emailTemplate"
                :rules="dialogModes === 'view'? [] : [{required: checkTemplateRequired('email'), validator: (rule, value, callback) => checkTemplate(rule, value, callback, 'email'), trigger: 'blur'}]">
                <el-input :disabled="viewDisabled" v-model="temp.emailTemplate" maxlength="3000" type="textarea"
                          :rows="4" show-word-limit/>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item name="3">
              <template slot="title">
                <div class="title">
                  <span>短信消息设置</span>
                </div>
              </template>
              <el-form-item label="">
                <el-checkbox v-model="temp.isSendSms" :disabled="viewDisabled || temp.isForceSms">是否启用</el-checkbox>
                <el-checkbox v-model="temp.isForceSms" :disabled="viewDisabled" @change="hdlSms">是否强制</el-checkbox>
              </el-form-item>
              <el-form-item label="模板" prop="smsTemplate"
                :rules="dialogModes === 'view'? [] : [{required: checkTemplateRequired('sms'), validator: (rule, value, callback) => checkTemplate(rule, value, callback, 'sms'), trigger: 'blur'}]">
                <el-input :disabled="viewDisabled" v-model="temp.smsTemplate" maxlength="200" type="textarea" :rows="4"
                          show-word-limit/>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item v-for="item in temp.otherMsgConfig" :name="item.type" :key="item.type">
              <template slot="title">
                <div class="title">
                  <span>{{item.typeName}}消息设置</span>
                </div>
              </template>
              <el-form-item label="">
                <el-checkbox v-model="item.isSend" :disabled="viewDisabled || item.isForce">是否启用</el-checkbox>
                <el-checkbox v-model="item.isForce" :disabled="viewDisabled" @change="hdlGzt(item)">是否强制</el-checkbox>
              </el-form-item>
              <el-form-item label="模板" :prop="item.type + '_template'"
                :rules="dialogModes === 'view'? [] : [{required: checkTemplateRequired(item), validator: (rule, value, callback) => checkTemplate(rule, value, callback, item), trigger: 'blur'}]">
                <el-input :disabled="viewDisabled" v-model="item.template" maxlength="200" type="textarea" :rows="4"
                          show-word-limit/>
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-scrollbar>
    </el-container>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogMainFormVisible = false">{{dialogModes==='view'?'关闭':'取消'}}</el-button>
      <el-button class="save-btn" type="primary" @click="dialogModes==='create'?save():update()" v-if="!viewDisabled"
                 :loading="okLoading">保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import Vditor from 'vditor'
  import MarkDown from "@/core/components/MarkDown";
  import "@/core/styles/vditor/scss/index.scss"
  import SelectPlus from "@/core/components/SelectPlus";
  import PermButton from '@/core/components/PermButton'
  import Tip from '@/core/components/Tip'
  import {resourceCode} from "@/biz/http/settings"
  import {checkAlphanum} from '@/core/utils/validate'

  export default {
    components: {SelectPlus, PermButton, Tip, MarkDown},
    name: "MainDialog",
    data() {
      return {
        contentEditor: undefined,
        activeCollapse: ['1', '2', '3'],
        okLoading: false,
        modeMap: {
          create: '新增',
          update: '编辑',
          view: '查看'
        },
        // 新增/编辑列表
        temp: {
          params: [],
          actions: [],
          isForceSms: undefined,
          enabled: undefined,
          deleted: undefined,
          orderNo: undefined,
          createUserId: undefined,
          createTime: undefined,
          bizName: undefined,
          msgBizType: undefined,
          msgBizTypeName: undefined,
          msgTempalte: undefined,
          smsTemplate: undefined,
          emailTemplate: undefined,
          isSendMsg: undefined,
          isForceMsg: undefined,
          isSendEmail: undefined,
          isForceEmail: undefined,
          isSendSms: undefined,
          otherMsgConfig: [],
          mbId: undefined,
          demoCol_Ignore: undefined
        },
        defaultConfigs: config.gztSwitch ? [{
          type: 'gzt',
          typeName: '赣政通',
          template: undefined,
          isSend: false,
          isForce: false
        }] : [],
        selectParams: [],
        selectActions: [],
        dialogMainFormVisible: false,
        stompClient: undefined,
        ws: null
      }
    },
    props: {
      dialogModes: String,
      dialogFormVisible: Boolean,
      mbId: String,
      dictTypeTemp: Object,
      viewDisabled: Boolean
    },
    methods: {
      checkAlphanum(rule, value, callback) {
        checkAlphanum(rule, value, callback)
      },
      validateParams(rule, value, callback) {
        if (this.temp.params && this.temp.params.length > 0) {
          for (let i = 0; i < this.temp.params.length; i++) {
            if (!this.temp.params[i].paramName) {
              return callback(new Error('参数名称不能为空'));
            }
            if (!this.temp.params[i].remark) {
              return callback(new Error('参数描述不能为空'));
            }
            if (this.temp.params[i].dataType === 'date' || this.temp.params[i].dataType === 'time') {
              if (!this.temp.params[i].format) {
                return callback(new Error('参数类型为时间或日期类型时格式不能为空'));
              }
            }
          }
          let hash = {};
          for (var i in this.temp.params) {
            if (hash[this.temp.params[i].paramName]) {
              return callback(new Error('参数名称不得重复'));
            }
            hash[this.temp.params[i].paramName] = true;
          }
        }
        callback();
      },
      validateActions(rule, value, callback) {
        if (this.temp.actions && this.temp.actions.length > 0) {
          for (let i = 0; i < this.temp.actions.length; i++) {
            if (!this.temp.actions[i].actionCode) {
              return callback(new Error('动作编码不能为空'));
            }
            if (!this.temp.actions[i].actionName) {
              return callback(new Error('动作名称不能为空'));
            }
            if (this.temp.actions[i].actionType === '1' && !this.temp.actions[i].actionRoute) {
              return callback(new Error('动作路由不能为空'));
            }
          }
          let hash = {};
          for (var i in this.temp.actions) {
            if (hash[this.temp.actions[i].actionCode]) {
              this.$refs.dataForm.validateField('actions')
              return callback(new Error('动作编码不得重复'));
            }
            hash[this.temp.actions[i].actionCode] = true;
          }
        }
        callback();
      },
      hdlMsg(val) {
        if(val) {
          this.temp.isSendMsg = true;
        }
      },
      hdlEmail(val) {
        if(val) {
          this.temp.isSendEmail = true;
        }
      },
      hdlSms(val) {
        if(val) {
          this.temp.isSendSms = true;
        }
      },
      hdlGzt(item) {
        if(item.isForce) {
          item.isSend = true;
        }
      },
      checkTemplateRequired(param) {
        if(typeof param === 'string') {
          switch(param) {
            case 'msg':
              if(this.temp.isForceMsg || this.temp.isSendMsg) {
                return true;
              }
              break;
            case 'email':
              if(this.temp.isForceEmail || this.temp.isSendEmail) {
                return true;
              }
              break;
            case 'sms':
              if(this.temp.isForceSms || this.temp.isSendSms) {
                return true;
              }
              break;
            default:
              break;
          }
        } else if(typeof param === 'object') {
          // 除站内、邮件、短信等其他类型的消息设置
          if(param.isForce || param.isSend) {
            return true;
          }
        }
        return false;
      },
      checkTemplate(rule, value, callback, param) {
        if(value) {
          // 如果模板内容不为空，直接验证通过
          callback();
        }
        // 如果模板内容为空，则进一步判断是否勾选了启用或强制，只要勾选了其中一个，则验证不通过
        if(typeof param === 'string') {
          switch(param) {
            case 'msg':
              if(this.temp.isForceMsg || this.temp.isSendMsg) {
                return callback(new Error('站内消息设置模板为必填项'))
              }
              break;
            case 'email':
              if(this.temp.isForceEmail || this.temp.isSendEmail) {
                return callback(new Error('邮箱消息设置模板为必填项'))
              }
              break;
            case 'sms':
              if(this.temp.isForceSms || this.temp.isSendSms) {
                return callback(new Error('短信消息设置模板为必填项'))
              }
              break;
            default:
              break;
          }
        } else if(typeof param === 'object') {
          if((param.isForce || param.isSend) && !param.template) {
            return callback(new Error(`${param.typeName}消息设置模板为必填项`))
          }
        }
        callback();
      },
      changeFormat(row) {
        if (row && (row.index || row.index === 0)) {
          this.temp.params[row.index].format = ''
        }
      },
      tableRowClassName({row, rowIndex}) {
        // 把每一行的索引放进row
        row.index = rowIndex;
      },
      selectParamsTableRow(row, event, column) {
        this.selectParams = Object.assign([], row)
      },
      tableRowActionClassName({row, rowIndex}) {
        // 把每一行的索引放进row
        row.index = rowIndex;
      },
      selectActionTableRow(row, event, column) {
        this.selectActions = Object.assign([], row)
      },
      handleAddParam() {
        if (this.temp.params && this.temp.params.length > 0) {
          for (let i = 0; i < this.temp.params.length; i++) {
            if (!this.temp.params[i].paramName) {
              this.$refs.dataForm.validateField('params')
              return;
            }
            if (!this.temp.params[i].remark) {
              this.$refs.dataForm.validateField('params')
              return;
            }
            if (this.temp.params[i].dataType === 'date' || this.temp.params[i].dataType === 'time') {
              if (!this.temp.params[i].format) {
                this.$refs.dataForm.validateField('params')
                return;
              }
            }
          }
          let hash = {};
          for (var i in this.temp.params) {
            if (hash[this.temp.params[i].paramName]) {
              this.$refs.dataForm.validateField('params')
              return;
            }
            hash[this.temp.params[i].paramName] = true;
          }
        }
        if (!this.temp.params) {
          this.temp.params = []
        }
        this.temp.params.push({paramName: "", remark: "", dataType: "string", isRequired: "1", format: ""})
      },
      handleAddAction() {
        if (this.temp.actions && this.temp.actions.length > 0) {
          for (let i = 0; i < this.temp.actions.length; i++) {
            if (!this.temp.actions[i].actionCode) {
              this.$refs.dataForm.validateField('actions')
              return;
            }
            if (!this.temp.actions[i].actionName) {
              this.$refs.dataForm.validateField('actions')
              return;
            }
            if (this.temp.actions[i].actionType === '1' && !this.temp.actions[i].actionRoute) {
              this.$refs.dataForm.validateField('actions')
              return;
            }
          }
          let hash = {};
          for (var i in this.temp.actions) {
            if (hash[this.temp.actions[i].actionCode]) {
              this.$refs.dataForm.validateField('actions')
              return;
            }
            hash[this.temp.actions[i].actionCode] = true;
          }
        }
        if (!this.temp.actions) {
          this.temp.actions = []
        }
        this.temp.actions.push({actionCode: "", actionName: "", actionType: "1", actionRoute: ""})
      },
      handleDelParam() {
        if (this.selectParams && this.selectParams.length > 0) {
          for (let i = this.selectParams.length - 1; i >= 0; i--) {
            this.temp.params.splice(this.selectParams[i].index, 1)
          }
        }
      },
      handleDelAction() {
        if (this.selectActions && this.selectActions.length > 0) {
          for (let i = this.selectActions.length - 1; i >= 0; i--) {
            this.temp.actions.splice(this.selectActions[i].index, 1)
          }
        }
      },
      destrory() {
        if (this.stompClient) {
          this.stompClient.disconnect();
        }
        this.resetTemp();
        this.okLoading = false;
      },
      initDialog() {
        let toolbar = [
          'headings',
          'bold',
          'italic',
          'strike',
          'link',
          '|',
          'list',
          'ordered-list',
          'check',
          'outdent',
          'indent',
          '|',
          'line',
          'insert-before',
          'insert-after',
          '|',
          'table',
          '|',
          'undo',
          'redo',
          {
            hotkey: '⌘-⇧-p',
            name: 'params',
            tipPosition: 'ne',
            tip: '插入参数',
            icon: '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="768" height="768" viewBox="0 0 768 768"><path d="M342 426v-84h426v84h-426zM342 256v-86h426v86h-426zM0 0h768v86h-768v-86zM342 598v-86h426v86h-426zM0 214l170 170-170 170v-340zM0 768v-86h768v86h-768z"></path></svg>',
            click: () => {
              this.contentEditor.insertValue("\\$!params.get('参数名称')", true)
            }
          }
        ]
        if (this.viewDisabled) {
          toolbar = []
        }
        if (this.dialogModes !== 'view') {
          this.contentEditor = new Vditor('vditor', {
            cdn: 'vditor',
            toolbar: toolbar,
            mode: 'wysiwyg', // 可选模式：sv, ir, wysiwyg
            width: '100%',
            // toolbarConfig: {
            //   hide: this.viewDisabled
            // },
            cache: {
              enable: false
            },
            counter: {
              enable: true, // 是否启用计数器
              max: 3000, // 允许输入的最大值
              type: 'text' // 统计类型:md,text
            },
            after: () => {
              this.contentEditor.setValue(this.temp.msgTempalte ? this.temp.msgTempalte : '')
            }
          })
          this.contentEditor.enable()
        }
        this.$nextTick(() => {
          this.$refs.scrollbar.wrap.scrollTop = 0
          this.$refs.scrollbar.update()
        })
      },
      save() {
        let c = this.contentEditor.getValue()
        if (!c) {
          this.contentEditor.focus()
        }
        this.temp.msgTempalte = c.trim()
        this.$refs['dataForm'].validate((valid, obj) => {
          if (valid) {
            this.okLoading = true
            let tempData = Object.assign({}, this.temp)
            tempData.isForceEmail = this.temp.isForceEmail ? "1" : "0"
            tempData.isForceMsg = this.temp.isForceMsg ? "1" : "0"
            tempData.isForceSms = this.temp.isForceSms ? "1" : "0"
            tempData.isSendEmail = this.temp.isSendEmail ? "1" : "0"
            tempData.isSendMsg = this.temp.isSendMsg ? "1" : "0"
            tempData.isSendSms = this.temp.isSendSms ? "1" : "0"
            tempData.otherMsgConfig = []
            JSON.parse(JSON.stringify(this.temp.otherMsgConfig)).forEach(item => {
              item.isSend = item.isSend ? "1" : "0"
              item.isForce = item.isForce ? "1" : "0"
              tempData.otherMsgConfig.push(item)
            })
            tempData.otherMsgConfig = JSON.stringify(tempData.otherMsgConfig)
            this.$api.msgBiz.save(tempData, resourceCode.msgBiz).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: '新增成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      update() {
        let c = this.contentEditor.getValue()
        if (!c) {
          this.contentEditor.focus()
        }
        this.temp.msgTempalte = c.trim()
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.okLoading = true
            let tempData = Object.assign({}, this.temp)
            tempData.isForceEmail = this.temp.isForceEmail ? "1" : "0"
            tempData.isForceMsg = this.temp.isForceMsg ? "1" : "0"
            tempData.isForceSms = this.temp.isForceSms ? "1" : "0"
            tempData.isSendEmail = this.temp.isSendEmail ? "1" : "0"
            tempData.isSendMsg = this.temp.isSendMsg ? "1" : "0"
            tempData.isSendSms = this.temp.isSendSms ? "1" : "0"
            tempData.otherMsgConfig = []
            JSON.parse(JSON.stringify(this.temp.otherMsgConfig)).forEach(item => {
              item.isSend = item.isSend ? "1" : "0"
              item.isForce = item.isForce ? "1" : "0"
              tempData.otherMsgConfig.push(item)
            })
            tempData.otherMsgConfig = JSON.stringify(tempData.otherMsgConfig)
            this.$api.msgBiz.update(tempData, resourceCode.msgBiz).then((res) => {
              this.okLoading = false
              this.dialogMainFormVisible = false
              this.$emit("getList")
              this.$notify({
                title: '操作成功',
                message: '编辑成功',
                type: 'success',
                duration: 2000
              })
            }).catch((res) => {
              this.okLoading = false
            })
          }
        })
      },
      resetTemp() {
        this.temp = {
          params: [],
          actions: [],
          isForceSms: false,
          enabled: undefined,
          deleted: undefined,
          orderNo: undefined,
          createUserId: undefined,
          createTime: undefined,
          bizName: undefined,
          msgBizType: undefined,
          msgTempalte: undefined,
          smsTemplate: undefined,
          emailTemplate: undefined,
          isSendMsg: true,
          isForceMsg: false,
          isSendEmail: false,
          isForceEmail: false,
          isSendSms: false,
          otherMsgConfig: [],
          mbId: undefined,
          demoCol_Ignore: undefined
        }
        this.activeCollapse = ['1', '2', '3']
      }
    },
    watch: {
      dialogFormVisible: function (newValue, oldValue) {
        this.dialogMainFormVisible = newValue
        if (newValue) {
          if (this.dialogModes === 'create') {
            this.resCode = resourceCode.msgBizAdd // 新增资源编码
            this.resetTemp()
            this.temp.msgBizType = this.dictTypeTemp.dictId
            this.temp.msgBizTypeName = this.dictTypeTemp.name
            this.$set(this.temp, 'otherMsgConfig', [])
            this.defaultConfigs.forEach(item => {
              this.temp.otherMsgConfig.push(item)
              this.activeCollapse.push(item.type)
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          } else if (this.dialogModes === 'update') {
            this.$api.msgBiz.getById({mbId: this.mbId}, resourceCode.msgBiz).then(res => {
              this.temp = {
                params: res.data.params,
                actions: res.data.actions,
                isForceSms: res.data.isForceSms === "1",
                enabled: res.data.enabledCode,
                deleted: res.data.deleted,
                orderNo: res.data.orderNo,
                createUserId: res.data.createUserId,
                bizName: res.data.bizName,
                msgBizType: res.data.msgBizTypeId,
                msgBizTypeName: res.data.msgBizType,
                msgTempalte: res.data.msgTempalte,
                smsTemplate: res.data.smsTemplate,
                emailTemplate: res.data.emailTemplate,
                isSendMsg: res.data.isSendMsg === "1",
                isForceMsg: res.data.isForceMsg === "1",
                isSendEmail: res.data.isSendEmail === "1",
                isForceEmail: res.data.isForceEmail === "1",
                isSendSms: res.data.isSendSms === "1",
                mbId: res.data.mbId,
                demoCol_Ignore: undefined
              }
              this.$set(this.temp, 'otherMsgConfig', [])
              this.defaultConfigs.forEach(item => {
                let lst = res.data.otherMsgConfig && JSON.parse(res.data.otherMsgConfig).filter(item2 => item.type === item2.type)
                if(lst && lst.length > 0) {
                  let d = lst[0]
                  d.isForce = d.isForce === "1"
                  d.isSend = d.isSend === "1"
                  this.temp.otherMsgConfig.push(d)
                } else {
                  this.temp.otherMsgConfig.push(item)
                }
                this.activeCollapse.push(item.type)
              })
              if (this.contentEditor) {
                if (!this.contentEditor.getValue().trim() && this.temp.msgTempalte) {
                  this.contentEditor.setValue(this.temp.msgTempalte)
                }
              }
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          } else if (this.dialogModes === 'view') {
            this.$api.msgBiz.getById({mbId: this.mbId}, resourceCode.msgBiz).then(res => {
              this.temp = {
                params: res.data.params,
                actions: res.data.actions,
                isForceSms: res.data.isForceSms === "1",
                enabled: res.data.enabledCode,
                deleted: res.data.deleted,
                orderNo: res.data.orderNo,
                createUserId: res.data.createUserId,
                bizName: res.data.bizName,
                msgBizType: res.data.msgBizTypeId,
                msgBizTypeName: res.data.msgBizType,
                msgTempalte: res.data.msgTempalte,
                smsTemplate: res.data.smsTemplate,
                emailTemplate: res.data.emailTemplate,
                isSendMsg: res.data.isSendMsg === "1",
                isForceMsg: res.data.isForceMsg === "1",
                isSendEmail: res.data.isSendEmail === "1",
                isForceEmail: res.data.isForceEmail === "1",
                isSendSms: res.data.isSendSms === "1",
                mbId: res.data.mbId,
                demoCol_Ignore: undefined
              }
              this.$set(this.temp, 'otherMsgConfig', [])
              this.defaultConfigs.forEach(item => {
                let lst = res.data.otherMsgConfig && JSON.parse(res.data.otherMsgConfig).filter(item2 => item.type === item2.type)
                if(lst && lst.length > 0) {
                  let d = lst[0]
                  d.isForce = d.isForce === "1"
                  d.isSend = d.isSend === "1"
                  this.temp.otherMsgConfig.push(d)
                } else {
                  this.temp.otherMsgConfig.push(item)
                }
                this.activeCollapse.push(item.type)
              })
            })
            this.$nextTick(() => {
              this.$refs['dataForm'].clearValidate()
            })
          }
        }
      },
      dialogMainFormVisible: function (newV, oldV) {
        this.$emit('closeDialog', newV)
      }
    }
  }
</script>

<style scoped lang="scss">
>>>.msg.el-input+ .el-form-item__error{
  margin-top: -38px;
}
</style>
