<template>
  <div class="queryTree-wrapper">
    <el-input
      placeholder="输入系统名称进行过滤"
      v-model="filterText"
      size="small">
    </el-input>
    <div class="tree-wrapper query list">
      <el-scrollbar wrap-class="scrollbar-wrapper" style="width:100%; height: 100%;">
        <el-tree class="list"
          v-loading="listLoading"
          :props="defaultProps"
          :data="list"
          node-key='systemId'
          highlight-current
          default-expand-all
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          :filter-node-method="filterNode"
          ref="systemTree">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span :title="node.label">{{ node.label }}</span>
          </span>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'SystemTree',
    data() {
      return {
        filterText: '',
        oldClickSystemId: '',
        selectSystemId: '',
        selectSystemName: '',
        listLoading: true,
        list: [],
        total: '',
        treeKey: 0,
        defaultProps: {
          children: 'children',
          label: 'sysName'
        }
      }
    },
    props: {
      resourceCode: String
    },
    methods: {
      getHeight() {
        this.contentStyleObj.height = window.innerHeight - 120 + 'px'
      },
      getSystemTree() {
        this.listLoading = true
        this.$api.system.getList({treeFlag: true}, this.resourceCode).then((res) => {
          // 加载角色集合
          this.listLoading = false;
          this.list = res.data;
          // 当前用户系统树列表只有一个值时
          if(this.list.length === 1) {
            this.$emit('systemTreeTransfer', this.list[0].systemId, this.list[0].sysName);// 将值绑定到systemTreeTransfer上传递过去
          }
        })
      },
      handleNodeClick(arg0, arg1, arg2) {
        const {systemId, sysName} = arg0
        if (this.oldClickSystemId === systemId) {
          // 取消选择
          this.$refs.systemTree.setCurrentKey();
          this.selectSystemId = ''
          this.selectSystemName = ''
          this.oldClickSystemId = ''
        } else {
          this.oldClickSystemId = systemId
          this.selectSystemId = systemId
          this.selectSystemName = sysName
        }
        this.$emit('systemTreeTransfer', this.selectSystemId, this.selectSystemName)// 将值绑定到systemTreeTransfer上传递过去
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.sysName.indexOf(value) !== -1;
      }
    },
    watch: {
      filterText(val) {
        this.$refs.systemTree.filter(val);
      }
    },
    created() {
      this.getSystemTree()
    }
  }
</script>
<style lang="scss" scoped>
.el-scrollbar__view {
  display: inline-block;
}
>>>.el-tree-node__label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
