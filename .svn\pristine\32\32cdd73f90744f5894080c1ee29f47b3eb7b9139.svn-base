<template>
  <div class="site-wrapper site-page--not-found">
    <div class="site-content__warpper">
      <div class="site-content">
        <h2 class="not-found-title">404</h2>
        <p class="not-found-desc">抱歉！您访问的页面不存在</p>
        <el-button @click="$router.go(-1)">返回上一页</el-button>
        <el-button type="primary" class="not-found-btn-gohome" @click="$router.push('/intro')">进入首页</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'page404',
  activated() {
    let _this = this
    this.$message({
      type: "error",
      message: "正在自动帮您跳转至首页！",
      duration: 3000,
      onClose: function() {
        _this.$nextTick(() => {
          let btn = document.querySelector('.not-found-btn-gohome')
          btn && btn.click()
        })
      }
    });
  }
}
</script>

<style lang="scss" scoped>
  .site-wrapper.site-page--not-found {
    position: absolute;
    top: 60px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    .site-content__wrapper {
      padding: 0;
      margin: 0;
      background-color: #fff;
    }
    .site-content {
      position: fixed;
      top: 15%;
      left: 50%;
      z-index: 2;
      padding: 30px;
      text-align: center;
      transform: translate(-50%, 0);
    }
    .not-found-title {
      margin: 20px 0 15px;
      font-size: 8em;
      font-weight: 500;
      color: rgb(55, 71, 79);
    }
    .not-found-desc {
      margin: 0 0 30px;
      font-size: 26px;
      text-transform: uppercase;
      color: rgb(118, 131, 143);
      > em {
        font-style: normal;
        color: #ee8145;
      }
    }
    .not-found-btn-gohome {
      margin-left: 30px;
    }
  }
</style>
