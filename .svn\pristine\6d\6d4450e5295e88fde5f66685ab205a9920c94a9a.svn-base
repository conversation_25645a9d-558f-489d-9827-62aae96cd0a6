<template>
  <div class="theme-container" v-show="dialogVisible">
    <el-dialog ref="themeDialog" v-dialogDrag width="1140px" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div slot="title" class="el-dialog__title">
        <span class="title">切换风格</span>
      </div>
      <el-scrollbar wrap-class="scrollbar-wrapper" style="height:100%;">
        <el-row :gutter="20">
          <el-col :span="24" class="thumbnail-title">{{thumbnailLabel}}</el-col>
        </el-row>
        <el-row :gutter="20" class="thumbnail-wrapper">
          <el-col :span="19" class="thumbnail-container">
            <img :src="BASE_URL + 'config/pics/theme/thumbnail/' + thumbnailImg + '_m.png'"/>
          </el-col>
          <el-col :span="5" class="thumbnail-swiper">
            <swiper class="swiper2" :options="swiperOption2">
              <swiper-slide v-for="item in thumbnails" :key="item.label">
                <div class="img-container" @click="chgThumbnail(item)"><img :src="BASE_URL + 'config/pics/theme/thumbnail/' + item.name + '_s.png'" /></div>
              </swiper-slide>
            </swiper>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" class="image-title">图案选择</el-col>
        </el-row>
        <el-row :gutter="20" class="image-swiper">
          <el-col :span="24">
            <swiper class="swiper" :options="swiperOption">
              <swiper-slide v-for="item in pictures" :key="item.label">
                <div :class="['img-container', picClass?picClass:'']" @click="chgPicture(item)"><img :src="BASE_URL + 'config/pics/theme/picture/' + item.name + '.png'" /></div>
                <el-checkbox :checked="true"></el-checkbox>
                <div class="title" @click="chgPicture(item)">{{item.label}}</div>
              </swiper-slide>
              <div class="swiper-button-prev" slot="button-prev"></div>
              <div class="swiper-button-next" slot="button-next"></div>
            </swiper>
          </el-col>
        </el-row>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" icon="uims-icon-cancel" @click.native="closeDialog" :loading="loading">关闭</el-button>
        <el-button class="save-btn" icon="uims-icon-save" type="primary" @click.native="save" :loading="loading">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import { toggleClass } from '@/core/utils/theme';
export default {
  name: "index",
  components: {
    Swiper,
    SwiperSlide
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      BASE_URL: process.env.BASE_URL,
      // imgUrl: require('../../../assets/pics/theme/thumbnail/blue_m.png'),
      dialogVisible: this.show,
      loading: false,
      swiperOption: {
        watchSlidesVisibility: true, // 防止不可点击
        // centeredSlides: true,
        // centeredSlidesBounds: true,
        // slideActiveClass: 'swiper-slide-active',
        slideToClickedSlide: true,
        preventClicksPropagation: true,
        slidesPerView: 5,
        spaceBetween: 20,
        slidesPerGroup: 1,
        loop: false,
        loopFillGroupWithBlank: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        /* initialSlide: localStorage.getItem('thumbnail') ? config.theme.pictures.findIndex(o => o.name === JSON.parse(localStorage.getItem('thumbnail')).thumbnailPicture) : 0 */
        on: {
          imagesReady: function() {
            this.emit('transitionEnd');
            var mySwiper = document.querySelector('.swiper').swiper;
            Object.values(mySwiper.slides).map(item => {
              if(typeof item === 'object') {
                item.classList.remove("swiperSlideActive");
              }
            })
            let thumbnail = config.theme.thumbnails.find(o => o.name === (localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css));
            let index = thumbnail.pictures.findIndex(o => o.name === (localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailPicture : 'nopic'));
            index = index > -1 ? index : 0;
            mySwiper.slides[index] && mySwiper.slides[index].classList.add("swiperSlideActive");
          }
        }
      },
      swiperOption2: {
        watchSlidesVisibility: true, // 防止不可点击
        // centeredSlides: true,
        // centeredSlidesBounds: true,
        // slideActiveClass: 'swiper-slide-active',
        direction: 'vertical',
        progressbarOpposite: true,
        slideToClickedSlide: true,
        preventClicksPropagation: true,
        slidesPerView: 4,
        spaceBetween: 15,
        slidesPerGroup: 1,
        mousewheel: true,
        grabCursor: true,
        initialSlide: config.theme.thumbnails.findIndex(o => o.name === (localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css)),
        slidesOffsetBefore: 0,
        slidesOffsetAfter: 0,
        normalizeSlideIndex: false,
        runCallbacksOnInit: true,
        on: {
          imagesReady: function() {
            this.emit('transitionEnd');
            var mySwiper2 = document.querySelector('.swiper2').swiper;
            Object.values(mySwiper2.slides).map(item => {
              if(typeof item === 'object') {
                item.classList.remove("swiperSlideActive");
              }
            })
            let index = config.theme.thumbnails.findIndex(o => o.name === (localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css));
            mySwiper2.slides[index] && mySwiper2.slides[index].classList.add("swiperSlideActive");
          }
        }
      },
      thumbnails: config.theme.thumbnails,
      // pictures: config.theme.pictures,
      pictures: {},
      picClass: localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css,
      thumbnailImg: localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css,
      thumbnailLabel: '',
      picture: localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailPicture : ''
    }
  },
  watch: {
    dialogVisible: function (newV, oldV) {
      // 告诉父元素改变show的值
      this.$emit('closeModal', newV)
    },
    show: function (newV, oldV) {
      this.dialogVisible = newV
      if(newV) {
        let h = document.documentElement.clientHeight || document.body.clientHeight
        let dialogEl = this.$refs.themeDialog.$el.firstElementChild
        if(h <= 940) {
          dialogEl.style['marginTop'] = '0px'
          dialogEl.style['marginBottom'] = '0px'
        } else {
          dialogEl.style['marginTop'] = 'calc((100vh - 940px) / 2)'
        }
      }
    },
    themeColor: {
      handler() {
        toggleClass(document.body, config.css + ' custom-' + this.themeColor)
      }
    }
  },
  methods: {
    // 切换用户
    closeDialog: function () {
      this.dialogVisible = false
    },
    save: function() {
      this.picClass = this.thumbnailImg;
      let thumbnail = {'thumbnailImg': this.thumbnailImg, 'thumbnailPicture': this.picture};
      localStorage.setItem('thumbnail', JSON.stringify(thumbnail));
      // 将皮肤设置存服务器上
      // { version: '1', theme: { skin: 'green', picture: 'science' } }
      const data = { version: config.theme.version, theme: { skin: this.thumbnailImg, picture: this.picture } }
      this.loading = true
      this.$api.authority.saveTheme(data).then((res) => {
        this.loading = false
        this.$store.commit('setThumbnail', thumbnail);
        toggleClass(document.body, this.thumbnailImg);
        this.dialogVisible = false
        this.$notify({
          title: '操作成功',
          message: '编辑皮肤设置成功',
          type: 'success',
          duration: 2000
        })
      }).catch((res) => {
        this.loading = false
      })
    },
    // 切换图像预览
    chgThumbnail: function (item) {
      this.picClass = item.name;
      this.thumbnailImg = item.name;
      this.thumbnailLabel = item.label;
      var mySwiper2 = document.querySelector('.swiper2').swiper;
      Object.values(mySwiper2.slides).map(item => {
        if(typeof item === 'object') {
          item.classList.remove("swiperSlideActive");
        }
      })
      mySwiper2.clickedSlide && mySwiper2.clickedSlide.classList.add("swiperSlideActive");
      if(config.theme.thumbnails.filter(obj => obj.name === item.name).length > 0) {
        this.pictures = config.theme.thumbnails.filter(obj => obj.name === item.name)[0].pictures;
      }
    },
    chgPicture: function (item) {
      this.picture = item.name
      var mySwiper = document.querySelector('.swiper').swiper;
      Object.values(mySwiper.slides).map(item => {
        if(typeof item === 'object') {
          item.classList.remove("swiperSlideActive");
        }
      })
      mySwiper.clickedSlide && mySwiper.clickedSlide.classList.add("swiperSlideActive");
    },
    init() {
      let thumbnailImgName = localStorage.getItem('thumbnail') ? JSON.parse(localStorage.getItem('thumbnail')).thumbnailImg : config.css;
      if(config.theme.thumbnails.filter(obj => obj.name === thumbnailImgName).length > 0) {
        this.pictures = config.theme.thumbnails.filter(obj => obj.name === thumbnailImgName)[0].pictures;
      }
    }
  },
  mounted() {
    let thumbnail = null;
    if(localStorage.getItem('thumbnail')) {
      thumbnail = JSON.parse(localStorage.getItem('thumbnail'))
    }
    let skin = thumbnail ? thumbnail.thumbnailImg : config.css;
    // toggleClass(document.body, config.css + ' custom-' + this.themeColor);
    toggleClass(document.body, skin);
    this.init();
  },
  computed: {
    themeColor: {
      get() {
        return this.$store.state.app.themeColor
      },
      set(val) {
        this.$store.commit('setThemeColor', { themeColor: val, flag: true })
      }
    }
  }
}
</script>
